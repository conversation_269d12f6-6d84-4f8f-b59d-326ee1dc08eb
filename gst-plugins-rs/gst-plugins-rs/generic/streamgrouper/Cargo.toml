[package]
name = "gst-plugin-streamgrouper"
authors = ["<PERSON> <<EMAIL>>"]
license = "MPL-2.0"
description = "Filter element that makes all the incoming streams share a group-id"
version.workspace = true
repository.workspace = true
edition.workspace = true
rust-version.workspace = true

[dependencies]
gst.workspace = true

[dev-dependencies]
gst-check.workspace = true

[lib]
name = "gststreamgrouper"
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[build-dependencies]
gst-plugin-version-helper.workspace = true

[features]
static = []
capi = []
doc = ["gst/v1_18"]

[package.metadata.capi]
min_version = "0.8.0"

[package.metadata.capi.header]
enabled = false

[package.metadata.capi.library]
install_subdir = "gstreamer-1.0"
versioning = false

[package.metadata.capi.pkg_config]
requires_private = "gstreamer-1.0, gobject-2.0, glib-2.0, gmodule-2.0"