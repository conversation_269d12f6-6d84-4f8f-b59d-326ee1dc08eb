// Copyright (C) 2017 Author: <PERSON><PERSON> <<EMAIL>>
//
// This Source Code Form is subject to the terms of the Mozilla Public License, v2.0.
// If a copy of the MPL was not distributed with this file, You can obtain one at
// <https://mozilla.org/MPL/2.0/>.
//
// SPDX-License-Identifier: MPL-2.0

use gst::glib;
use gst::prelude::*;

mod imp;

glib::wrapper! {
    pub struct S3Src(ObjectSubclass<imp::S3Src>) @extends gst_base::BaseSrc, gst::Element, gst::Object, @implements gst::URIHandler;
}

pub fn register(plugin: &gst::Plugin) -> Result<(), glib::BoolError> {
    #[cfg(not(feature = "doc"))]
    gst::Element::register(
        Some(plugin),
        "rusotos3src",
        gst::Rank::PRIMARY,
        S3Src::static_type(),
    )?;
    gst::Element::register(
        Some(plugin),
        "awss3src",
        gst::Rank::PRIMARY,
        S3Src::static_type(),
    )
}
