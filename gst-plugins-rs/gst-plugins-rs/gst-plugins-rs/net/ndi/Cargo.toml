[package]
name = "gst-plugin-ndi"
version.workspace = true
authors = ["<PERSON><PERSON> <rub<PERSON><PERSON><PERSON>@teltek.es>", "<PERSON> <<EMAIL>>", "<PERSON> <se<PERSON><PERSON>@centricular.com>"]
repository.workspace = true
license = "MPL-2.0"
description = "GStreamer NewTek NDI Plugin"
edition.workspace = true
rust-version.workspace = true

[dependencies]
glib.workspace = true
gst = { workspace = true, features = ["v1_16"] }
gst-base = { workspace = true, features = ["v1_16"] }
gst-audio = { workspace = true, features = ["v1_16"] }
gst-video = { workspace = true, features = ["v1_16"] }
anyhow = "1.0"
byte-slice-cast = "1"
byteorder = "1.0"
data-encoding = "2.4.0"
libloading = "0.8"
quick-xml = "0.37"
smallvec = { version = "1.11", features = ["const_generics"] }
thiserror = "2"

[build-dependencies]
gst-plugin-version-helper.workspace = true

[features]
default = ["sink"]
sink = ["gst/v1_18", "gst-base/v1_18"]
advanced-sdk = []
static = []
capi = []
doc = ["gst/v1_18"]

[lib]
name = "gstndi"
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[package.metadata.capi]
min_version = "0.9.21"

[package.metadata.capi.header]
enabled = false

[package.metadata.capi.library]
install_subdir = "gstreamer-1.0"
versioning = false
import_library = false

[package.metadata.capi.pkg_config]
requires_private = "gstreamer-1.0, gstreamer-base-1.0, gstreamer-audio-1.0, gstreamer-video-1.0, gobject-2.0, glib-2.0, gmodule-2.0"
