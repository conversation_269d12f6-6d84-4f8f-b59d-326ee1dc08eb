[package]
name = "gst-plugin-hlsmultivariantsink"
description = "GStreamer HLS (HTTP Live Streaming) multi-variant sink Plugin"
repository.workspace = true
version.workspace = true
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
edition.workspace = true
license = "MPL-2.0"
rust-version.workspace = true

[dependencies]
gst.workspace = true
gst-app.workspace = true
gio.workspace = true
m3u8-rs = "6.0"
chrono = "0.4"
gst-pbutils = { workspace = true, features = ["v1_22"] }
anyhow = "1"
bitreader = "0.3"
bytes = "1"
byteorder = "1.5"
enumn = "0.1"
thiserror = "2"

[dev-dependencies]
gst-audio.workspace = true
gst-video.workspace = true
gst-check.workspace = true
m3u8-rs = "6.0"
anyhow = "1"
gst-plugin-hlssink3 = { path = "../hlssink3" }
gst-plugin-fmp4 = { path = "../../mux/fmp4" }
serial_test = "3"

[build-dependencies]
gst-plugin-version-helper.workspace = true

[lib]
name = "gsthlsmultivariantsink"
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[features]
static = []
capi = []
doc = ["gst/v1_18"]

[package.metadata.capi]
min_version = "0.9.21"

[package.metadata.capi.header]
enabled = false

[package.metadata.capi.library]
install_subdir = "gstreamer-1.0"
versioning = false
import_library = false

[package.metadata.capi.pkg_config]
requires_private = "gstreamer-1.0, gstreamer-base-1.0, gobject-2.0, glib-2.0, gmodule-2.0"
