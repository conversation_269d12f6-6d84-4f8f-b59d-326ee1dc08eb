[package]
name = "gst-plugin-hlssink3"
description = "GStreamer HLS (HTTP Live Streaming) Plugin"
repository.workspace = true
version.workspace = true
authors = ["<PERSON> <<EMAIL>>"]
edition.workspace = true
license = "MPL-2.0"
rust-version.workspace = true

[dependencies]
gst.workspace = true
gst-app.workspace = true
gio.workspace = true
m3u8-rs = "6.0"
chrono = "0.4"
sprintf = "0.4"

[dev-dependencies]
gst-audio.workspace = true
gst-video.workspace = true
gst-check.workspace = true
gst-pbutils = { workspace = true, features = ["v1_20"] }
m3u8-rs = "6.0"
anyhow = "1"

[build-dependencies]
gst-plugin-version-helper.workspace = true

[lib]
name = "gsthlssink3"
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[features]
static = []
capi = []
doc = ["gst/v1_18"]

[package.metadata.capi]
min_version = "0.9.21"

[package.metadata.capi.header]
enabled = false

[package.metadata.capi.library]
install_subdir = "gstreamer-1.0"
versioning = false
import_library = false

[package.metadata.capi.pkg_config]
requires_private = "gstreamer-1.0, gstreamer-base-1.0, gobject-2.0, glib-2.0, gmodule-2.0"
