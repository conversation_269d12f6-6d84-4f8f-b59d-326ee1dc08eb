[package]
name = "gst-plugin-csound"
version.workspace = true
authors = ["Nat<PERSON>el Mojica <<EMAIL>>"]
repository.workspace = true
license = "MPL-2.0"
edition.workspace = true
rust-version.workspace = true
description = "GStreamer Audio Filter plugin based on Csound"

[dependencies]
gst.workspace = true
gst-base.workspace = true
gst-audio.workspace = true
csound = "0.1.8"
byte-slice-cast = "1.0"

[dev-dependencies]
gst-check.workspace = true

[lib]
name = "gstcsound"
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[[example]]
name = "csound-effect"
path = "examples/effect_example.rs"

[build-dependencies]
gst-plugin-version-helper.workspace = true

[features]
static = []
capi = []
doc = ["gst/v1_18"]

[package.metadata.capi]
min_version = "0.9.21"

[package.metadata.capi.header]
enabled = false

[package.metadata.capi.library]
install_subdir = "gstreamer-1.0"
versioning = false
import_library = false

[package.metadata.capi.pkg_config]
requires_private = "gstreamer-1.0, gstreamer-base-1.0, gstreamer-audio-1.0, gobject-2.0, glib-2.0, gmodule-2.0, csound"
