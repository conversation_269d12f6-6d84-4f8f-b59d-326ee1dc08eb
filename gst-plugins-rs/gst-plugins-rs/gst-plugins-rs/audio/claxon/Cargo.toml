[package]
name = "gst-plugin-claxon"
version.workspace = true
authors = ["<PERSON><PERSON> <r<PERSON><PERSON><PERSON>@fluendo.com>"]
repository.workspace = true
license = "MIT OR Apache-2.0"
description = "GStreamer Claxon FLAC Decoder Plugin"
edition.workspace = true
rust-version.workspace = true

[dependencies]
gst.workspace = true
gst-audio.workspace = true
claxon = { version = "0.4" }
byte-slice-cast = "1.0"
atomic_refcell = "0.1"

[dev-dependencies]
gst-check.workspace = true

[lib]
name = "gstclaxon"
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[build-dependencies]
gst-plugin-version-helper.workspace = true

[features]
static = []
capi = []
doc = ["gst/v1_18"]

[package.metadata.capi]
min_version = "0.9.21"

[package.metadata.capi.header]
enabled = false

[package.metadata.capi.library]
install_subdir = "gstreamer-1.0"
versioning = false
import_library = false

[package.metadata.capi.pkg_config]
requires_private = "gstreamer-1.0, gstreamer-audio-1.0, gobject-2.0, glib-2.0, gmodule-2.0"
