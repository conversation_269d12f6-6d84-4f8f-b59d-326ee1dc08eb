[package]
name = "gst-plugin-spotify"
version.workspace = true
authors = ["<PERSON> <guillau<PERSON>@desmottes.be>"]
repository.workspace = true
license = "MPL-2.0"
description = "GStreamer Spotify Plugin"
edition.workspace = true
rust-version.workspace = true

[dependencies]
gst.workspace = true
gst-base.workspace = true
librespot-core = "0.6"
librespot-metadata = "0.6"
librespot-playback = { version = "0.6", features = ['passthrough-decoder'] }
tokio = { version = "1.0", features = ["rt-multi-thread"] }
futures = "0.3"
anyhow = "1.0"
url = "2.3"

[lib]
name = "gstspotify"
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[build-dependencies]
gst-plugin-version-helper.workspace = true

[features]
static = []
capi = []
doc = ["gst/v1_18"]

[package.metadata.capi]
min_version = "0.9.21"

[package.metadata.capi.header]
enabled = false

[package.metadata.capi.library]
install_subdir = "gstreamer-1.0"
versioning = false
import_library = false

[package.metadata.capi.pkg_config]
requires_private = "gstreamer-1.0, gstreamer-base-1.0, gobject-2.0, glib-2.0, gmodule-2.0"
