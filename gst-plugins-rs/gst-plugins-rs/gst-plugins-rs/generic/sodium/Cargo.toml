[package]
name = "gst-plugin-sodium"
version.workspace = true
authors = ["<PERSON> <<EMAIL>>"]
repository.workspace = true
description = "GStreamer plugin for libsodium-based file encryption and decryption"
license = "MIT"
edition.workspace = true
rust-version.workspace = true

[dependencies]
gst.workspace = true
gst-base.workspace = true
sodiumoxide = "0.2.1"
hex = "0.4"
smallvec = "1.0"

# example
clap = { version = "4", optional = true, features = ["derive"] }
serde = { version = "1.0", features = ["derive"], optional = true }
serde_json = { version = "1.0", optional = true }

[dev-dependencies]
pretty_assertions = "1"
rand = "0.9"
gst-check.workspace = true
gst-app.workspace = true

[lib]
name = "gstsodium"
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[[example]]
name = "generate-keys"
path = "examples/generate_keys.rs"
required-features = ["serde", "serde_json", "clap"]

[[example]]
name = "encrypt-example"
path = "examples/encrypt_example.rs"
required-features = ["serde", "serde_json", "clap"]

[[example]]
name = "decrypt-example"
path = "examples/decrypt_example.rs"
required-features = ["serde", "serde_json", "clap"]

[build-dependencies]
gst-plugin-version-helper.workspace = true

[features]
static = []
capi = []
doc = ["gst/v1_18"]

[package.metadata.capi]
min_version = "0.9.21"

[package.metadata.capi.header]
enabled = false

[package.metadata.capi.library]
install_subdir = "gstreamer-1.0"
versioning = false
import_library = false

[package.metadata.capi.pkg_config]
requires_private = "gstreamer-1.0, gstreamer-base-1.0, gobject-2.0, glib-2.0, gmodule-2.0, libsodium"
