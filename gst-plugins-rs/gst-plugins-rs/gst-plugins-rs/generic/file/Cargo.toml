[package]
name = "gst-plugin-file"
version.workspace = true
authors = ["<PERSON> <<EMAIL>>"]
repository.workspace = true
license = "MIT OR Apache-2.0"
description = "GStreamer Rust File Source/Sink Plugin"
edition.workspace = true
rust-version.workspace = true

[dependencies]
url = "2"
gst.workspace = true
gst-base.workspace = true

[lib]
name = "gstrsfile"
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[build-dependencies]
gst-plugin-version-helper.workspace = true

[features]
static = []
capi = []
doc = ["gst/v1_18"]

[package.metadata.capi]
min_version = "0.9.21"

[package.metadata.capi.header]
enabled = false

[package.metadata.capi.library]
install_subdir = "gstreamer-1.0"
versioning = false
import_library = false

[package.metadata.capi.pkg_config]
requires_private = "gstreamer-1.0, gstreamer-base-1.0, gobject-2.0, glib-2.0, gmodule-2.0"
