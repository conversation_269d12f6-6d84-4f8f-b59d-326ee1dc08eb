[package]
name = "gst-plugin-threadshare"
version.workspace = true
authors = ["<PERSON> <<EMAIL>>"]
license = "LGPL-2.1-or-later"
description = "GStreamer Threadshare Plugin"
repository.workspace = true
edition.workspace = true
rust-version.workspace = true

[dependencies]
async-task = "4.3.0"
cfg-if = "1"
concurrent-queue = "2.2.0"
flume = "0.11"
futures = "0.3.28"
gio.workspace = true
gst.workspace = true
gst-audio.workspace = true
gst-net.workspace = true
gst-rtp.workspace = true
pin-project-lite = "0.2.0"
polling = "3.1.0"
rand = "0.9"
rustix = { version = "1.0", default-features = false, features = ["std", "fs", "net"] }
slab = "0.4.7"
socket2 = {features = ["all"], version = "0.5"}
waker-fn = "1.1"
bitflags = "2.6.0"
libc = "0.2"

[target.'cfg(target_os = "windows")'.dependencies]
windows-sys = { version = ">=0.52, <=0.59", features = ["Win32_Foundation"] }

[target.'cfg(not(target_os = "android"))'.dependencies]
getifaddrs = "0.1"

[dev-dependencies]
gst-check.workspace = true
gst-app.workspace = true
# Used by examples
clap = { version = "4", features = ["derive"] }

[lib]
name = "gstthreadshare"
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[[example]]
name = "ts-benchmark"
path = "examples/benchmark.rs"

[[example]]
name = "udpsrc-benchmark-sender"
path = "examples/udpsrc_benchmark_sender.rs"

[[example]]
name = "tcpclientsrc-benchmark-sender"
path = "examples/tcpclientsrc_benchmark_sender.rs"

[[example]]
name = "ts-standalone"
path = "examples/standalone/main.rs"

[build-dependencies]
gst-plugin-version-helper.workspace = true
cc = "1.0.38"
pkg-config = "0.3.15"

[features]
static = []
capi = []
# Adds performance counters used by benchmarking tools.
tuning = []
doc = ["gst/v1_18"]

[package.metadata.capi]
min_version = "0.9.21"

[package.metadata.capi.header]
enabled = false

[package.metadata.capi.library]
install_subdir = "gstreamer-1.0"
versioning = false
import_library = false

[package.metadata.capi.pkg_config]
requires_private = "gstreamer-1.0, gstreamer-net-1.0, gstreamer-rtp-1.0, gobject-2.0, glib-2.0, gmodule-2.0"
