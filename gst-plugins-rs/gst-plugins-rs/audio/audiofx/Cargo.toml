[package]
name = "gst-plugin-audiofx"
version.workspace = true
authors = ["<PERSON> <se<PERSON><EMAIL>>"]
repository.workspace = true
license = "MPL-2.0"
description = "GStreamer Rust Audio Effects Plugin"
edition.workspace = true
rust-version.workspace = true

[dependencies]
gst = { workspace = true, features = ["v1_20"] }
gst-base = { workspace = true, features = ["v1_20"] }
gst-audio = { workspace = true, features = ["v1_20"] }
anyhow = "1"
byte-slice-cast = "1.0"
num-traits = "0.2"
ebur128 = "0.1"
hrtf = "0.8"
nnnoiseless = { version = "0.5", default-features = false }
smallvec = "1"
atomic_refcell = "0.1"
rayon = "1.5"

[lib]
name = "gstrsaudiofx"
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[dev-dependencies]
gst-check = { workspace = true, features = ["v1_18"] }
gst-app.workspace = true

[build-dependencies]
gst-plugin-version-helper.workspace = true

[features]
static = []
capi = []
doc = ["gst/v1_18"]

[package.metadata.capi]
min_version = "0.9.21"

[package.metadata.capi.header]
enabled = false

[package.metadata.capi.library]
install_subdir = "gstreamer-1.0"
versioning = false
import_library = false

[package.metadata.capi.pkg_config]
requires_private = "gstreamer-1.0, gstreamer-base-1.0, gstreamer-audio-1.0, gobject-2.0, glib-2.0, gmodule-2.0"
