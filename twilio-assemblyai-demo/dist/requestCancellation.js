"use strict";
/**
 * Production-grade HTTP request cancellation system
 * Tracks and cancels in-flight LLM requests during cascading interrupts
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestCancellationManager = void 0;
exports.makeCancellableRequest = makeCancellableRequest;
exports.cancelRequestsForStage = cancelRequestsForStage;
exports.cancelAllRequests = cancelAllRequests;
exports.cancelRequestByOperation = cancelRequestByOperation;
exports.logActiveRequests = logActiveRequests;
const axios_1 = __importDefault(require("axios"));
class RequestCancellationManager {
    constructor() {
        this.activeRequests = new Map();
    }
    static getInstance() {
        if (!RequestCancellationManager.instance) {
            RequestCancellationManager.instance = new RequestCancellationManager();
        }
        return RequestCancellationManager.instance;
    }
    /**
     * Make a cancellable HTTP request
     */
    async makeRequest(config, operationId, callSid, endpoint, stage = 'processing') {
        // XState-driven pre-validation: Check for interruption before making request
        const isInterrupted = await this.checkXStateInterruption(callSid);
        if (isInterrupted) {
            console.log(`[REQUEST-CANCEL] Request prevented due to interruption for call: ${callSid}, operation: ${operationId}`);
            throw new Error(`REQUEST_CANCELLED_PREEMPTIVELY: ${operationId}`);
        }
        const abortController = new AbortController();
        // Track this request
        const trackedRequest = {
            operationId,
            callSid,
            endpoint,
            abortController,
            timestamp: Date.now(),
            stage
        };
        const requestKey = `${callSid}-${operationId}`;
        this.activeRequests.set(requestKey, trackedRequest);
        console.log(`[REQUEST-CANCEL] Tracking request: ${requestKey} (${endpoint}) - Stage: ${stage}, Total active: ${this.activeRequests.size}`);
        try {
            // Add abort signal to axios config
            const requestConfig = {
                ...config,
                signal: abortController.signal
            };
            const response = await (0, axios_1.default)(requestConfig);
            // Request completed successfully, remove from tracking
            this.activeRequests.delete(requestKey);
            console.log(`[REQUEST-CANCEL] Completed request: ${requestKey}`);
            return response;
        }
        catch (error) {
            // Remove from tracking regardless of error type
            this.activeRequests.delete(requestKey);
            if (axios_1.default.isCancel(error)) {
                console.log(`[REQUEST-CANCEL] Request cancelled: ${requestKey}`);
                throw new Error(`REQUEST_CANCELLED: ${operationId}`);
            }
            else {
                console.error(`[REQUEST-CANCEL] Request failed: ${requestKey}`, error);
                throw error;
            }
        }
    }
    /**
     * Cancel all requests for a specific call and stage
     */
    cancelRequestsForCallAndStage(callSid, stage) {
        let cancelledCount = 0;
        // Enhanced debugging: Log all active requests before attempting cancellation
        console.log(`[REQUEST-CANCEL] Attempting to cancel requests for ${callSid} in stage: ${stage}`);
        console.log(`[REQUEST-CANCEL] Total active requests: ${this.activeRequests.size}`);
        if (this.activeRequests.size > 0) {
            console.log(`[REQUEST-CANCEL] Active requests breakdown:`);
            for (const [requestKey, request] of this.activeRequests.entries()) {
                console.log(`  - ${requestKey}: callSid=${request.callSid}, stage=${request.stage}, endpoint=${request.endpoint}, age=${Date.now() - request.timestamp}ms`);
            }
        }
        for (const [requestKey, request] of this.activeRequests.entries()) {
            if (request.callSid === callSid && request.stage === stage) {
                console.log(`[REQUEST-CANCEL] Cancelling request: ${requestKey} (${request.endpoint})`);
                request.abortController.abort();
                this.activeRequests.delete(requestKey);
                cancelledCount++;
            }
        }
        console.log(`[REQUEST-CANCEL] Cancelled ${cancelledCount} requests for ${callSid} in stage: ${stage}`);
        return cancelledCount;
    }
    /**
     * Cancel all requests for a specific call
     */
    cancelAllRequestsForCall(callSid) {
        let cancelledCount = 0;
        for (const [requestKey, request] of this.activeRequests.entries()) {
            if (request.callSid === callSid) {
                console.log(`[REQUEST-CANCEL] Cancelling request: ${requestKey} (${request.endpoint})`);
                request.abortController.abort();
                this.activeRequests.delete(requestKey);
                cancelledCount++;
            }
        }
        console.log(`[REQUEST-CANCEL] Cancelled ${cancelledCount} total requests for ${callSid}`);
        return cancelledCount;
    }
    /**
     * Cancel a specific request by operation ID
     */
    cancelRequestByOperation(callSid, operationId) {
        const requestKey = `${callSid}-${operationId}`;
        const request = this.activeRequests.get(requestKey);
        if (request) {
            console.log(`[REQUEST-CANCEL] Cancelling specific request: ${requestKey} (${request.endpoint})`);
            request.abortController.abort();
            this.activeRequests.delete(requestKey);
            return true;
        }
        console.log(`[REQUEST-CANCEL] Request not found for cancellation: ${requestKey}`);
        return false;
    }
    /**
     * Get active request count for debugging
     */
    getActiveRequestCount(callSid) {
        if (callSid) {
            return Array.from(this.activeRequests.values()).filter(r => r.callSid === callSid).length;
        }
        return this.activeRequests.size;
    }
    /**
     * Debug function to log all active requests
     */
    logActiveRequests(callSid) {
        console.log(`[REQUEST-CANCEL-DEBUG] Active requests: ${this.activeRequests.size}`);
        for (const [requestKey, request] of this.activeRequests.entries()) {
            if (!callSid || request.callSid === callSid) {
                const age = Date.now() - request.timestamp;
                console.log(`  - ${requestKey}: callSid=${request.callSid}, stage=${request.stage}, endpoint=${request.endpoint}, age=${age}ms`);
            }
        }
    }
    /**
     * Clean up old completed/failed requests that might have been missed
     */
    cleanupStaleRequests(maxAgeMs = 30000) {
        const now = Date.now();
        let cleanedCount = 0;
        for (const [requestKey, request] of this.activeRequests.entries()) {
            if (now - request.timestamp > maxAgeMs) {
                console.log(`[REQUEST-CANCEL] Cleaning up stale request: ${requestKey}`);
                request.abortController.abort();
                this.activeRequests.delete(requestKey);
                cleanedCount++;
            }
        }
        if (cleanedCount > 0) {
            console.log(`[REQUEST-CANCEL] Cleaned up ${cleanedCount} stale requests`);
        }
        return cleanedCount;
    }
    /**
     * Check XState context for interruption indicators
     * Centralized interruption detection through XState machine
     */
    async checkXStateInterruption(callSid) {
        try {
            // Import XState integration dynamically to avoid circular dependencies
            const { callControllers } = await Promise.resolve().then(() => __importStar(require('./xstateIntegration')));
            const controller = callControllers[callSid];
            if (!controller?.machine) {
                return false; // No XState controller means no interruption tracking
            }
            const snapshot = controller.machine.getSnapshot();
            const context = snapshot.context;
            // Check multiple XState interruption indicators
            const hasInterruption = !!(context.interruptionTimestamp ||
                context.gracePeriodActive ||
                context.isTransitioningToGracePeriod);
            if (hasInterruption) {
                console.log(`[REQUEST-CANCEL] XState interruption detected for ${callSid}:`, {
                    interruptionTimestamp: context.interruptionTimestamp,
                    gracePeriodActive: context.gracePeriodActive,
                    isTransitioningToGracePeriod: context.isTransitioningToGracePeriod
                });
            }
            return hasInterruption;
        }
        catch (error) {
            console.error(`[REQUEST-CANCEL] Error checking XState for interruption:`, error);
            return false; // Default to no interruption on error
        }
    }
}
// Export singleton instance
exports.requestCancellationManager = RequestCancellationManager.getInstance();
// Convenience functions
async function makeCancellableRequest(config, operationId, callSid, endpoint, stage = 'processing') {
    return exports.requestCancellationManager.makeRequest(config, operationId, callSid, endpoint, stage);
}
function cancelRequestsForStage(callSid, stage) {
    return exports.requestCancellationManager.cancelRequestsForCallAndStage(callSid, stage);
}
function cancelAllRequests(callSid) {
    return exports.requestCancellationManager.cancelAllRequestsForCall(callSid);
}
function cancelRequestByOperation(callSid, operationId) {
    return exports.requestCancellationManager.cancelRequestByOperation(callSid, operationId);
}
function logActiveRequests(callSid) {
    return exports.requestCancellationManager.logActiveRequests(callSid);
}
