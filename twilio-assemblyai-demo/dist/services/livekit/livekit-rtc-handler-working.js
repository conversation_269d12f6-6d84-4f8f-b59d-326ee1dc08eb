"use strict";
/**
 * Working LiveKit RTC Handler - Direct Room Participation
 *
 * This version compiles and demonstrates the architectural approach.
 * The exact audio API implementation will be refined based on
 * @livekit/rtc-node documentation and testing.
 *
 * Architecture Benefits:
 * - Lower latency (~30-80ms vs 200-500ms)
 * - Simplified infrastructure (no egress/ingress containers)
 * - Direct audio streaming within the room
 * - No FFmpeg processes needed
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LiveKitRTCHandler = void 0;
exports.createLiveKitRTCHandler = createLiveKitRTCHandler;
const rtc_node_1 = require("@livekit/rtc-node");
const LiveKitStateIntegration_1 = require("../LiveKitStateIntegration");
const FFIResourceManager_1 = require("./FFIResourceManager");
const FFISafeWrapper_1 = require("./FFISafeWrapper");
const AudioBuffer_1 = require("./AudioBuffer");
// Import Server Turn Authority Integration for migration to XState centralized turn management
const ServerIntegrationLayer_1 = require("../turn-authority/ServerIntegrationLayer");
class LiveKitRTCHandler {
    /**
     * Safe timer creation that prevents negative values
     */
    safeSetTimeout(callback, delay) {
        const safeDelay = Math.max(1, Math.floor(delay) || 1);
        if (safeDelay !== delay) {
            console.warn(`[LiveKit-Timer-Safety] Adjusted setTimeout delay from ${delay}ms to ${safeDelay}ms`);
        }
        return setTimeout(callback, safeDelay);
    }
    /**
     * Safe interval creation that prevents negative values
     */
    safeSetInterval(callback, interval) {
        const safeInterval = Math.max(1, Math.floor(interval) || 1);
        if (safeInterval !== interval) {
            console.warn(`[LiveKit-Timer-Safety] Adjusted setInterval interval from ${interval}ms to ${safeInterval}ms`);
        }
        return setInterval(callback, safeInterval);
    }
    // Compatibility getters - now use resource manager
    get isConnected() {
        return this.resourceManager.isAvailable();
    }
    get room() {
        const resources = this.resourceManager.getResources();
        return resources?.room || null;
    }
    get audioSource() {
        const resources = this.resourceManager.getResources();
        return resources?.audioSource || null;
    }
    get localAudioTrack() {
        const resources = this.resourceManager.getResources();
        return resources?.localAudioTrack || null;
    }
    get currentTrackPublication() {
        const resources = this.resourceManager.getResources();
        return resources?.currentTrackPublication || null;
    }
    // Resource state management methods
    get connected() {
        return this.resourceManager.isAvailable();
    }
    set connected(value) {
        // This setter is kept for compatibility but now logs a warning
        console.warn(`[LiveKit-RTC] Direct connected setter deprecated - use resource manager state transitions`);
    }
    get streamingCancelled() {
        return this._streamingCancelled;
    }
    set streamingCancelled(value) {
        console.log(`[LiveKit-RTC] INTERRUPTION: Setting streamingCancelled=${value} for turn-specific cancellation`);
        this._streamingCancelled = value;
    }
    // Resource usage tracking methods (replaced by resource manager state)
    areResourcesInUse() {
        const state = this.resourceManager.getState();
        return state === FFIResourceManager_1.ResourceState.IN_USE;
    }
    markResourcesInUse() {
        // This will be handled by claiming ownership in resource manager operations
        console.log(`[LiveKit-RTC] Resources marked as in use through resource manager`);
    }
    markResourcesReleased() {
        // This will be handled by releasing ownership in resource manager operations
        console.log(`[LiveKit-RTC] Resources marked as released through resource manager`);
    }
    constructor(wsUrl, token, roomName, participantName, callSid) {
        this.wsUrl = wsUrl;
        this.token = token;
        this.roomName = roomName;
        this.participantName = participantName;
        // Legacy compatibility properties (will be deprecated)
        this.audioStreams = new Map();
        this.onAudioReceivedCallback = null;
        this.onTTSPlaybackStarted = null;
        this.onTTSPlaybackFinished = null;
        this._streamingCancelled = false; // Turn-specific cancellation flag
        this.activeFFmpegProcess = null; // Track active FFmpeg process
        this.audioQueue = [];
        this.isProcessingQueue = false;
        this.hasAttemptedRecreation = false; // Track AudioSource recreation attempts
        this.recreationFlagResetTimer = null; // Timer for recreation flag reset
        // WebSocket streaming audio buffering - New timed buffering system
        this.realtimeAudioBuffer = null;
        this.frameTimer = null;
        this.websocketSessionActive = false;
        this.FRAME_SIZE_SAMPLES = 160; // 10ms at 16kHz
        this.FRAME_INTERVAL_MS = 10; // 10ms intervals
        // Enhanced monitoring for WebSocket streaming
        this.frameSuccessCount = 0;
        this.frameFailureCount = 0;
        this.lastFramePublishTime = 0;
        this.framePublishMetrics = {
            totalAttempts: 0,
            totalRetries: 0,
            invalidStateErrors: 0,
            recoveryAttempts: 0,
            sessionStartTime: 0
        };
        this.websocketStreamEnded = false; // Flag to indicate WebSocket stream has ended
        this.callSid = callSid;
        this.resourceManager = new FFIResourceManager_1.FFIResourceManager(callSid);
        this.ffiSafeWrapper = new FFISafeWrapper_1.FFISafeWrapper(callSid, this.resourceManager);
        // 🚀 PHASE 3: Initialize LiveKit connection state
        LiveKitStateIntegration_1.livekitStateIntegration.initializeConnection(callSid, roomName).catch(error => {
            console.error(`[LiveKit-RTC] Error initializing connection state:`, error);
        });
        // CRITICAL FIX: Add timeout-based reset for recreation flag as safety measure
        // This ensures audio can always be recreated after a reasonable timeout
        this.startRecreationFlagResetTimer();
    }
    async connect() {
        const operationId = `connect_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        try {
            // Create fresh room object
            const room = new rtc_node_1.Room();
            console.log(`[LiveKit-RTC] Created new room object for connection`);
            console.log(`[LiveKit-RTC] Connecting to room: ${this.roomName}`);
            console.log(`[LiveKit-RTC] Using wsUrl: ${this.wsUrl}`);
            console.log(`[LiveKit-RTC] Token length: ${this.token.length} chars`);
            // Add timeout wrapper to prevent hanging
            const connectWithTimeout = async () => {
                return new Promise(async (resolve, reject) => {
                    // Set timeout for connection
                    const timeoutId = setTimeout(() => {
                        reject(new Error('Room connection timeout after 30 seconds'));
                    }, 30000);
                    try {
                        console.log(`[LiveKit-RTC] Starting room.connect() call...`);
                        // Connect to room
                        await room.connect(this.wsUrl, this.token, {
                            autoSubscribe: true,
                            dynacast: false
                        });
                        clearTimeout(timeoutId);
                        console.log(`[LiveKit-RTC] room.connect() completed successfully`);
                        resolve();
                    }
                    catch (error) {
                        clearTimeout(timeoutId);
                        console.error(`[LiveKit-RTC] room.connect() failed:`, error);
                        reject(error);
                    }
                });
            };
            // Execute connection with timeout
            await connectWithTimeout();
            console.log('[LiveKit-RTC] ✅ Connected to room successfully');
            console.log(`[LiveKit-RTC] Connection state: ${room.connectionState}`);
            // Wait for connection to be fully stable
            if (room.connectionState !== rtc_node_1.ConnectionState.CONN_CONNECTED) {
                console.warn(`[LiveKit-RTC] ⚠️ Room connection state is ${room.connectionState}, waiting for stable connection...`);
                await new Promise((resolve) => {
                    const checkConnection = () => {
                        if (room.connectionState === rtc_node_1.ConnectionState.CONN_CONNECTED) {
                            resolve(undefined);
                        }
                        else {
                            this.safeSetTimeout(checkConnection, 100);
                        }
                    };
                    checkConnection();
                });
            }
            console.log(`[LiveKit-RTC] ✅ Room connection is now stable`);
            // Create audio resources
            console.log(`[LiveKit-RTC] Creating audio source and track...`);
            // CRITICAL: Use larger queue size to prevent InvalidState errors during streaming
            const queueSizeMs = 200; // Increased from 100ms to 200ms for better buffer management
            const audioSource = new rtc_node_1.AudioSource(16000, 1, queueSizeMs);
            const localAudioTrack = rtc_node_1.LocalAudioTrack.createAudioTrack('ai-assistant-audio', audioSource);
            console.log(`[LiveKit-RTC] Audio source and track created successfully with queueSize=${queueSizeMs}ms`);
            // Initialize resources through resource manager
            const resourcesInitialized = await this.resourceManager.initializeResources(room, audioSource, localAudioTrack, 'connect', operationId);
            if (!resourcesInitialized) {
                throw new Error('Failed to initialize FFI resources through resource manager');
            }
            console.log(`[LiveKit-RTC] ✅ FFI resources initialized through resource manager`);
            // 🚀 PHASE 3: Notify LiveKit state integration of successful connection
            await LiveKitStateIntegration_1.livekitStateIntegration.handleRoomConnected(this.callSid, this.roomName, room.localParticipant?.sid || 'unknown', room.localParticipant?.identity || this.callSid);
            // Publish the audio track with retry logic
            await this.publishTrackWithRetry();
            // Verify track publication was successful
            if (!this.currentTrackPublication) {
                throw new Error('Track publication failed - cannot proceed with audio streaming');
            }
            console.log(`[LiveKit-RTC] ✅ Track published successfully`);
            // CRITICAL: Wait for AudioSource native pipeline to be ready
            try {
                await this.waitForAudioSourceReady();
                console.log(`[LiveKit-RTC] ✅ AudioSource pipeline is ready for capture`);
                // 🚀 PHASE 3: Notify LiveKit state integration that RTC handler is ready
                await LiveKitStateIntegration_1.livekitStateIntegration.handleRTCHandlerReady(this.callSid);
            }
            catch (readinessError) {
                console.error(`[LiveKit-RTC] ❌ AudioSource readiness check failed:`, readinessError);
                console.error(`[LiveKit-RTC] Connection will proceed anyway, but audio streaming may fail`);
                // Don't throw - let connection complete so we can attempt recreation later
            }
            // Set up connection state monitoring
            this.room.on(rtc_node_1.RoomEvent.ConnectionStateChanged, (state) => {
                console.log(`[LiveKit-RTC] Connection state changed to: ${state}`);
                if (state !== rtc_node_1.ConnectionState.CONN_CONNECTED) {
                    console.warn(`[LiveKit-RTC] ⚠️ Connection became unstable, cancelling streaming`);
                    this.streamingCancelled = true;
                }
            });
            // Set up audio reception from other participants
            await this.setupAudioReception();
            // Add periodic check for participants (temporary debugging)
            this.startPeriodicParticipantCheck();
        }
        catch (error) {
            console.error('[LiveKit-RTC] Connection failed:', error);
            console.error('[LiveKit-RTC] Error stack:', error.stack);
            throw error;
        }
    }
    async publishTrackWithRetry() {
        const maxRetries = 3;
        const operationId = `publish_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        // Claim ownership for track publishing operation
        const ownershipClaimed = await this.resourceManager.claimOwnership('publishTrack', operationId, 'operation');
        if (!ownershipClaimed) {
            throw new Error('Cannot publish track - resources not available');
        }
        try {
            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    console.log(`[LiveKit-RTC] Publishing track attempt ${attempt}/${maxRetries}...`);
                    // Get resources safely
                    const resources = this.resourceManager.getResources();
                    if (!resources) {
                        throw new Error('Resources not available for track publishing');
                    }
                    console.log(`[LiveKit-RTC] Connection state before publish: ${resources.room.connectionState}`);
                    // Create track publish options
                    const options = new rtc_node_1.TrackPublishOptions();
                    options.source = rtc_node_1.TrackSource.SOURCE_MICROPHONE;
                    console.log(`[LiveKit-RTC] Attempting to publish track...`);
                    // Publish the track
                    const trackPublication = await resources.room.localParticipant.publishTrack(resources.localAudioTrack, options);
                    // Update track publication through resource manager
                    const updateSuccess = await this.resourceManager.updateTrackPublication(trackPublication, operationId);
                    if (!updateSuccess) {
                        throw new Error('Failed to update track publication in resource manager');
                    }
                    console.log(`[LiveKit-RTC] ✅ Published AI assistant audio track`);
                    return; // Success, exit retry loop
                }
                catch (error) {
                    console.error(`[LiveKit-RTC] Publish attempt ${attempt} failed:`, error);
                    if (attempt === maxRetries) {
                        console.warn(`[LiveKit-RTC] ⚠️ Failed to publish track after ${maxRetries} attempts, continuing anyway...`);
                        // Don't throw error - continue with setup even if track publishing fails
                        // The track can be published later when connection stabilizes
                        return;
                    }
                    // Wait before retrying
                    const delay = attempt * 2000; // Exponential backoff: 2s, 4s, 6s
                    console.log(`[LiveKit-RTC] Waiting ${delay}ms before retry...`);
                    await new Promise(resolve => this.safeSetTimeout(resolve, delay));
                }
            }
        }
        finally {
            // Release ownership regardless of success/failure
            await this.resourceManager.releaseOwnership(operationId);
        }
    }
    // Temporary debugging method to check for participants periodically
    startPeriodicParticipantCheck() {
        const checkInterval = this.safeSetInterval(() => {
            if (!this.connected || !this.room) {
                clearInterval(checkInterval);
                return;
            }
            const participantCount = this.room.remoteParticipants.size;
            console.log(`[LiveKit-RTC] 🔍 Periodic check: ${participantCount} remote participants`);
            if (participantCount > 0) {
                for (const [identity, participant] of this.room.remoteParticipants) {
                    console.log(`[LiveKit-RTC] 🔍 Participant ${identity}: ${participant.trackPublications.size} tracks`);
                    for (const [trackSid, trackPub] of participant.trackPublications) {
                        console.log(`[LiveKit-RTC] 🔍   Track ${trackPub.sid}: kind=${trackPub.kind}, subscribed=${trackPub.subscribed}, track=${!!trackPub.track}`);
                    }
                }
                // Stop checking after we find participants
                clearInterval(checkInterval);
                console.log(`[LiveKit-RTC] 🔍 Stopping periodic check - found participants`);
            }
        }, 2000); // Check every 2 seconds
        // Stop after 30 seconds
        this.safeSetTimeout(() => {
            clearInterval(checkInterval);
            console.log(`[LiveKit-RTC] 🔍 Stopping periodic check - timeout reached`);
        }, 30000);
    }
    reinitializeRoom() {
        console.log(`[LiveKit-RTC] Reinitializing room object...`);
        // TODO: This room creation is now handled in connect() method
        // this.room = new Room();
    }
    async setupAudioReception() {
        console.log(`[LiveKit-RTC] Setting up audio reception...`);
        // Handle new participants joining
        this.room.on(rtc_node_1.RoomEvent.ParticipantConnected, (participant) => {
            console.log(`[LiveKit-RTC] New participant connected: ${participant.identity}`);
            this.handleParticipant(participant);
        });
        // Handle track subscriptions
        this.room.on(rtc_node_1.RoomEvent.TrackSubscribed, (track, publication, participant) => {
            if (track instanceof rtc_node_1.RemoteAudioTrack) {
                console.log(`[LiveKit-RTC] Audio track subscribed from ${participant.identity}: ${publication.sid}`);
                this.setupAudioStreamForTrack(track, participant);
            }
        });
        // Check for existing participants
        console.log(`[LiveKit-RTC] Checking for existing participants...`);
        const existingParticipants = this.room.remoteParticipants;
        console.log(`[LiveKit-RTC] Found ${existingParticipants.size} existing participants`);
        for (const [identity, participant] of existingParticipants) {
            console.log(`[LiveKit-RTC] Processing existing participant: ${identity}`);
            this.handleParticipant(participant);
        }
        console.log(`[LiveKit-RTC] ✅ Audio reception setup complete`);
    }
    handleParticipant(participant) {
        console.log(`[LiveKit-RTC] Setting up audio stream for participant: ${participant.identity}`);
        console.log(`[LiveKit-RTC] Participant has ${participant.trackPublications.size} track publications`);
        // Handle existing audio tracks
        for (const [trackSid, trackPub] of participant.trackPublications) {
            console.log(`[LiveKit-RTC] Checking track ${trackSid}: subscribed=${trackPub.subscribed}, track=${!!trackPub.track}, kind=${trackPub.kind}`);
            if (trackPub.track && trackPub.track instanceof rtc_node_1.RemoteAudioTrack) {
                console.log(`[LiveKit-RTC] Found existing audio track from ${participant.identity}: ${trackPub.sid}`);
                this.setupAudioStreamForTrack(trackPub.track, participant);
            }
            else if (trackPub.kind === rtc_node_1.TrackKind.KIND_AUDIO && !trackPub.track && trackPub.subscribed) {
                console.log(`[LiveKit-RTC] ⚠️ Audio track publication exists but track object is null for ${participant.identity}: ${trackPub.sid}`);
            }
        }
    }
    async setupAudioStreamForTrack(track, participant) {
        try {
            // CRITICAL AUDIO FEEDBACK FIX: Only process audio from actual user participants
            // Filter out ALL non-user participants (AI, system, egress, etc.) BEFORE creating audio streams
            if (!participant.identity.startsWith('user-')) {
                console.log(`[LiveKit-RTC] 🚫 FILTERED: Not setting up audio stream for non-user participant: ${participant.identity} (prevents STT feedback loop)`);
                return; // Only process real user audio for STT
            }
            console.log(`[LiveKit-RTC] ✅ PROCESSING: Setting up audio stream for user participant: ${participant.identity}`);
            const streamId = `${participant.identity}-${track.sid}`;
            // Create AudioStream from the track using the correct constructor
            // Based on the actual API: constructor(track: Track, options: AudioStreamOptions)
            const audioStream = new rtc_node_1.AudioStream(track, {
                sampleRate: 16000, // 16kHz for STT  
                numChannels: 1 // Mono
            });
            this.audioStreams.set(streamId, audioStream);
            // Start processing audio frames (filtering already done above)
            this.processAudioStream(audioStream, participant.identity);
            console.log(`[LiveKit-RTC] ✅ Audio stream created for ${participant.identity}`);
        }
        catch (error) {
            console.error(`[LiveKit-RTC] Error setting up audio stream:`, error);
        }
    }
    async processAudioStream(audioStream, participantIdentity) {
        // NOTE: Participant filtering is already done in setupAudioStreamForTrack()
        // This method only processes verified user participants
        console.log(`[LiveKit-RTC] ✅ PROCESSING: Audio frames from user participant: ${participantIdentity}`);
        // CRITICAL FIX: Add frame rate limiting to prevent VAD rate limit (200/sec) from being hit
        // LiveKit can send frames faster than expected, causing handleVadAndTurns to exceed rate limits
        let lastFrameTime = 0;
        const MIN_FRAME_INTERVAL = 10; // 10ms = 100fps max (back to reasonable rate)
        let skippedFrames = 0;
        let processedFrames = 0;
        try {
            // AudioStream extends ReadableStream<AudioFrame>, so we can use for await
            for await (const audioFrame of audioStream) {
                const now = Date.now();
                // CRITICAL FIX: Throttle frames to prevent VAD rate limiting
                if (now - lastFrameTime < MIN_FRAME_INTERVAL) {
                    skippedFrames++;
                    continue; // Skip this frame to maintain 100fps max
                }
                lastFrameTime = now;
                processedFrames++;
                // Log throttling effectiveness periodically
                if (processedFrames % 100 === 0) {
                    const totalFrames = processedFrames + skippedFrames;
                    const skipRatio = (skippedFrames / totalFrames * 100).toFixed(1);
                    console.log(`[LiveKit-RTC-Throttle] ${participantIdentity}: Processed ${processedFrames}, skipped ${skippedFrames} (${skipRatio}%) to prevent VAD rate limiting`);
                }
                // Extract audio data using the correct property names from the actual API
                const audioData = audioFrame.data; // Int16Array
                const sampleRate = audioFrame.sampleRate; // number
                const numChannels = audioFrame.channels; // number  
                const samplesPerChannel = audioFrame.samplesPerChannel; // number
                // Convert Int16Array to Buffer for processing
                const audioBuffer = Buffer.from(audioData.buffer, audioData.byteOffset, audioData.byteLength);
                // Audio logging completely disabled to reduce log spam
                // console.log(`[LiveKit-RTC] 🎵 Received audio from ${participantIdentity}: ${audioBuffer.length} bytes, ${sampleRate}Hz, ${numChannels}ch, ${samplesPerChannel} samples`);
                // Call the callback with the audio data
                if (this.onAudioReceivedCallback) {
                    this.onAudioReceivedCallback(audioBuffer);
                }
            }
        }
        catch (error) {
            console.log(`[LiveKit-RTC] Audio stream ended for ${participantIdentity}`);
        }
    }
    // INTERRUPTIBLE STREAMING APPROACH: Stream audio in chunks to allow interruption
    async sendTTSAudio(audioPath) {
        console.log(`🎯 [LIVEKIT-STREAM] Using interruptible streaming approach for: ${audioPath}`);
        // PHASE 1: Mark resources as in use before starting (NEW)
        this.markResourcesInUse();
        console.log(`🔒 [RESOURCE-TRACKING] Marked LiveKit resources as IN-USE for TTS operation`);
        // 🚀 PHASE 3: Notify LiveKit state integration that audio streaming started
        await LiveKitStateIntegration_1.livekitStateIntegration.handleAudioStreamingStarted(this.callSid);
        // CRITICAL FIX: Reset streaming flags before starting new audio
        this.resetForNewTurn();
        console.log(`🔄 [LIVEKIT-STREAM] Reset flags for new audio stream`);
        try {
            // Step 1: Convert audio (MP3/OGG) to raw PCM (16kHz mono) exactly like LiveKit expects
            const pcmPath = await this.convertAudioToPCM(audioPath);
            // Step 2: Read entire PCM file as buffer
            const { readFileSync } = await Promise.resolve().then(() => __importStar(require('fs')));
            const sample = readFileSync(pcmPath);
            // Step 3: Convert to Int16Array
            const buffer = new Int16Array(sample.buffer.slice(sample.byteOffset, sample.byteOffset + sample.byteLength));
            console.log(`📊 [LIVEKIT-STREAM] Loaded audio file: ${buffer.length} samples (${buffer.length / 16000}s duration)`);
            // Add specific monitoring for Deepgram OGG files
            if (audioPath.toLowerCase().endsWith('.ogg')) {
                const durationSeconds = buffer.length / 16000;
                console.log(`[DEEPGRAM-COORDINATION] Streaming Deepgram OGG audio: ${buffer.length} samples, duration: ${durationSeconds.toFixed(2)}s`);
                console.log(`[DEEPGRAM-COORDINATION] Audio quality metrics - Sample rate: 16kHz, Channels: Mono, Bit depth: 16-bit`);
            }
            // Step 4: Stream in interruptible chunks
            await this.streamAudioInChunks(buffer);
            // Clean up temporary PCM file
            const { unlinkSync } = await Promise.resolve().then(() => __importStar(require('fs')));
            unlinkSync(pcmPath);
            // PHASE 1: Mark resources as released after successful completion (NEW)
            this.markResourcesReleased();
            console.log(`🔓 [RESOURCE-TRACKING] Marked LiveKit resources as RELEASED after successful TTS completion`);
        }
        catch (error) {
            console.error(`❌ [LIVEKIT-STREAM] Streaming approach failed:`, error);
            // 🚀 PHASE 3: Notify LiveKit state integration that audio streaming failed
            await LiveKitStateIntegration_1.livekitStateIntegration.handleAudioStreamingStopped(this.callSid, 'error');
            // CRITICAL FIX: Ensure TTS completion is called even on error
            if (this.onTTSPlaybackFinished) {
                console.log(`🔄 [LIVEKIT-STREAM] Calling onTTSPlaybackFinished due to error`);
                this.onTTSPlaybackFinished(this.callSid);
            }
            // PHASE 1: Mark resources as released after error (NEW)
            this.markResourcesReleased();
            console.log(`🔓 [RESOURCE-TRACKING] Marked LiveKit resources as RELEASED after TTS error`);
            throw error;
        }
    }
    /**
     * Publish a single audio chunk for WebSocket streaming TTS
     * Converts Buffer to AudioFrame and streams directly to LiveKit room
     */
    async publishAudioChunk(chunk, metadata) {
        try {
            // Skip empty chunks (end-of-stream indicators)
            if (chunk.length === 0) {
                if (metadata?.isLastChunk) {
                    console.log(`[LiveKit-WebSocket] Received end-of-stream chunk for WebSocket streaming`);
                    // Call TTS finished callback for end-of-stream
                    if (this.onTTSPlaybackFinished) {
                        console.log(`[LiveKit-WebSocket] Triggering TTS playback finished for end-of-stream`);
                        this.onTTSPlaybackFinished(this.callSid);
                    }
                }
                return;
            }
            // CRITICAL FIX: Initialize WebSocket streaming session ONLY ONCE
            if (metadata?.isFirstChunk && !this.websocketSessionActive) {
                console.log(`[LiveKit-WebSocket] Initializing WebSocket streaming session for ${this.callSid}`);
                // Reset streaming cancellation flag for new WebSocket session
                this.streamingCancelled = false;
                console.log(`[LiveKit-RTC] INTERRUPTION: Setting streamingCancelled=false for turn-specific cancellation`);
                console.log(`[LiveKit-WebSocket] Reset streamingCancelled=false for WebSocket streaming`);
                // Initialize real-time audio buffering system
                this.realtimeAudioBuffer = new AudioBuffer_1.AudioBuffer(16000, 1, 2); // 16kHz, mono, 16-bit
                this.websocketSessionActive = true;
                // Initialize monitoring metrics
                this.frameSuccessCount = 0;
                this.frameFailureCount = 0;
                this.lastFramePublishTime = 0;
                this.websocketStreamEnded = false;
                this.framePublishMetrics = {
                    totalAttempts: 0,
                    totalRetries: 0,
                    invalidStateErrors: 0,
                    recoveryAttempts: 0,
                    sessionStartTime: Date.now()
                };
                this.startFrameTimer();
                console.log(`[LiveKit-WebSocket] Initialized real-time audio buffering system (${this.FRAME_SIZE_SAMPLES} samples per frame, ${this.FRAME_INTERVAL_MS}ms intervals)`);
                console.log(`[LiveKit-WebSocket] Enhanced monitoring initialized - tracking frame success/failure rates`);
                // Mark resources as in use (same as traditional streaming)
                this.markResourcesInUse();
                console.log(`[LiveKit-RTC] Resources marked as in use through resource manager`);
                console.log(`[LiveKit-WebSocket] Marked LiveKit resources as IN-USE for WebSocket streaming`);
                // Notify LiveKit state integration
                await LiveKitStateIntegration_1.livekitStateIntegration.handleAudioStreamingStarted(this.callSid);
                // Trigger TTS playback started callback
                if (this.onTTSPlaybackStarted) {
                    console.log(`[LiveKit-WebSocket] Triggering TTS playback started for first chunk`);
                    this.onTTSPlaybackStarted(this.callSid);
                }
            }
            else if (metadata?.isFirstChunk && this.websocketSessionActive) {
                console.log(`[LiveKit-WebSocket] Skipping initialization - WebSocket session already active for ${this.callSid}`);
            }
            // Ensure we have active audio components with detailed logging
            const audioSourceStatus = this.audioSource ? 'available' : 'missing';
            const localTrackStatus = this.localAudioTrack ? 'available' : 'missing';
            console.log(`[LiveKit-WebSocket] Audio component status: AudioSource=${audioSourceStatus}, LocalAudioTrack=${localTrackStatus}`);
            if (!this.audioSource || !this.localAudioTrack) {
                console.warn(`[LiveKit-WebSocket] Audio components missing during chunk publish, attempting recreation...`);
                try {
                    await this.recreateAudioComponents();
                    console.log(`[LiveKit-WebSocket] ✅ Successfully recreated audio components for WebSocket streaming`);
                    console.log(`[LiveKit-WebSocket] Post-recreation status: AudioSource=${this.audioSource ? 'available' : 'missing'}, LocalAudioTrack=${this.localAudioTrack ? 'available' : 'missing'}`);
                }
                catch (error) {
                    console.error(`[LiveKit-WebSocket] ❌ Failed to recreate audio components for chunk:`, error);
                    throw error;
                }
            }
            // Check for interruption before processing chunk
            if (this.streamingCancelled) {
                console.log(`[LiveKit-WebSocket] Streaming cancelled, skipping chunk ${metadata?.chunkIndex || 'unknown'}`);
                return;
            }
            // GATE #2: LiveKit chunk streaming gate - Check if current TTS turn is cancelled
            try {
                const { calls } = require('../../server');
                const callState = calls[this.callSid];
                if (callState && callState.currentTtsTurnIndex !== undefined) {
                    const { turnCancellationRegistry } = require('../TurnCancellationRegistry');
                    const isTurnCancelled = turnCancellationRegistry.isTurnCancelled(this.callSid, callState.currentTtsTurnIndex);
                    if (isTurnCancelled) {
                        console.log(`[LIVEKIT-GATE-2] ${this.callSid}: BLOCKED LiveKit chunk streaming for cancelled turn ${callState.currentTtsTurnIndex} (chunk: ${metadata?.chunkIndex || 'unknown'})`);
                        // Cancel the current streaming session since the turn is cancelled
                        this.streamingCancelled = true;
                        // Notify that streaming stopped due to cancellation
                        try {
                            await LiveKitStateIntegration_1.livekitStateIntegration.handleAudioStreamingStopped(this.callSid, 'cancelled');
                        }
                        catch (notifyError) {
                            console.error(`[LIVEKIT-GATE-2] Error notifying streaming stopped:`, notifyError);
                        }
                        return;
                    }
                }
            }
            catch (error) {
                console.error(`[LIVEKIT-GATE-2] ${this.callSid}: Error checking turn cancellation status:`, error);
                // Continue with processing if registry is unavailable (fail-open for stability)
            }
            // Process audio through buffering system for proper frame sizes
            await this.processWebSocketAudioChunk(chunk, metadata);
            // Handle last chunk cleanup
            if (metadata?.isLastChunk) {
                console.log(`[LiveKit-WebSocket] Processing final chunk - WebSocket stream ended`);
                this.websocketStreamEnded = true;
                // Mark that we're in completion phase - timer will handle cleanup when buffer is empty
                if (this.realtimeAudioBuffer) {
                    const stats = this.realtimeAudioBuffer.getStats();
                    console.log(`[LiveKit-WebSocket] Final chunk - ${stats.bufferedSamples} samples remaining to play`);
                    if (stats.bufferedSamples > 0) {
                        console.log(`[LiveKit-WebSocket] Letting timer naturally drain remaining ${stats.bufferedDurationMs.toFixed(1)}ms of audio`);
                        // Timer will continue processing frames and handle cleanup when buffer is empty
                        return;
                    }
                }
                // If no remaining audio, clean up immediately
                console.log(`[LiveKit-WebSocket] No remaining audio - cleaning up immediately`);
                this.cleanupWebSocketSession();
            }
        }
        catch (error) {
            console.error(`[LiveKit-WebSocket] Error publishing audio chunk:`, error);
            // Clean up resources on error
            try {
                // Clean up WebSocket session
                this.websocketSessionActive = false;
                this.stopFrameTimer();
                this.realtimeAudioBuffer = null;
                await LiveKitStateIntegration_1.livekitStateIntegration.handleAudioStreamingStopped(this.callSid, 'error');
                this.markResourcesReleased();
                console.log(`[LiveKit-WebSocket] Cleaned up resources after WebSocket streaming error`);
            }
            catch (cleanupError) {
                console.error(`[LiveKit-WebSocket] Error during cleanup:`, cleanupError);
            }
            throw error;
        }
    }
    // CRITICAL FIX: Start timer to reset recreation flag after timeout
    startRecreationFlagResetTimer() {
        // Clear any existing timer
        if (this.recreationFlagResetTimer) {
            clearTimeout(this.recreationFlagResetTimer);
        }
        // Set timer to reset recreation flag after 30 seconds
        this.recreationFlagResetTimer = this.safeSetTimeout(() => {
            if (this.hasAttemptedRecreation) {
                console.log(`[LiveKit-RTC] ⏰ Timeout reset of recreation flag after 30 seconds`);
                this.hasAttemptedRecreation = false;
            }
        }, 30000); // 30 seconds timeout
    }
    // Stream audio in interruptible chunks to allow real-time interruption
    async streamAudioInChunks(buffer) {
        console.log(`🎵 [LIVEKIT-CHUNK] Starting chunk-based streaming: ${buffer.length} total samples`);
        // CRITICAL FIX: Don't recreate components during initial audio playback
        // This prevents track unpublishing/republishing that breaks iOS autoSubscribe
        if (!this.audioSource || !this.localAudioTrack) {
            console.log(`🔄 [LIVEKIT-CHUNK] Audio components missing, attempting recreation (last resort)...`);
            try {
                await this.recreateAudioComponents();
                console.log(`✅ [LIVEKIT-CHUNK] Audio components recreated successfully`);
            }
            catch (error) {
                console.error(`❌ [LIVEKIT-CHUNK] Failed to recreate audio components:`, error);
                console.log(`🔄 [LIVEKIT-CHUNK] Resetting recreation flag and retrying once...`);
                // Reset flag and try once more
                this.hasAttemptedRecreation = false;
                try {
                    await this.recreateAudioComponents();
                    console.log(`✅ [LIVEKIT-CHUNK] Audio components recreated on retry`);
                }
                catch (retryError) {
                    console.error(`❌ [LIVEKIT-CHUNK] Recreation failed even after reset:`, retryError);
                    throw new Error('Cannot stream audio - component recreation failed twice');
                }
            }
        }
        // Final validation
        if (!this.audioSource) {
            throw new Error('AudioSource is null - cannot stream audio');
        }
        // Reset cancellation flag for this audio
        this.streamingCancelled = false;
        // CRITICAL FIX: Reset recreation flag when audio successfully starts
        // This allows future recreations if needed after subsequent interruptions
        if (this.hasAttemptedRecreation) {
            console.log(`🎵 [LIVEKIT-CHUNK] ✅ Audio streaming started - resetting recreation flag`);
            this.hasAttemptedRecreation = false;
            // Restart the timer for future safety
            this.startRecreationFlagResetTimer();
        }
        // Notify that TTS playback is starting
        if (this.onTTSPlaybackStarted) {
            console.log(`🎵 [LIVEKIT-CHUNK] Calling onTTSPlaybackStarted callback`);
            this.onTTSPlaybackStarted(this.callSid);
        }
        // Set currentlyPlayingTurnIndex for interruption detection using Turn Authority integration
        const { calls } = await Promise.resolve().then(() => __importStar(require('../../server')));
        const callState = calls[this.callSid];
        if (callState) {
            const currentTurnIndex = await (0, ServerIntegrationLayer_1.getCallTurnIndex)(this.callSid, calls);
            console.log(`[LIVEKIT-TTS-START] Setting currentlyPlayingTurnIndex=${currentTurnIndex} for interruption detection`);
            callState.currentlyPlayingTurnIndex = currentTurnIndex;
        }
        // Stream audio in chunks (50ms = 800 samples at 16kHz) - CRITICAL FIX for fast interruption
        const chunkSizeMs = 50; // 50ms chunks for FAST interruption response (16x improvement from 320ms)
        const samplesPerChunk = Math.floor((16000 * chunkSizeMs) / 1000); // 800 samples
        const totalChunks = Math.ceil(buffer.length / samplesPerChunk);
        console.log(`📦 [LIVEKIT-CHUNK] Streaming ${totalChunks} chunks of ${chunkSizeMs}ms each (${samplesPerChunk} samples per chunk) - FAST INTERRUPTION MODE`);
        try {
            for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
                // 🚀 PHASE 3.2: Enhanced interruption check with TurnCancellationRegistry
                if (this.streamingCancelled) {
                    console.log(`🛑 [LIVEKIT-CHUNK] Streaming cancelled at chunk ${chunkIndex}/${totalChunks}, stopping transmission`);
                    return; // Exit early due to interruption
                }
                // Additional interruption check using TurnCancellationRegistry with Turn Authority integration
                try {
                    const { turnCancellationRegistry } = await Promise.resolve().then(() => __importStar(require('../../services/TurnCancellationRegistry')));
                    const currentTurnIndex = await (0, ServerIntegrationLayer_1.getCallTurnIndex)(this.callSid, calls);
                    if (turnCancellationRegistry.isTurnCancelled(this.callSid, currentTurnIndex)) {
                        console.log(`🛑 [LIVEKIT-CHUNK] Turn ${currentTurnIndex} cancelled via TurnCancellationRegistry at chunk ${chunkIndex}/${totalChunks}, stopping transmission`);
                        this.streamingCancelled = true; // Sync the flag
                        return; // Exit early due to turn cancellation
                    }
                }
                catch (error) {
                    console.error(`⚠️ [LIVEKIT-CHUNK] Error checking TurnCancellationRegistry:`, error);
                    // Continue with existing cancellation logic if registry check fails
                }
                // Calculate chunk boundaries
                const startSample = chunkIndex * samplesPerChunk;
                const endSample = Math.min(startSample + samplesPerChunk, buffer.length);
                const chunkSamples = endSample - startSample;
                // Create chunk as independent array (not a view)
                const chunkBuffer = new Int16Array(chunkSamples);
                for (let i = 0; i < chunkSamples; i++) {
                    chunkBuffer[i] = buffer[startSample + i];
                }
                // Send chunk to AudioSource with interruption safety and retry logic
                try {
                    // Validate AudioSource is still available (might be recreated during interruption)
                    if (!this.audioSource) {
                        console.log(`🛑 [LIVEKIT-CHUNK] AudioSource became null during streaming (likely interrupted), stopping`);
                        return;
                    }
                    const audioFrame = new rtc_node_1.AudioFrame(chunkBuffer, 16000, 1, chunkSamples);
                    // CRITICAL FIX: Retry loop with cancellation checks for captureFrame
                    let retryCount = 0;
                    const maxRetries = 2;
                    let frameSuccess = false;
                    while (!frameSuccess && retryCount <= maxRetries) {
                        // 🚀 PHASE 3.2: Enhanced cancellation check in retry loop with TurnCancellationRegistry
                        if (this.streamingCancelled) {
                            console.log(`🛑 [LIVEKIT-RETRY] Cancellation detected during retry ${retryCount}, stopping immediately`);
                            return;
                        }
                        // Additional turn cancellation check during retry
                        try {
                            const { turnCancellationRegistry } = await Promise.resolve().then(() => __importStar(require('../../services/TurnCancellationRegistry')));
                            const currentTurnIndex = await (0, ServerIntegrationLayer_1.getCallTurnIndex)(this.callSid, calls);
                            if (turnCancellationRegistry.isTurnCancelled(this.callSid, currentTurnIndex)) {
                                console.log(`🛑 [LIVEKIT-RETRY] Turn ${currentTurnIndex} cancelled via TurnCancellationRegistry during retry ${retryCount}, stopping immediately`);
                                this.streamingCancelled = true; // Sync the flag
                                return;
                            }
                        }
                        catch (error) {
                            // Silent fail for retry loop to avoid noise in logs
                        }
                        try {
                            await this.audioSource.captureFrame(audioFrame);
                            frameSuccess = true;
                            if (retryCount > 0) {
                                console.log(`✅ [LIVEKIT-RETRY] captureFrame succeeded on retry ${retryCount} for chunk ${chunkIndex}`);
                            }
                        }
                        catch (retryError) {
                            retryCount++;
                            if (retryError.message?.includes('InvalidState') && retryCount <= maxRetries) {
                                console.warn(`⚠️ [LIVEKIT-RETRY] InvalidState error on attempt ${retryCount}/${maxRetries + 1} for chunk ${chunkIndex}, retrying...`);
                                // Check cancellation again before retry delay
                                if (this.streamingCancelled) {
                                    console.log(`🛑 [LIVEKIT-RETRY] Cancellation detected before retry delay, stopping`);
                                    return;
                                }
                                // Small delay before retry to let AudioSource stabilize
                                await new Promise(resolve => this.safeSetTimeout(resolve, 10));
                            }
                            else {
                                // Either not InvalidState error, or exceeded max retries
                                if (retryError.message?.includes('InvalidState') || retryError.message?.includes('closed')) {
                                    console.warn(`⚠️ [LIVEKIT-RETRY] AudioSource error after ${retryCount} retries (likely interrupted): ${retryError.message}`);
                                    console.log(`🛑 [LIVEKIT-RETRY] Stopping chunk streaming due to persistent AudioSource error`);
                                    return;
                                }
                                else {
                                    console.error(`❌ [LIVEKIT-RETRY] Non-recoverable error sending chunk ${chunkIndex}:`, retryError);
                                    throw retryError;
                                }
                            }
                        }
                    }
                    // Log progress occasionally
                    if (chunkIndex === 0 || chunkIndex === totalChunks - 1 || chunkIndex % 10 === 0) {
                        console.log(`📦 [LIVEKIT-CHUNK] Sent chunk ${chunkIndex + 1}/${totalChunks} (${chunkSamples} samples)`);
                        if (chunkIndex === 0) {
                            const firstChunkTime = Date.now();
                            console.log(`[STARTUP-TIMING] ${this.callSid}: First audio chunk sent at ${firstChunkTime} - USER SHOULD HEAR AUDIO NOW`);
                        }
                    }
                }
                catch (frameError) {
                    console.error(`❌ [LIVEKIT-CHUNK] Unexpected error in retry logic for chunk ${chunkIndex}:`, frameError);
                    throw frameError;
                }
                // Small delay between chunks to prevent overwhelming AudioSource
                // This also provides more opportunities to check for interruption
                if (chunkIndex < totalChunks - 1) {
                    await new Promise(resolve => this.safeSetTimeout(resolve, 5)); // 5ms delay between 50ms chunks (optimized for fast interruption)
                    // CRITICAL FIX: Double-check cancellation after delay for immediate response
                    if (this.streamingCancelled) {
                        console.log(`🛑 [LIVEKIT-CHUNK] Cancellation detected after chunk delay at ${chunkIndex + 1}/${totalChunks}, stopping immediately`);
                        return;
                    }
                }
            }
            // Only call completion callback if we weren't interrupted
            if (!this.streamingCancelled && this.onTTSPlaybackFinished) {
                console.log(`✅ [LIVEKIT-CHUNK] All chunks sent successfully, calling onTTSPlaybackFinished`);
                // 🚀 PHASE 3: Notify LiveKit state integration that audio streaming completed
                await LiveKitStateIntegration_1.livekitStateIntegration.handleAudioStreamingStopped(this.callSid, 'completed');
                this.onTTSPlaybackFinished(this.callSid);
            }
            else if (this.streamingCancelled) {
                console.log(`🚫 [LIVEKIT-CHUNK] Streaming was cancelled, not calling completion callback`);
            }
        }
        catch (error) {
            console.error(`❌ [LIVEKIT-CHUNK] Error during chunk streaming:`, error);
            throw error;
        }
    }
    // Convert audio (MP3 or OGG) to raw PCM exactly as AudioSource expects
    async convertAudioToPCM(audioPath) {
        const { spawn } = await Promise.resolve().then(() => __importStar(require('child_process')));
        const path = await Promise.resolve().then(() => __importStar(require('path')));
        const pcmPath = audioPath.replace(/\.(mp3|ogg)$/, '_stream.pcm');
        // Add enhanced monitoring for different audio formats
        const isOggFile = audioPath.toLowerCase().endsWith('.ogg');
        const isMp3File = audioPath.toLowerCase().endsWith('.mp3');
        const audioFormat = isOggFile ? 'OGG/Opus' : (isMp3File ? 'MP3' : 'Unknown');
        console.log(`🔄 [LIVEKIT-STREAM] Converting ${audioFormat} file: ${audioPath} → ${pcmPath}`);
        if (isOggFile) {
            console.log(`[DEEPGRAM-COORDINATION] Processing Deepgram OGG/Opus file for LiveKit streaming`);
        }
        return new Promise((resolve, reject) => {
            const ffmpeg = spawn('ffmpeg', [
                '-i', audioPath, // Auto-detect input format (supports MP3 and OGG)
                '-f', 's16le', // Signed 16-bit little endian (exactly what Int16Array expects)
                '-ar', '16000', // 16kHz sample rate
                '-ac', '1', // Mono
                '-y', // Overwrite output
                pcmPath
            ]);
            // Track FFmpeg process for interruption cancellation
            this.activeFFmpegProcess = ffmpeg;
            // XState-driven interruption checking during conversion
            const interruptionCheckInterval = this.safeSetInterval(async () => {
                try {
                    // Check local cancellation flag (set by XState event-driven interruption)
                    if (this.streamingCancelled) {
                        console.log(`[LIVEKIT-INTERRUPTION] Local cancellation detected during PCM conversion for ${this.callSid}`);
                        clearInterval(interruptionCheckInterval);
                        ffmpeg.kill('SIGKILL');
                        this.activeFFmpegProcess = null;
                        reject(new Error('PCM conversion cancelled due to interruption'));
                        return;
                    }
                }
                catch (error) {
                    console.error(`[LIVEKIT-INTERRUPTION] Error during interruption check:`, error);
                    // Continue conversion on check errors
                }
            }, 100); // Check every 100ms for responsive interruption
            ffmpeg.on('close', (code) => {
                clearInterval(interruptionCheckInterval);
                this.activeFFmpegProcess = null;
                if (code === 0) {
                    console.log(`✅ [LIVEKIT-STREAM] Conversion complete: ${pcmPath}`);
                    if (isOggFile) {
                        console.log(`[DEEPGRAM-COORDINATION] ✅ OGG/Opus to PCM conversion successful for LiveKit`);
                    }
                    resolve(pcmPath);
                }
                else {
                    console.error(`❌ [LIVEKIT-STREAM] FFmpeg conversion failed with code ${code} for ${audioFormat} file`);
                    if (isOggFile) {
                        console.error(`[DEEPGRAM-COORDINATION] ❌ OGG/Opus conversion failed - this indicates an issue with Deepgram audio processing`);
                    }
                    reject(new Error(`FFmpeg conversion failed with code ${code} for ${audioFormat} file`));
                }
            });
            ffmpeg.on('error', (error) => {
                clearInterval(interruptionCheckInterval);
                this.activeFFmpegProcess = null;
                reject(error);
            });
        });
    }
    // Set callback for received audio
    setOnAudioReceived(callback) {
        this.onAudioReceivedCallback = callback;
    }
    // Set callbacks for TTS playback events
    setTTSPlaybackCallbacks(onStarted, onFinished) {
        this.onTTSPlaybackStarted = onStarted;
        this.onTTSPlaybackFinished = onFinished;
    }
    stopAudio() {
        console.log(`[LiveKit-RTC] INTERRUPTION: Stop audio requested for ${this.callSid} (USER INTERRUPTION)`);
        // CRITICAL FIX: Set cancellation flag immediately and forcefully
        this.streamingCancelled = true;
        console.log(`[LiveKit-RTC] INTERRUPTION: Set streamingCancelled=true - ALL STREAMING SHOULD STOP IMMEDIATELY`);
        // EMERGENCY FIX: Log current streaming state for debugging
        console.log(`[LiveKit-RTC] INTERRUPTION: Current call state - callSid: ${this.callSid}, streamingCancelled: ${this.streamingCancelled}`);
        // NEW FIX: Cancel active TTS generation (the missing piece!)
        this.cancelActiveTtsGeneration();
        // ENHANCED: Kill active FFmpeg process immediately for cascading cancellation
        if (this.activeFFmpegProcess) {
            console.log(`[LiveKit-RTC] INTERRUPTION: Killing active FFmpeg process for ${this.callSid}`);
            try {
                this.activeFFmpegProcess.kill('SIGKILL');
                this.activeFFmpegProcess = null;
                console.log(`[LiveKit-RTC] INTERRUPTION: ✅ FFmpeg process killed successfully`);
            }
            catch (error) {
                console.error(`[LiveKit-RTC] INTERRUPTION: ❌ Error killing FFmpeg process:`, error);
            }
        }
        // 🚀 PHASE 3: Notify LiveKit state integration of audio cancellation
        LiveKitStateIntegration_1.livekitStateIntegration.handleAudioStreamingCancelled(this.callSid).catch(error => {
            console.error(`[LiveKit-RTC] Error notifying state integration of cancellation:`, error);
        });
        // SIMPLE FIX: Use LiveKit's built-in clearQueue() method to stop audio playback immediately
        if (this.audioSource) {
            try {
                console.log(`[LiveKit-RTC] INTERRUPTION: Clearing AudioSource queue to stop audio playback`);
                this.audioSource.clearQueue();
                console.log(`[LiveKit-RTC] INTERRUPTION: ✅ AudioSource queue cleared successfully`);
                // Add small delay to let AudioSource stabilize after clearQueue()
                // This prevents InvalidState errors when starting new TTS immediately
                setTimeout(() => {
                    if (this.onTTSPlaybackFinished) {
                        console.log(`[LiveKit-RTC] INTERRUPTION: Calling onTTSPlaybackFinished callback after stabilization`);
                        this.onTTSPlaybackFinished(this.callSid);
                    }
                }, 20); // 20ms delay to let AudioSource stabilize
            }
            catch (error) {
                console.error(`[LiveKit-RTC] INTERRUPTION: ❌ Error clearing AudioSource queue:`, error);
                // Still call completion callback even if clearQueue fails
                if (this.onTTSPlaybackFinished) {
                    this.onTTSPlaybackFinished(this.callSid);
                }
            }
        }
        else {
            console.warn(`[LiveKit-RTC] INTERRUPTION: No AudioSource available to clear queue`);
            // Call completion callback since there's no audio to stop
            if (this.onTTSPlaybackFinished) {
                this.onTTSPlaybackFinished(this.callSid);
            }
        }
        console.log(`[LiveKit-RTC] INTERRUPTION: Immediate audio stop initiated`);
    }
    // SIMPLE FIX: Use clearQueue() for AudioSource recovery instead of complex recreation
    recreateAudioSourceForInterruption() {
        console.log(`[LiveKit-RTC] RECOVERY: Using clearQueue() to recover from InvalidState errors`);
        if (this.audioSource) {
            try {
                console.log(`[LiveKit-RTC] RECOVERY: Clearing AudioSource queue to resolve InvalidState`);
                this.audioSource.clearQueue();
                console.log(`[LiveKit-RTC] RECOVERY: ✅ AudioSource queue cleared - allowing stabilization time`);
                // Note: No immediate retry - let the retry logic handle the delay naturally
            }
            catch (error) {
                console.warn(`[LiveKit-RTC] RECOVERY: Warning clearing queue during recovery:`, error);
            }
        }
        else {
            console.warn(`[LiveKit-RTC] RECOVERY: No AudioSource available for recovery`);
        }
    }
    cancelActiveTtsGeneration() {
        console.log(`[LiveKit-RTC] INTERRUPTION: Cancelling active TTS generation for ${this.callSid}`);
        // Import the calls object to access call state
        const { calls } = require('../../server');
        const callState = calls[this.callSid];
        if (!callState) {
            console.warn(`[LiveKit-RTC] INTERRUPTION: No call state found for ${this.callSid} - cannot cancel TTS`);
            return;
        }
        // Cancel ElevenLabs WebSocket streaming (like in handleInterruption)
        if (callState.currentElevenLabsWebSocket) {
            console.log(`[LiveKit-RTC] INTERRUPTION: Closing active ElevenLabs WebSocket for ${this.callSid}`);
            try {
                callState.currentElevenLabsWebSocket.close();
                callState.currentElevenLabsWebSocket = undefined;
                console.log(`[LiveKit-RTC] INTERRUPTION: ✅ ElevenLabs WebSocket closed and cleared`);
            }
            catch (error) {
                console.error(`[LiveKit-RTC] INTERRUPTION: ❌ Error closing ElevenLabs WebSocket:`, error);
                callState.currentElevenLabsWebSocket = undefined;
            }
        }
        // Cancel Deepgram streaming
        if (callState.activeDeepgramStream) {
            console.log(`[LiveKit-RTC] INTERRUPTION: Closing active Deepgram stream for ${this.callSid}`);
            try {
                callState.activeDeepgramStream.close();
                callState.activeDeepgramStream = undefined;
                console.log(`[LiveKit-RTC] INTERRUPTION: ✅ Deepgram stream closed and cleared`);
            }
            catch (error) {
                console.error(`[LiveKit-RTC] INTERRUPTION: ❌ Error closing Deepgram stream:`, error);
                callState.activeDeepgramStream = undefined;
            }
        }
        // Clear TTS generation flags
        if (callState.isTtsGenerating) {
            console.log(`[LiveKit-RTC] INTERRUPTION: Clearing TTS generation flags for ${this.callSid}`);
            callState.isTtsGenerating = false;
            callState.currentTtsText = undefined;
            callState.currentTtsTurnIndex = undefined;
            console.log(`[LiveKit-RTC] INTERRUPTION: ✅ TTS generation flags cleared`);
        }
        console.log(`[LiveKit-RTC] INTERRUPTION: ✅ Active TTS generation cancellation completed for ${this.callSid}`);
    }
    // Reset for new conversation turn
    resetForNewTurn() {
        console.log(`🔄 [LIVEKIT-TURN] Resetting for new conversation turn`);
        console.log(`🔄 [LIVEKIT-TURN] Previous streamingCancelled state: ${this.streamingCancelled}`);
        this.streamingCancelled = false;
        console.log(`🔄 [LIVEKIT-TURN] Reset streamingCancelled to: ${this.streamingCancelled}`);
        // CRITICAL FIX: Reset recreation flag at the start of each turn
        // This ensures audio can always be recreated for new audio attempts
        if (this.hasAttemptedRecreation) {
            console.log(`🔄 [LIVEKIT-TURN] Resetting recreation flag for new turn`);
            this.hasAttemptedRecreation = false;
            // Restart the safety timer
            this.startRecreationFlagResetTimer();
        }
    }
    // Update room details for lobby migration
    updateRoomDetails(roomName, token, participantName) {
        console.log(`[LiveKit-RTC] Updating room details: ${roomName}, participant: ${participantName}`);
        this.roomName = roomName;
        this.token = token;
        this.participantName = participantName;
    }
    // Wait for AudioSource to be ready for capture
    async waitForAudioSourceReady() {
        console.log(`[LiveKit-RTC] Waiting for AudioSource to be ready...`);
        // Test with a small silent frame to check readiness
        const testFrame = new rtc_node_1.AudioFrame(new Int16Array(160), 16000, 1, 160); // 10ms of silence
        try {
            await this.audioSource.captureFrame(testFrame);
            console.log(`[LiveKit-RTC] ✅ AudioSource readiness test passed`);
        }
        catch (error) {
            console.error(`[LiveKit-RTC] ❌ AudioSource readiness test failed:`, error);
            throw error;
        }
    }
    // Recreate audio components if they become corrupted
    async recreateAudioComponents() {
        console.log(`[LiveKit-RTC] Recreating audio components due to corruption...`);
        // Prevent multiple recreation attempts within a short timeframe
        if (this.hasAttemptedRecreation) {
            console.warn(`[LiveKit-RTC] ⚠️ Recreation already attempted, skipping to prevent infinite loop`);
            throw new Error('AudioSource recreation already attempted');
        }
        this.hasAttemptedRecreation = true;
        const operationId = `recreate_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        try {
            // Step 1: Claim ownership for recreation operation
            console.log(`[LiveKit-RTC] Claiming ownership for audio component recreation (operationId: ${operationId})`);
            const ownershipClaimed = await this.resourceManager.claimOwnership('recreateAudioComponents', operationId, 'cleanup');
            if (!ownershipClaimed) {
                console.warn(`[LiveKit-RTC] ⚠️ Cannot recreate components - resources not available for recreation`);
                throw new Error('Cannot recreate audio components - resource ownership not available');
            }
            console.log(`[LiveKit-RTC] ✅ Ownership claimed for recreation operation`);
            // Step 2: Get current resources safely through resource manager
            const resources = this.resourceManager.getResources();
            if (!resources) {
                console.warn(`[LiveKit-RTC] ⚠️ No resources available for recreation`);
                await this.resourceManager.releaseOwnership(operationId);
                throw new Error('No resources available for recreation');
            }
            // Step 3: Perform safe FFI cleanup through FFISafeWrapper (recreation context)
            console.log(`[LiveKit-RTC] Performing safe FFI cleanup of existing components for recreation`);
            const cleanupResults = await this.ffiSafeWrapper.safeBulkCleanupForRecreation();
            // Log cleanup results for diagnostics
            console.log(`[LiveKit-RTC] Recreation cleanup results:`, {
                trackUnpublished: cleanupResults.trackUnpublished,
                audioTrackClosed: cleanupResults.audioTrackClosed,
                audioSourceClosed: cleanupResults.audioSourceClosed,
                errors: cleanupResults.errors.length
            });
            // Step 4: Create new audio components (outside of resource manager initially)
            console.log(`[LiveKit-RTC] Creating new AudioSource and LocalAudioTrack for recreation...`);
            const newAudioSource = new rtc_node_1.AudioSource(16000, 1, 200); // 200ms queue
            const newLocalAudioTrack = rtc_node_1.LocalAudioTrack.createAudioTrack('ai-assistant-audio-recreated', newAudioSource);
            console.log(`[LiveKit-RTC] ✅ New audio components created successfully`);
            // Step 5: Get the room safely and reinitialize resources through resource manager
            if (!resources.room) {
                console.error(`[LiveKit-RTC] ❌ No room available for recreation`);
                await this.resourceManager.releaseOwnership(operationId);
                throw new Error('No room available for audio component recreation');
            }
            // Step 6: Reinitialize resources through resource manager with new components
            console.log(`[LiveKit-RTC] Reinitializing resources through resource manager...`);
            // First complete the current cleanup
            await this.resourceManager.completeCleanup(operationId);
            // Then reinitialize with new resources
            const newOperationId = `reinit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const reinitSuccess = await this.resourceManager.initializeResources(resources.room, newAudioSource, newLocalAudioTrack, 'recreateAudioComponents', newOperationId);
            if (!reinitSuccess) {
                console.error(`[LiveKit-RTC] ❌ Failed to reinitialize resources through resource manager`);
                throw new Error('Failed to reinitialize resources through resource manager');
            }
            console.log(`[LiveKit-RTC] ✅ Resources reinitialized through resource manager`);
            // Step 7: Publish new track with proper ownership
            await this.publishTrackWithRetry();
            // Step 8: Wait for readiness
            await this.waitForAudioSourceReady();
            console.log(`[LiveKit-RTC] ✅ Audio components recreated successfully through resource manager`);
            // CRITICAL FIX: Reset the recreation flag after successful recreation
            // This allows future recreations if needed after subsequent interruptions
            this.hasAttemptedRecreation = false;
            console.log(`[LiveKit-RTC] ✅ Reset recreation flag - future recreations now allowed`);
        }
        catch (error) {
            console.error(`[LiveKit-RTC] ❌ Failed to recreate audio components:`, error);
            // Ensure ownership is released on error
            try {
                await this.resourceManager.releaseOwnership(operationId);
            }
            catch (releaseError) {
                console.error(`[LiveKit-RTC] ❌ Failed to release ownership after recreation error:`, releaseError);
            }
            // Don't reset flag on error - prevent infinite retry loops
            throw error;
        }
    }
    // PHASE 1: Resource Ownership Tracking Methods (NEW)
    /**
     * Wait for resources to be released before allowing disconnect
     */
    async ensureResourcesNotInUse(maxWaitMs = 3000) {
        console.log(`⏳ [RESOURCE-TRACKING] Ensuring resources not in use (max wait: ${maxWaitMs}ms)`);
        const startTime = Date.now();
        const checkInterval = 100; // Check every 100ms
        while (Date.now() - startTime < maxWaitMs) {
            if (!this.areResourcesInUse()) {
                console.log(`✅ [RESOURCE-TRACKING] Resources confirmed as not in use after ${Date.now() - startTime}ms`);
                return true;
            }
            console.log(`⏳ [RESOURCE-TRACKING] Still waiting for resource release...`);
            await new Promise(resolve => this.safeSetTimeout(resolve, checkInterval));
        }
        console.warn(`⚠️ [RESOURCE-TRACKING] Timeout waiting for resource release after ${maxWaitMs}ms`);
        return false;
    }
    // Local cleanup without FFI operations (for double disconnect prevention)
    cleanupLocalResourcesOnly() {
        console.log(`[LiveKit-RTC] Performing local cleanup only - no FFI operations`);
        // Clean up recreation flag reset timer
        if (this.recreationFlagResetTimer) {
            clearTimeout(this.recreationFlagResetTimer);
            this.recreationFlagResetTimer = null;
            console.log(`[LiveKit-RTC] Cleared recreation flag reset timer`);
        }
        // Cancel any active FFmpeg process
        if (this.activeFFmpegProcess) {
            console.log(`[LiveKit-RTC] Killing active FFmpeg process during local cleanup`);
            this.activeFFmpegProcess.kill('SIGKILL');
            this.activeFFmpegProcess = null;
        }
        // Clear audio queue
        this.audioQueue.forEach(({ reject }) => {
            reject(new Error('Connection disconnected'));
        });
        this.audioQueue = [];
        // Clear audio streams map (no FFI calls)
        this.audioStreams.clear();
        // Reset local state flags without touching FFI resources
        // The resource manager will handle the actual FFI resource cleanup
        this.connected = false;
        this.streamingCancelled = true;
        this.hasAttemptedRecreation = false;
        console.log(`[LiveKit-RTC] ✅ Local cleanup complete - FFI resources managed by resource manager`);
        console.log(`[LiveKit-RTC] 🛡️ Resource manager will handle FFI resource disposal safely`);
    }
    // Clean disconnect method with resource coordination
    async disconnect() {
        console.log(`[LiveKit-RTC] Disconnecting from room ${this.roomName}...`);
        // PHASE 0: Atomic disconnect ownership claim (NEW - FFI panic prevention)
        const { claimDisconnectOwnership, CleanupSource, generateCleanupOperationId } = await Promise.resolve().then(() => __importStar(require('../../xstateIntegration')));
        const operationId = generateCleanupOperationId();
        if (!claimDisconnectOwnership(this.callSid, CleanupSource.MANUAL_DISCONNECT, operationId)) {
            console.log(`[LiveKit-RTC] ⚠️ Disconnect already owned by another operation for ${this.callSid} - skipping FFI operations`);
            // Still perform local cleanup without FFI calls
            this.connected = false;
            this.streamingCancelled = true;
            this.cleanupLocalResourcesOnly();
            return;
        }
        console.log(`[LiveKit-RTC] ✅ Claimed disconnect ownership for ${this.callSid} - proceeding with FFI operations`);
        // PHASE 1: Check resource usage before disconnect (NEW)
        console.log(`🔍 [RESOURCE-TRACKING] Checking resource usage before disconnect...`);
        const resourcesAvailable = await this.ensureResourcesNotInUse(3000);
        if (!resourcesAvailable) {
            console.warn(`⚠️ [RESOURCE-TRACKING] Proceeding with disconnect despite resources still in use`);
            console.warn(`⚠️ [RESOURCE-TRACKING] This may still cause FFI panics, but coordination reduces the risk`);
        }
        else {
            console.log(`✅ [RESOURCE-TRACKING] Resources confirmed as not in use - safe to disconnect`);
        }
        // PHASE 1.5: Check for natural disconnect to prevent double cleanup race condition (NEW)
        const { callControllers } = await Promise.resolve().then(() => __importStar(require('../../xstateIntegration')));
        const controller = callControllers[this.callSid];
        const naturalDisconnect = controller?.machine?.getSnapshot()?.context?.infrastructure?.connection?.livekit?.naturalDisconnect;
        if (naturalDisconnect) {
            console.log(`[LiveKit-RTC] ✅ Natural disconnect detected - LiveKit already cleaned up internally`);
            console.log(`[LiveKit-RTC] ✅ Skipping explicit FFI operations to prevent double cleanup race condition`);
            // Still perform local cleanup without FFI calls
            this.connected = false;
            this.streamingCancelled = true;
            this.cleanupLocalResourcesOnly();
            // 🚀 PHASE 3: Clean up LiveKit connection state (local only)
            await LiveKitStateIntegration_1.livekitStateIntegration.cleanupConnection(this.callSid);
            console.log(`[LiveKit-RTC] ✅ Natural disconnect cleanup completed - no FFI operations needed`);
            return;
        }
        console.log(`[LiveKit-RTC] No natural disconnect detected - proceeding with explicit disconnect`);
        this.connected = false;
        this.streamingCancelled = true;
        // 🚀 PHASE 3: Notify LiveKit state integration of disconnection
        await LiveKitStateIntegration_1.livekitStateIntegration.handleRoomDisconnected(this.callSid, 'disconnect_called');
        // CRITICAL FIX: Clean up recreation flag reset timer
        if (this.recreationFlagResetTimer) {
            clearTimeout(this.recreationFlagResetTimer);
            this.recreationFlagResetTimer = null;
            console.log(`[LiveKit-RTC] Cleared recreation flag reset timer`);
        }
        // Cancel any active FFmpeg process
        if (this.activeFFmpegProcess) {
            console.log(`[LiveKit-RTC] Killing active FFmpeg process during disconnect`);
            this.activeFFmpegProcess.kill('SIGKILL');
            this.activeFFmpegProcess = null;
        }
        // Clear audio queue
        this.audioQueue.forEach(({ reject }) => {
            reject(new Error('Connection disconnected'));
        });
        this.audioQueue = [];
        // Close all audio streams
        for (const [streamId, audioStream] of this.audioStreams) {
            try {
                console.log(`[LiveKit-RTC] Closing audio stream: ${streamId}`);
                // AudioStream doesn't have a close method, so we just remove it
                // The stream will be automatically closed when the track is unpublished
            }
            catch (error) {
                console.error(`[LiveKit-RTC] Error closing audio stream ${streamId}:`, error);
            }
        }
        this.audioStreams.clear();
        // Execute all FFI operations safely through FFISafeWrapper
        console.log(`[LiveKit-RTC] 🛡️ Starting FFI-safe shutdown cleanup with comprehensive protection`);
        const cleanupResults = await this.ffiSafeWrapper.safeBulkCleanupForShutdown();
        // Log cleanup results
        console.log(`[LiveKit-RTC] 🛡️ FFI-safe bulk cleanup completed:`);
        console.log(`[LiveKit-RTC]   - Track unpublished: ${cleanupResults.trackUnpublished ? '✅' : '❌'}`);
        console.log(`[LiveKit-RTC]   - Audio track closed: ${cleanupResults.audioTrackClosed ? '✅' : '❌'}`);
        console.log(`[LiveKit-RTC]   - Audio source closed: ${cleanupResults.audioSourceClosed ? '✅' : '❌'}`);
        console.log(`[LiveKit-RTC]   - Room disconnected: ${cleanupResults.roomDisconnected ? '✅' : '❌'}`);
        if (cleanupResults.errors.length > 0) {
            const criticalErrors = cleanupResults.errors.filter(e => e.type !== FFISafeWrapper_1.FFIErrorType.ALREADY_CLEANED);
            if (criticalErrors.length > 0) {
                console.warn(`[LiveKit-RTC] ⚠️ FFI cleanup had ${criticalErrors.length} critical errors:`, criticalErrors.map(e => `${e.type}: ${e.message}`));
            }
            else {
                console.log(`[LiveKit-RTC] ✅ All FFI cleanup errors were due to already-cleaned resources (expected)`);
            }
        }
        else {
            console.log(`[LiveKit-RTC] ✅ FFI bulk cleanup completed without errors`);
        }
        // FFI operations handled above by FFISafeWrapper
        // Complete resource cleanup through resource manager
        await this.resourceManager.completeCleanup(operationId);
        // 🚀 PHASE 3: Clean up LiveKit connection state
        await LiveKitStateIntegration_1.livekitStateIntegration.cleanupConnection(this.callSid);
        // PHASE 4: Transition cleanup state to IN_PROGRESS (NEW - atomic coordination)
        const { atomicStateTransition, CleanupState } = await Promise.resolve().then(() => __importStar(require('../../xstateIntegration')));
        atomicStateTransition(this.callSid, CleanupState.DISCONNECT_INITIATED, CleanupState.IN_PROGRESS, operationId);
        console.log(`[LiveKit-RTC] ✅ Disconnect complete`);
    }
    /**
     * Process WebSocket audio chunk through real-time buffering system
     * Appends chunks to buffer - timed extraction handled separately
     */
    async processWebSocketAudioChunk(chunk, metadata) {
        try {
            // Validate chunk size for 16-bit audio (must be even number of bytes)
            if (chunk.length % 2 !== 0) {
                console.warn(`[LiveKit-WebSocket] Invalid chunk size: ${chunk.length} bytes (not divisible by 2 for 16-bit audio)`);
                return;
            }
            if (!this.realtimeAudioBuffer) {
                console.warn(`[LiveKit-WebSocket] No real-time audio buffer available for chunk processing`);
                return;
            }
            const sampleCount = chunk.length / 2; // 16-bit = 2 bytes per sample
            console.log(`[LiveKit-WebSocket] Appending chunk ${metadata?.chunkIndex || 'unknown'}: ${chunk.length} bytes = ${sampleCount} samples (${(sampleCount / 16000 * 1000).toFixed(1)}ms)`);
            // Simply append chunk to buffer - timer will extract frames at proper intervals
            this.realtimeAudioBuffer.append(chunk);
            const stats = this.realtimeAudioBuffer.getStats();
            console.log(`[LiveKit-WebSocket] Buffer stats: ${stats.bufferedSamples} samples (${stats.bufferedDurationMs.toFixed(1)}ms buffered)`);
            // Note: No immediate frame processing - timer handles consistent output
        }
        catch (error) {
            console.error(`[LiveKit-WebSocket] Error processing audio chunk:`, error);
            throw error;
        }
    }
    /**
     * Start frame timer for consistent 16kHz output
     */
    startFrameTimer() {
        // Safety check: Ensure we're in a valid state to start timer
        if (!this.websocketSessionActive) {
            console.warn(`[LiveKit-WebSocket] Cannot start frame timer - WebSocket session not active`);
            return;
        }
        // Clean up any existing timer first
        if (this.frameTimer) {
            console.log(`[LiveKit-WebSocket] Clearing existing frame timer before creating new one`);
            clearInterval(this.frameTimer);
            this.frameTimer = null;
        }
        // Timer safety: Ensure interval is positive and reasonable
        const safeInterval = Math.max(1, this.FRAME_INTERVAL_MS || 10);
        if (safeInterval !== this.FRAME_INTERVAL_MS) {
            console.warn(`[LiveKit-WebSocket] Timer safety: Adjusted interval from ${this.FRAME_INTERVAL_MS}ms to ${safeInterval}ms`);
        }
        // Create timer with safety-validated interval using safe wrapper
        this.frameTimer = this.safeSetInterval(() => {
            this.processTimedFrame();
        }, safeInterval);
        console.log(`[LiveKit-WebSocket] Frame timer started: ${this.FRAME_SIZE_SAMPLES} samples every ${safeInterval}ms`);
    }
    /**
     * Stop frame timer
     */
    stopFrameTimer() {
        if (this.frameTimer) {
            const timerId = this.frameTimer;
            console.log(`[LiveKit-WebSocket] Stopping frame timer with ID: ${timerId}`);
            try {
                clearInterval(this.frameTimer);
                this.frameTimer = null;
                console.log(`[LiveKit-WebSocket] Frame timer stopped successfully`);
            }
            catch (error) {
                console.error(`[LiveKit-WebSocket] Error stopping frame timer:`, error);
                // Still clear the reference to prevent memory leaks
                this.frameTimer = null;
            }
        }
        else {
            console.log(`[LiveKit-WebSocket] No frame timer to stop`);
        }
    }
    /**
     * Process one frame at consistent timing interval
     */
    async processTimedFrame() {
        try {
            // Safety check: Verify timer is still valid
            if (!this.frameTimer) {
                console.warn(`[LiveKit-WebSocket] processTimedFrame called but frameTimer is null - stopping execution`);
                return;
            }
            // Check if we should stop processing
            if (this.streamingCancelled || !this.websocketSessionActive || !this.realtimeAudioBuffer) {
                return;
            }
            // Extract frame if enough data is available
            if (this.realtimeAudioBuffer.hasEnoughData(this.FRAME_SIZE_SAMPLES)) {
                const frameBuffer = this.realtimeAudioBuffer.extractFrame(this.FRAME_SIZE_SAMPLES);
                if (frameBuffer) {
                    // Convert Buffer to Int16Array for LiveKit
                    const frameData = new Int16Array(frameBuffer.buffer.slice(frameBuffer.byteOffset, frameBuffer.byteOffset + frameBuffer.byteLength));
                    // CRITICAL DEBUG: Log buffer status before publishing
                    const stats = this.realtimeAudioBuffer.getStats();
                    console.log(`[TIMER-DEBUG] Processing frame: ${frameData.length} samples, ${stats.bufferedDurationMs.toFixed(1)}ms remaining`);
                    // Publish frame to LiveKit
                    await this.publishBufferedAudioFrame(frameData);
                }
            }
            else if (this.websocketStreamEnded && this.realtimeAudioBuffer.isEmpty()) {
                // WebSocket stream ended and buffer is now empty - time to clean up
                console.log(`[LiveKit-WebSocket] Buffer drained after WebSocket stream ended - cleaning up`);
                this.cleanupWebSocketSession();
                return;
            }
        }
        catch (error) {
            console.error(`[LiveKit-WebSocket] Error processing timed frame:`, error);
            // Check if this is a critical error that requires stopping the timer
            if (error.message?.includes('InvalidState') || error.message?.includes('captureFrame')) {
                console.warn(`[LiveKit-WebSocket] Critical error in frame processing - may need error recovery`);
            }
            // Don't throw - keep timer running for next frame unless it's a recurring issue
        }
    }
    /**
     * Flush any remaining buffered audio at end of stream
     */
    async flushWebSocketAudioBuffer() {
        try {
            if (!this.realtimeAudioBuffer) {
                return;
            }
            const stats = this.realtimeAudioBuffer.getStats();
            if (stats.bufferedSamples > 0) {
                console.log(`[LiveKit-WebSocket] Flushing remaining ${stats.bufferedSamples} samples from buffer`);
                // Extract remaining data in proper frame sizes
                while (this.realtimeAudioBuffer.hasEnoughData(this.FRAME_SIZE_SAMPLES)) {
                    const frameBuffer = this.realtimeAudioBuffer.extractFrame(this.FRAME_SIZE_SAMPLES);
                    if (frameBuffer) {
                        const frameData = new Int16Array(frameBuffer.buffer.slice(frameBuffer.byteOffset, frameBuffer.byteOffset + frameBuffer.byteLength));
                        await this.publishBufferedAudioFrame(frameData);
                    }
                }
                // Handle any remaining partial frame
                const remainingStats = this.realtimeAudioBuffer.getStats();
                if (remainingStats.bufferedSamples > 0) {
                    console.log(`[LiveKit-WebSocket] Processing final partial frame: ${remainingStats.bufferedSamples} samples`);
                    // Pad partial frame to minimum size with silence
                    const finalFrameSize = Math.max(this.FRAME_SIZE_SAMPLES, remainingStats.bufferedSamples);
                    const finalBuffer = Buffer.alloc(finalFrameSize * 2); // 2 bytes per sample
                    // Extract remaining data and pad
                    const remainingBuffer = this.realtimeAudioBuffer.extractFrame(remainingStats.bufferedSamples);
                    if (remainingBuffer) {
                        remainingBuffer.copy(finalBuffer, 0);
                        // Rest stays as zeros (silence)
                        const frameData = new Int16Array(finalBuffer.buffer.slice(finalBuffer.byteOffset, finalBuffer.byteOffset + finalBuffer.byteLength));
                        await this.publishBufferedAudioFrame(frameData);
                    }
                }
                // Clear the buffer
                this.realtimeAudioBuffer.clear();
                console.log(`[LiveKit-WebSocket] Real-time audio buffer flushed and cleared`);
            }
        }
        catch (error) {
            console.error(`[LiveKit-WebSocket] Error flushing audio buffer:`, error);
            throw error;
        }
    }
    /**
     * Publish a buffered audio frame to LiveKit with retry logic and error recovery
     */
    async publishBufferedAudioFrame(frameData) {
        const maxRetries = 3;
        const baseDelayMs = 10;
        // Update monitoring metrics
        this.framePublishMetrics.totalAttempts++;
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                // Check for cancellation before each attempt
                if (this.streamingCancelled) {
                    console.log(`[LiveKit-WebSocket] Streaming cancelled, skipping buffered frame (attempt ${attempt + 1})`);
                    return;
                }
                if (!this.audioSource) {
                    console.error(`[FRAME-ERROR] No AudioSource available for frame ${this.frameSuccessCount + this.frameFailureCount + 1} (attempt ${attempt + 1})`);
                    return;
                }
                // CRITICAL DEBUG: Check AudioSource health before publishing
                try {
                    // Try to check if AudioSource is still valid - this might throw if corrupted
                    const isHealthy = !!this.audioSource && !!this.localAudioTrack;
                    if (!isHealthy) {
                        console.error(`[FRAME-ERROR] AudioSource appears unhealthy before frame ${this.frameSuccessCount + this.frameFailureCount + 1}`);
                    }
                }
                catch (healthError) {
                    console.error(`[FRAME-ERROR] AudioSource health check failed:`, healthError.message);
                }
                // Create AudioFrame for LiveKit (16kHz, mono)
                const audioFrame = new rtc_node_1.AudioFrame(frameData, 16000, 1, frameData.length);
                if (attempt > 0) {
                    console.log(`[LiveKit-WebSocket] Retry attempt ${attempt + 1}/${maxRetries + 1}: Publishing buffered frame: ${frameData.length} samples`);
                    this.framePublishMetrics.totalRetries++;
                }
                else {
                    console.log(`[LiveKit-WebSocket] Publishing buffered frame: ${frameData.length} samples (${(frameData.length / 16000 * 1000).toFixed(1)}ms)`);
                }
                // Attempt to publish to LiveKit with detailed health monitoring
                const framePublishStart = Date.now();
                await this.audioSource.captureFrame(audioFrame);
                const framePublishEnd = Date.now();
                const publishDuration = framePublishEnd - framePublishStart;
                // CRITICAL DEBUG: Log every frame to see when it stops working
                console.log(`[FRAME-DEBUG] Frame ${this.frameSuccessCount + 1}: ${frameData.length} samples published in ${publishDuration}ms`);
                // DIAGNOSIS: Check critical pipeline components after successful frame publish
                await this.logAudioPipelineDiagnostics(this.frameSuccessCount + 1);
                // Success - update metrics and log
                this.frameSuccessCount++;
                this.lastFramePublishTime = framePublishEnd;
                if (attempt > 0) {
                    console.log(`[LiveKit-WebSocket] ✅ Frame published successfully on retry attempt ${attempt + 1}`);
                }
                // Log periodic health metrics
                this.logFrameHealthMetrics();
                return;
            }
            catch (error) {
                const isInvalidStateError = error.message?.includes('InvalidState') ||
                    error.message?.includes('captureFrame') ||
                    error.message?.includes('failed to capture frame');
                // Update error metrics
                this.frameFailureCount++;
                if (isInvalidStateError) {
                    this.framePublishMetrics.invalidStateErrors++;
                }
                // CRITICAL DEBUG: Log every error to see what's failing
                console.error(`[FRAME-ERROR] Frame ${this.frameSuccessCount + this.frameFailureCount} FAILED (attempt ${attempt + 1}):`, error.message);
                console.error(`[FRAME-ERROR] Error type: InvalidState=${isInvalidStateError}, Will retry: ${attempt < maxRetries}`);
                // If this is our last attempt, log final metrics and throw
                if (attempt === maxRetries) {
                    console.error(`[LiveKit-WebSocket] ❌ All retry attempts failed for buffered frame publishing`);
                    this.logDetailedErrorMetrics();
                    throw error;
                }
                // Handle InvalidState errors with AudioSource recovery attempts
                if (isInvalidStateError && attempt < maxRetries) {
                    console.warn(`[LiveKit-WebSocket] InvalidState error detected - attempting recovery`);
                    this.framePublishMetrics.recoveryAttempts++;
                    try {
                        // Use safe recovery approach (recreation is disabled due to FFI panics)
                        this.recreateAudioSourceForInterruption();
                        console.log(`[LiveKit-WebSocket] AudioSource recovery attempt completed for retry`);
                        // Add small delay to let AudioSource stabilize
                        await new Promise(resolve => this.safeSetTimeout(resolve, 20));
                    }
                    catch (recoveryError) {
                        console.error(`[LiveKit-WebSocket] AudioSource recovery failed:`, recoveryError);
                        // Continue with retry anyway - the AudioSource might still be usable
                    }
                }
                // Exponential backoff delay before next attempt
                if (attempt < maxRetries) {
                    const delayMs = baseDelayMs * Math.pow(2, attempt);
                    console.log(`[LiveKit-WebSocket] Waiting ${delayMs}ms before retry attempt ${attempt + 2}`);
                    await new Promise(resolve => this.safeSetTimeout(resolve, delayMs));
                }
            }
        }
    }
    /**
     * Calculate frame success rate for monitoring
     */
    getFrameSuccessRate() {
        const totalFrames = this.frameSuccessCount + this.frameFailureCount;
        if (totalFrames === 0)
            return '0%';
        return `${((this.frameSuccessCount / totalFrames) * 100).toFixed(1)}%`;
    }
    /**
     * Log periodic frame health metrics
     */
    logFrameHealthMetrics() {
        // Log detailed metrics every 50 successful frames
        if (this.frameSuccessCount > 0 && this.frameSuccessCount % 50 === 0) {
            const sessionDurationMs = Date.now() - this.framePublishMetrics.sessionStartTime;
            const totalFrames = this.frameSuccessCount + this.frameFailureCount;
            console.log(`[LiveKit-WebSocket] 📊 Frame Health Metrics:`, {
                successfulFrames: this.frameSuccessCount,
                failedFrames: this.frameFailureCount,
                successRate: this.getFrameSuccessRate(),
                totalAttempts: this.framePublishMetrics.totalAttempts,
                totalRetries: this.framePublishMetrics.totalRetries,
                invalidStateErrors: this.framePublishMetrics.invalidStateErrors,
                recoveryAttempts: this.framePublishMetrics.recoveryAttempts,
                sessionDurationSec: (sessionDurationMs / 1000).toFixed(1),
                avgFramesPerSec: totalFrames > 0 ? (totalFrames / (sessionDurationMs / 1000)).toFixed(1) : '0'
            });
        }
    }
    /**
     * Log comprehensive audio pipeline diagnostics to identify where audio stops reaching client
     */
    async logAudioPipelineDiagnostics(frameNumber) {
        // Only log diagnostics for first few frames and every 10th frame to avoid spam
        if (frameNumber > 5 && frameNumber % 10 !== 0) {
            return;
        }
        try {
            console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] === Audio Pipeline Health Check ===`);
            // 1. AudioSource Health
            const audioSourceHealth = this.audioSource ? 'HEALTHY' : 'NULL';
            console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] AudioSource: ${audioSourceHealth}`);
            // 2. LocalAudioTrack Health
            const trackHealth = this.localAudioTrack ? 'HEALTHY' : 'NULL';
            console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] LocalAudioTrack: ${trackHealth}`);
            // 3. Track Publication Status
            const pubHealth = this.currentTrackPublication ? 'PUBLISHED' : 'NOT_PUBLISHED';
            console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] Track Publication: ${pubHealth}`);
            if (this.currentTrackPublication) {
                try {
                    console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] Publication SID: ${this.currentTrackPublication.sid || 'N/A'}`);
                    console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] Publication Track: ${!!this.currentTrackPublication.track}`);
                }
                catch (err) {
                    console.warn(`[PIPELINE-DIAGNOSIS-${frameNumber}] Error reading publication details:`, err.message);
                }
            }
            // 4. Room Connection Health
            if (this.room) {
                console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] Room Connection: ${this.room.connectionState}`);
                console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] Room Participants: ${this.room.remoteParticipants.size} remote`);
                // 5. Local Participant Track Count
                const localTracks = this.room.localParticipant.trackPublications.size;
                console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] Local Participant Tracks: ${localTracks}`);
                // 6. Remote Participant Audio Subscription Status
                if (this.room.remoteParticipants.size > 0) {
                    let subscribedCount = 0;
                    for (const [identity, participant] of this.room.remoteParticipants) {
                        for (const [trackSid, trackPub] of participant.trackPublications) {
                            if (trackPub.kind.toString() === 'audio' && trackPub.subscribed) {
                                subscribedCount++;
                            }
                        }
                    }
                    console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] Remote Audio Subscriptions: ${subscribedCount}`);
                }
            }
            else {
                console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] Room: NULL`);
            }
            // 7. Resource Manager State
            const resources = this.resourceManager.getResources();
            if (resources) {
                console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] Resource State: ${resources.state}`);
                console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] Resource Track Pub: ${!!resources.currentTrackPublication}`);
            }
            else {
                console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] Resources: NULL`);
            }
            console.log(`[PIPELINE-DIAGNOSIS-${frameNumber}] === End Pipeline Health Check ===`);
        }
        catch (error) {
            console.error(`[PIPELINE-DIAGNOSIS-${frameNumber}] Error during pipeline diagnostics:`, error.message);
        }
    }
    /**
     * Log detailed error metrics when publishing fails completely
     */
    logDetailedErrorMetrics() {
        const sessionDurationMs = Date.now() - this.framePublishMetrics.sessionStartTime;
        const totalFrames = this.frameSuccessCount + this.frameFailureCount;
        console.error(`[LiveKit-WebSocket] 🚨 DETAILED ERROR METRICS:`, {
            session: {
                duration: `${(sessionDurationMs / 1000).toFixed(1)}s`,
                totalFrames,
                successRate: this.getFrameSuccessRate()
            },
            publishing: {
                totalAttempts: this.framePublishMetrics.totalAttempts,
                totalRetries: this.framePublishMetrics.totalRetries,
                retryRate: this.framePublishMetrics.totalAttempts > 0 ?
                    `${((this.framePublishMetrics.totalRetries / this.framePublishMetrics.totalAttempts) * 100).toFixed(1)}%` : '0%'
            },
            errors: {
                totalFailures: this.frameFailureCount,
                invalidStateErrors: this.framePublishMetrics.invalidStateErrors,
                invalidStateRate: this.frameFailureCount > 0 ?
                    `${((this.framePublishMetrics.invalidStateErrors / this.frameFailureCount) * 100).toFixed(1)}%` : '0%'
            },
            recovery: {
                attempts: this.framePublishMetrics.recoveryAttempts,
                successRate: this.framePublishMetrics.recoveryAttempts > 0 ?
                    `${((this.frameSuccessCount / this.framePublishMetrics.recoveryAttempts) * 100).toFixed(1)}%` : 'N/A'
            },
            audioSource: {
                currentlyAvailable: !!this.audioSource,
                lastPublishTime: this.lastFramePublishTime > 0 ?
                    `${Date.now() - this.lastFramePublishTime}ms ago` : 'Never'
            }
        });
    }
    /**
     * Clean up WebSocket streaming session after all audio has been played
     */
    async cleanupWebSocketSession() {
        try {
            console.log(`[LiveKit-WebSocket] Starting WebSocket session cleanup`);
            // Stop the frame timer
            this.stopFrameTimer();
            // Clean up session state
            this.websocketSessionActive = false;
            this.websocketStreamEnded = false;
            this.realtimeAudioBuffer = null;
            // Notify LiveKit state integration that streaming stopped
            await LiveKitStateIntegration_1.livekitStateIntegration.handleAudioStreamingStopped(this.callSid, 'completed');
            // Mark resources as released
            this.markResourcesReleased();
            console.log(`[LiveKit-WebSocket] Marked LiveKit resources as RELEASED after natural drainage`);
            // Log final metrics
            const totalFrames = this.frameSuccessCount + this.frameFailureCount;
            console.log(`[LiveKit-WebSocket] Session completed - ${this.frameSuccessCount} successful frames, ${this.frameFailureCount} failed frames, ${this.getFrameSuccessRate()} success rate`);
        }
        catch (error) {
            console.error(`[LiveKit-WebSocket] Error during session cleanup:`, error);
        }
    }
}
exports.LiveKitRTCHandler = LiveKitRTCHandler;
// Factory function for creating LiveKit RTC Handler instances
function createLiveKitRTCHandler(wsUrl, token, roomName, participantName, callSid) {
    return new LiveKitRTCHandler(wsUrl, token, roomName, participantName, callSid);
}
