"use strict";
/**
 * Pure Grace Period Evaluator - No State, No Exceptions
 *
 * Replaces the stateful Grace<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> with a pure function that:
 * 1. Never throws exceptions
 * 2. Has no internal state
 * 3. Only evaluates when given complete inputs
 * 4. Returns clear decisions with rationale
 *
 * This function is called ONLY when an interruption has occurred,
 * eliminating the "every turn" evaluation that causes exceptions.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_GRACE_CONFIG = void 0;
exports.evaluateGrace = evaluateGrace;
/**
 * Pure grace period evaluator - called only when interruption occurred
 */
function evaluateGrace(input) {
    const { prevTurn, currentTurn, interruptionTimestamp, playbackWindow, config } = input;
    // DEBUGGING: Log all input values to understand the failure
    console.log(`[EVALUATE-GRACE] === Grace Period Evaluation Debug ===`);
    console.log(`[EVALUATE-GRACE] prevTurn:`, prevTurn ? {
        turnNumber: prevTurn.turnNumber,
        transcript: `"${prevTurn.transcript}"`,
        userStoppedAt: prevTurn.userStoppedAt,
        turnFinalizedAt: prevTurn.turnFinalizedAt,
        ttsStartedAt: prevTurn.ttsStartedAt
    } : null);
    console.log(`[EVALUATE-GRACE] currentTurn:`, currentTurn ? {
        turnNumber: currentTurn.turnNumber,
        transcript: `"${currentTurn.transcript}"`,
        userStoppedAt: currentTurn.userStoppedAt,
        turnFinalizedAt: currentTurn.turnFinalizedAt
    } : null);
    console.log(`[EVALUATE-GRACE] interruptionTimestamp: ${interruptionTimestamp}`);
    console.log(`[EVALUATE-GRACE] playbackWindow:`, playbackWindow);
    console.log(`[EVALUATE-GRACE] config:`, config);
    // Validate inputs - return safe default if incomplete
    if (!prevTurn || !currentTurn || !interruptionTimestamp) {
        const missingFields = [];
        if (!prevTurn)
            missingFields.push('prevTurn');
        if (!currentTurn)
            missingFields.push('currentTurn');
        if (!interruptionTimestamp)
            missingFields.push('interruptionTimestamp');
        const rationale = `Incomplete input data (missing: ${missingFields.join(', ')}) - defaulting to individual processing`;
        console.log(`[EVALUATE-GRACE] ❌ INPUT VALIDATION FAILED: ${rationale}`);
        return {
            action: 'cutPreviousAndRespondNow',
            rationale: rationale
        };
    }
    const now = Date.now();
    // Phase 1: Pre-TTS Grace Period (always combine)
    if (!playbackWindow.start) {
        const rationale = 'Pre-TTS interruption - combining utterances';
        const combinedText = combineUtterances(prevTurn.transcript, currentTurn.transcript);
        console.log(`[EVALUATE-GRACE] ✅ PRE-TTS GRACE PERIOD: ${rationale}`);
        console.log(`[EVALUATE-GRACE] ✅ Combined: "${prevTurn.transcript}" + "${currentTurn.transcript}" = "${combinedText}"`);
        return {
            action: 'combineWithCurrent',
            rationale: rationale,
            combinedText: combinedText,
            shouldCutTts: false
        };
    }
    // Phase 2: Post-TTS Grace Period (combine if within window)
    const timeSinceTtsStart = interruptionTimestamp - playbackWindow.start;
    const withinGracePeriod = timeSinceTtsStart <= config.postTtsWindowMs;
    console.log(`[EVALUATE-GRACE] POST-TTS TIMING ANALYSIS:`);
    console.log(`[EVALUATE-GRACE] - interruptionTimestamp: ${interruptionTimestamp}`);
    console.log(`[EVALUATE-GRACE] - playbackWindow.start: ${playbackWindow.start}`);
    console.log(`[EVALUATE-GRACE] - timeSinceTtsStart: ${timeSinceTtsStart}ms`);
    console.log(`[EVALUATE-GRACE] - config.postTtsWindowMs: ${config.postTtsWindowMs}ms`);
    console.log(`[EVALUATE-GRACE] - withinGracePeriod: ${withinGracePeriod}`);
    if (withinGracePeriod) {
        const combinedText = combineUtterances(prevTurn.transcript, currentTurn.transcript);
        // Check combined length limit
        if (combinedText.length > config.maxCombineLength) {
            const rationale = `Combined text too long (${combinedText.length} > ${config.maxCombineLength}) - processing individually`;
            console.log(`[EVALUATE-GRACE] ❌ TEXT TOO LONG: ${rationale}`);
            return {
                action: 'cutPreviousAndRespondNow',
                rationale: rationale,
                shouldCutTts: true
            };
        }
        const rationale = `Post-TTS interruption within grace period (${timeSinceTtsStart}ms <= ${config.postTtsWindowMs}ms)`;
        console.log(`[EVALUATE-GRACE] ✅ POST-TTS GRACE PERIOD: ${rationale}`);
        console.log(`[EVALUATE-GRACE] ✅ Combined: "${prevTurn.transcript}" + "${currentTurn.transcript}" = "${combinedText}"`);
        return {
            action: 'combineWithCurrent',
            rationale: rationale,
            combinedText,
            shouldCutTts: true
        };
    }
    // Outside grace period - process individually
    const rationale = `Post-TTS interruption outside grace period (${timeSinceTtsStart}ms > ${config.postTtsWindowMs}ms)`;
    console.log(`[EVALUATE-GRACE] ❌ OUTSIDE GRACE PERIOD: ${rationale}`);
    return {
        action: 'cutPreviousAndRespondNow',
        rationale: rationale,
        shouldCutTts: true
    };
}
/**
 * Helper function to combine utterances intelligently
 */
function combineUtterances(prevText, currentText) {
    if (!prevText?.trim())
        return currentText?.trim() || '';
    if (!currentText?.trim())
        return prevText?.trim() || '';
    const prev = prevText.trim();
    const current = currentText.trim();
    // Add proper spacing/punctuation between utterances
    const needsPunctuation = !prev.match(/[.!?]$/);
    const separator = needsPunctuation ? '. ' : ' ';
    return `${prev}${separator}${current}`;
}
/**
 * Default grace period configuration
 */
exports.DEFAULT_GRACE_CONFIG = {
    preTtsWindowMs: 0, // Pre-TTS always combines
    postTtsWindowMs: 3000, // 3 seconds after TTS starts
    maxCombineLength: 500 // Reasonable limit for combined text
};
