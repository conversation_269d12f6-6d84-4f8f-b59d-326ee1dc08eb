"use strict";
/**
 * Production-grade response deduplication system
 * Prevents duplicate or very similar TTS responses from being processed
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.responseDeduplicationManager = void 0;
exports.checkForDuplicate = checkForDuplicate;
exports.recordTTSResponse = recordTTSResponse;
exports.cleanupCallResponses = cleanupCallResponses;
class ResponseDeduplicationManager {
    constructor() {
        this.recentResponses = new Map(); // callSid -> recent responses
        this.MAX_RECENT_RESPONSES = 10; // Keep last 10 responses per call
        this.SIMILARITY_THRESHOLD = 0.95; // 95% similarity threshold (stricter)
        this.TIME_WINDOW_MS = 1000; // 1 second window for duplicate detection (much shorter)
    }
    static getInstance() {
        if (!ResponseDeduplicationManager.instance) {
            ResponseDeduplicationManager.instance = new ResponseDeduplicationManager();
        }
        return ResponseDeduplicationManager.instance;
    }
    /**
     * Calculate similarity between two strings using simple ratio
     * Returns value between 0 and 1 (1 = identical)
     */
    calculateSimilarity(str1, str2) {
        if (str1 === str2)
            return 1;
        if (!str1 || !str2)
            return 0;
        // Normalize strings - remove extra whitespace, convert to lowercase
        const normalize = (s) => s.replace(/\s+/g, ' ').trim().toLowerCase();
        const a = normalize(str1);
        const b = normalize(str2);
        if (a === b)
            return 1;
        // Simple character-based similarity
        const longer = a.length > b.length ? a : b;
        const shorter = a.length > b.length ? b : a;
        if (longer.length === 0)
            return 1;
        // Count matching characters in order
        let matches = 0;
        let j = 0;
        for (let i = 0; i < shorter.length && j < longer.length; i++) {
            if (shorter[i] === longer[j]) {
                matches++;
                j++;
            }
            else {
                // Find the character in the longer string
                while (j < longer.length && longer[j] !== shorter[i]) {
                    j++;
                }
                if (j < longer.length) {
                    matches++;
                    j++;
                }
            }
        }
        return matches / longer.length;
    }
    /**
     * Check if a response is a duplicate or very similar to recent responses
     * TURN-AWARE: Only considers responses from the same turn as potential duplicates
     */
    isDuplicate(callSid, content, operationId, turnNumber = 0) {
        const now = Date.now();
        const callResponses = this.recentResponses.get(callSid) || [];
        // TURN-AWARE DEDUPLICATION: Only check responses from the same turn
        // This allows legitimate new questions in different turns (e.g., "what is the color green?" in a new turn)
        const sameTurnResponses = callResponses.filter(response => response.turnNumber === turnNumber);
        console.log(`[TURN-AWARE-DEDUP] ${callSid}: Checking turn ${turnNumber}, found ${sameTurnResponses.length} responses in same turn, ${callResponses.length} total responses`);
        // Check for exact duplicates or high similarity within the same turn only
        for (const response of sameTurnResponses) {
            const timeDiff = now - response.timestamp;
            // Skip if outside time window (but allow longer window for same turn)
            if (timeDiff > this.TIME_WINDOW_MS * 5)
                continue; // 5x longer window for same turn
            // Check for exact content match
            if (response.content === content) {
                return {
                    isDuplicate: true,
                    reason: `Exact content duplicate within turn ${turnNumber} from operation ${response.operationId} (${timeDiff}ms ago)`,
                    similarTo: response
                };
            }
            // Check for high similarity within same turn
            const similarity = this.calculateSimilarity(content, response.content);
            if (similarity >= this.SIMILARITY_THRESHOLD) {
                return {
                    isDuplicate: true,
                    reason: `High similarity (${(similarity * 100).toFixed(1)}%) within turn ${turnNumber} to operation ${response.operationId} (${timeDiff}ms ago)`,
                    similarTo: response
                };
            }
            // Check for same operation ID within same turn
            if (response.operationId === operationId && operationId !== 'unknown' && operationId !== 'server-monitor') {
                return {
                    isDuplicate: true,
                    reason: `Same operation ID within turn ${turnNumber}: ${operationId}`,
                    similarTo: response
                };
            }
        }
        console.log(`[TURN-AWARE-DEDUP] ${callSid}: Content allowed for turn ${turnNumber}: "${content.substring(0, 50)}..."`);
        return { isDuplicate: false };
    }
    /**
     * Record a response that's being processed
     */
    recordResponse(callSid, content, operationId, turnNumber = 0) {
        const now = Date.now();
        const response = {
            content,
            timestamp: now,
            operationId,
            turnNumber
        };
        let callResponses = this.recentResponses.get(callSid) || [];
        // Add new response
        callResponses.push(response);
        // Remove old responses outside time window
        callResponses = callResponses.filter(r => (now - r.timestamp) <= this.TIME_WINDOW_MS);
        // Limit to max recent responses
        if (callResponses.length > this.MAX_RECENT_RESPONSES) {
            callResponses = callResponses.slice(-this.MAX_RECENT_RESPONSES);
        }
        this.recentResponses.set(callSid, callResponses);
        console.log(`[RESPONSE-DEDUP] Recorded response for ${callSid}: "${content.substring(0, 50)}..." (operation: ${operationId})`);
    }
    /**
     * Clean up responses for a call that has ended
     */
    cleanupCall(callSid) {
        this.recentResponses.delete(callSid);
        console.log(`[RESPONSE-DEDUP] Cleaned up responses for call ${callSid}`);
    }
    /**
     * Clean up old responses across all calls
     */
    cleanupOldResponses() {
        const now = Date.now();
        let cleaned = 0;
        for (const [callSid, responses] of this.recentResponses.entries()) {
            const oldLength = responses.length;
            const filtered = responses.filter(r => (now - r.timestamp) <= this.TIME_WINDOW_MS);
            if (filtered.length === 0) {
                this.recentResponses.delete(callSid);
                cleaned += oldLength;
            }
            else if (filtered.length < oldLength) {
                this.recentResponses.set(callSid, filtered);
                cleaned += (oldLength - filtered.length);
            }
        }
        if (cleaned > 0) {
            console.log(`[RESPONSE-DEDUP] Cleaned up ${cleaned} old responses`);
        }
        return cleaned;
    }
    /**
     * Get stats for debugging
     */
    getStats() {
        const totalCalls = this.recentResponses.size;
        const totalResponses = Array.from(this.recentResponses.values()).reduce((sum, responses) => sum + responses.length, 0);
        const avgResponsesPerCall = totalCalls > 0 ? totalResponses / totalCalls : 0;
        return { totalCalls, totalResponses, avgResponsesPerCall };
    }
}
// Export singleton instance
exports.responseDeduplicationManager = ResponseDeduplicationManager.getInstance();
// Convenience functions
function checkForDuplicate(callSid, content, operationId, turnNumber = 0) {
    return exports.responseDeduplicationManager.isDuplicate(callSid, content, operationId, turnNumber);
}
function recordTTSResponse(callSid, content, operationId, turnNumber = 0) {
    return exports.responseDeduplicationManager.recordResponse(callSid, content, operationId, turnNumber);
}
function cleanupCallResponses(callSid) {
    return exports.responseDeduplicationManager.cleanupCall(callSid);
}
