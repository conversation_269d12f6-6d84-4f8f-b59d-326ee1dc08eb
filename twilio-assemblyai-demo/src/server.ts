/*******************************************************************
 * server.ts
 *
 * 1) Twilio => Node => local WAV + AssemblyAI real-time STT
 * 2) Also calls a Python-based VAD endpoint on port 8085
 * 3) Tracks speaker turns: multiple final transcripts accumulate until
 *    we detect ~1 second of silence -> finalise turn
 *******************************************************************/

// Load environment variables FIRST, before any other imports
import dotenv from 'dotenv'
import path from 'path'
dotenv.config({ path: path.join(__dirname, '../../.env') })

// Add WebSocket polyfill for Node.js before importing Deepgram SDK
if (typeof global.WebSocket === 'undefined') {
    global.WebSocket = require('ws');
}

// Initialize new logging system (compatibility mode OFF)
import { initializeLogging, LoggingPresets } from './utils';
const config = {
  ...LoggingPresets.development(),
  console: true,
  file: true, // Enable file logging
  logDir: './conversations', // Save logs with conversation history
  structured: false, // Human-readable format for debugging
  components: {
    // Enable only key components initially
    'turn-coordination': { enabled: true, level: 2 }, // INFO
    'grace-period': { enabled: true, level: 3 },      // WARN  
    'interruption': { enabled: true, level: 2 },      // INFO
    'race-condition': { enabled: true, level: 3 },    // WARN
    'lifecycle': { enabled: true, level: 2 },         // INFO
    // Disable noisy components
    'vad': { enabled: false },
    'chunk-streaming': { enabled: false },
    'throttle': { enabled: false },
    'conversation-timing': { enabled: false },
    'context-debug': { enabled: false },
    'bootstrap': { enabled: false }
  }
};
initializeLogging(config);

import express from 'express'
import bodyParser from 'body-parser'
import cors from 'cors'
import * as http from 'http';
import * as https from 'https';
import * as url from 'url'; // Added for manual upgrade handling
import { WebSocketServer, WebSocket } from 'ws'
import VoiceResponse from 'twilio/lib/twiml/VoiceResponse'
import { spawn, ChildProcessWithoutNullStreams } from 'child_process'
import axios from 'axios'
import { AssemblyAI, RealtimeTranscriber, RealtimeTranscript } from 'assemblyai'
import fs from 'fs'
import { OpenAI } from 'openai'
import { DefaultAzureCredential, getBearerTokenProvider } from '@azure/identity'
import * as sdk from 'microsoft-cognitiveservices-speech-sdk'
import { createClient } from '@supabase/supabase-js'
import { ElevenLabsClient } from 'elevenlabs'
import { createClient as createDeepgramClient, LiveTranscriptionEvents, LiveConnectionState, LiveTTSEvents } from '@deepgram/sdk'
import * as alawmulaw from 'alawmulaw'
import { interpret } from 'xstate'
import createOsceMachine from './osceMachine'
import { CaseData, TurnTiming, OsceContext, ConversationTurn, UserPreferences, PendingCallDetailsEntry } from './types';
import { latencyTracker, LatencySteps } from './latencyTracker';
// Removed external turnManager - LiveKit now uses same simple turn system as Twilio
// import { getCurrentTurn, startSilenceDetection, startLlmProcessing, startTtsGeneration, startAudioPlayback, waitForUser, initializeCallTurns, cleanupCallTurns } from './turnManager';

// Import optimized FFmpeg converter for FANG-level latency
import { ffmpegConverter } from './services/ffmpeg-simple';
import { createAtomicCallState, ENABLE_ATOMIC_AUDIO, ENABLE_CENTRALIZED_TURNS } from './services/CallStateProxy';
import { turnCoordinationService } from './services/TurnCoordinationService';
import { StreamingMetadata } from './services/TextToSpeechManager';

// Import VAD rate limiter to prevent flooding and call unresponsiveness
import { vadRateLimiter } from './vadRateLimiter';

// Import Server Turn Authority Integration for read-only turn access (centralized in XState)
import { getCallTurnIndex } from './services/turn-authority/ServerIntegrationLayer';

// Import direct WebSocket handler for web/iOS clients  
import { DirectWebSocketHandler } from './services/directWebSocketHandler';

// Import WebRTC signaling handler
// import { WebRTCSignalingHandler } from './services/webrtc/signaling-handler';
import { WebSocketAuthService } from './services/websocketAuth';

// Import LiveKit room service and lobby manager
import { livekitRoomService } from './services/livekit/livekit-room-service';
import { livekitLobbyManager } from './services/livekit/livekit-lobby-manager';

// Import shared constants to avoid duplication
import { 
  SILENCE_FRAMES_THRESHOLD, 
  MIN_SPEECH_FRAMES, 
  MIN_SPEECH_FRAMES_LIVEKIT,
  getMinSpeechFrames,
  INTERRUPT_SPEECH_THRESHOLD,
  INTERRUPT_SPEECH_THRESHOLD_TWILIO,
  INTERRUPT_SPEECH_THRESHOLD_LIVEKIT,
  INTERRUPT_SPEECH_THRESHOLD_WEBRTC,
  RESET_INTERRUPT_SILENCE_THRESHOLD,
  FRAME_DURATION_MS,
  TRANSCRIPT_WAIT_TIMEOUT,
  MEDIA_LOG_INTERVAL,
  VAD_LOG_INTERVAL,
  TTS_PROVIDER,
  TtsProviderType,
  STT_PROVIDER,
  SttProviderType
} from './constants';

// Import audio processing constants
import { 
  getAudioConfig, 
  ConnectionType, 
  VAD_ENDPOINT as AUDIO_VAD_ENDPOINT, 
  DEFAULT_FRAME_SIZE, 
  DEFAULT_SAMPLE_RATE, 
  validateAudioConfig,
  AudioProcessingConfig 
} from './config/audio-constants';

// XState integration import
import { 
  AudioProcessingState, 
  callControllers, 
  createAudioProcessingState, 
  initializeCallStateMachine, 
  fetchUserPreferences, 
  fetchUserCompletedCases, 
  fetchCasesByCategories, 
  selectCaseForUser,
  sendSpeechFrame,
  sendSilenceFrame,
  sendSilenceFrameWithText,
  sendSilenceFrameShort,
  sendSilenceFrameLong,
  forceSetUserUtterance,
  sendInterrupt,
  sendTtsFinished,
  sendAcceptTransition,
  sendRejectTransition,
  requestTransition,
  getCurrentState,
  getResponseToSpeak,
  clearResponseText,
  getConversationHistory,
  stopMachine,
  sendUserUtterance,
  processTransitionUtterance,
  sendSelectCase,
  migrateConversationHistory,
  sendDtmfInput
} from './xstateIntegration'
import { TransitionReasons, createForceResponseEvent, createInternalSetCaseEvent } from './osceEvents';
import { routeConverseLLM, routeTransitionDecision, routeStreamingRAG } from './llm-mode-router';
import { Request, Response, NextFunction } from 'express';
import { preserveContext, restoreContext } from './contextUtils';
import { SessionManager, type CreateSessionResult } from './sessionManager';
// 🏗️ PHASE 4: Import infrastructure cutover functions
import { 
  getTranscriptState, 
  updateTranscriptState, 
  getVADState, 
  trackProcessWithInfrastructure,
  transitionToState,
  processVADWithMonitoring,
  INFRASTRUCTURE_FLAGS 
} from './infrastructureHelpers';

// WebRTC Integration
// import { WebRTCIntegration } from './services/webrtc/webrtc-integration'; // COMMENTED OUT: WebRTC P2P connection path


/**
 * Helper function to update turn timing fields in a type-safe way
 */
function updateTurnTiming(
  turnTimings: TurnTiming[],
  turnIndex: number,
  updates: Partial<Omit<TurnTiming, 'turnIndex'>>
): TurnTiming[] {
  const newTurnTimings = [...(turnTimings || [])];
  let timing = newTurnTimings.find(t => t.turnIndex === turnIndex);
  
  if (!timing) {
    console.log(`[TurnTiming] Creating new timing for turn ${turnIndex}`);
    timing = { turnIndex };
    newTurnTimings.push(timing);
  }
  
  // Update all provided fields
  Object.assign(timing, updates);
  
  // Log the update for debugging
  const updatedFields = Object.keys(updates).join(', ');
  console.log(`[TurnTiming] Updated turn ${turnIndex}: ${updatedFields}`);
  
  return newTurnTimings;
}

/** 
 * We'll define a small function to split a Buffer into 20ms frames
 * at 8 kHz, 16-bit PCM => 320 bytes per frame.
 * If leftover data remains, we keep it in a global or callSid-based 
 * leftover buffer for the next chunk.
 */
export function splitTo20msFrames(buffer: Buffer, leftover: Buffer, frameSize?: number): Buffer[] {
  // Use provided frameSize or fall back to default for backward compatibility
  const FRAME_SIZE = frameSize || DEFAULT_FRAME_SIZE; // 320 bytes for Twilio compatibility
  
  // Combine leftover + new chunk
  const combined = Buffer.concat([leftover, buffer])

  const frames: Buffer[] = []
  let offset = 0

  // Only create frames of exactly FRAME_SIZE bytes
  while (offset + FRAME_SIZE <= combined.length) {
    const frame = combined.slice(offset, offset + FRAME_SIZE);
    // Sanity check to ensure we're sending valid frames
    if (frame.length === FRAME_SIZE) {
      frames.push(frame);
    } else {
      console.warn(`Created invalid frame size: ${frame.length} bytes, expected ${FRAME_SIZE}`);
    }
    offset += FRAME_SIZE;
  }

  // The leftover portion - data that's not a complete frame
  const newLeftover = combined.slice(offset);

  // Return frames + leftover as the last element
  return [...frames, newLeftover];
}

/**
 * Diagnostic logging helper for delay investigation
 * Writes to both console and logs.txt file for comprehensive tracking
 */
function logDiagnostic(callSid: string, category: string, message: string, data?: any): void {
  const timestamp = new Date().toISOString();
  const logLine = `[${timestamp}] [${category}] ${callSid}: ${message}${data ? ' | ' + JSON.stringify(data) : ''}`;
  
  // Log to console
  console.log(logLine);
  
  // Log to file for persistent analysis
  try {
    const logDir = path.join(__dirname, '..', 'conversations', callSid);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    const logFile = path.join(logDir, 'logs.txt');
    fs.appendFileSync(logFile, logLine + '\n');
  } catch (error) {
    console.error(`Failed to write diagnostic log for ${callSid}:`, error);
  }
}

//-----------------------------------------------
// Configuration
//-----------------------------------------------
const PORT = process.env.PORT || 5000
// VAD endpoint is now imported from audio-constants.ts

// DTMF Configuration
const DTMF_ENABLED = process.env.ENABLE_DTMF !== 'false' // Enabled by default unless explicitly disabled
// RAG_SERVER_URL is now handled by the LLM router system

// --------------------------------------------------------------------------------------
// Logging Configuration
// --------------------------------------------------------------------------------------
// Centralized logging controls - set these to true/false to control logging volume
const LOGGING = {
  // Core app logs - should generally remain true
  CORE_EVENTS: true,          // Log core events like calls starting/ending
  STATE_TRANSITIONS: true,    // Log when state machine changes states
  
  // State machine detailed logs
  CONTEXT_CHANGES: true,      // Log when context changes (can be verbose)
  CONTEXT_DUMP: true,         // Log detailed context dumps
  STATE_CHANGE_VERBOSE: true, // More verbose state change logging
  
  // Media processing logs - very noisy
  MEDIA_PACKETS: false,       // Log media packet counts
  STREAM_DEBUG: false,        // Log stream processing details
  FFMPEG_STDERR: false,       // Log ffmpeg progress output
  
  // Speech processing logs
  TTS_EVENTS: true,           // Text-to-speech events
  TTS_VERBOSE: true,         // Detailed TTS processing
  STT_EVENTS: true,           // Speech-to-text events
  STT_VERBOSE: false,         // Detailed STT processing
  PARTIAL_TRANSCRIPTS: false, // Log partial (non-final) transcripts
  
  // Voice activity detection
  VAD_EVENTS: false,          // Voice activity detection events
  VAD_FRAMES: false,          // Log individual voice frames
  SILENCE_DETECTION: false,   // Silence detection logging
  TIMING_EVENTS: true         // Log detailed turn timing events
};

// STT Provider Configuration - now imported from constants
console.log(`Using STT Provider: ${STT_PROVIDER}`);

// Export STT_PROVIDER for use in other modules
export { STT_PROVIDER };

// Log TTS Provider
console.log(`Using TTS Provider: ${TTS_PROVIDER}`);

// Simple connection pool for Azure Speech TTS - QUICK LATENCY FIX
const ttsConnectionPool = new Map<string, { synthesizer: sdk.SpeechSynthesizer; lastUsed: number }>();
const TTS_CONNECTION_TIMEOUT = 30000; // 30 seconds

/**
 * Calculate intelligent timeout for TTS streaming based on text length
 * Uses conservative speaking rate to ensure long responses don't get cut off
 */
function calculateTTSTimeout(text: string): number {
  const DEFAULT_TTS_TIMEOUT = 120000; // 2 minutes fallback
  const MIN_TIMEOUT = 60000; // 1 minute minimum
  const MAX_TIMEOUT = 300000; // 5 minutes maximum
  
  if (!text || text.trim().length === 0) {
    return DEFAULT_TTS_TIMEOUT;
  }
  
  // Conservative estimates for TTS duration:
  // - Average speaking rate: ~100 words per minute (slower than normal for safety)
  // - Average word length: ~5 characters
  // - So: ~500 characters per minute = ~8.3 characters per second
  const charactersPerSecond = 8;
  const estimatedDurationMs = (text.length / charactersPerSecond) * 1000;
  
  // Add 50% buffer for network latency, processing time, and safety margin
  const timeoutWithBuffer = estimatedDurationMs * 1.5;
  
  // Ensure timeout is within reasonable bounds
  const finalTimeout = Math.max(MIN_TIMEOUT, Math.min(timeoutWithBuffer, MAX_TIMEOUT));
  
  console.log(`[INTELLIGENT-TIMEOUT] Text length: ${text.length} chars, calculated timeout: ${Math.round(finalTimeout/1000)}s`);
  
  return finalTimeout;
}

/**
 * Inactivity timeout configuration
 */
const INACTIVITY_WARNING_TIMEOUT = 120000; // 2 minutes (more reasonable for conversation pauses)
const INACTIVITY_END_CALL_TIMEOUT = 60000; // Additional 1 minute after warning (3 minutes total)

// Rate limiting for inactivity timer reset logs
const inactivityLogTimestamps: Record<string, number> = {};
const INACTIVITY_LOG_RATE_LIMIT = 10000; // Only log once every 10 seconds per call

/**
 * Reset the inactivity timer for a call
 * Call this whenever there's user activity (speech, transcript, interaction)
 */
export function resetInactivityTimer(callSid: string): void {
  // OLD INACTIVITY SYSTEM DISABLED - XState handles timeouts now
  return;
  const callState = calls[callSid];
  if (!callState) {
    console.warn(`[INACTIVITY] Cannot reset timer - call state not found: ${callSid}`);
    return;
  }

  // Clear existing timer
  if (callState.inactivityTimeoutId) {
    clearTimeout(callState.inactivityTimeoutId);
    callState.inactivityTimeoutId = undefined;
  }

  // Reset flags and timestamp
  callState.lastActivityTime = Date.now();
  callState.waitingForUserResponse = false;
  callState.inactivityPromptSent = false;

  // Start new inactivity timer
  callState.inactivityTimeoutId = setTimeout(() => {
    checkInactivity(callSid);
  }, INACTIVITY_WARNING_TIMEOUT);

  // Rate-limited logging - only log once every 10 seconds per call
  const now = Date.now();
  const lastLogTime = inactivityLogTimestamps[callSid] || 0;
  if (now - lastLogTime > INACTIVITY_LOG_RATE_LIMIT) {
    console.log(`[INACTIVITY] Timer reset for ${callSid} - next check in ${INACTIVITY_WARNING_TIMEOUT/1000}s`);
    inactivityLogTimestamps[callSid] = now;
  }
}

/**
 * Update metadata for the latest AI turn in conversation history
 */
function updateLatestAITurnMetadata(callSid: string, metadata: Partial<ConversationTurn>) {
  const state = calls[callSid];
  if (!state || !state.conversation || state.conversation.length === 0) {
    console.warn(`[CONVERSATION-META] No conversation found for ${callSid}`);
    return;
  }
  
  // Find the latest assistant turn
  for (let i = state.conversation.length - 1; i >= 0; i--) {
    if (state.conversation[i].speaker === 'assistant') {
      Object.assign(state.conversation[i], metadata);
      console.log(`[CONVERSATION-META] Updated AI turn ${i} for ${callSid}:`, metadata);
      
      // Save updated conversation with metadata
      saveEnhancedConversation(callSid);
      return;
    }
  }
  
  console.warn(`[CONVERSATION-META] No assistant turn found to update for ${callSid}`);
}

/**
 * Get conversation history with interrupted responses filtered out
 * Useful for generating clean transcripts for marking and resumption
 */
function getCleanConversationHistory(callSid: string): ConversationTurn[] {
  const state = calls[callSid];
  if (!state || !state.conversation) {
    return [];
  }
  
  return getCleanConversationFromData(state.conversation);
}

/**
 * PHASE 1: New function to clean conversation data directly (supports XState data)
 * Get conversation history with interrupted responses filtered out
 */
function getCleanConversationFromData(conversationData: any[]): ConversationTurn[] {
  if (!conversationData || !Array.isArray(conversationData)) {
    return [];
  }
  
  return conversationData.filter(turn => {
    // Keep all user messages
    if (turn.speaker === 'caller') return true;
    
    // For AI messages, only keep non-interrupted ones or those with high completion
    if (turn.speaker === 'assistant') {
      // Type guard: check if this is an enhanced ConversationTurn or legacy format
      const enhancedTurn = turn as ConversationTurn;
      if (enhancedTurn.interrupted === false) return true; // Definitely completed
      if (enhancedTurn.interrupted === undefined) return true; // Legacy entries without metadata
      if (enhancedTurn.interrupted === true && (enhancedTurn.completionPercent || 0) >= 80) return true; // Mostly completed
      return false; // Interrupted with low completion
    }
    
    return true;
  }).map(turn => ({
    speaker: turn.speaker as 'caller' | 'assistant',
    text: turn.text,
    ...(turn as ConversationTurn) // Spread any additional metadata if present
  }));
}

/**
 * Synchronize server conversation state FROM XState (single source of truth)
 * This ensures server state mirrors XState for legacy functions that still depend on it
 */
function syncConversationFromXState(callSid: string): void {
  const state = calls[callSid];
  if (!state) return;
  
  const { getConversationFromXState } = require('./xstateIntegration');
  const xstateConversation = getConversationFromXState(callSid);
  
  if (xstateConversation.length > 0) {
    state.conversation = xstateConversation;
    console.log(`[SYNC] Updated server conversation state from XState: ${xstateConversation.length} turns`);
  }
}

/**
 * Get full conversation history including interrupted responses with metadata
 * Useful for debugging and detailed analysis
 */
function getFullConversationHistory(callSid: string): ConversationTurn[] {
  // PRODUCTION-GRADE FIX: Get conversation data FROM XState (single source of truth)
  const { getConversationFromXState } = require('./xstateIntegration');
  const xstateConversation = getConversationFromXState(callSid);
  
  if (xstateConversation.length > 0) {
    return xstateConversation.map((turn: ConversationTurn) => ({
      speaker: turn.speaker as 'caller' | 'assistant',
      text: turn.text,
      timestamp: turn.timestamp,
      xstate_state: turn.xstate_state,
      interrupted: turn.interrupted,
      turnIndex: turn.turnIndex,
      ...(turn as ConversationTurn) // Spread any additional metadata if present
    }));
  }
  
  // Fallback to server state if XState unavailable (should not happen in production)
  const state = calls[callSid];
  if (state?.conversation) {
    console.warn(`[CONVERSATION-FALLBACK] Using server state for ${callSid} - XState unavailable`);
    return state.conversation.map(turn => ({
      speaker: turn.speaker as 'caller' | 'assistant',
      text: turn.text,
      ...(turn as ConversationTurn)
    }));
  }
  
  return [];
}

/**
 * Check for inactivity and handle timeout logic
 * 30s inactivity → "are you still there?" prompt
 * Additional 30s → end call with cleanup
 */
async function checkInactivity(callSid: string): Promise<void> {
  const callState = calls[callSid];
  if (!callState) {
    console.warn(`[INACTIVITY] Cannot check - call state not found: ${callSid}`);
    return;
  }

  const now = Date.now();
  const timeSinceActivity = now - (callState.lastActivityTime || now);
  
  // Skip if AI is currently speaking (don't count AI speech as inactivity)
  if (callState.isModelSpeaking) {
    console.log(`[INACTIVITY] Skipping check for ${callSid} - AI is speaking`);
    // Reset timer to check again after AI finishes
    resetInactivityTimer(callSid);
    return;
  }
  
  // CRITICAL FIX: Skip inactivity checks during WebSocket grace period
  const controller = callControllers[callSid];
  if (controller?.machine && controller.machine.getSnapshot().context.gracePeriodState?.active) {
    console.log(`[INACTIVITY] Skipping check for ${callSid} - in WebSocket reconnection grace period`);
    // Reset timer to check again after grace period
    resetInactivityTimer(callSid);
    return;
  }

  if (!callState.waitingForUserResponse) {
    // First timeout: Send "are you still there?" prompt
    console.log(`[INACTIVITY] First timeout for ${callSid} after ${timeSinceActivity/1000}s - sending prompt`);
    
    callState.waitingForUserResponse = true;
    callState.inactivityPromptSent = true;
    
    try {
      // Generate and send "are you still there?" TTS using Turn Authority integration
      const promptText = "Are you still there?";
      const currentTurnIndex = await getCallTurnIndex(callSid, calls);
      const audioPath = await generateAudioResponse(promptText, callSid, currentTurnIndex + 1);
      
      if (audioPath) {
        // Send the prompt audio to the appropriate connection type
        if (callState.connectionType === 'livekit' && callState.livekitRoomName) {
          await sendTTSToLiveKit(callSid, audioPath, currentTurnIndex + 1);
        } else {
          await streamAudioToCall(audioPath, callSid, currentTurnIndex + 1);
        }
        console.log(`[INACTIVITY] Sent "are you still there?" prompt to ${callSid}`);
      }
    } catch (error) {
      console.error(`[INACTIVITY] Error sending prompt to ${callSid}:`, error);
    }
    
    // Set second timer for final timeout
    callState.inactivityTimeoutId = setTimeout(() => {
      checkInactivity(callSid);
    }, INACTIVITY_END_CALL_TIMEOUT);
    
  } else {
    // Second timeout: End call with cleanup
    console.log(`[INACTIVITY] Final timeout for ${callSid} - ending call due to inactivity`);
    
    try {
      // Send goodbye message using Turn Authority integration
      const goodbyeText = "Thank you for using our service. The call will now end due to inactivity.";
      const currentTurnIndex = await getCallTurnIndex(callSid, calls);
      const audioPath = await generateAudioResponse(goodbyeText, callSid, currentTurnIndex + 1);
      
      if (audioPath) {
        if (callState.connectionType === 'livekit' && callState.livekitRoomName) {
          await sendTTSToLiveKit(callSid, audioPath, currentTurnIndex + 1);
        } else {
          await streamAudioToCall(audioPath, callSid, currentTurnIndex + 1);
        }
        
        // Wait a moment for audio to play before ending call
        setTimeout(() => {
          endCallDueToInactivity(callSid);
        }, 3000);
      } else {
        // If audio generation fails, end call immediately
        endCallDueToInactivity(callSid);
      }
    } catch (error) {
      console.error(`[INACTIVITY] Error sending goodbye to ${callSid}:`, error);
      endCallDueToInactivity(callSid);
    }
  }
}

/**
 * End call due to inactivity with full cleanup
 */
function endCallDueToInactivity(callSid: string): void {
  console.log(`[INACTIVITY] Ending call ${callSid} due to inactivity timeout`);
  
  const callState = calls[callSid];
  if (callState) {
    // Clear inactivity timer
    if (callState.inactivityTimeoutId) {
      clearTimeout(callState.inactivityTimeoutId);
      callState.inactivityTimeoutId = undefined;
    }
    
    // Perform full cleanup (this will handle all file cleanup)
    cleanupCall(callSid);
  }
}

// AssemblyAI configuration (only used if STT_PROVIDER is 'assemblyai')
const ASSEMBLYAI_API_KEY = '********************************' 

// Azure Speech configuration (used for TTS and STT)
const AZURE_SPEECH_KEY = process.env.AZURE_SPEECH_KEY || 'c539857332064fc38e4aa5075b652087';
const AZURE_SPEECH_REGION = process.env.AZURE_SPEECH_REGION || 'swedencentral';
const AZURE_SPEECH_ENDPOINT = process.env.AZURE_SPEECH_ENDPOINT || ''; // Optional custom endpoint
const AZURE_SPEECH_VOICE = 'en-US-AvaMultilingualNeural'; // High quality neural voice

// ElevenLabs configuration (used for TTS when TTS_PROVIDER is 'elevenlabs')
const ELEVENLABS_API_KEY = process.env.ELEVENLABS_KEY || '';
const ELEVENLABS_VOICE_ID = process.env.ELEVENLABS_VOICE_ID || 'c8MZcZcr0JnMAwkwnTIu'; // Default voice ID
// Alternative voice option: 'Fahco4VZzobUeiPqni1S'
const ELEVENLABS_MODEL_ID = process.env.ELEVENLABS_MODEL_ID || 'eleven_multilingual_v2'; // Default model

// Deepgram configuration (used for STT and TTS when providers are 'deepgram')
const DEEPGRAM_API_KEY = process.env.DEEPGRAM_API_KEY || '';
const DEEPGRAM_STT_MODEL = 'nova-3'; // Nova-3 model for STT
const DEEPGRAM_TTS_MODEL = 'aura-2-thalia-en'; // Aura-2 Thalia voice for TTS

// Constants for speech detection
// These are now primarily managed by the state machine
// const SILENCE_FRAMES_THRESHOLD = 35; // Removed - now imported from shared constants
// const LONG_PAUSE_THRESHOLD = 5; 
// const MIN_SPEECH_FRAMES = 5; // Removed - now imported from shared constants

// Track media packet counts to reduce logging
const mediaPacketCounts: Record<string, number> = {};

// Logging control variables - derived from LOGGING settings
// const MEDIA_LOG_INTERVAL = 10000; // Set to a very high number to effectively disable
const TRANSCRIPT_LOG_ALL = LOGGING.STT_VERBOSE;
const TRANSCRIPT_LOG_FINAL_ONLY = !LOGGING.STT_EVENTS;
// const VAD_LOG_INTERVAL = LOGGING.VAD_FRAMES ? 500 : 10000; // Log every 500 frames if enabled, otherwise rarely
const SILENCE_DETECTION_LOGS = LOGGING.SILENCE_DETECTION;
const VERBOSE_VAD_LOGGING = false; // Set to false to reduce logging
// const TRANSCRIPT_WAIT_TIMEOUT = 2000; // Removed - now imported from shared constants

// Environment variables
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const SUPABASE_SERVICE_KEY = process.env.NEXT_SERVICE_SUPABASE_SERVICE_KEY || '';

// Initialize Supabase client with service key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Temporary storage for details received in /voice webhook before WS connection starts
export const pendingCallDetails: Record<string, PendingCallDetailsEntry> = {};

// Environmental flag to enable/disable streaming TTS (add this near the top with other constants)
const ENABLE_STREAMING_TTS = process.env.ENABLE_STREAMING_TTS === 'true' || false;

//-----------------------------------------------
// Setup Express
//-----------------------------------------------
const app = express()

// CORS configuration for web clients
app.use(cors({
  origin: [
    'https://andromedaagents.com',
    'http://localhost:3000',
    'http://localhost:5173',
    /^https:\/\/.*\.ngrok-free\.app$/,
    true // Allow all origins for development (restrict in production)
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type', 
    'Authorization',
    'ngrok-skip-browser-warning',
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  optionsSuccessStatus: 200 // Some legacy browsers choke on 204
}))

// Apply raw body parsing for webhook endpoint specifically
app.use('/api/livekit/webhook', express.raw({ type: 'application/webhook+json' }));

// Apply body parsing middleware for all other endpoints
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: false }))

// Handle LiveKit webhook endpoint
app.post('/api/livekit/webhook', function(req, res) {
  (async () => {
    try {
      console.log('[LiveKit Webhook] Received webhook event');
      await livekitWebhookListener.handleWebhook(req, res, livekitServerHandler);
    } catch (error) {
      console.error('[LiveKit Webhook] Error processing webhook:', error);
      res.status(500).json({ error: 'Webhook processing failed' });
    }
  })();
});

// AssemblyAI client (conditionally initialized)
let aaiClient: AssemblyAI | null = null;
if (STT_PROVIDER === 'assemblyai') {
    if (!ASSEMBLYAI_API_KEY) {
        console.error("Error: ASSEMBLYAI_API_KEY is required when STT_PROVIDER is 'assemblyai'");
        process.exit(1);
    }
    aaiClient = new AssemblyAI({ apiKey: ASSEMBLYAI_API_KEY });
}

// ElevenLabs client (conditionally initialized)
let elevenLabsClient: ElevenLabsClient | null = null;
if (TTS_PROVIDER === 'elevenlabs' || TTS_PROVIDER === 'elevenlabs_stream') {
    if (!ELEVENLABS_API_KEY) {
        console.error(`Error: ELEVENLABS_KEY is required when TTS_PROVIDER is '${TTS_PROVIDER}'`);
        process.exit(1);
    }
    elevenLabsClient = new ElevenLabsClient({
        apiKey: ELEVENLABS_API_KEY,
    });
    console.log('ElevenLabs client initialized');
}

// Deepgram client (conditionally initialized)
let deepgramClient: any | null = null;
if (STT_PROVIDER === 'deepgram' || TTS_PROVIDER === 'deepgram') {
    if (!DEEPGRAM_API_KEY) {
        console.error(`Error: DEEPGRAM_API_KEY is required when STT_PROVIDER or TTS_PROVIDER is 'deepgram'`);
        process.exit(1);
    }
    // Try different WebSocket URL configuration
    deepgramClient = createDeepgramClient(DEEPGRAM_API_KEY, {
        global: {
            url: "https://api.deepgram.com"
        }
    });
    console.log('Deepgram client initialized with custom URL');
}

// Define a common interface for STT providers
interface SttProvider {
    sendAudio(chunk: Buffer): void;
    close(): void;
    connect?(): Promise<void>; // Optional connect method if needed async
}

// Track call state
export interface CallState {
  // Core call identification
  callSid: string;
  userId?: string; // User ID for case selection
  sessionId?: string; // Session ID linking multiple calls
  
  // Session readiness tracking (iOS Audio Ready Signal)
  sessionReady?: boolean; // True when LiveKit room is ready
  iosAudioReady?: boolean; // True when iOS audio pipeline is ready
  
  // Audio processing state
  leftover: Buffer;
  consecutiveSilenceFrames: number;
  silenceFrameCounter: number;
  speechFrameCounter: number;
  speechFramesInCurrentTurn: number;
  hasSpeechDetected: boolean;
  isModelSpeaking: boolean;
  finalizationInProgress: boolean;
  
  // Conversation tracking - DEPRECATED FIELDS (XState infrastructure is single source of truth)
  currentTurnText?: string; // DEPRECATED: Use XState infrastructure getTranscriptState() instead
  previousTurnText?: string; // DEPRECATED: Grace period logic handled by XState context
  conversation: ConversationTurn[]; // Use full ConversationTurn interface for enhanced tracking
  turnTimings: TurnTiming[];
  currentTurnIndex: number; // Track the turn number
  currentTurnStartTime?: number;
  
  // Call metadata
  callStartTime: number; // Timestamp when the call started
  connectionType?: 'twilio' | 'direct' | 'livekit';
  audioQuality?: 'standard' | 'high';
  lastUpdated: number;
  startTime: string;
  
  // STT/TTS providers
  sttProvider?: SttProvider;
  audioStream?: sdk.PushAudioInputStream;
  speechRecognizer?: sdk.SpeechRecognizer;
  deepgramConnection?: any; // Deepgram live transcription connection
  
  // Session and case data
  sessionInfo?: CreateSessionResult;
  caseSelectionAttempted?: boolean;
  selectedCase?: string;
  caseData?: any;
  
  // Response handling
  currentModelResponse?: string;
  lastProcessedResponse?: string;
  lastRagResponse?: { speaker: string; text: string };
  
  // File and path management
  audioResponsePath?: string;
  recordingStoragePath?: string; // Store just the storage path instead of full URL
  
  // Process management
  recordFfmpeg?: ChildProcessWithoutNullStreams;
  decodeFfmpeg?: ChildProcessWithoutNullStreams;
  currentFfmpegProcess?: ChildProcessWithoutNullStreams;
  
  // Timeouts and intervals
  currentSpeakingTimeout?: NodeJS.Timeout;
  pendingFinalizeTimeout?: NodeJS.Timeout;
  
  // Inactivity timeout system
  lastActivityTime?: number; // Timestamp of last user activity (speech, transcript, etc.)
  inactivityTimeoutId?: NodeJS.Timeout; // Timer for checking inactivity
  waitingForUserResponse?: boolean; // True when "are you still there?" prompt was sent
  inactivityPromptSent?: boolean; // Track if we've already sent the inactivity prompt
  
  // WebSockets
  currentElevenLabsWebSocket?: WebSocket;
  directWebSocket?: any; // Store WebSocket connection for direct calls
  
  // Counters and flags
  chunkCount?: number;
  streamSid?: string;
  
  // Audio processing
  silenceStart: number | null; // Required for AudioProcessingState
  
  // Recording and playback state
  recordingActualStartTime?: number; // Timestamp when the first decoded audio chunk is processed
  currentlyPlayingTurnIndex?: number; // Track which turn's audio is currently playing
  recording: boolean; // Recording state
  
  // Allow any other properties
  [key: string]: any;
  recordingStartTime: number | null;
  recordingPath: string | null;
  recordingWriter: any | null;
  isInitialized?: boolean; // Flag to prevent double initialization
  selectedCaseData?: CaseData | null; // Store selected case data here
  audioStreamingStartAt?: number; // Timestamp when audio streaming starts
  effectiveRecordingStartTime?: number; // Effective start time for recording
  activeTtsOperation: boolean; // Prevent TTS race conditions
  
  // TTS Race Condition Prevention
  isTtsGenerating?: boolean; // Track if TTS generation is in progress
  currentTtsText?: string; // Current TTS text being generated
  currentTtsTurnIndex?: number; // Current TTS turn index being generated
  
  // Legacy TTS tracking (kept for backward compatibility during transition)
  activeSynthesizers?: Array<any>; // Deprecated - use TextToSpeechManager task system
  activeSentenceStreams?: Array<{process: any, sentenceIndex: number}>; // Active FFmpeg sentence streaming processes
  
  // Note: LiveKit TTS cancellation uses the same flags as Twilio (isTtsGenerating, etc.)
  ttsQueue?: string[]; // Queue for TTS messages when a TTS operation is active
  vadBuffer?: Buffer; // Buffer for combining LiveKit 10ms chunks into 20ms VAD frames
  lastProcessedTurnNumber?: number; // Track the last processed turn number
  transcriptSentForCurrentTurn?: boolean; // Track if transcript has been sent for current turn
  longSignalSentForCurrentTurn?: boolean; // Track if long signal has been sent for current turn
  vadDecisionLogged?: boolean; // Flag to prevent VAD decision logging spam
  lastAudioReceived?: number; // Timestamp when the last audio packet was received
  audioPipelineConfigLogged?: boolean; // Flag to prevent audio pipeline config logging spam
  
  // LiveKit integration
  livekitRoomName?: string; // LiveKit room name for this call
  livekitEgressPending?: boolean; // Whether track egress creation is pending
  livekitEgressId?: string; // LiveKit egress ID for room composite recording
  livekitEgressCompleted?: boolean; // Whether egress recording completed successfully
  livekitEgressFailed?: boolean; // Whether egress recording failed
  livekitEgressError?: string; // Error message if egress failed
  livekitEgressEndTime?: number; // Timestamp when egress ended (success or failure)
  livekitRecordingProcessed?: boolean; // Whether recording file was processed (converted & uploaded)
  livekitRecordingProcessedAt?: number; // Timestamp when recording processing completed
  livekitRecordingEmpty?: boolean; // Whether the recording file was empty (0 bytes)
  livekitIngressUrl?: string; // RTMP ingress URL for TTS audio streaming
  livekitRTCHandler?: LiveKitRTCHandler; // RTC handler for direct room participation
  
  // WebSocket reconnection grace period now managed by XState
  
  // Audio duration prediction tracking
  audioDurationPredictions?: Record<number, any>; // Turn index -> prediction details
  
  // Cleanup coordination flag to prevent phantom call detection
  isCleaningUp?: boolean; // Set to true when cleanup starts, prevents double cleanup
}
// CRITICAL FIX: Add global context cache to recover from XState context loss during transitions
// Define it in the global scope so it can be accessed from anywhere
global.contextCache = {
  cache: {} as Record<string, {
    callSid: string;
    caseData: any;
    conversation: ConversationTurn[];
    currentState?: string;
    lastUpdated: number;
  }>,
  
  // Make these functions available globally for cross-module access
  updateContext: function(callSid: string, contextUpdate: Partial<{
    caseData: any;
    conversation: ConversationTurn[];
    currentState: string;
  }>) {
    if (!callSid) {
      console.error(`[CRITICAL] Attempted to update global context with invalid callSid`);
      return;
    }
    
    // Initialize if not exists
    if (!this.cache[callSid]) {
      this.cache[callSid] = {
        callSid,
        caseData: null,
        conversation: [],
        lastUpdated: Date.now()
      };
    }
    
    // Update fields
    if (contextUpdate.caseData) {
      this.cache[callSid].caseData = contextUpdate.caseData;
    }
    
    if (contextUpdate.conversation) {
      this.cache[callSid].conversation = contextUpdate.conversation;
    }
    
    if (contextUpdate.currentState) {
      this.cache[callSid].currentState = contextUpdate.currentState;
    }
    
    this.cache[callSid].lastUpdated = Date.now();
    console.log(`[Context:${callSid}] Updated global context cache with new data`);
  },
  
  getContext: function(callSid: string) {
    return this.cache[callSid] || null;
  }
};

// Create simple global function wrappers for easier access from any module
(global as any).updateGlobalContext = function(callSid: string, contextUpdate: any) {
  return global.contextCache.updateContext(callSid, contextUpdate);
};

(global as any).getGlobalContext = function(callSid: string) {
  return global.contextCache.getContext(callSid);
};

// Type definition to make TypeScript aware of our global additions
declare global {
  var contextCache: {
    cache: Record<string, {
      callSid: string;
      caseData: any;
      conversation: ConversationTurn[];
      currentState?: string;
      lastUpdated: number;
    }>;
    updateContext: Function;
    getContext: Function;
  };
  function updateGlobalContext(callSid: string, contextUpdate: any): void;
  function getGlobalContext(callSid: string): any;
}

// Global map to store call state, STT clients, and other call-specific data
export const calls: Record<string, CallState> = {}; // EXPORTED

// Make calls accessible globally for XState integration
(global as any).calls = calls;

// VAD diagnostics tracking (highest probability per second)
const vadDiagnostics: { [callSid: string]: { highestProb: number; bestSamples: string; lastLogTime: number } } = {};

// Global map to track interruption threshold effectiveness
const thresholdDiagnostics: Record<string, { 
  connectionType: ConnectionType;
  totalFrames: number;
  speechFrames: number;
  interruptionAttempts: number;
  successfulInterruptions: number;
  thresholdUsed: number;
}> = {};

// LiveKit RTC handler management
const livekitRTCHandlers: Map<string, LiveKitRTCHandler> = new Map();

// Helper functions for LiveKit RTC handler management
export function getLiveKitRTCHandler(callSid: string): LiveKitRTCHandler | null {
  return livekitRTCHandlers.get(callSid) || null;
}

export function setLiveKitRTCHandler(callSid: string, handler: LiveKitRTCHandler): void {
  livekitRTCHandlers.set(callSid, handler);
}

export function removeLiveKitRTCHandler(callSid: string): void {
  const handler = livekitRTCHandlers.get(callSid);
  if (handler) {
    handler.disconnect().catch(console.error);
    livekitRTCHandlers.delete(callSid);
  }
}

// Create audio directory if it doesn't exist
const AUDIO_DIR = path.join(__dirname, '../audio_responses')
if (!fs.existsSync(AUDIO_DIR)) {
  fs.mkdirSync(AUDIO_DIR, { recursive: true })
}

// Create conversation directory
const CONVERSATIONS_DIR = path.join(__dirname, '../conversations')
if (!fs.existsSync(CONVERSATIONS_DIR)) {
  fs.mkdirSync(CONVERSATIONS_DIR, { recursive: true })
}

// Helper functions for conversation and database access
function safeJSONParse(text: string) {
  try {
    return JSON.parse(text);
  } catch (e) {
    console.error('Failed to parse JSON:', e);
    return {};
  }
}

function getCallData(data: any) {
  try {
    return data?.start?.callSid || data?.media?.track === 'inbound' && data?.streamSid;
  } catch (e) {
    console.error('Failed to extract call data:', e);
    return null;
  }
}

// Function to save conversation to disk
export function saveConversation(callSid: string, conversation: ConversationTurn[]) {
  const filename = path.join(CONVERSATIONS_DIR, `${callSid}.json`);
  try {
    fs.writeFileSync(filename, JSON.stringify(conversation, null, 2));
    console.log(`Saved conversation for call ${callSid} to disk`);
  } catch (err) {
    console.error(`Error saving conversation for call ${callSid}:`, err);
  }
}

/**
 * Save conversation with full interruption metadata for debugging and analysis
 */
export function saveConversationWithMetadata(callSid: string, conversation: ConversationTurn[]) {
  const filename = path.join(CONVERSATIONS_DIR, `${callSid}_full.json`);
  try {
    fs.writeFileSync(filename, JSON.stringify(conversation, null, 2));
    console.log(`Saved full conversation with metadata for call ${callSid} to disk`);
  } catch (err) {
    console.error(`Error saving full conversation for call ${callSid}:`, err);
  }
}

// Batched database saving state
const databaseSaveTimers: Record<string, NodeJS.Timeout> = {};
const BATCH_SAVE_INTERVAL = 30000; // 30 seconds

/**
 * Enhanced conversation saving with batched database updates
 * PHASE 1: Updated to support XState conversation data as parameter
 */
export function saveEnhancedConversation(callSid: string, conversationData?: any[]) {
  // 🔍 DEBUG: Create debug folder structure for this call
  const debugDir = path.join(__dirname, '../conversations', callSid);
  try {
    if (!fs.existsSync(debugDir)) {
      fs.mkdirSync(debugDir, { recursive: true });
    }
  } catch (error) {
    console.error(`[DEBUG] Failed to create debug directory ${debugDir}:`, error);
  }

  // PHASE 1: Use provided conversation data OR get from XState (single source of truth)
  let rawConversation = conversationData;
  let conversationSource = 'Provided parameter';
  
  if (!rawConversation) {
    const { getConversationFromXState } = require('./xstateIntegration');
    rawConversation = getConversationFromXState(callSid);
    conversationSource = rawConversation.length > 0 ? 'XState (authoritative)' : 'XState (empty)';
    
    // Final fallback to server state (should not happen in production)
    if (rawConversation.length === 0 && calls[callSid]?.conversation) {
      rawConversation = calls[callSid].conversation;
      conversationSource = 'Server (deprecated fallback)';
      console.warn(`[CONVERSATION-DEPRECATED] Using server state for ${callSid} - XState should be authoritative`);
    }
  }
  
  try {
    const rawPath = path.join(debugDir, '1_raw_conversation.json');
    fs.writeFileSync(rawPath, JSON.stringify(rawConversation, null, 2));
    console.log(`[DEBUG] Saved raw conversation: ${rawPath} (${rawConversation.length} turns) - Source: ${conversationSource}`);
  } catch (error) {
    console.error(`[DEBUG] Failed to save raw conversation:`, error);
  }

  // Always save to disk immediately (for real-time access and crash recovery)
  // PHASE 1: Use provided conversation data for cleaning instead of server state
  const cleanConversation = conversationData 
    ? getCleanConversationFromData(rawConversation)
    : getCleanConversationHistory(callSid);
  const cleanSimplified = cleanConversation.map(turn => ({ speaker: turn.speaker, text: turn.text }));
  
  // 🔍 DEBUG: Save clean conversation after filtering
  try {
    const cleanPath = path.join(debugDir, '2_clean_conversation.json');
    fs.writeFileSync(cleanPath, JSON.stringify(cleanConversation, null, 2));
    console.log(`[DEBUG] Saved clean conversation: ${cleanPath} (${cleanConversation.length} turns)`);
  } catch (error) {
    console.error(`[DEBUG] Failed to save clean conversation:`, error);
  }

  // 🔍 DEBUG: Save simplified conversation that goes to database
  try {
    const simplifiedPath = path.join(debugDir, '3_simplified_conversation.json');
    fs.writeFileSync(simplifiedPath, JSON.stringify(cleanSimplified, null, 2));
    console.log(`[DEBUG] Saved simplified conversation: ${simplifiedPath} (${cleanSimplified.length} turns)`);
  } catch (error) {
    console.error(`[DEBUG] Failed to save simplified conversation:`, error);
  }

  saveConversation(callSid, cleanSimplified);
  
  // Save full version with metadata to disk (for debugging and analysis)  
  const fullConversation = getFullConversationHistory(callSid);
  saveConversationWithMetadata(callSid, fullConversation);
  
  // 🔍 DEBUG: Save full conversation with metadata
  try {
    const fullPath = path.join(debugDir, '4_full_conversation.json');
    fs.writeFileSync(fullPath, JSON.stringify(fullConversation, null, 2));
    console.log(`[DEBUG] Saved full conversation: ${fullPath} (${fullConversation.length} turns)`);
  } catch (error) {
    console.error(`[DEBUG] Failed to save full conversation:`, error);
  }
  
  // Schedule batched database save (debounced to avoid excessive DB writes)
  // Only schedule if the call hasn't been cleaned up yet
  if (calls[callSid]) {
    scheduleBatchedDatabaseSave(callSid);
  } else {
    console.log(`[DEBUG] Call ${callSid} already cleaned up, skipping database save scheduling`);
  }
}

/**
 * Schedule a batched database save with debouncing
 */
function scheduleBatchedDatabaseSave(callSid: string) {
  // Clear any existing timer for this call
  if (databaseSaveTimers[callSid]) {
    clearTimeout(databaseSaveTimers[callSid]);
  }
  
  // Schedule new save in 30 seconds
  databaseSaveTimers[callSid] = setTimeout(() => {
    // Double-check that the call hasn't been cleaned up before saving
    if (calls[callSid]) {
      saveToDatabaseNow(callSid);
    } else {
      console.log(`[BATCH-SAVE] Call ${callSid} was cleaned up before batch save, skipping`);
    }
    delete databaseSaveTimers[callSid];
  }, BATCH_SAVE_INTERVAL);
}

/**
 * Force immediate database save and cleanup (used when call ends)
 */
export async function saveConversationFinalAndCleanup(callSid: string) {
  console.log(`[FINAL-SAVE] Starting final save and cleanup for ${callSid}`);
  
  // Cancel any pending batch save
  if (databaseSaveTimers[callSid]) {
    clearTimeout(databaseSaveTimers[callSid]);
    delete databaseSaveTimers[callSid];
  }
  
  // Final database save
  await saveToDatabaseNow(callSid);
  
  // Cleanup files after successful database save
  await cleanupCallFiles(callSid);
}

/**
 * Perform immediate database save
 */
async function saveToDatabaseNow(callSid: string) {
  try {
    const cleanConversation = getCleanConversationHistory(callSid);
    const cleanSimplified = cleanConversation.map(turn => ({ speaker: turn.speaker, text: turn.text }));
    await saveConversationToDb(callSid, cleanSimplified);
    console.log(`[BATCH-SAVE] Saved conversation to database for ${callSid}`);
  } catch (error) {
    console.error(`[BATCH-SAVE] Failed to save conversation to database for ${callSid}:`, error);
  }
}

/**
 * Cleanup conversation JSON files and audio files after final database save
 */
async function cleanupCallFiles(callSid: string) {
  const cleanupResults = {
    conversationFiles: 0,
    audioFiles: 0,
    errors: [] as string[]
  };
  
  try {
    // Clean up conversation JSON files
    const conversationFiles = [
      path.join(CONVERSATIONS_DIR, `${callSid}.json`),
      path.join(CONVERSATIONS_DIR, `${callSid}_full.json`)
    ];
    
    for (const file of conversationFiles) {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
        cleanupResults.conversationFiles++;
        console.log(`[CLEANUP] Deleted conversation file: ${file}`);
      }
    }
    
    // Clean up audio files for this call
    const audioDir = path.join(__dirname, '../audio_responses');
    if (fs.existsSync(audioDir)) {
      const audioFiles = fs.readdirSync(audioDir).filter(file => file.startsWith(callSid));
      
      for (const file of audioFiles) {
        const filePath = path.join(audioDir, file);
        fs.unlinkSync(filePath);
        cleanupResults.audioFiles++;
        console.log(`[CLEANUP] Deleted audio file: ${filePath}`);
      }
    }
    
    console.log(`[CLEANUP] Completed for ${callSid}: ${cleanupResults.conversationFiles} conversation files, ${cleanupResults.audioFiles} audio files deleted`);
    
  } catch (error) {
    const errorMsg = `Failed to cleanup files for ${callSid}: ${error}`;
    cleanupResults.errors.push(errorMsg);
    console.error(`[CLEANUP] ${errorMsg}`);
  }
  
  return cleanupResults;
}

/**
 * Immediate file cleanup that doesn't wait for uploads
 * Only removes files that aren't needed for background uploads
 */
async function cleanupCallFilesImmediately(callSid: string) {
  const cleanupResults = {
    conversationFiles: 0,
    tempFiles: 0,
    errors: [] as string[]
  };
  
  try {
    // Clean up conversation JSON files (safe to delete, data is already in memory for background upload)
    const conversationFiles = [
      path.join(CONVERSATIONS_DIR, `${callSid}.json`),
      path.join(CONVERSATIONS_DIR, `${callSid}_full.json`)
    ];
    
    for (const file of conversationFiles) {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
        cleanupResults.conversationFiles++;
        console.log(`[IMMEDIATE-CLEANUP] Deleted conversation file: ${file}`);
      }
    }
    
    // Clean up temporary files (safe to delete)
    const tempDir = path.join(__dirname, '../temp');
    if (fs.existsSync(tempDir)) {
      const tempFiles = fs.readdirSync(tempDir).filter(file => file.includes(callSid));
      
      for (const file of tempFiles) {
        const filePath = path.join(tempDir, file);
        fs.unlinkSync(filePath);
        cleanupResults.tempFiles++;
        console.log(`[IMMEDIATE-CLEANUP] Deleted temp file: ${filePath}`);
      }
    }
    
    // NOTE: Audio files are NOT deleted here - they're needed for background uploads
    // The background upload service will delete them after successful upload
    
    console.log(`[IMMEDIATE-CLEANUP] Completed for ${callSid}: ${cleanupResults.conversationFiles} conversation files, ${cleanupResults.tempFiles} temp files deleted`);
    console.log(`[IMMEDIATE-CLEANUP] Audio files preserved for background upload processing`);
    
  } catch (error) {
    const errorMsg = `Failed to cleanup files immediately for ${callSid}: ${error}`;
    cleanupResults.errors.push(errorMsg);
    console.error(`[IMMEDIATE-CLEANUP] ${errorMsg}`);
  }
  
  return cleanupResults;
}

// Function to load conversation from disk
function loadConversation(callSid: string): Array<{ speaker: string; text: string }> {
  const filename = path.join(CONVERSATIONS_DIR, `${callSid}.json`);
  try {
    if (fs.existsSync(filename)) {
      const data = fs.readFileSync(filename, 'utf8');
      return JSON.parse(data);
    }
  } catch (err) {
    console.error(`Error loading conversation for call ${callSid}:`, err);
  }
  return [];
}

// Function to finalize a user's turn after receiving a transcript
export async function finalizeTurn(callSid: string, finalizationReason: string = "silence") { // EXPORTED
  const state = calls[callSid];
  
  // Check if we have a valid state
  if (!state) {
    console.error(`[FINALIZE-ERROR] Cannot finalize turn for missing call ${callSid}`);
    return;
  }
  
  // Enhanced race condition protection
  if (state.finalizationInProgress) {
    console.log(`[FINALIZE-RACE] Turn finalization already in progress for call ${callSid}, skipping duplicate attempt. Reason: ${finalizationReason}`);
    return;
  }
  
  // SINGLE SOURCE OF TRUTH: Get transcript from XState infrastructure
  const { getTranscriptState } = require('./infrastructureHelpers');
  const transcriptState = getTranscriptState(callSid);
  const text = transcriptState?.currentText?.trim() || '';
  
  // Lock finalization to prevent race conditions
  state.finalizationInProgress = true;
  // Reduced logging: only log when there's actual text to process
  if (text) {
    console.log(`[TURN-FINALIZE] Call ${callSid} - Processing turn with ${state.speechFramesInCurrentTurn} speech frames, ${state.consecutiveSilenceFrames} silence frames`);
    console.log(`[TURN-FINALIZE] Transcript from XState infrastructure: "${text}"`);
  }
  if (text) {
    // CRITICAL FIX: Track that we've processed this turn number using Turn Authority
    const currentTurnNumber = await getCallTurnIndex(callSid, calls);
    state.lastProcessedTurnNumber = currentTurnNumber;
    console.log(`[FINALIZE-TURN] Processing turn ${currentTurnNumber} with text: "${text}"`);
    
    // Get current XState machine state
    const currentStateInfo = getCurrentState(callSid);
    const currentXStateState = currentStateInfo?.state || 'unknown';
    
    // REMOVED: Conversation management moved to XState as single source of truth
    // XState turnBasedGracePeriodHandler now handles all conversation updates
    
    // Sync server state from XState and save enhanced conversation
    syncConversationFromXState(callSid);
    saveEnhancedConversation(callSid);
    
    // Also update the XState machine with this utterance directly with timestamp
    const utteranceTimestamp = Date.now();
    sendUserUtterance(callSid, text, true, utteranceTimestamp); // Pass true for isFinal with timestamp
    
    // Send activity detection event to XState for inactivity tracking
    const controller = callControllers[callSid];
    if (controller?.machine) {
      controller.machine.send({
        type: 'ACTIVITY_DETECTED',
        source: 'transcription',
        callSid: callSid,
        timestamp: utteranceTimestamp
      });
    }
    
    // TURN-BASED GRACE F9: Add turn lifecycle integration - STT finalized
    try {
      const { turnCoordinationService } = await import('./services/TurnCoordinationService');
      turnCoordinationService.updateTurnState(callSid, {
        stage: require('./types').TurnLifecycleStage.STT_FINALIZED,
        timestamp: utteranceTimestamp,
        userText: text
      });
    } catch (error) {
      console.error(`[TURN-LIFECYCLE] ${callSid}: Error updating turn state after STT finalization:`, error);
    }
    
    // Log grace period diagnostics
    logGracePeriodDiagnostics(callSid);
    
    // Check the current state to decide what to do with transcript
    const currentState = getCurrentState(callSid);
    console.log(`[RESPONSE-DEBUG] Current state after user utterance: ${currentState?.state || 'unknown'}`);
    
    // Log the final counts of speech/silence frames for this turn
    if (state.speechFrameCounter) {
    }
    
    // SINGLE SOURCE OF TRUTH: XState infrastructure handles transcript state
    // Legacy state.currentTurnText is no longer used
    // Grace period functionality is handled by XState context
    state.speechFramesInCurrentTurn = 0; // Reset for handleTranscript logic
    // NOTE: hasSpeechDetected now resets at clean turn boundaries (TTS_FINISHED), not after STT processing
    state.speechFrameCounter = 0;      // Reset speech frame counter
    state.silenceFrameCounter = 0;     // Reset silence frame counter
    state.transcriptSentForCurrentTurn = false; // Reset transcript sent flag
    state.longSignalSentForCurrentTurn = false; // Reset long signal sent flag
    state.vadDecisionLogged = false;   // Reset VAD decision logging flag
    
    // CENTRALIZATION FIX: Turn increments now handled by XState via COORDINATED_TURN_COMPLETE events
    // The turn completion flow should be triggered through the turn coordination system
    // REMOVED: await incrementCallTurnIndex(callSid, calls, 'manual_increment', 'Turn finalization completed');
    console.log(`[FINALIZE-TURN] Turn finalization completed for ${callSid}`);
  }
  
  state.finalizationInProgress = false; // Release lock
  // Removed excessive lock logging
  
  // Check for special state handling (removed verbose logging)
  try {
    const controller = callControllers[callSid];
    if (controller?.machine) {
      const snapshot = controller.machine.getSnapshot();
      const context = snapshot.context as any;
      // Format the active states properly
      let active;
      try {
        if (typeof snapshot.value === 'string') {
          active = snapshot.value;
        } else {
          active = JSON.stringify(snapshot.value);
        }
      } catch (e) {
        active = 'unknown';
      }
      
      // If we're in history_taking but no response is set, force one using FORCE_RESPONSE event
      if (active && active.includes('history_taking') && !context.responseText) {
        console.log(`[STATE-INFO] In history_taking without response, sending FORCE_RESPONSE event`);  
        // Use the controller directly to send the event
        const controller = callControllers[callSid];
        if (controller && controller.machine) {
          controller.machine.send(createForceResponseEvent("I'm afraid I didn't understand that. Could you please rephrase your question?"));
        }
      }
    }
  } catch (err) {
    console.error(`[STATE-INFO] Error getting state info:`, err);
  }
}

// Database lookup functions
async function findUserIdByPhoneNumber(phoneNumber: string): Promise<string | null> {
  // Format the phone number: Remove leading '+' if present
  const formattedPhoneNumber = phoneNumber.startsWith('+') ? phoneNumber.slice(1) : phoneNumber;

  console.log(`[DB-RPC] Looking up user ID for formatted phone: ${formattedPhoneNumber} (Original: ${phoneNumber})`);
  try {
    // Call the PostgreSQL function with the formatted number
    const { data, error } = await supabase.rpc('get_user_id_by_phone', {
      p_phone_number: formattedPhoneNumber
    });

    if (error) {
      console.error(`[DB-RPC] Error calling get_user_id_by_phone for ${formattedPhoneNumber}:`, error);
      return null;
    }

    if (data) {
      console.log(`[DB-RPC] ✅ Found user ID: ${data} for phone ${formattedPhoneNumber}`);
    } else {
      console.log(`[DB-RPC] ❌ No user found for phone ${formattedPhoneNumber}`);
    }

    return data; // data will be the UUID or null
  } catch (error) {
    console.error(`[DB-RPC] Exception during RPC call for ${formattedPhoneNumber}:`, error);
    return null;
  }
}

// We use the updateCallRecordingPath function defined earlier in the file

//-----------------------------------------------
// 1) Twilio /voice -> Return <Start><Stream>
//-----------------------------------------------
app.post('/voice', async (req, res) => { // <-- Make this function async
  const twiml = new VoiceResponse();
  const callSid = req.body.CallSid;
  const fromNumber = req.body.From || null;
  const toNumber = req.body.To || null;
  
  console.log(`Incoming call SID: ${callSid}, From: ${fromNumber}, To: ${toNumber}`);

  // Store details temporarily, associated with CallSid
  if (callSid) {
    // Look up user ID based on From number
    let userId: string | null = null;
    if (fromNumber) {
        try {
            userId = await findUserIdByPhoneNumber(fromNumber);
            if (userId) {
                console.log(`Found user ID ${userId} for phone number ${fromNumber}`);
            } else {
                console.log(`No user found for phone number ${fromNumber}`);
            }
        } catch (lookupError) {
            console.error(`Error looking up user ID for phone ${fromNumber}:`, lookupError);
        }
    }

    pendingCallDetails[callSid] = { from: fromNumber, to: toNumber, userId: userId }; // Store userId too
    console.log(`Stored initial details for call ${callSid} (userId: ${userId || 'N/A'})`);

    // Set a timeout to clean up pending details if WS never connects (e.g., 60 seconds)
    setTimeout(() => {
      if (pendingCallDetails[callSid]) {
        console.warn(`Cleaning up stale pending details for call ${callSid} (WS likely never connected)`);
        delete pendingCallDetails[callSid];
      }
    }, 60000); 
  }

  // First play a greeting
  //twiml.say({
  //voice: 'alice'
  //}, 'Hi!');
  
  // Then set up the bidirectional stream with <Connect><Stream>
  // per https://www.twilio.com/docs/voice/twiml/stream
  const connect = twiml.connect();
  connect.stream({
    url: `wss://${req.headers.host}/media`
    // No 'track' parameter - for bidirectional streams, only inbound_track is supported
  });
  
  console.log('Responding with TwiML:', twiml.toString());
  res.type('text/xml').send(twiml.toString());
});

//-----------------------------------------------
// 2) WebSocket for /media
//    Twilio => Node => ffmpeg => local WAV + STT (AAI or Azure)
//    Also do VAD checks => turn detection
//-----------------------------------------------

// Create both HTTP and HTTPS servers for dev/prod WebSocket support
const server = http.createServer(app);

// HTTPS server for production (WSS) - only create if certificates are available
let httpsServer: https.Server | null = null;
try {
  // Check if SSL certificates exist for production
  if (fs.existsSync('/etc/ssl/certs/server.crt') && fs.existsSync('/etc/ssl/private/server.key')) {
    const httpsOptions = {
      cert: fs.readFileSync('/etc/ssl/certs/server.crt'),
      key: fs.readFileSync('/etc/ssl/private/server.key')
    };
    httpsServer = https.createServer(httpsOptions, app);
    console.log('[HTTPS] HTTPS server created for WSS connections');
  } else {
    console.log('[HTTPS] SSL certificates not found, WSS connections will not be available');
  }
} catch (error) {
  console.log('[HTTPS] Could not create HTTPS server:', error.message);
}

// Create WebSocket servers
export const wss = new WebSocketServer({
  noServer: true // We will handle upgrades manually
});

const directWss = new WebSocketServer({
  noServer: true // We will handle upgrades manually
});

// WebSocket upgrade handler function
function handleWebSocketUpgrade(request: http.IncomingMessage, socket: any, head: Buffer) {
  const pathname = request.url ? url.parse(request.url).pathname : undefined;
  const protocol = (request.connection as any).encrypted ? 'WSS' : 'WS';

  console.log(`[UPGRADE] Incoming ${protocol} upgrade request for path: ${pathname}`);

  if (pathname === '/media') {
    wss.handleUpgrade(request, socket, head, (ws) => {
      console.log(`[UPGRADE] Handing off to /media WebSocket server (${protocol}).`);
      wss.emit('connection', ws, request);
    });
  } else if (pathname === '/direct-call') {
    directWss.handleUpgrade(request, socket, head, (ws) => {
      console.log(`[UPGRADE] Handing off to /direct-call WebSocket server (${protocol}).`);
      directWss.emit('connection', ws, request);
    });
  // } else if (pathname === '/webrtc-signaling') {
  //   webrtcWss.handleUpgrade(request, socket, head, (ws) => {
  //     console.log(`[UPGRADE] Handing off to /webrtc-signaling WebSocket server (${protocol}).`);
  //     webrtcWss.emit('connection', ws, request);
  //   });
  } else if (pathname?.startsWith('/api/livekit/egress-ws/')) {
    egressWss.handleUpgrade(request, socket, head, (ws) => {
      console.log(`[UPGRADE] Handing off to LiveKit egress WebSocket server (${protocol}).`);
      egressWss.emit('connection', ws, request);
    });
  } else {
    console.log(`[UPGRADE] No handler for path ${pathname}. Destroying socket.`);
    socket.destroy();
  }
}

// Apply upgrade handler to both HTTP and HTTPS servers
server.on('upgrade', handleWebSocketUpgrade);
if (httpsServer) {
  httpsServer.on('upgrade', handleWebSocketUpgrade);
}
// const wssDirect = new WebSocketServer({
//   server,
//   path: '/direct-call'
// });

// // Log when the Direct WebSocket server is created
// console.log('Direct WebSocket server created and listening on path: /direct-call');

// const directWebSocketHandler = new DirectWebSocketHandler(wssDirect);

// Log when the WebSocket server is created
console.log('WebSocket server created and listening on path: /media');

// Add connection attempt logging
wss.on('headers', (headers, req) => {
  console.log('WebSocket connection headers received:', headers.join(', '));
  console.log('Connection attempt from:', req.socket.remoteAddress);
});

wss.on('connection', (ws, request) => { // Added 'request' parameter
  console.log('[WSS_ON_CONNECTION] New WebSocket connection established.');
  console.log('[WSS_ON_CONNECTION] Request URL:', request.url);
  console.log('[WSS_ON_CONNECTION] Request Headers:', JSON.stringify(request.headers, null, 2));
  console.log('Twilio connected to /media WebSocket at:', new Date().toISOString());

  let callSid: string | null = null

  // Add callSid property to the WebSocket object for tracking
  Object.defineProperty(ws, 'callSid', {
    get: function() { return callSid; },
    set: function(value) { callSid = value; }
  });

  // Log WebSocket close events
  ws.on('close', (code, reason) => {
    console.log(`[TWILIO-WS:${callSid || 'unknown'}] WebSocket closed with code ${code}, reason: ${reason || 'none'}`);
    
    // RACE CONDITION FIX: Route through XState instead of direct cleanup
    if (callSid) {
      console.log(`[TWILIO-WS:${callSid}] 🔄 Routing cleanup through XState to prevent race conditions`);
      
      // Send call-ended notification through other WebSocket connections (like direct WebSocket) before cleanup
      try {
        const { sendWebSocketMessage } = require('./websocketManager');
        const callEndedMessage = JSON.stringify({
          event: 'call-ended',
          callSid: callSid,
          reason: 'participant_disconnected',
          disconnectCode: code,
          timestamp: new Date().toISOString()
        });
        
        sendWebSocketMessage(callSid, callEndedMessage)
          .then((sent) => {
            if (sent) {
              console.log(`[TWILIO-WS:${callSid}] ✅ Call-ended notification sent through alternate WebSocket connection`);
            } else {
              console.log(`[TWILIO-WS:${callSid}] No alternate WebSocket connection available for call-ended notification`);
            }
          })
          .catch((error) => {
            console.error(`[TWILIO-WS:${callSid}] Error sending call-ended notification through alternate connection:`, error);
          });
      } catch (notificationError) {
        console.error(`[TWILIO-WS:${callSid}] Error preparing call-ended notification:`, notificationError);
      }
      
      // Check if XState controller exists and send PARTICIPANT_DISCONNECTED event
      try {
        const { callControllers } = require('./xstateIntegration');
        const controller = callControllers[callSid];
        
        if (controller?.machine) {
          console.log(`[TWILIO-WS:${callSid}] ✅ Sending PARTICIPANT_DISCONNECTED to XState machine`);
          controller.machine.send({
            type: 'PARTICIPANT_DISCONNECTED',
            callSid: callSid,
            source: 'twilio_websocket',
            disconnectCode: code,
            reason: reason?.toString() || 'websocket_close'
          });
        } else {
          console.warn(`[TWILIO-WS:${callSid}] ⚠️ No XState controller found - falling back to direct cleanup`);
          cleanupCall(callSid);
        }
      } catch (error) {
        console.error(`[TWILIO-WS:${callSid}] ❌ Error routing through XState, falling back to direct cleanup:`, error);
        cleanupCall(callSid);
      }
    }
  });

  // Log WebSocket errors
  ws.on('error', (error) => {
    console.error(`WebSocket error for call ${callSid || 'unknown'}:`, error);
  });

  // Single message handler that combines monitoring and main logic
  ws.on('message', async (rawMessage) => {
    try {
      // Parse the message once
      const message = rawMessage.toString();
      const data = safeJSONParse(message);
    
      if (data.event === 'error') return; // Invalid JSON
      
      // Debug: Log only important non-media events to reduce verbosity
      if (data.event !== 'media' && ['connected', 'start', 'stop', 'error'].includes(data.event)) {
        console.log(`[DEBUG] Important event:`, JSON.stringify(data, null, 2));
      }
      
      // Enhanced monitoring for specific events
      if (data.event === 'mark') {
        console.log(`[WS-MONITOR] Received mark event: ${JSON.stringify(data)}`);
      } else if (data.event === 'clear') {
        console.log(`[WS-MONITOR] Received clear event: ${JSON.stringify(data)}`);
      } else if (data.event === 'error') {
        console.log(`[WS-MONITOR] Received error event: ${JSON.stringify(data)}`);
      }
    
    // Special debug logging for speech events
    if (data.type && data.type.includes('speech')) {
      console.log('SPEECH EVENT DETECTED:', data.type);
    }
    
    // Only log non-media events to reduce console spam
    if (data.event !== 'media') {
      console.log(`Received WS message event: ${data.event}`);
      
      // Add more detailed logging to debug the data structure
      if (data.event === 'start') {
        console.log('Start event data:', JSON.stringify(data, null, 2));
      }
    } else {
      // For media events, only log occasionally to show activity
      if (callSid) {
        if (!mediaPacketCounts[callSid]) {
          mediaPacketCounts[callSid] = 0;
        }
        
        mediaPacketCounts[callSid]++;
        
        // Skip logging completely by commenting this out
        /*
        if (LOGGING.MEDIA_PACKETS && mediaPacketCounts[callSid] % MEDIA_LOG_INTERVAL === 0) {
          console.log(`Received ${MEDIA_LOG_INTERVAL} media packets for call ${callSid} (total: ${mediaPacketCounts[callSid]})`);
        }
        */
      }
    }

    switch (data.event) {
      case 'connected':
          console.log('Twilio event: connected');
          break;

      case 'start':
          // Use the safer callSid extraction method
          console.log('[START_EVENT_HANDLER] Processing start event. Data:', JSON.stringify(data, null, 2));
          const extractedCallSid = getCallData(data);
          console.log('[START_EVENT_HANDLER] Extracted callSid from getCallData:', extractedCallSid);
          if (!extractedCallSid) {
            console.error('Could not extract callSid from start event:', data);
            return;
          }
          
          callSid = extractedCallSid;
          // Store callSid with the WebSocket object
          (ws as any).callSid = callSid;
          console.log('[START_EVENT_HANDLER] Assigned callSid to WebSocket object. ws.callSid is now:', (ws as any).callSid);
          console.log('Media start event for callSid:', callSid);

          // Register WebSocket connection with WebSocket manager for call-ended notifications
          const { registerWebSocketConnection } = require('./websocketManager');
          registerWebSocketConnection(callSid, ws);
          console.log(`[WSManager] Registered WebSocket connection for callSid: ${callSid}`);

          // *** GUARD AGAINST RE-INITIALIZATION (MOVED TO TOP) ***
          if (calls[callSid]?.isInitialized) {
            console.warn(`[SERVER:${callSid}] !!! WARNING: Received duplicate 'start' event. Skipping re-initialization.`);
            break; // Exit the switch case for this message
          }
          // **************************************

          // Extract streamSid if available
          let streamSid = data.streamSid || callSid; // Use streamSid if present, fallback to callSid
          console.log(`Using streamSid: ${streamSid}`);
          console.log(`[Server.ts] callSid: ${callSid}`);
          console.log(`[Server.ts] pendingCallDetails content:`, JSON.stringify(pendingCallDetails, null, 2));
          console.log(`[Server.ts] pendingCallDetails[${callSid}]:`, JSON.stringify(pendingCallDetails[callSid], null, 2));

          // --- BEGIN SESSION MANAGEMENT AND CALL HISTORY ---
          const callStartTime = new Date();
          // Retrieve stored From/To numbers and userId
          const initialDetails = pendingCallDetails[callSid] || { from: null, to: null, userId: null };
          const fromNumberFromWebhook = initialDetails.from;
          const toNumberFromWebhook = initialDetails.to;
          const userIdFromWebhook = initialDetails.userId; // Get the userId

          // Session Management: Create or resume session
          let sessionResult: CreateSessionResult | null = null;
          if (userIdFromWebhook && fromNumberFromWebhook) {
            try {
              sessionResult = await SessionManager.createOrResumeSession(userIdFromWebhook, fromNumberFromWebhook);
              if (sessionResult) {
                console.log(`[SessionManager] ${sessionResult.isNewSession ? 'Created new' : 'Resumed'} session ${sessionResult.sessionId} for user ${userIdFromWebhook}`);
                if (!sessionResult.isNewSession && sessionResult.previousPhase) {
                  console.log(`[SessionManager] Resuming from phase: ${sessionResult.previousPhase}`);
                }
              } else {
                console.error(`[SessionManager] Failed to create/resume session for user ${userIdFromWebhook}`);
              }
            } catch (error) {
              console.error(`[SessionManager] Exception during session creation/resumption:`, error);
            }
          } else {
            console.warn(`[SessionManager] Missing userId (${userIdFromWebhook}) or phoneNumber (${fromNumberFromWebhook}) - cannot create session`);
          }

          // Insert call record with session ID
          const sessionId = sessionResult?.sessionId || null;
          insertInitialCallRecord(callSid, callStartTime, fromNumberFromWebhook, toNumberFromWebhook, userIdFromWebhook, sessionId).catch(err => {
              console.error(`[DB] Failed initial insert for call ${callSid}:`, err);
          });
          
          // Link call to session if we have both
          if (sessionId && userIdFromWebhook) {
            SessionManager.linkCallToSession(callSid, sessionId, userIdFromWebhook).catch(err => {
              console.error(`[SessionManager] Failed to link call ${callSid} to session ${sessionId}:`, err);
            });
          }

          // Clean up temporary storage now that WS has started
          // pendingCallDetails cleanup is now handled by contextUtils.restoreContext
          // if (pendingCallDetails[callSid]) {
          //   delete pendingCallDetails[callSid];
          //   console.log(`Cleaned up pending details for call ${callSid}`);
          // }
          // --- END SESSION MANAGEMENT AND CALL HISTORY ---

          // Check for existing conversation history (from disk)
          const existingConversation = loadConversation(callSid);

        // Initialize audio processing state
        const initialAudioState = createAudioProcessingState();
        initialAudioState.streamSid = streamSid;
        
        // Initialize state tracking to avoid duplicate logs
        const stateTracker = { lastState: null };
        
        /**
         * Helper function to handle TTS playback triggered by the state machine's speakResponse action
         * This avoids the race condition by being triggered directly by state machine actions
         */
        async function speakTTSForMachine(context: any, callSid: string) {
          const responseText = context.responseText;

          if (!callSid || !responseText) {
            console.log(`[speakTTSForMachine] Missing callSid (${callSid}) or responseText (${responseText ? 'present' : 'missing'}), skipping TTS.`);
            return;
          }

          const currentCallState = calls[callSid];
          if (!currentCallState) {
            console.log(`[speakTTSForMachine:${callSid}] Call state not found, skipping TTS.`);
            return;
          }

          if (currentCallState.isModelSpeaking) {
            console.log(`[speakTTSForMachine:${callSid}] Model already speaking, skipping TTS for: "${responseText.substring(0,30)}..."`);
            return; // Avoid overlapping TTS triggers
          }

          console.log(`[speakTTSForMachine:${callSid}] Triggering TTS for: "${responseText.substring(0, 50)}..."`);
          
          // Mark that model is processing/speaking
          currentCallState.isModelSpeaking = true;
          
          // Get current XState machine state
          const currentStateInfo = getCurrentState(callSid);
          const currentXStateState = currentStateInfo?.state || 'unknown';
          
          // Update conversation history with interruption metadata
          // REMOVED: Conversation management moved to XState as single source of truth
          // XState LLM response handler now manages all assistant conversation entries
          
          // Sync server state from XState and save enhanced conversation
          syncConversationFromXState(callSid);
          saveEnhancedConversation(callSid);
          
          try {
            // Generate audio using Turn Authority integration
            const currentTurnIndex = await getCallTurnIndex(callSid, calls);
            const audioPath = await generateAudioResponse(responseText, callSid, currentTurnIndex);
            
            if (audioPath) {
              currentCallState.audioResponsePath = audioPath;
              
              // For Azure TTS (default), we need to stream the generated file
              // ElevenLabs providers handle streaming internally
              if (TTS_PROVIDER !== 'elevenlabs' && TTS_PROVIDER !== 'elevenlabs_stream') {
                // Route TTS based on connection type to prevent duplication
                if (currentCallState.livekitRoomName) {
                  console.log(`[TTS-ROUTING-VALIDATION] LiveKit path selected - connectionType: ${currentCallState.connectionType}, room: ${currentCallState.livekitRoomName}`);
                  await sendTTSToLiveKit(callSid, audioPath, currentTurnIndex);
                } else {
                  console.log(`[TTS-ROUTING-VALIDATION] Twilio path selected - connectionType: ${currentCallState.connectionType}, file: ${audioPath}`);
                  await streamAudioToCall(audioPath, callSid, context.turnNumber);
                }
              } else {
                console.log(`[TTS-SUCCESS] ElevenLabs audio generated and streamed internally: ${audioPath}`);
              }
              
              // CENTRALIZATION FIX: Turn increments now handled by XState TTS_FINISHED event below
              // REMOVED: await incrementCallTurnIndex(callSid, calls, 'tts_finished', 'TTS playback completed');

              // Send TTS_FINISHED event to the state machine
              const controller = callControllers[callSid]; 
              if (controller?.machine) {
                controller.machine.send({ type: 'TTS_FINISHED' });
                console.log(`[speakTTSForMachine:${callSid}] Sent TTS_FINISHED event to machine ID: ${controller.machine.id}.`);
              } else {
                console.warn(`[speakTTSForMachine:${callSid}] TTS finished, but machine controller not found.`);
              }
            } else {
              console.error(`[speakTTSForMachine:${callSid}] Failed to generate audio`);
            }
          } catch (error) {
            console.error(`[speakTTSForMachine:${callSid}] Error during TTS playback:`, error);
          } finally {
            // Ensure isModelSpeaking is set to false even if TTS errors
            if (calls[callSid]) {
              calls[callSid].isModelSpeaking = false;
            }
          }
        }

        // Initialize XState machine controller for this call
        // Create a simpler transition handler that only logs state changes
        // and handles TTS when responseText is present
        
        // Track the last known state to avoid redundant logging (using closure)
        let lastKnownState = null;
        
        // Get userId from pendingCallDetails
        const wsCallDetails = pendingCallDetails[callSid] || { from: null, to: null, userId: null };
        const { userId } = wsCallDetails;

        // pendingCallDetails entry is now cleaned up within contextUtils.restoreContext after use.

        const controller = initializeCallStateMachine(
          callSid,
          initialAudioState,
          userId,
          sessionResult?.sessionId || null, // Pass session ID
          pendingCallDetails, // Pass pendingCallDetails here
          async (state) => {
            // Log state transitions only when they actually change
            if (state.context?.currentState && state.context.currentState !== lastKnownState) {
              if (LOGGING.STATE_TRANSITIONS) {
                console.log(`[XState:${callSid}] State transition: ${state.context.currentState}`);
                // DEBUG: Log responseText right after transition
                console.log(`[XState:${callSid}] DEBUG - responseText after transition: '${state.context.responseText || "none"}'`);
              }
              lastKnownState = state.context.currentState;
            }
            
            // Store the current response text for comparison to detect changes
            const currentResponseText = state.context?.responseText;
            
            // Check for responseText, avoid processing if model is already speaking,
            // and don't reprocess the same response text
            if (currentResponseText && 
                calls[callSid] && !calls[callSid].isModelSpeaking && 
                currentResponseText !== (calls[callSid]?.lastProcessedResponse || '')) {
                
              // TTS State Locking - Prevent race conditions
              // BUT allow new TTS if this is a different message (different responseText)
              if (calls[callSid].activeTtsOperation) {
                const lastProcessed = calls[callSid].lastProcessedResponse || '';
                if (currentResponseText === lastProcessed) {
                  console.log(`[TTS-RACE-PROTECTION:${callSid}] TTS operation already active for same message, skipping duplicate: "${currentResponseText.substring(0, 30)}..."`);
                return;
                } else {
                  console.log(`[TTS-RACE-OVERRIDE:${callSid}] New message detected, releasing previous TTS lock. Old: "${lastProcessed.substring(0, 30)}..." New: "${currentResponseText.substring(0, 30)}..."`);
                  calls[callSid].activeTtsOperation = false; // Release lock for new message
                }
              }
              
              // Lock TTS operation
              calls[callSid].activeTtsOperation = true;
              console.log(`[TTS-LOCK:${callSid}] Acquired TTS lock for: "${currentResponseText.substring(0, 30)}..."`);
                
              // Save this response to prevent reprocessing the same text
              calls[callSid].lastProcessedResponse = currentResponseText;
              
              // Handle TTS playback
              if (LOGGING.TTS_EVENTS) {
                console.log(`[XState:${callSid}] Processing new responseText from state machine`);
              }
              
              // Check if this response came from streaming (using context flag)
              const currentState = controller.machine.getSnapshot();
              const wasStreamed = currentState.context.wasStreamed;
              
              if (wasStreamed) {
                console.log(`[STREAMING-SKIP] Response was streamed - TTS already handled for ${callSid}`);
                
                // Note: Conversation is now maintained in XState context only - no server duplication
                
                // CENTRALIZATION FIX: Turn increments now handled by XState TTS_FINISHED event below
                // REMOVED: await incrementCallTurnIndex(callSid, calls, 'streaming_complete', 'Response was streamed - TTS already handled');
                
                // Send TTS_FINISHED event immediately since no TTS processing is needed
                const eventId = Math.random().toString(36).substring(2, 9);
                console.log(`[XState:${callSid}] Sending immediate TTS_FINISHED event (id: ${eventId}) for streaming completion`);
                controller.machine.send({ type: 'TTS_FINISHED' });
                console.log(`[XState:${callSid}] Sent immediate TTS_FINISHED event (id: ${eventId}) - no TTS processing needed`);
                
                // Release TTS lock
                calls[callSid].activeTtsOperation = false;
                console.log(`[TTS-UNLOCK:${callSid}] Released TTS lock for streaming completion`);
                
                // Clear the wasStreamed flag for next response
                controller.machine.send({ type: 'CLEAR_STREAMING_FLAG' });
                
                return; // Exit early - no TTS processing needed
              }
              
              // Log the response we're about to speak
              if (LOGGING.TTS_VERBOSE) {
                console.log(`[XState:${callSid}] Speaking response: "${currentResponseText.substring(0, 50)}..."`);
              }
              
              // Mark that model is speaking
              calls[callSid].isModelSpeaking = true;
              
              // Note: Conversation is now maintained in XState context only - no server duplication
              
              // Disabled TTS deduplication - users can legitimately repeat phrases
              // const { checkForDuplicate } = await import('./responseDeduplication');
              console.log(`[TTS-PROCESSING] Processing TTS for ${callSid}: "${currentResponseText.substring(0, 50)}..."`);
              
              try {
                // Check if call was interrupted before generating TTS using Turn Authority integration
                const currentTurn = await getCallTurnIndex(callSid, calls);
                const turnTiming = calls[callSid].turnTimings?.find(t => t.turnIndex === currentTurn);
                const wasInterrupted = turnTiming?.interruptionTimestamp !== undefined;
                
                if (wasInterrupted) {
                  console.log(`[TTS-SKIP] Skipping TTS generation for ${callSid} turn ${currentTurn} - call was interrupted at ${turnTiming?.interruptionTimestamp}`);
                  calls[callSid].activeTtsOperation = false;
                  console.log(`[TTS-UNLOCK:${callSid}] Released TTS lock due to interruption`);
                  return;
                }
                
                // Store the turn index before any processing to maintain consistency using Turn Authority
                const ttsGenerationTurnIndex = await getCallTurnIndex(callSid, calls);
                console.log(`[TTS-TURN-CONSISTENCY] Using turn index ${ttsGenerationTurnIndex} for TTS generation and streaming`);
                
                // Generate audio response
                const audioPath = await generateAudioResponse(currentResponseText, callSid, ttsGenerationTurnIndex);
                if (audioPath) {
                  // TTS generation successful - operation lifecycle management handled by XState
                  console.log(`[TTS-SUCCESS] ${callSid}: Audio generated for operation, length: ${currentResponseText.length} chars`);
                  
                  if (audioPath === 'DIRECT_STREAM_SUCCESS') {
                    // Deepgram direct streaming completed successfully
                    console.log(`[TTS-SUCCESS] Deepgram direct streaming completed successfully for ${callSid}`);
                    calls[callSid].audioResponsePath = `deepgram_direct_stream_${ttsGenerationTurnIndex}`;
                    
                    // CRITICAL: For DeepGram direct streaming, TTS_FINISHED is already sent by completeStream()
                    // CENTRALIZATION FIX: Turn increments handled by XState when TTS_FINISHED is processed
                    // REMOVED: await incrementCallTurnIndex(callSid, calls, 'direct_stream', 'Deepgram direct streaming completed');
                    calls[callSid].isModelSpeaking = false;
                    console.log(`[TTS-SKIP-DUPLICATE] Skipping duplicate TTS_FINISHED for DeepGram direct streaming`);
                    return; // Early return to prevent double TTS_FINISHED
                  } else {
                    // File-based TTS (Azure/ElevenLabs)
                    calls[callSid].audioResponsePath = audioPath;
                    
                    // For Azure TTS (default), we need to stream the generated file
                    // ElevenLabs providers handle streaming internally
                    if (TTS_PROVIDER !== 'elevenlabs' && TTS_PROVIDER !== 'elevenlabs_stream') {
                      console.log(`[TTS-STREAMING] Streaming Azure TTS file to call: ${audioPath}`);
                      await streamAudioToCall(audioPath, callSid, ttsGenerationTurnIndex);
                    } else {
                      console.log(`[TTS-SUCCESS] ElevenLabs audio generated and streamed internally: ${audioPath}`);
                    }
                  }
                } else {
                  // Audio generation failed
                  console.error(`[TTS-ERROR] Audio generation failed for ${callSid}`);
                }
                  
                // CENTRALIZATION FIX: Turn increments now handled by XState TTS_FINISHED event below
                // REMOVED: await incrementCallTurnIndex(callSid, calls, 'tts_finished', 'TTS processing completed (Azure/ElevenLabs)');
                calls[callSid].isModelSpeaking = false;
                  
                // Send TTS_FINISHED event to the state machine
                  const controller = callControllers[callSid]; 
                  if (controller?.machine) {
                    controller.machine.send({ type: 'TTS_FINISHED' });
                    console.log(`[speakTTSForMachine:${callSid}] Sent TTS_FINISHED event to machine ID: ${controller.machine.id}.`);
                  } else {
                    console.warn(`[speakTTSForMachine:${callSid}] TTS finished, but machine controller not found.`);
                  }
              } catch (err) {
                console.error(`[XState:${callSid}] Error in TTS playback:`, err);
                
                // Send TTS_FINISHED event even in case of error to prevent state machine from getting stuck
                try {
                  const eventId = Math.random().toString(36).substring(2, 9);
                  console.log(`[XState:${callSid}] Sending TTS_FINISHED event (id: ${eventId}) to machine ID: ${controller.machine.id}`);
                  
                  // Capture pre-event state for debugging
                  const preEventState = controller.machine.getSnapshot().value;
                  const preEventContext = controller.machine.getSnapshot().context;
                  console.log(`[XState:${callSid}] Pre-event state: ${JSON.stringify(preEventState)}`);
                  console.log(`[XState:${callSid}] Pre-event context currentState: ${preEventContext.currentState}`);
                  
                  // Reduced context logging - only show essential fields instead of full case data
                  const contextSummary = {
                    currentState: preEventContext.currentState,
                    conversationTurns: preEventContext.conversation?.length || 0,
                    caseTitle: preEventContext.caseData?.title || preEventContext.caseData?.id || 'none',
                    hasResponseText: !!preEventContext.responseText,
                    turnNumber: preEventContext.turnNumber || 0
                  };
                  console.log(`[XState:${callSid}] PRE-SEND CONTEXT: ${JSON.stringify(contextSummary)}`);
                  
                  // Send the event (keep track of ID in logs only)
                  controller.machine.send({ type: 'TTS_FINISHED' });
                  
                  // Log the completion of the event
                  console.log(`[XState:${callSid}] Sent TTS_FINISHED event (id: ${eventId}) after error`);
                  
                  // Verify the post-event state to ensure transition happened
                  const postEventState = controller.machine.getSnapshot().value;
                  const postEventContext = controller.machine.getSnapshot().context;
                  console.log(`[XState:${callSid}] Post-event state: ${JSON.stringify(postEventState)}`);
                  console.log(`[XState:${callSid}] Post-event context currentState: ${postEventContext.currentState}`);
                  
                  // Log information to help debug transition issues
                  if (preEventContext.currentState === postEventContext.currentState) {
                    console.log(`[XState:${callSid}] WARNING: State did not change after TTS_FINISHED event (id: ${eventId})`);
                  }
                } catch (sendErr) {
                  console.error(`[XState:${callSid}] Failed to send TTS_FINISHED after error:`, sendErr);
                }
              } finally {
                // Ensure the isModelSpeaking flag is reset
                if (calls[callSid]) {
                  calls[callSid].isModelSpeaking = false;
                  // Release TTS lock
                  calls[callSid].activeTtsOperation = false;
                  console.log(`[TTS-UNLOCK:${callSid}] Released TTS lock`);
                }
              }
            }
          },
          'simulation', // PHASE 1: Default to simulation session type
          'twilio' // Connection type for Twilio WebSocket connections
        );
        
        // Store the controller
        callControllers[callSid] = controller;
        
        // Get session context if resuming
        let sessionConversation: Array<{ speaker: string; text: string }> = [];
        if (sessionResult && !sessionResult.isNewSession && sessionResult.sessionId) {
          try {
            const sessionContext = await SessionManager.getSessionContext(sessionResult.sessionId);
            if (sessionContext) {
              sessionConversation = sessionContext.previousConversations;
              console.log(`[SessionResume] Loaded ${sessionConversation.length} previous conversation turns for session ${sessionResult.sessionId}`);
            }
          } catch (error) {
            console.error(`[SessionResume] Error loading session context:`, error);
          }
        }

        // Use session conversation if available, otherwise use existing conversation from disk
        const initialConversation = sessionConversation.length > 0 ? sessionConversation : 
                                   (existingConversation.length > 0 ? existingConversation : []);

        // Initialize traditional call state 
        calls[callSid] = {
          // Core call identification & Timestamps
          callSid: callSid,
          userId: userIdFromWebhook,
          sessionId: sessionResult?.sessionId || null,
          callStartTime: callStartTime.getTime(), // Numeric timestamp for when the call started
          startTime: new Date(callStartTime.getTime()).toISOString(), // String timestamp as required by CallState
          lastUpdated: Date.now(),
          lastAudioReceived: Date.now(),

          // Connection & Stream Info
          connectionType: 'twilio',
          streamSid: streamSid,
          audioQuality: 'standard',

          // Audio processing state
          leftover: Buffer.alloc(0),
          silenceStart: null,
          consecutiveSilenceFrames: 0,
          silenceFrameCounter: 0,
          speechFrameCounter: 0,
          speechFramesInCurrentTurn: 0,
          hasSpeechDetected: false,
          isModelSpeaking: false,
          finalizationInProgress: false,

          // Conversation tracking
          conversation: initialConversation,
          // DEPRECATED: Legacy transcript fields no longer used (XState infrastructure is source of truth)
          currentTurnText: '',
          previousTurnText: '',
          currentTurnTranscription: '',
          currentTurnIntent: '',
          currentTurnSentiment: 'neutral',
          currentTurnEmotion: 'neutral',
          currentTurnAction: '',
          currentTurnStatus: 'idle',
          currentTurnError: null,
          currentTurnRetryCount: 0,
          currentTurnMaxRetries: 3,
          currentTurnTimeout: 30000,
          currentTurnIsFinal: false,
          currentTurnIsComplete: false,
          currentTurnIsError: false,
          currentTurnIsTimeout: false,
          currentTurnIsRetry: false,
          currentTurnIsFirstTurn: initialConversation.length === 0,
          currentTurnIsLastTurn: false,
          currentTurnVadResults: [],
          currentTurnSttResults: [],
          currentTurnTtsResults: [],
          currentTurnEntities: [],
          currentTurnSpeaker: 'user',
          currentTurnIndex: 0,
          currentTurnStartTime: undefined, // Initialize optional field

          // LLM & Case Data
          llmResponse: null,
          caseData: null,
          selectedCase: undefined,
          selectedCaseData: null,
          caseSelectionAttempted: false,

          // Recording
          recording: false,
          recordingFile: '', // Will be updated if recording starts
          recordingPath: `./recordings/${callSid}_caller.wav`, // Initial path
          recordingWriter: null,
          recordingStartTime: null, // Timestamp when actual recording starts
          recordingActualStartTime: Date.now(), // Timestamp of first audio chunk for recording; review if needed alongside recordingStartTime
          effectiveRecordingStartTime: undefined,
          recordingStoragePath: undefined,

          // Session and Turn Management
          sessionInfo: sessionResult || undefined,
          turnTimings: [],
          lastProcessedTurnNumber: undefined,
          transcriptSentForCurrentTurn: false,
          longSignalSentForCurrentTurn: false,
          vadDecisionLogged: false,
          
          // TTS State Tracking for logging
          lastTtsStateWarning: null, // Track last warning to prevent repetitive logs

          // Error Counts
          audioProcessingErrorCount: 0,
          sttErrorCount: 0,
          ttsErrorCount: 0,
          llmErrorCount: 0,

          // Flags and Operations
          isInitialized: true,
          activeTtsOperation: false,

          // STT/TTS providers and streams (optional, initialize as undefined)
          sttProvider: undefined,
          audioStream: undefined,
          speechRecognizer: undefined,
          
          // Finals-only processing state
          lastPartialTranscript: null,
          lastPartialTimestamp: null,
          partialTimeoutId: null,
          finalTranscriptReceived: false,
          
          // STT Processing Timeline Analysis - Phase 1
          lastSpeechEndTime: null,
          lastSilenceDetectionTime: null,

          // Process management (optional, initialize as undefined)
          recordFfmpeg: undefined,
          decodeFfmpeg: undefined,
          currentFfmpegProcess: undefined,

          // Timeouts and intervals (optional, initialize as undefined)
          currentSpeakingTimeout: undefined,
          pendingFinalizeTimeout: undefined,

          // WebSockets (optional, initialize as undefined)
          currentElevenLabsWebSocket: undefined,
          
          // TTS Race Condition Prevention
          isTtsGenerating: false,
          currentTtsText: undefined,
          currentTtsTurnIndex: undefined,
          activeSynthesizers: [],
          activeSentenceStreams: [],
          activeDeepgramStream: undefined,

          // Other optional fields from CallState
          chunkCount: 0,
          currentModelResponse: undefined,
          lastProcessedResponse: undefined,
          lastRagResponse: undefined,
          audioResponsePath: undefined,
          currentlyPlayingTurnIndex: undefined,
          audioStreamingStartAt: undefined,
        } as CallState;

        // PHASE 1: Atomic Audio Process Management Integration
        // Feature flag allows safe rollback if needed
        if (ENABLE_ATOMIC_AUDIO) {
          console.log(`[ATOMIC-AUDIO] ${callSid}: Enabling atomic audio process management (Twilio)`);
          calls[callSid] = createAtomicCallState(callSid, calls[callSid]);
        }

        // Initialize inactivity timer for new call
        resetInactivityTimer(callSid);
          
        // Log if we loaded existing conversation
        if (existingConversation.length > 0) {
          console.log(`Loaded existing conversation history for call ${callSid} with ${existingConversation.length} messages`);
        }

        // 1) ffmpeg #1 => record WAV locally (remains the same)
        try {
          const outPath = `./recordings/${callSid}_caller.wav`;
          
          // Capture necessary values at initialization time
          const capturedState = calls[callSid];
          const capturedAudioStreamingStartAt = capturedState?.audioStreamingStartAt;
          const capturedEffectiveRecordingStartTime = capturedState?.effectiveRecordingStartTime;
          const capturedCallStartTime = capturedState?.callStartTime;
          
          const recordFfmpeg = spawn('ffmpeg', [
            '-f', 'mulaw',
            '-ar', '8000',
            '-i', 'pipe:0',
            '-ac', '1',
            '-ar', '8000',
            '-f', 'wav',
            '-acodec', 'pcm_mulaw', // Output µ-law encoded WAV
            outPath,
          ]);
          
          recordFfmpeg.on('close', (code) => {
            console.log(`recordFfmpeg for ${callSid} exited with code ${code}`);
            
            // Use captured values instead of accessing potentially deleted state
            if (capturedAudioStreamingStartAt) {
              const recordingEndTime = Date.now();
              const effectiveStartTime = capturedEffectiveRecordingStartTime || capturedAudioStreamingStartAt;
              
              console.log(`[Mixer ${callSid}] Effective recording start time used for mixing: ${effectiveStartTime} (Actual: ${capturedAudioStreamingStartAt}, CallStart: ${capturedCallStartTime})`);
              
              const recordingDurationMs = recordingEndTime - effectiveStartTime;
              console.log(`[Mixer ${callSid}] Recording duration: ${Math.round(recordingDurationMs / 1000)}s`);
              
              // Update call record with duration even if state is gone
              updateCallRecordOnEnd(callSid, new Date(), Math.round(recordingDurationMs / 1000), outPath).catch(err => {
                console.error(`[DB] Error updating call record on end for ${callSid}:`, err);
              });
            }
          });
           
          recordFfmpeg.on('error', (err) => {
            console.error(`recordFfmpeg error for ${callSid}:`, err);
          });

          calls[callSid].recordFfmpeg = recordFfmpeg
        } catch (err) {
          console.error('Error launching record ffmpeg:', err)
        }
 
        
        // 2) ffmpeg #2 => decode mulaw -> 8kHz PCM => STT Provider (AAI or Azure)
        try {
          const decodeFfmpeg = spawn('ffmpeg', [
            '-f', 'mulaw',
            '-ar', '8000',
            '-i', 'pipe:0',
            '-ac', '1',
            '-ar', '8000', // Output 8kHz PCM for STT
            '-f', 's16le', // Signed 16-bit Little Endian PCM
            'pipe:1',
          ])
          decodeFfmpeg.on('close', (code) => {
            console.log(`decodeFfmpeg for ${callSid} exited with code ${code}`)
          })
          decodeFfmpeg.on('error', (err) => {
            console.error(`decodeFfmpeg error for ${callSid}:`, err)
          })

          calls[callSid].decodeFfmpeg = decodeFfmpeg
        } catch (err) {
          console.error('Error launching decode ffmpeg:', err)
        }

        // 3) Initialize and connect the selected STT provider
        try {
          if (STT_PROVIDER === 'assemblyai') {
            if (!aaiClient) throw new Error("AssemblyAI client not initialized.");
            const aaiTranscriber = aaiClient.realtime.transcriber({
                sampleRate: 8000, // Matches ffmpeg output
            });

            aaiTranscriber.on('open', ({ sessionId }) => {
                console.log(`[AssemblyAI] Session opened: ${sessionId}`)
            });

            aaiTranscriber.on('transcript', (rt: RealtimeTranscript) => {
                if (!rt.text) return;
                
                // Log every transcript from AssemblyAI
                console.log(`[AssemblyAI:${callSid}] ${rt.message_type || 'unknown'}: "${rt.text}"`)
                
                // Get current VAD state for debugging
                const state = calls[callSid];
                if (state) {
                    console.log(`[AssemblyAI-VAD:${callSid}] Speech frames: ${state.speechFramesInCurrentTurn}, Silence frames: ${state.consecutiveSilenceFrames}, Speech detected: ${state.hasSpeechDetected}`)
                }
                
                // Process transcript normally
                handleTranscript(callSid!, { 
                    text: rt.text, 
                    isFinal: rt.message_type === 'FinalTranscript' 
                });
            });

            aaiTranscriber.on('error', (error: Error) => {
              console.error(`[AssemblyAI] Error for ${callSid}:`, error)
            });

            aaiTranscriber.on('close', (code: number, reason: string) => {
              console.log(`[AssemblyAI] Closed for ${callSid}: ${code} ${reason}`)
            });

            await aaiTranscriber.connect();
            
            // Wrap AssemblyAI transcriber in our common interface
            calls[callSid].sttProvider = {
                sendAudio: (chunk) => aaiTranscriber.sendAudio(chunk),
                close: () => aaiTranscriber.close(),
            };
            console.log(`[STT] Initialized AssemblyAI for call ${callSid}`);

          } else {
              // Initialize STT provider through AudioProviderManager for Azure/Deepgram
              console.log(`[STT] Initializing STT provider for Twilio call ${callSid}`);
              const { audioProviderManager } = await import('./services/AudioProviderManager');
              calls[callSid].sttProvider = await audioProviderManager.initializeSTT(callSid!, ConnectionType.TWILIO);
              
              // Connect the provider if it has a connect method
              if (calls[callSid].sttProvider?.connect) {
                  await calls[callSid].sttProvider!.connect!();
              }
              console.log(`[STT] ✅ STT provider initialized through AudioProviderManager for Twilio call ${callSid}`);
          }

          // 4) read decodeFfmpeg stdout => feed STT provider + do VAD
          const decodeFfmpeg = calls[callSid].decodeFfmpeg
          if (decodeFfmpeg) {
            decodeFfmpeg.stdout.on('data', async (chunk: Buffer) => {
                // Record the actual start time when the first chunk arrives
                const state = calls[callSid!]; // Get state again inside handler
                if (state && !state.recordingActualStartTime) {
                    state.recordingActualStartTime = Date.now();
                    console.log(`[Recording Start] Set recordingActualStartTime for ${callSid} to ${state.recordingActualStartTime}`);
                }

                // Send chunk to the active STT provider
                const sttProvider = calls[callSid!]?.sttProvider;
                if (sttProvider) {
                    sttProvider.sendAudio(chunk);
                }
                
                // Also do VAD chunking using Twilio connection type
                await handleVadAndTurns(callSid!, chunk, ConnectionType.TWILIO);
            });
                } else {
              console.error(`decodeFfmpeg not initialized for call ${callSid}`);
          }
        } catch (err) {
          console.error(`Error initializing STT provider for call ${callSid}:`, err)
        }
        // REMOVED: controller.machine.start(); // Machine is already started in initializeCallStateMachine
        // NOTE: Connection type sync removed - now set correctly during machine initialization
        // The connection type is passed during initializeCallStateMachine call above (Twilio) 
        // or in callBootstrap.ts (LiveKit/Direct), so no runtime sync needed
        
        controller.machine.send({ type: 'START', callSid: callSid }); // Keep sending the START event
        console.log(`[XState:${callSid}] --> Sent START event to machine.`);
        break

      case 'media': {
        if (!callSid) break
        const audioBuffer = Buffer.from(data.media.payload, 'base64')

        // Note: Don't reset inactivity timer on every audio packet - only reset on actual voice activity detected by VAD

        // Write chunk to recordFfmpeg (remains the same)
        const recordFfmpeg = calls[callSid]?.recordFfmpeg
        if (recordFfmpeg) {
          recordFfmpeg.stdin.write(audioBuffer)
        }

        // Write chunk to decodeFfmpeg (remains the same)
        const decodeFfmpeg = calls[callSid]?.decodeFfmpeg
        if (decodeFfmpeg) {
          decodeFfmpeg.stdin.write(audioBuffer)
        }

          // Update streamSid if provided in media event and not already set
          if (data.streamSid && calls[callSid] && !calls[callSid].streamSid) {
            calls[callSid].streamSid = data.streamSid;
            console.log(`Updated streamSid for ${callSid} to ${data.streamSid}`);
        }
        break
      }

      case 'stop':
        console.log('Twilio stop event for callSid:', callSid)
        if (callSid) {
          cleanupCall(callSid)
        }
        break

      case 'dtmf':
        if (!DTMF_ENABLED) {
          console.log('[WS] DTMF input ignored - DTMF handling is disabled');
          return;
        }

        if (!callSid) {
          console.warn('[WS] Received DTMF for unknown callSid.');
          return;
        }
        
        // Validate DTMF data structure
        if (!data.dtmf || typeof data.dtmf !== 'object') {
          console.warn(`[WS:${callSid}] Invalid DTMF event structure:`, JSON.stringify(data));
          return;
        }

        // Extract and validate digit
        const digit = data.dtmf.digit;
        if (!digit || typeof digit !== 'string') {
          console.warn(`[WS:${callSid}] Missing or invalid DTMF digit:`, JSON.stringify(data.dtmf));
          return;
        }
        
        console.log(`[WS:${callSid}] Received DTMF digit: ${digit}`);
        
        // DEBUG: Check if controller exists just before sending
        const controllerExists = !!callControllers[callSid];
        console.log(`[WS:${callSid}] Controller exists before sendDtmfInput? ${controllerExists}`);
        
        // Use the xstateIntegration helper function instead of direct send
        // This ensures proper context preservation and restoration
        if (sendDtmfInput(callSid, digit)) {
          console.log(`[WS:${callSid}] Successfully sent DTMF digit ${digit} to state machine`);
        } else {
          console.error(`[WS:${callSid}] Failed to send DTMF digit ${digit} to state machine`);
        }
        break;

      default:
        console.log('Unknown Twilio event:', data.event)
    }
    } catch (err) {
      console.error(`[WEBSOCKET-ERROR] Error handling WebSocket message for callSid ${callSid || 'unknown'}:`, err);
      console.error(`[WEBSOCKET-ERROR] Stack trace:`, err.stack);
      // Don't re-throw the error to prevent WebSocket disconnection
    }
  }); // End of ws.on('message', ...)
}); // End of wss.on('connection', ...)

// Log WebSocket server errors
wss.on('error', (error) => {
  console.error('WebSocket server error:', error);
});

//-----------------------------------------------
// Helper: Unified Transcript Handling
//-----------------------------------------------
// Add makeRagRequest function before handleTranscript
export async function makeRagRequest(userMessage: string, callSid: string, conversationHistory: Array<{ speaker: string; text: string }> = [], turnIndex?: number): Promise<string> {
  // Log what's being sent to the LLM
  console.log(`[LLM-REQUEST:${callSid}] Sending user message to LLM: "${userMessage}"`)
  console.log(`[LLM-REQUEST:${callSid}] Conversation history: ${conversationHistory.length} turns`)
  
  // Record latency step if turnIndex provided
  if (turnIndex !== undefined) {
    latencyTracker.recordStep(callSid, turnIndex, LatencySteps.LLM_REQUEST_STARTED, {
      messageLength: userMessage.length,
      historyTurns: conversationHistory.length
    });
  }
  
  // Log the last few turns for context (max 3)
  const recentHistory = conversationHistory.slice(-3);
  if (recentHistory.length > 0) {
    console.log(`[LLM-REQUEST:${callSid}] Recent conversation:`)
    recentHistory.forEach((turn, i) => {
      console.log(`[LLM-REQUEST:${callSid}]   Turn ${conversationHistory.length - recentHistory.length + i + 1}: ${turn.speaker}: "${turn.text.substring(0, 50)}${turn.text.length > 50 ? '...' : ''}"`)
    })
  }
  
  try {
    console.log(`[LLM-REQUEST:${callSid}] Sending request via LLM router`)
    const startTime = Date.now();
    
    // Record when request is actually sent
    if (turnIndex !== undefined) {
      latencyTracker.recordStep(callSid, turnIndex, LatencySteps.LLM_REQUEST_SENT);
    }
    
    const ragResponse = await routeConverseLLM(
      callSid,
      userMessage,
      conversationHistory
    );
    
    const duration = Date.now() - startTime;
    const responseText = ragResponse.assistantReply || '';
    
    // Record when response is received
    if (turnIndex !== undefined) {
      latencyTracker.recordStep(callSid, turnIndex, LatencySteps.LLM_RESPONSE_RECEIVED, {
        responseLength: responseText.length,
        llmDuration: duration
      });
    }
    
    console.log(`[LLM-RESPONSE:${callSid}] Received after ${duration}ms: "${responseText.substring(0, 50)}${responseText.length > 50 ? '...' : ''}"`)
    
    // Record when response is processed
    if (turnIndex !== undefined) {
      latencyTracker.recordStep(callSid, turnIndex, LatencySteps.LLM_RESPONSE_PROCESSED);
    }
    
    return responseText;
  } catch (err) {
    console.error(`[LLM-ERROR:${callSid}] Error making RAG request:`, err);
    return 'I apologize, but I am having trouble processing your request at the moment.';
  }
}

// Add specialized transition RAG request function
async function makeTransitionRagRequest(
  userMessage: string, 
  callSid: string, 
  fromState: string,
  toState: string,
  transitionReason: string,
  conversationHistory: Array<{ speaker: string; text: string }> = []
): Promise<{response: string, action: 'accept' | 'reject'}> {
  try {
    console.log(`[Transition RAG] Using LLM router for transition decision`);
    
    const routerResponse = await routeTransitionDecision(
      callSid,
      userMessage,
      fromState,
      toState,
      transitionReason,
      conversationHistory
    );
    
    // Expect a structured response with both text and decision
    const result = {
      response: routerResponse.response || '',
      action: routerResponse.action || 'reject' // Default to reject if no decision
    };
    
    // Validate the action is one of the allowed values
    if (result.action !== 'accept' && result.action !== 'reject') {
      console.error(`[Transition RAG] Invalid action received: ${result.action}, defaulting to 'reject'`);
      result.action = 'reject';
    }
    
    console.log(`[Transition RAG] LLM decision for ${callSid}: ${result.action}`);
    return result;
  } catch (err) {
    console.error(`Error making transition RAG request for call ${callSid}:`, err);
    // Default to reject on error, with error message
    return {
      response: 'I apologize, but I am having trouble processing your request. Let\'s continue where we were.',
      action: 'reject'
    };
  }
}

// Modify handleTranscript to be async
// 🏗️ PHASE 4: Infrastructure-Managed Transcript Handler (Finals-Only Processing)
export async function handleTranscript(callSid: string, transcriptData: { text: string; isFinal: boolean }) {
    console.log(`[HANDLE-TRANSCRIPT] ${callSid}: Processing transcript - isFinal=${transcriptData.isFinal}, text="${transcriptData.text}" (Finals-Only Mode)`);
    const state = calls[callSid];
    if (!state) {
        console.log(`[TRANSCRIPT-DEBUG] No state found for callSid ${callSid}`);
        return;
    }
    if (!transcriptData.text) {
        console.log(`[TRANSCRIPT-DEBUG] Empty transcript text for callSid ${callSid}`);
        return;
    }

    const { text, isFinal } = transcriptData;
    
    // Reset inactivity timer on any transcript activity (user is actively speaking)
    resetInactivityTimer(callSid);
    
    // 🏗️ PHASE 4: Try infrastructure first, fall back to old logic
    if (INFRASTRUCTURE_FLAGS.USE_INFRASTRUCTURE_TRANSCRIPT) {
        console.log(`[INFRASTRUCTURE-TRANSCRIPT] Processing via XState for ${callSid}: isFinal=${isFinal}, text="${text}"`);
        
        // Update XState infrastructure state
        const updated = updateTranscriptState(callSid, text, isFinal);
        if (updated) {
            // Get the processed state from infrastructure
            const transcriptState = getTranscriptState(callSid);
            if (transcriptState) {
                console.log(`[INFRASTRUCTURE-TRANSCRIPT] Transcript processed (isFinal=${isFinal}): "${transcriptState.currentText}"`);
                
                // SINGLE SOURCE OF TRUTH: XState infrastructure context is the only transcript source
                // Legacy state.currentTurnText is no longer used - all reads go through getTranscriptState()
                
                // Only process final transcript logic for final transcripts
                if (isFinal) {
                state.hasSpeechDetected = true;
                state.transcriptSentForCurrentTurn = false;
                state.longSignalSentForCurrentTurn = false;
                
                // Update timing
                if (!state.currentTurnStartTime) {
                    state.currentTurnStartTime = Date.now();
                }
                
                // CRITICAL FIX: Use XState action instead of direct turnTimings update
                // This ensures synchronization between XState context and global calls object
                const controller = callControllers[callSid];
                if (controller?.machine) {
                  controller.machine.send({
                    type: 'UPDATE_TRANSCRIPT_RECEIVED',
                    timestamp: Date.now(),
                    turnIndex: state.currentTurnIndex,
                    callSid: callSid
                  });
                }
                
                // CRITICAL FIX: DO NOT immediately add to conversation or increment turn!
                // Let VAD silence detection handle turn completion like working commit
                console.log(`[INFRASTRUCTURE-TRANSCRIPT] Transcript accumulated, waiting for VAD turn completion`);
                
                // Update the state for backward compatibility but don't finalize turn
                state.hasSpeechDetected = true; // Ensure VAD knows speech was detected
                
                // DIAGNOSTIC: Track STT transcript processing timing
                const transcriptProcessingTime = Date.now();
                logDiagnostic(callSid, 'STT-TRANSCRIPT-PROCESSING', 'Final transcript received', {
                  transcriptProcessingTime,
                  transcriptText: transcriptState.currentText,
                  textLength: transcriptState.currentText?.length || 0,
                  isFinal: true,
                  hasSpeechDetected: state.hasSpeechDetected,
                  currentTurnIndex: state.currentTurnIndex,
                  turnStartTime: state.currentTurnStartTime,
                  timeSinceTurnStart: state.currentTurnStartTime ? transcriptProcessingTime - state.currentTurnStartTime : 'unknown'
                });

                // NEW: Notify coordinator of final transcript
                const { turnCompletionCoordinator } = require('./turnCompletionCoordinator');
                
                // DIAGNOSTIC: Track coordinator notification for transcript
                logDiagnostic(callSid, 'COORDINATOR-TRANSCRIPT-NOTIFY', 'Calling onFinalTranscript', {
                  coordinatorCallTime: Date.now(),
                  timeSinceProcessing: Date.now() - transcriptProcessingTime,
                  transcriptLength: transcriptState.currentText?.length || 0
                });
                
                turnCompletionCoordinator.onFinalTranscript(callSid, transcriptState.currentText);
                
                // DIAGNOSTIC: Track coordinator notification completion for transcript
                logDiagnostic(callSid, 'COORDINATOR-TRANSCRIPT-NOTIFY-COMPLETE', 'onFinalTranscript completed', {
                  coordinatorCompleteTime: Date.now(),
                  totalTranscriptNotificationTime: Date.now() - transcriptProcessingTime
                });
                
                // REMOVED: Immediate conversation adding, turn incrementing, and state reset
                // These actions should only happen when VAD detects silence (in finalizeTurn)
                console.log(`[INFRASTRUCTURE-TRANSCRIPT] Current accumulated text: "${transcriptState.currentText}" - waiting for silence detection`);
                } // End if (isFinal)
            }
            return; // Infrastructure handled it
        }
    }
    
    // SINGLE SOURCE OF TRUTH: XState infrastructure is required
    console.error(`[TRANSCRIPT-ERROR] XState infrastructure failed for ${callSid}. Infrastructure must be working for proper operation.`);
    console.error(`[TRANSCRIPT-ERROR] This indicates a critical system issue that needs investigation.`);
    return;
}


//-----------------------------------------------
// Helper: Initialize Azure STT (Skeleton)
//-----------------------------------------------
/**
 * PHASE 2 BACKWARD COMPATIBILITY WRAPPER
 * Azure STT initialization - now delegates to AudioProviderManager
 */
export function initializeAzureStt(callSid: string, connectionType: ConnectionType = ConnectionType.TWILIO): SttProvider {
    console.log(`[Azure STT Wrapper] Initializing via AudioProviderManager for call ${callSid} with ${connectionType}`);
    
    // Create a synchronous wrapper that handles the async initialization internally
    let actualProvider: SttProvider | null = null;
    
    // Initialize the provider asynchronously but return a synchronous interface
    (async () => {
        const { audioProviderManager } = await import('./services/AudioProviderManager');
        actualProvider = await audioProviderManager.initializeSTT(callSid, connectionType);
    })().catch(error => {
        console.error(`[Azure STT Wrapper] Failed to initialize provider for ${callSid}:`, error);
    });
    
    // Return a proxy that forwards calls to the actual provider when ready
    return {
        sendAudio: (chunk: Buffer) => {
            if (actualProvider) {
                actualProvider.sendAudio(chunk);
            } else {
                console.warn(`[Azure STT Wrapper] Provider not ready yet for ${callSid}, buffering audio chunk`);
            }
        },
        close: () => {
            if (actualProvider) {
                actualProvider.close();
            } else {
                console.warn(`[Azure STT Wrapper] Provider not ready yet for ${callSid}, close called`);
            }
        }
    };
}

//-----------------------------------------------
// Helper: Initialize Deepgram STT
//-----------------------------------------------
export async function initializeDeepgramStt(callSid: string, connectionType: ConnectionType = ConnectionType.TWILIO): Promise<SttProvider> {
    console.log(`[Deepgram STT] Initializing for call ${callSid} with ${connectionType} audio configuration...`);
    const state = calls[callSid];
    if (!state) {
        throw new Error(`Call state not found for ${callSid} during Deepgram STT init.`);
    }

    if (!deepgramClient) {
        throw new Error("Deepgram client not initialized!");
    }

    // Get audio configuration for the connection type
    const audioSettings = getAudioConfig(connectionType);
    const sttSampleRate = audioSettings.stt.sampleRate;
    
    console.log(`[Deepgram STT] Using sample rate: ${sttSampleRate}Hz for ${connectionType} connection`);

    // Create live transcription connection with minimal config first
    console.log(`[Deepgram STT] Creating connection with config: model=${DEEPGRAM_STT_MODEL}, sampleRate=${sttSampleRate}Hz, language=en-US`);
    
    const connection = deepgramClient.listen.live({
        model: DEEPGRAM_STT_MODEL,
        language: 'en-US',
        smart_format: true,
        interim_results: true,
        sample_rate: sttSampleRate,
        channels: 1,
        encoding: 'linear16'
    });
    
    console.log(`[Deepgram STT] Connection object created, attempting to establish WebSocket connection...`);

    // Store connection in state for cleanup
    state.deepgramConnection = connection;

    // Wait for connection to be ready (should work now with WebSocket polyfill)
    console.log(`[Deepgram STT] Waiting for WebSocket connection to open...`);
    await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
            console.error(`[Deepgram STT] Connection timeout after 5 seconds for ${callSid}`);
            reject(new Error(`Deepgram connection timeout after 5 seconds for ${callSid}`));
        }, 5000); // Reduced timeout since it should connect quickly now

        const onOpen = () => {
            console.log(`[Deepgram STT] ✅ WebSocket connection opened successfully for ${callSid}`);
            clearTimeout(timeout);
            connection.off(LiveTranscriptionEvents.Open, onOpen);
            connection.off(LiveTranscriptionEvents.Error, onError);
            resolve(undefined);
        };

        const onError = (error: any) => {
            console.error(`[Deepgram STT] ❌ WebSocket connection error for ${callSid}:`, error);
            clearTimeout(timeout);
            connection.off(LiveTranscriptionEvents.Open, onOpen);
            connection.off(LiveTranscriptionEvents.Error, onError);
            reject(new Error(`Deepgram connection failed: ${error.message || error}`));
        };

        connection.on(LiveTranscriptionEvents.Open, onOpen);
        connection.on(LiveTranscriptionEvents.Error, onError);
    });

    // Set up event handlers after connection is established
    connection.on(LiveTranscriptionEvents.Transcript, (data: any) => {
        if (!data?.channel?.alternatives?.[0]?.transcript) return;
        
        const transcript = data.channel.alternatives[0].transcript;
        const isFinal = data.is_final || false;
        const transcriptTimestamp = Date.now();
        
        // 📊 TIMING: Track STT processing pipeline
        if (isFinal) {
            console.log(`[Deepgram STT Final] ${callSid}: "${transcript}" at ${transcriptTimestamp}`);
            
            // PHASE 1: STT Processing Timeline Analysis - Track final transcript timing
            if (state.lastSpeechEndTime) {
                const sttProcessingTime = transcriptTimestamp - state.lastSpeechEndTime;
                console.log(`[STT-TIMING] ${callSid}: STT processing took ${sttProcessingTime}ms from speech end to final transcript`);
                
                // Track detailed STT pipeline timing
                console.log(`[STT-PIPELINE] ${callSid}: Speech ended at ${state.lastSpeechEndTime}, final transcript at ${transcriptTimestamp}`);
            }
            
            // Track how long after silence detection this arrives
            if (state.lastSilenceDetectionTime) {
                const silenceToTranscriptDelay = transcriptTimestamp - state.lastSilenceDetectionTime;
                console.log(`[STT-COORDINATION] ${callSid}: Final transcript arrived ${silenceToTranscriptDelay}ms after silence detection`);
                
                if (silenceToTranscriptDelay > 2000) {
                    console.warn(`[STT-COORDINATION] ${callSid}: CRITICAL DELAY - Final transcript took ${silenceToTranscriptDelay}ms after silence (threshold: 2000ms)`);
                }
            }
            
            // CRITICAL FIX: Set flag to prevent timeout firing after final transcript received
            state.finalTranscriptReceived = true;
            
            // Clear any pending timeout since we now have the final transcript
            if (state.partialTimeoutId) {
                clearTimeout(state.partialTimeoutId);
                state.partialTimeoutId = null;
                console.log(`[STT-TIMEOUT-FIX] ${callSid}: Cancelled timeout - final transcript received`);
            }
            
            handleTranscript(callSid, { text: transcript, isFinal: true });
        } else {
            console.log(`[Deepgram STT Partial] ${callSid}: "${transcript}" at ${transcriptTimestamp} - STORED ONLY, NOT PROCESSED (Finals-Only Mode)`);
            // Store partial for timeout fallback
            state.lastPartialTranscript = transcript;
            state.lastPartialTimestamp = transcriptTimestamp;
            
            // Track partial transcript timing for analysis
            if (state.lastSpeechEndTime) {
                const partialDelay = transcriptTimestamp - state.lastSpeechEndTime;
                console.log(`[STT-PARTIAL-TIMING] ${callSid}: Partial transcript arrived ${partialDelay}ms after speech end`);
            }
            
            // Clear existing timeout and set new one
            if (state.partialTimeoutId) {
                clearTimeout(state.partialTimeoutId);
            }
            
            // CRITICAL FIX: Reset final transcript flag when starting to wait for new transcript
            // This ensures timeouts can fire for new utterances even after previous finals
            state.finalTranscriptReceived = false;
            
            // Process partial as fallback if no final arrives within 1.5 seconds
            state.partialTimeoutId = setTimeout(() => {
                if (state.lastPartialTranscript && !state.finalTranscriptReceived) {
                    const timeoutTimestamp = Date.now();
                    console.warn(`[Deepgram STT Timeout] ${callSid}: No final transcript received within 1.5s, processing partial: "${state.lastPartialTranscript}" at ${timeoutTimestamp}`);
                    
                    // Track timeout fallback timing
                    const timeoutProcessingTime = timeoutTimestamp - (state.lastSpeechEndTime || transcriptTimestamp);
                    console.log(`[STT-TIMEOUT-TIMING] ${callSid}: Timeout fallback took ${timeoutProcessingTime}ms from speech end`);
                    
                    handleTranscript(callSid, { text: state.lastPartialTranscript, isFinal: true });
                    state.lastPartialTranscript = null;
                }
            }, 1500); // Reduced from 3000ms to 1500ms for better responsiveness
        }
    });

    connection.on(LiveTranscriptionEvents.Error, (error: any) => {
        console.error(`[Deepgram STT] Runtime error for ${callSid}:`, error);
    });

    connection.on(LiveTranscriptionEvents.Close, (event: any) => {
        console.log(`[Deepgram STT] Connection closed for ${callSid}:`, event);
    });

    connection.on(LiveTranscriptionEvents.UtteranceEnd, (data: any) => {
        console.log(`[Deepgram STT] Utterance end detected for ${callSid} at ${data.last_word_end}ms`);
        // This event indicates a natural speech break - informational only
        // VAD integration handled separately via handleVadAndTurns() like Azure STT
    });

    // Return the provider interface implementation
    return {
        sendAudio: (chunk: Buffer) => {
            try {
                if (connection.getReadyState() === LiveConnectionState.OPEN) {
                    connection.send(chunk);
                }
            } catch (error) {
                console.error(`[Deepgram STT] Error sending audio chunk for ${callSid}:`, error);
            }
        },
        close: () => {
            console.log(`[Deepgram STT] Closing connection for call ${callSid}...`);
            try {
                if (state.partialTimeoutId) {
                    clearTimeout(state.partialTimeoutId);
                    state.partialTimeoutId = null;
                }
                if (state.deepgramConnection) {
                    state.deepgramConnection.finish();
                    state.deepgramConnection = undefined;
                }
            } catch (error) {
                console.error(`[Deepgram STT] Error during close for ${callSid}:`, error);
            }
        }
    };
}

//-----------------------------------------------
// Helper: Comprehensive Singleton Cleanup
//-----------------------------------------------
/**
 * COMPREHENSIVE cleanup function that addresses ALL singleton service state accumulation
 * 
 * This function calls ALL singleton service cleanup methods that were identified
 * in the comprehensive analysis as never being called during centralized cleanup.
 * 
 * Root cause: 6 singleton services accumulate state that persists across calls:
 * 1. vadRateLimiter - Has cleanupCall() method
 * 2. requestCancellationManager - Has cancelAllRequestsForCall() and cleanupStaleRequests() methods  
 * 3. liveKitConnectionService - Has cleanupConnection() method
 * 4. turnCoordinationService - Has cleanupCoordinationState() method
 * 5. textToSpeechManagerComplete - Has cleanupWithCoordination() method
 * 6. callControllers registry - Missing cleanup (handled here)
 */
export async function comprehensiveCallCleanup(callSid: string): Promise<void> {
  console.log(`[COMPREHENSIVE-CLEANUP] Starting complete cleanup for ${callSid}`);
  
  try {
    // PHASE 1: Prevent new operations by setting cleanup state
    const { callControllers, CleanupState } = await import('./xstateIntegration');
    const controller = callControllers[callSid];
    if (controller) {
      controller.cleanupState = CleanupState.PREPARING;
      console.log(`[COMPREHENSIVE-CLEANUP] Set cleanup state to PREPARING for ${callSid}`);
    }
    
    // PHASE 2: Cancel active HTTP requests (MISSING from current cleanup)
    const { requestCancellationManager } = await import('./requestCancellation');
    const cancelledRequests = await requestCancellationManager.cancelAllRequestsForCall(callSid);
    console.log(`[COMPREHENSIVE-CLEANUP] Cancelled ${cancelledRequests} active HTTP requests for ${callSid}`);
    
    // PHASE 3: TTS coordination with LiveKit (WORKING - already implemented)
    const { textToSpeechManagerComplete } = await import('./services/TextToSpeechManager');
    await textToSpeechManagerComplete.cleanupWithCoordination(callSid);
    console.log(`[COMPREHENSIVE-CLEANUP] TTS cleanup with coordination completed for ${callSid}`);
    
    // PHASE 4: LiveKit connection cleanup (MISSING from current cleanup)
    const { liveKitConnectionService } = await import('./services/LiveKitConnectionService');
    await liveKitConnectionService.cleanupConnection(callSid);
    console.log(`[COMPREHENSIVE-CLEANUP] LiveKit connection state cleaned up for ${callSid}`);
    
    // PHASE 5: Turn coordination cleanup (MISSING from current cleanup)
    const { turnCoordinationService } = await import('./services/TurnCoordinationService');
    await turnCoordinationService.cleanupCoordinationState(callSid);
    console.log(`[COMPREHENSIVE-CLEANUP] Turn coordination state cleaned up for ${callSid}`);
    
    // PHASE 6: VAD rate limiter cleanup (MISSING from current cleanup)
    const { vadRateLimiter } = await import('./vadRateLimiter');
    vadRateLimiter.cleanupCall(callSid);
    console.log(`[COMPREHENSIVE-CLEANUP] VAD rate limiter state cleaned up for ${callSid}`);
    
    // PHASE 7: Cleanup stale requests (MISSING from current cleanup)
    const cleanedStaleRequests = requestCancellationManager.cleanupStaleRequests();
    console.log(`[COMPREHENSIVE-CLEANUP] Cleaned up ${cleanedStaleRequests} stale HTTP requests`);
    
    // PHASE 8: XState controller cleanup (DEADLOCK FIX: Don't stop machine during service execution)
    if (controller) {
      // CRITICAL FIX: Remove machine.stop() to prevent deadlock
      // The machine cannot stop itself while running the cleanup service
      // Machine cleanup will happen in the onDone action after service completes
      console.log(`[COMPREHENSIVE-CLEANUP] Skipping machine stop during service execution to prevent deadlock for ${callSid}`);
      console.log(`[COMPREHENSIVE-CLEANUP] XState controller reference will be cleaned up after service completion`);
    }
    
    // PHASE 9: Call state removal (MISSING from current cleanup)
    delete calls[callSid];
    delete pendingCallDetails[callSid];
    console.log(`[COMPREHENSIVE-CLEANUP] Call state and pending details deleted for ${callSid}`);
    
    // PHASE 10: Verify cleanup completion
    const verificationResult = verifyCompleteCleanup(callSid);
    if (verificationResult.isClean) {
      console.log(`[COMPREHENSIVE-CLEANUP] ✅ Complete cleanup finished and verified for ${callSid}`);
    } else {
      console.warn(`[COMPREHENSIVE-CLEANUP] ⚠️ Cleanup completed but verification found issues for ${callSid}:`, verificationResult.issues);
      console.warn(`[COMPREHENSIVE-CLEANUP] Verification details:`, verificationResult.details);
    }
    
  } catch (error) {
    console.error(`[COMPREHENSIVE-CLEANUP] ❌ Error during comprehensive cleanup for ${callSid}:`, error);
    throw error;
  }
}

//-----------------------------------------------
// Helper: Cleanup Verification System
//-----------------------------------------------
/**
 * Cleanup verification system to ensure complete resource release
 * 
 * Verifies that all singleton services have properly cleaned up their state
 * for the specified call. This helps detect incomplete cleanup that causes
 * phantom calls.
 */
export function verifyCompleteCleanup(callSid: string): {
  isClean: boolean;
  issues: string[];
  details: Record<string, any>;
} {
  const issues: string[] = [];
  const details: Record<string, any> = {};
  
  console.log(`[CLEANUP-VERIFICATION] Starting cleanup verification for ${callSid}`);
  
  try {
    // Check 1: Global call state
    if (calls[callSid]) {
      issues.push("calls[callSid] still exists");
      details.callState = "exists";
    } else {
      details.callState = "cleaned";
    }
    
    // Check 2: XState controller registry
    const { callControllers } = require('./xstateIntegration');
    if (callControllers[callSid]) {
      issues.push("callControllers[callSid] still exists");
      details.xstateController = "exists";
    } else {
      details.xstateController = "cleaned";
    }
    
    // Check 3: Pending call details
    if (pendingCallDetails[callSid]) {
      issues.push("pendingCallDetails[callSid] still exists");
      details.pendingDetails = "exists";
    } else {
      details.pendingDetails = "cleaned";
    }
    
    // Check 4: VAD rate limiter
    try {
      const { vadRateLimiter } = require('./vadRateLimiter');
      const vadStatus = vadRateLimiter.getStatus(callSid);
      if (vadStatus) {
        issues.push("VAD rate limiter state still exists");
        details.vadRateLimiter = "has_state";
      } else {
        details.vadRateLimiter = "cleaned";
      }
    } catch (error) {
      details.vadRateLimiter = "check_failed";
      console.warn(`[CLEANUP-VERIFICATION] Could not check VAD rate limiter:`, error);
    }
    
    // Check 5: Request cancellation manager
    try {
      const { requestCancellationManager } = require('./requestCancellation');
      const activeRequestCount = requestCancellationManager.getActiveRequestCount(callSid);
      if (activeRequestCount > 0) {
        issues.push(`${activeRequestCount} active HTTP requests still exist`);
        details.activeRequests = activeRequestCount;
      } else {
        details.activeRequests = 0;
      }
    } catch (error) {
      details.activeRequests = "check_failed";
      console.warn(`[CLEANUP-VERIFICATION] Could not check active requests:`, error);
    }
    
    // Check 6: LiveKit connection service
    try {
      const { liveKitConnectionService } = require('./services/LiveKitConnectionService');
      const connectionState = liveKitConnectionService.getConnectionState(callSid);
      if (connectionState) {
        issues.push("LiveKit connection state still exists");
        details.liveKitConnection = "has_state";
      } else {
        details.liveKitConnection = "cleaned";
      }
    } catch (error) {
      details.liveKitConnection = "check_failed";
      console.warn(`[CLEANUP-VERIFICATION] Could not check LiveKit connection:`, error);
    }
    
    // Check 7: Turn coordination service
    try {
      const { turnCoordinationService } = require('./services/TurnCoordinationService');
      const coordinationState = turnCoordinationService.getCoordinationState(callSid);
      if (coordinationState.hasLock || coordinationState.isCoordinating) {
        issues.push("Turn coordination locks or state still exist");
        details.turnCoordination = "has_state";
      } else {
        details.turnCoordination = "cleaned";
      }
    } catch (error) {
      details.turnCoordination = "check_failed";
      console.warn(`[CLEANUP-VERIFICATION] Could not check turn coordination:`, error);
    }
    
    // Check 8: TTS Manager active tasks
    try {
      const { textToSpeechManagerComplete } = require('./services/TextToSpeechManager');
      // Note: TextToSpeechManager doesn't expose a public method to check active tasks for a specific call
      // This would need to be added to the TTS manager if we want to check it
      details.ttsManager = "check_not_implemented";
    } catch (error) {
      details.ttsManager = "check_failed";
      console.warn(`[CLEANUP-VERIFICATION] Could not check TTS manager:`, error);
    }
    
    const isClean = issues.length === 0;
    
    if (isClean) {
      console.log(`[CLEANUP-VERIFICATION] ✅ Complete cleanup verified for ${callSid}`);
    } else {
      console.error(`[CLEANUP-VERIFICATION] ❌ Cleanup incomplete for ${callSid}:`, issues);
      console.error(`[CLEANUP-VERIFICATION] Details:`, details);
    }
    
    return {
      isClean,
      issues,
      details
    };
    
  } catch (error) {
    console.error(`[CLEANUP-VERIFICATION] ❌ Error during cleanup verification for ${callSid}:`, error);
    return {
      isClean: false,
      issues: [`Verification error: ${error.message}`],
      details: { verificationError: error.message }
    };
  }
}

//-----------------------------------------------
// Helper: End-of-call cleanup
//-----------------------------------------------
export async function cleanupCall(callSid: string) {
  const cleanupStartTime = Date.now();
  console.log(`[CLEANUP:${callSid}] 🏁 Starting cleanup process at ${new Date().toISOString()}`);
  
  // PHASE 0: Mark call as cleaning up FIRST to prevent phantom call detection
  const state = calls[callSid];
  if (!state) { 
    console.error(`[CLEANUP:${callSid}] ❌ FAILED - Call state not found in calls object`);
    return;
  }
  
  // Log current call state before cleanup
  console.log(`[CLEANUP:${callSid}] 📊 Current state: isCleaningUp=${state.isCleaningUp}, connectionType=${state.connectionType}, userId=${state.userId}`);
  
  if (state.isCleaningUp) {
    console.warn(`[CLEANUP:${callSid}] ⚠️ Call already marked as cleaning up - potential duplicate cleanup attempt`);
  }
  
  // CRITICAL: Set cleanup flag immediately to prevent double cleanup
  state.isCleaningUp = true;
  const flagSetTime = Date.now();
  console.log(`[CLEANUP:${callSid}] ✅ Phase 0: isCleaningUp flag set (${flagSetTime - cleanupStartTime}ms)`);
  
  // PHASE 1: Use centralized cleanup orchestration
  console.log(`[CLEANUP:${callSid}] 🔄 Phase 1: Attempting centralized cleanup via XState`);
  const { performCentralizedCleanup } = await import('./xstateIntegration');

  const centralizedStartTime = Date.now();
  const success = await performCentralizedCleanup(callSid, 'server', 'call_ended');
  const centralizedDuration = Date.now() - centralizedStartTime;
  
  if (success) {
    console.log(`[CLEANUP:${callSid}] ✅ Centralized cleanup SUCCEEDED (${centralizedDuration}ms) - cleanup complete`);
    const totalDuration = Date.now() - cleanupStartTime;
    console.log(`[CLEANUP:${callSid}] 🏆 TOTAL CLEANUP TIME: ${totalDuration}ms`);
    return; // Exit early - XState handled all cleanup
  } else {
    console.error(`[CLEANUP:${callSid}] ❌ Centralized cleanup FAILED (${centralizedDuration}ms) - falling back to legacy cleanup`);
    console.log(`[CLEANUP:${callSid}] 🔧 Phase 2: Starting legacy cleanup fallback`);
    // Fall through to legacy cleanup
  }

  console.log(`Initiating legacy cleanup for call ${callSid}...`);
  
  // LEGACY CLEANUP: Only executed if no XState controller exists
  
  // Queue background uploads instead of blocking cleanup
  try {
    const { backgroundUploadService } = await import('./services/backgroundUploadService');
    
    // Queue conversation data upload
    if (state?.conversation && state.conversation.length > 0) {
      const conversationData = state.conversation.map(turn => ({
        speaker: turn.speaker,
        text: turn.text
      }));
      backgroundUploadService.queueConversationDataUpload(callSid, conversationData);
      console.log(`[LEGACY-CLEANUP] Queued conversation data upload for ${callSid}`);
    }
    
    // Stop LiveKit recording and queue egress processing if exists
    if (state?.livekitEgressId) {
      try {
        console.log(`[LEGACY-CLEANUP] Stopping LiveKit recording: ${state.livekitEgressId}`);
        const { livekitRoomService } = await import('./services/livekit/livekit-room-service');
        await livekitRoomService.stopRoomRecording(state.livekitEgressId);
        console.log(`[LEGACY-CLEANUP] ✅ LiveKit recording stopped successfully`);
      } catch (recordingStopError) {
        console.error(`[LEGACY-CLEANUP] ❌ Failed to stop LiveKit recording:`, recordingStopError);
      }
      
      backgroundUploadService.queueLiveKitEgressProcessing(callSid, state.livekitEgressId);
      console.log(`[LEGACY-CLEANUP] Queued LiveKit egress processing for ${callSid}`);
    }
    
    // Queue audio recording upload if exists
    if (state?.recordingStoragePath) {
      backgroundUploadService.queueAudioRecordingUpload(callSid, state.recordingStoragePath);
      console.log(`[LEGACY-CLEANUP] Queued audio recording upload for ${callSid}`);
    }
    
  } catch (uploadError) {
    console.warn(`[LEGACY-CLEANUP] Failed to queue background uploads for ${callSid}:`, uploadError);
  }
  
  // Clean up local files immediately (non-blocking)
  await cleanupCallFilesImmediately(callSid);

  // Clean up finals-only processing timeout
  if (state.partialTimeoutId) {
    clearTimeout(state.partialTimeoutId);
    state.partialTimeoutId = null;
    console.log(`[CLEANUP] Cleared partial transcript timeout for call ${callSid}`);
  }

  // Clean up LiveKit RTC handler if it exists
  if (state.livekitRTCHandler) {
    console.log(`[CLEANUP] Disconnecting LiveKit RTC handler for call ${callSid}`);
    try {
      await state.livekitRTCHandler.disconnect();
    } catch (error) {
      console.error(`[CLEANUP] Error disconnecting LiveKit RTC handler:`, error);
    }
    delete state.livekitRTCHandler;
  }

  // PHASE 2: Complete controller cleanup after LiveKit operations (legacy code)
  if (callControllers[callSid]) {
    console.log(`[XState:${callSid}] Completing legacy controller cleanup after LiveKit operations...`);
    
    // Wait a brief moment for any remaining async LiveKit events to drain
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Delete controller (XState cleanup already handled this)
    delete callControllers[callSid];
    console.log(`[XState:${callSid}] Legacy controller cleanup completed and deleted`);
  }

  // Pause session if it exists (don't complete it - user might call back)
  if (state.sessionId) {
    SessionManager.pauseSession(state.sessionId).catch(err => {
      console.error(`[SessionManager] Failed to pause session ${state.sessionId} for call ${callSid}:`, err);
    });
  }

  // Clean up TTS connection pool for this call
  const ttsConnection = ttsConnectionPool.get(callSid);
  if (ttsConnection) {
    console.log(`[TTS-POOL] Cleaning up connection for ${callSid}`);
    ttsConnection.synthesizer.close();
    ttsConnectionPool.delete(callSid);
  }

  // --- BEGIN UPDATE CALL HISTORY ON END ---
  const callEndTime = new Date();
  
  const callDurationSeconds = state.callStartTime ? 
    Math.floor((callEndTime.getTime() - state.callStartTime) / 1000) : null;
  
  console.log(`Call ${callSid} ended. Duration: ${callDurationSeconds}s`);
  
  // Get recording storage path if available
  const recordingPath = state.recordingStoragePath || null;
  
  // Update the call record non-blockingly
  updateCallRecordOnEnd(callSid, callEndTime, callDurationSeconds, recordingPath).catch(err => {
    console.error(`[DB] Failed to update call record on end for ${callSid}:`, err);
  });
  // --- END UPDATE CALL HISTORY ON END ---

  // Close recordFfmpeg
  if (state.recordFfmpeg) {
    console.log(`[Cleanup ${callSid}] Closing recordFfmpeg...`);
    
    // Capture all necessary state before ending the process to prevent accessing undefined later
    const capturedCallStartTime = state.callStartTime;
    const capturedRecordingActualStartTime = state.recordingActualStartTime;
    // Deep copy the turnTimings array to prevent issues with undefined properties
    const capturedTurnTimings = JSON.parse(JSON.stringify(state.turnTimings || []));
    const capturedAudioDir = AUDIO_DIR;
    
    // Log the captured timing data for debugging
    console.log(`[Mixer ${callSid}] Captured turnTimings for ${capturedTurnTimings.length} turns before cleanup`);
    
    state.recordFfmpeg.stdin.end();

    // Wait for the ffmpeg process to close to ensure the file is written
    state.recordFfmpeg.on('close', async (code) => {
      console.log(`recordFfmpeg for ${callSid} exited with code ${code}`);

      let uploadSuccess = false;
      let fileToUploadPath: string | null = null;
      let targetFileName: string = `${callSid}.wav`; // Default to original if mixing fails or isn't needed

      if (code === 0) {
        const mainRecordingPath = path.join(__dirname, `../recordings/${callSid}_caller.wav`);
        const mixedOutputPath = path.join(__dirname, `../recordings/${callSid}.wav`);

        // --- Start Precise Mixing Logic ---
        const ttsInputs: { timing: typeof state.turnTimings[0]; path: string }[] = []; // Holds validated TTS info
 
        if (fs.existsSync(mainRecordingPath) && capturedTurnTimings && capturedTurnTimings.length > 0 && capturedCallStartTime) {
          // Use the more accurate recordingActualStartTime if available, otherwise fallback to callStartTime
          const recordingStartMs = capturedRecordingActualStartTime || capturedCallStartTime;
          console.log(`[Mixer ${callSid}] Effective recording start time used for mixing: ${recordingStartMs} (Actual: ${capturedRecordingActualStartTime}, CallStart: ${capturedCallStartTime})`);
 
          // Log the timing data for debugging
          console.log(`[Mixer ${callSid}] Processing ${capturedTurnTimings.length} turns for mixing`);
          
          for (const timing of capturedTurnTimings) {
            // Debug log each timing object
            console.log(`[Mixer ${callSid}] Processing turn ${timing.turnIndex}:`, {
              ttsFile: timing.ttsFilename,
              audioStart: timing.audioStreamingStartAt,
              interruption: timing.interruptionTimestamp,
              durationMs: timing.audioDurationMs,
              hasInterruption: !!timing.interruptionTimestamp
            });
            
            // Check if this turn has the necessary info for mixing with stronger type checking
            if (timing && timing.audioStreamingStartAt && typeof timing.audioStreamingStartAt === 'number' && timing.ttsFilename) {
              try {
                const ttsFilePath = path.join(capturedAudioDir, timing.ttsFilename);
 
                if (fs.existsSync(ttsFilePath)) {
                  console.log(`[Mixer ${callSid}] Found TTS file ${timing.ttsFilename} for turn ${timing.turnIndex}.`);
                  ttsInputs.push({ timing, path: ttsFilePath });
                } else {
                  console.warn(`[Mixer ${callSid}] TTS file ${timing.ttsFilename} for turn ${timing.turnIndex} not found.`);
                }
              } catch (err) {
                console.error(`[Mixer ${callSid}] Error accessing TTS file for turn ${timing.turnIndex}:`, err);
              }
            } else {
              console.log(`[Mixer ${callSid}] Skipping turn ${timing.turnIndex} for mixing - missing audioStreamingStartAt or ttsFilename.`);
            }
          }
 
          // Proceed with mixing if we found TTS files to mix
          if (ttsInputs.length > 0) {
            const inputArgs: string[] = ['-i', mainRecordingPath];
            const filterComplexParts: string[] = [];
 
            ttsInputs.forEach((ttsInput, index) => {
              if (!ttsInput.timing) {
              console.warn(`[Mixer ${callSid}] TTS input at index ${index} skipped: timing data is null.`);
              return; // continue to next ttsInput
            }
            const streamIndex = index + 1; // 0 is main recording
              inputArgs.push('-i', ttsInput.path);
 
              // Calculate delay
              const delayMs = Math.max(0, ttsInput.timing.audioStreamingStartAt! - recordingStartMs);
              let streamFilter = '';
              
              // Check for valid interruption
              const hasValidInterruption = ttsInput.timing.interruptionTimestamp && 
                                        ttsInput.timing.interruptionTimestamp > ttsInput.timing.audioStreamingStartAt! &&
                                        ttsInput.timing.interruptionTimestamp < (ttsInput.timing.audioStreamingStartAt! + (ttsInput.timing.audioDurationMs || 0));
              
              if (hasValidInterruption) {
                const playDurationMs = ttsInput.timing.interruptionTimestamp! - ttsInput.timing.audioStreamingStartAt!;
                const playDurationSec = Math.max(0.1, (playDurationMs / 1000)).toFixed(6); // Ensure at least 100ms to avoid FFmpeg errors
                
                // Debug logging for interruption details
                console.log(`[Mixer Debug ${callSid}] Turn ${ttsInput.timing.turnIndex} Interruption Details:`);
                console.log(`  audioStreamingStartAt: ${ttsInput.timing.audioStreamingStartAt}`);
                console.log(`  audioDurationMs: ${ttsInput.timing.audioDurationMs}`);
                console.log(`  interruptionTimestamp: ${ttsInput.timing.interruptionTimestamp}`);
                console.log(`  Calculated playDurationMs: ${playDurationMs}`);
                console.log(`  Calculated playDurationSec: ${playDurationSec}`);
                
                console.log(`[Mixer ${callSid}] Turn ${ttsInput.timing.turnIndex} interrupted. Applying atrim=0:${playDurationSec}`);
                streamFilter = `[${streamIndex}:a]atrim=0:${playDurationSec},adelay=${delayMs}|${delayMs}[d${streamIndex}]`;
              } else {
                // No interruption, just apply delay
                streamFilter = `[${streamIndex}:a]adelay=${delayMs}|${delayMs}[d${streamIndex}]`;
              }
              filterComplexParts.push(streamFilter);
            });
 
            // Build the final amix part
            const mixInputs = ttsInputs.map((_, index) => `[d${index + 1}]`).join('');
            // Disable amix normalization to prevent volume gradient issues
            const amixFilter = `[0:a]${mixInputs}amix=inputs=${ttsInputs.length + 1}:normalize=false[a]`;
            filterComplexParts.push(amixFilter);
 
            const ffmpegArgs: string[] = [
              ...inputArgs,
              '-filter_complex', filterComplexParts.join(';'),
              '-map', '[a]', // Map the final mixed output
              '-ac', '1', // Force mono
              '-ar', '8000', // Ensure output sample rate is 8kHz
              '-acodec', 'pcm_mulaw', // Output µ-law encoded WAV
              mixedOutputPath
            ];

            console.log(`[Mixer ${callSid}] Starting precise ffmpeg mixing process...`);
            console.log(`[Mixer ${callSid}] ffmpeg command args: ${ffmpegArgs.join(' ')}`); // Log command for debug

            try {
              await new Promise<void>((resolve, reject) => {
                const mixFfmpeg = spawn('ffmpeg', ffmpegArgs);

                let ffmpegStderr = '';
                mixFfmpeg.stderr.on('data', (data) => { // Capture stderr
                   ffmpegStderr += data.toString();
                });

                mixFfmpeg.on('close', (mixCode) => {
                  // Log full FFmpeg stderr output
                  console.log(`[Mixer ${callSid}] FFmpeg process finished with code: ${mixCode}`);
                  if (ffmpegStderr.length > 0) {
                    console.log(`[Mixer ${callSid}] Full FFmpeg stderr output:\n>>>>>>>>>>\n${ffmpegStderr}\n<<<<<<<<<<`);
                  } else {
                    console.log(`[Mixer ${callSid}] No stderr output captured from FFmpeg.`);
                  }
                  
                  if (mixCode === 0) {
                    console.log(`[Mixer ${callSid}] Successfully created mixed recording: ${mixedOutputPath}`);
                    fileToUploadPath = mixedOutputPath;
                    targetFileName = `${callSid}.wav`; // Set target for upload
                    resolve();
                  } else {
                    console.error(`[Mixer ${callSid}] ffmpeg mixing process failed with code ${mixCode}. Uploading original.`);
                    console.error(`[Mixer ${callSid}] ffmpeg stderr: ${ffmpegStderr}`); // Log captured stderr on error
                    fileToUploadPath = mainRecordingPath; // Fallback to original
                    targetFileName = `${callSid}_caller.wav`;
                    resolve(); // Resolve even on failure to allow original upload
                  }
                });

                mixFfmpeg.on('error', (mixErr) => {
                  console.error(`[Mixer ${callSid}] Failed to start ffmpeg mixing process:`, mixErr);
                  fileToUploadPath = mainRecordingPath; // Fallback to original
                  targetFileName = `${callSid}_caller.wav`;
                  resolve(); // Resolve even on failure
                });
              });
            } catch (mixErr) {
              console.error(`[Mixer ${callSid}] Error during mixing process:`, mixErr);
              fileToUploadPath = mainRecordingPath; // Fallback to original
              targetFileName = `${callSid}_caller.wav`;
            }
          } else {
            console.warn(`[Mixer ${callSid}] Skipping mixing: Main recording not found, missing timing data, or necessary start timestamps.`);
            // Determine fallback upload path even if mixing is skipped
            fileToUploadPath = fs.existsSync(mainRecordingPath) ? mainRecordingPath : null;
            targetFileName = fs.existsSync(mainRecordingPath) ? `${callSid}_caller.wav` : `${callSid}.wav`;
          }
          // --- End Precise Mixing Logic ---

          // --- Start Upload Logic --- (Modified for Opus Transcoding)
          if (fileToUploadPath && fs.existsSync(fileToUploadPath)) {
            const opusOutputPath = fileToUploadPath.replace(/\.wav$/, '.opus');
            const targetOpusFileName = targetFileName.replace(/\.wav$/, '.opus');
            let transcodingSuccess = false;

            try {
              console.log(`[Opus Transcode] Transcoding ${fileToUploadPath} to ${opusOutputPath}...`);
              await new Promise<void>((resolve, reject) => {
                const transcodeFfmpeg = spawn('ffmpeg', [
                  '-i', fileToUploadPath,        // Input WAV file
                  '-acodec', 'libopus',        // Output Opus codec
                  '-b:a', '16k',             // Bitrate (e.g., 16kbps for good quality voice)
                  '-application', 'voip',     // Optimize for voice
                  '-f', 'opus',              // Output format
                  opusOutputPath
                ]);

                let ffmpegStderr = '';
                transcodeFfmpeg.stderr.on('data', (data) => { ffmpegStderr += data.toString(); });

                transcodeFfmpeg.on('close', (transcodeCode) => {
                  // Log full FFmpeg stderr output
                  console.log(`[Opus Transcode] FFmpeg process finished with code: ${transcodeCode}`);
                  if (ffmpegStderr.length > 0) {
                    console.log(`[Opus Transcode] Full FFmpeg stderr output:\n>>>>>>>>>>\n${ffmpegStderr}\n<<<<<<<<<<`);
                  } else {
                    console.log(`[Opus Transcode] No stderr output captured from FFmpeg.`);
                  }
                  
                  if (transcodeCode === 0) {
                    console.log(`[Opus Transcode] Successfully transcoded to ${opusOutputPath}`);
                    transcodingSuccess = true;
                    resolve();
                  } else {
                    console.error(`[Opus Transcode] ffmpeg transcoding failed with code ${transcodeCode}.`);
                    console.error(`[Opus Transcode] ffmpeg stderr: ${ffmpegStderr}`);
                    reject(new Error(`Transcoding failed with code ${transcodeCode}`));
                  }
                });

                transcodeFfmpeg.on('error', (transcodeErr) => {
                  console.error(`[Opus Transcode] Failed to start ffmpeg transcoding process:`, transcodeErr);
                  reject(transcodeErr);
                });
              });
            } catch (err) {
              console.error(`[Opus Transcode/Upload] Error during process for ${callSid}:`, err);
            }
            
            // If transcoding was successful, proceed with upload
            if (transcodingSuccess && fs.existsSync(opusOutputPath)) {
              console.log(`[Supabase Storage] Attempting to upload ${opusOutputPath} as ${targetOpusFileName}...`);
              const fileContent = fs.readFileSync(opusOutputPath);
              const { data: uploadData, error: uploadError } = await supabase.storage
                .from('call_recordings')
                .upload(targetOpusFileName, fileContent, {
                  contentType: 'audio/opus', // Set correct content type
                  upsert: true
                });

              if (uploadError) {
                console.error(`[Supabase Storage] Error uploading ${targetOpusFileName}:`, uploadError); // Changed error.message to error
              } else {
                console.log(`[Supabase Storage] Successfully uploaded ${targetOpusFileName} from ${opusOutputPath}`);
                uploadSuccess = true; // Mark overall success
                
                // Store just the storage path for the Opus recording
                const storagePath = `call_recordings/${targetOpusFileName}`;
                state.recordingStoragePath = storagePath;
                console.log(`[Supabase Storage] Stored storage path: ${storagePath}`);
                
                // Update the database with the recording path
                const updateTime = new Date();
                updateCallRecordingPath(callSid, storagePath, updateTime).catch(err => {
                  console.error(`[DB] Failed to update recording path for ${callSid}:`, err); // Changed error.message to error
                });
              }
            } else {
               console.warn(`[Supabase Storage] Opus file not found or transcoding failed: ${opusOutputPath}`);
            }
          } else {
             console.warn(`[Supabase Storage] WAV file to transcode not found or not specified: ${fileToUploadPath}`);
          }
          // --- End Upload Logic ---

        } else {
          console.warn(`[Supabase Storage] Skipping mixing/upload for ${callSid} because recording ffmpeg exited with code ${code}`);
        }
        
        // --- Start Cleanup Logic ---
        // This cleanup should only happen if the initial ffmpeg process was successful (code === 0)
        // and the upload was also successful.
        if (uploadSuccess) { // Only cleanup local files if upload was successful
           cleanupLocalFiles(callSid, capturedTurnTimings);
        } else {
          // Log if cleanup is skipped due to upload failure, even if ffmpeg succeeded
          if (code === 0) { 
              console.warn(`[Cleanup ${callSid}] Skipping local file cleanup because Supabase upload failed or was skipped (ffmpeg code: ${code}).`);
          }
          // No cleanup needed if ffmpeg failed (code !== 0) as upload wouldn't have happened.
        }
        // --- End Cleanup Logic ---

      } else {
        console.warn(`[Supabase Storage] Skipping mixing/upload for ${callSid} because recording ffmpeg exited with code ${code}`);
      }
      
      // --- Start Cleanup Logic ---
      // This cleanup should only happen if the initial ffmpeg process was successful (code === 0)
      // and the upload was also successful.
      if (uploadSuccess) { // Only cleanup local files if upload was successful
         cleanupLocalFiles(callSid, capturedTurnTimings);
      } else {
        // Log if cleanup is skipped due to upload failure, even if ffmpeg succeeded
        if (code === 0) { 
            console.warn(`[Cleanup ${callSid}] Skipping local file cleanup because Supabase upload failed or was skipped (ffmpeg code: ${code}).`);
        }
        // No cleanup needed if ffmpeg failed (code !== 0) as upload wouldn't have happened.
      }
      // --- End Cleanup Logic ---

    });

    state.recordFfmpeg = undefined;
  }
  // Close decodeFfmpeg
  if (state.decodeFfmpeg) {
    console.log(`[Cleanup ${callSid}] Closing decodeFfmpeg...`);
    state.decodeFfmpeg.stdin.end()
    state.decodeFfmpeg = undefined
  }
  // Close the active STT provider
  if (state.sttProvider) {
    console.log(`[Cleanup ${callSid}] Closing STT provider (${STT_PROVIDER})...`);
    try {
      state.sttProvider.close();
    } catch (err) {
      console.error(`[Cleanup ${callSid}] Error closing STT provider:`, err);
    }
    state.sttProvider = undefined;
  } else {
    console.log(`[Cleanup ${callSid}] No active STT provider found to close.`);
  }
  
  // Kill any current audio streaming process (TTS playback)
  if (state.currentFfmpegProcess) {
    console.log(`[Cleanup ${callSid}] Killing current audio streaming process...`);
    try {
      state.currentFfmpegProcess.kill();
    } catch (err) {
      console.error(`[Cleanup ${callSid}] Error killing TTS ffmpeg process: ${err}`);
    }
    state.currentFfmpegProcess = undefined;
  }
  
  // Clear any audio duration timeout
  if (state.currentSpeakingTimeout) {
    console.log(`[Cleanup ${callSid}] Clearing audio duration timeout...`);
    clearTimeout(state.currentSpeakingTimeout);
    state.currentSpeakingTimeout = undefined;
  }

  // Clear any pending finalization timeout
  if (state.pendingFinalizeTimeout) {
    console.log(`[Cleanup ${callSid}] Clearing pending finalization timeout...`);
    clearTimeout(state.pendingFinalizeTimeout);
    state.pendingFinalizeTimeout = undefined;
  }

  // Clear inactivity timeout
  if (state.inactivityTimeoutId) {
    console.log(`[Cleanup ${callSid}] Clearing inactivity timeout...`);
    clearTimeout(state.inactivityTimeoutId);
    state.inactivityTimeoutId = undefined;
  }

  // Clean up inactivity log timestamp to prevent memory leaks
  if (inactivityLogTimestamps[callSid]) {
    delete inactivityLogTimestamps[callSid];
  }

  // SINGLE SOURCE OF TRUTH: Check XState infrastructure for any pending transcript
  const { getTranscriptState } = require('./infrastructureHelpers');
  const transcriptState = getTranscriptState(callSid);
  const pendingText = transcriptState?.currentText?.trim();
  
  if (pendingText) {
    console.log(`[Cleanup ${callSid}] Found pending transcript in XState infrastructure: "${pendingText}"`);
    // XState infrastructure will handle final turn processing during cleanup
  }
  
  // Save the final conversation state
  const { conversation } = state;
  saveConversation(callSid, conversation);

  // Log all turn timings for the completed call
  console.log(`
=============== CALL TIMING SUMMARY (${callSid}) ===============`);
  if (state.turnTimings && state.turnTimings.length > 0) {
    const calcDuration = (t1?: number, t2?: number) => (
      t1 && t2 ? `${((t2 - t1) / 1000).toFixed(2)}s` : 'N/A'
    );

    state.turnTimings.forEach(timings => {
      console.log(`
--- Turn ${timings.turnIndex} ---`);
      console.log(`  Silence -> Transcript: ${calcDuration(timings.silenceDetectedAt, timings.transcriptReceivedAt)}`);
      console.log(`  Transcript -> RAG Req: ${calcDuration(timings.transcriptReceivedAt, timings.ragRequestStartAt)}`);
      console.log(`  RAG Req -> RAG Res:  ${calcDuration(timings.ragRequestStartAt, timings.ragResponseReceivedAt)}`);
      console.log(`  RAG Res -> TTS Start: ${calcDuration(timings.ragResponseReceivedAt, timings.audioGenerationStartAt)}`);
      console.log(`  TTS Start -> TTS End:  ${calcDuration(timings.audioGenerationStartAt, timings.audioGenerationEndAt)}`);
      console.log(`  TTS End -> Stream Start: ${calcDuration(timings.audioGenerationEndAt, timings.audioStreamingStartAt)}`);
            const streamEndApproximation = (timings.audioStreamingStartAt && timings.audioDurationMs) ? timings.audioStreamingStartAt + timings.audioDurationMs : undefined;
      console.log(`  Stream Duration (approx): ${calcDuration(timings.audioStreamingStartAt, streamEndApproximation)} (using file duration)`);
    });
  } else {
    console.log("No turn timing data recorded for this call.");
  }
  console.log(`=================== END CALL SUMMARY (${callSid}) ===================\n`);

  // Reset media packet counter
  if (mediaPacketCounts[callSid]) {
    delete mediaPacketCounts[callSid];
  }

  // Note: Audio and conversation file cleanup is now handled within the recordFfmpeg close handler
  // to ensure mixing and uploading complete first.

  // Clean up any pending TTS completion timer
  const callState = calls[callSid];
  if (callState?.ttsCompletionTimer) {
    clearTimeout(callState.ttsCompletionTimer);
    console.log(`[CLEANUP:${callSid}] ✅ TTS completion timer cleared`);
  }

  // CRITICAL: Verify isCleaningUp flag before final deletion
  if (callState?.isCleaningUp) {
    console.log(`[CLEANUP:${callSid}] ⚠️ isCleaningUp flag still true before deletion - this is expected`);
  } else {
    console.warn(`[CLEANUP:${callSid}] ⚠️ isCleaningUp flag already false before deletion - potential race condition`);
  }

  // Delete the call state - THIS REMOVES THE PHANTOM CALL
  console.log(`[CLEANUP:${callSid}] 🗑️ Deleting call state from calls object`);
  delete calls[callSid];
  
  // Verify deletion succeeded
  if (calls[callSid]) {
    console.error(`[CLEANUP:${callSid}] ❌ CRITICAL ERROR: Call state deletion FAILED - phantom call will persist!`);
  } else {
    console.log(`[CLEANUP:${callSid}] ✅ Call state deletion CONFIRMED - phantom call prevention successful`);
  }
  
  // Note: XState controller cleanup is now handled earlier in the cleanup sequence
  // to prevent race conditions with LiveKit events
  
  // Clean up VAD rate limiter to prevent memory leaks
  try {
    vadRateLimiter.cleanupCall(callSid);
    console.log(`[CLEANUP:${callSid}] ✅ VAD rate limiter cleanup completed`);
  } catch (err) {
    console.error(`[CLEANUP:${callSid}] ❌ VAD rate limiter cleanup failed:`, err);
  }
  
  const totalCleanupTime = Date.now() - cleanupStartTime;
  console.log(`[CLEANUP:${callSid}] 🏆 LEGACY CLEANUP COMPLETE - Total time: ${totalCleanupTime}ms`);
  console.log(`[CLEANUP:${callSid}] 🔐 Call state removed - ready for new calls from this user`);
}

// Function to clean up audio files (TTS responses + recordings) for a specific call
// Should be called AFTER mixing/uploading is done.
function cleanupLocalFiles(callSid: string, completedTurnTimings: typeof calls[string]['turnTimings']) {
  console.log(`[Cleanup ${callSid}] Cleaning up local audio and conversation files...`);
  // Clean ALL TTS response files for this call (both tracked and untracked)
  if (fs.existsSync(AUDIO_DIR)) {
    const audioFiles = fs.readdirSync(AUDIO_DIR);
    let deletedCount = 0;
    for (const file of audioFiles) {
      // Delete any audio file that contains the callSid in its filename
      if (file.includes(callSid) && (file.endsWith('.wav') || file.endsWith('.mp3') || file.endsWith('.opus'))) {
        const filePath = path.join(AUDIO_DIR, file);
        try {
          fs.unlinkSync(filePath);
          deletedCount++;
          console.log(`[Cleanup ${callSid}] Deleted audio file: ${filePath}`);
        } catch (err) {
          console.error(`[Cleanup ${callSid}] Failed to delete audio file ${filePath}:`, err);
        }
      }
    }
    console.log(`[Cleanup ${callSid}] Deleted ${deletedCount} audio files from ${AUDIO_DIR}`);
  }

  // Also clean temporary files in /tmp that might be related to this call
  const tmpFiles = ['/tmp', '/var/tmp'];
  for (const tmpDir of tmpFiles) {
    if (fs.existsSync(tmpDir)) {
      try {
        const tempFiles = fs.readdirSync(tmpDir);
        let tempDeletedCount = 0;
        for (const file of tempFiles) {
          // Delete any temp file that contains the callSid and looks like audio
          if (file.includes(callSid) && (file.includes('response_') || file.includes('stream') || file.includes('tts'))) {
            const filePath = path.join(tmpDir, file);
            try {
              fs.unlinkSync(filePath);
              tempDeletedCount++;
              console.log(`[Cleanup ${callSid}] Deleted temp file: ${filePath}`);
            } catch (err) {
              console.error(`[Cleanup ${callSid}] Failed to delete temp file ${filePath}:`, err);
            }
          }
        }
        if (tempDeletedCount > 0) {
          console.log(`[Cleanup ${callSid}] Deleted ${tempDeletedCount} temp files from ${tmpDir}`);
        }
      } catch (err) {
        console.warn(`[Cleanup ${callSid}] Could not scan temp directory ${tmpDir}:`, err);
      }
    }
  }

  // Clean recording files (original WAV, mixed WAV, and final Opus)
  const recordingsDir = path.join(__dirname, '../recordings');
  if (fs.existsSync(recordingsDir)) {
    const recordingFiles = fs.readdirSync(recordingsDir);
    for (const file of recordingFiles) {
      if (file.startsWith(callSid) && (file.endsWith('.wav') || file.endsWith('.opus'))) { 
        const filePath = path.join(recordingsDir, file);
        try {
          fs.unlinkSync(filePath);
          console.log(`[Cleanup ${callSid}] Deleted recording file: ${filePath}`);
        } catch (err) {
          console.error(`[Cleanup ${callSid}] Failed to delete recording file ${filePath}:`, err);
        }
      }
    }
  }

  // Clean up conversation files
  if (!fs.existsSync(CONVERSATIONS_DIR)) return;
  try {
    const filePath = path.join(CONVERSATIONS_DIR, `${callSid}.json`);
    if (fs.existsSync(filePath)) {
      const completedDir = path.join(CONVERSATIONS_DIR, 'completed');
      if (!fs.existsSync(completedDir)) {
        fs.mkdirSync(completedDir, { recursive: true });
      }
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const newPath = path.join(completedDir, `${callSid}_${timestamp}.json`);
      fs.renameSync(filePath, newPath);
      console.log(`[Cleanup ${callSid}] Moved conversation file to completed directory`);
    }
  } catch (err) {
    console.error(`[Cleanup ${callSid}] Failed to cleanup conversation file:`, err);
  }
}

// Set up periodic audio file cleanup in case of abrupt server termination
function setupPeriodicCleanup() {
  // Clean up audio files older than 1 hour every 15 minutes
  setInterval(() => {
    try {
      if (!fs.existsSync(AUDIO_DIR)) return;
      
      const files = fs.readdirSync(AUDIO_DIR);
      const now = Date.now();
      const ONE_HOUR = 60 * 60 * 1000;
      
      for (const file of files) {
        const filePath = path.join(AUDIO_DIR, file);
        const stats = fs.statSync(filePath);
        
        // If file is older than 1 hour, delete it
        if (now - stats.mtimeMs > ONE_HOUR) {
          try {
            fs.unlinkSync(filePath);
            console.log(`Deleted old audio file: ${filePath}`);
          } catch (err) {
            console.error(`Failed to delete old audio file ${filePath}:`, err);
          }
        }
      }
    } catch (err) {
      console.error('Error in periodic cleanup:', err);
    }
  }, 15 * 60 * 1000); // Run every 15 minutes
}

// Handle system signals for graceful shutdown
process.on('SIGINT', function() {
  console.log('Server shutting down, cleaning up audio files...');
  cleanupAllAudioFiles();
  process.exit();
});

process.on('SIGTERM', () => {
  console.log('Server shutting down, cleaning up audio files...');
  cleanupAllAudioFiles();
  process.exit(0);
});

// Function to clean up all audio files
function cleanupAllAudioFiles() {
  if (!fs.existsSync(AUDIO_DIR)) return;

  try {
    const entries = fs.readdirSync(AUDIO_DIR);
    for (const entry of entries) {
      const entryPath = path.join(AUDIO_DIR, entry);
      try {
        const stat = fs.statSync(entryPath);
        if (stat.isDirectory()) {
          fs.rmSync(entryPath, { recursive: true, force: true });
          console.log(`Successfully deleted directory: ${entryPath}`);
        } else {
          fs.unlinkSync(entryPath);
          console.log(`Successfully deleted file: ${entryPath}`);
        }
      } catch (e) {
        console.error(`Failed to delete ${entryPath}:`, e);
      }
    }
    console.log('All audio files and subdirectories in audio_responses cleaned up');
  } catch (err) {
    console.error('Error cleaning up audio files and subdirectories:', err);
  }
}

//-----------------------------------------------
// 3) handleVadAndTurns => splits chunk, calls VAD (No changes needed here for STT provider switch)
//-----------------------------------------------

export async function handleVadAndTurns(callSid: string, chunk: Buffer, connectionType: ConnectionType = ConnectionType.TWILIO) {
  const state = calls[callSid];
  if (!state) return;

  const audioConfig = getAudioConfig(connectionType);
  
  // Verification Logging - only log once per call to avoid spam
  if (!state.audioPipelineConfigLogged) {
  console.log(`[Audio Pipeline] callSid: ${callSid}, type: ${connectionType}, config: ${JSON.stringify({
    inputSampleRate: audioConfig.input.sampleRate,
    outputSampleRate: audioConfig.output.sampleRate,
    frameSize: audioConfig.output.frameSize,
    vadEndpoint: audioConfig.vad.endpoint,
    sttSampleRate: audioConfig.stt.sampleRate
  })}`);
    state.audioPipelineConfigLogged = true;
  }

  // --- START OF NEW LOGIC ---

  if (connectionType === ConnectionType.LIVEKIT) {
    // LiveKit sends 10ms (320-byte) chunks. We must combine two to make a 20ms (640-byte) frame.
    
    // Initialize buffer if it doesn't exist
    if (!state.vadBuffer) {
      state.vadBuffer = Buffer.alloc(0);
      }

    // Add the new chunk to our buffer
    const combinedBuffer = Buffer.concat([state.vadBuffer, chunk]);

    if (combinedBuffer.length >= audioConfig.output.frameSize) {
      // We have enough data for at least one full 20ms frame
      const frameToSend = combinedBuffer.slice(0, audioConfig.output.frameSize);
        
      // Keep any leftover data for the next round
      state.vadBuffer = combinedBuffer.slice(audioConfig.output.frameSize);
      
      // Removed excessive frame-by-frame logging for cleaner output
      
      // --- ADD AUDIO SAMPLE DIAGNOSTICS ---
        const samples = [];
      for (let i = 0; i < frameToSend.length; i += 2) {
        samples.push(frameToSend.readInt16LE(i));
        }
        const maxSample = Math.max(...samples.map(Math.abs));
      // Only continue if the frame is NOT silent
      if (maxSample > 100) {
          // Audio sample logging completely disabled to reduce log spam
      }
      // --- END DIAGNOSTIC BLOCK ---
      
      try {
        // **CRITICAL: Check VAD rate limit to prevent flooding and call unresponsiveness**
        if (!vadRateLimiter.shouldAllowRequest(callSid)) {
          console.warn(`[VAD Rate Limit] ⚠️ VAD request blocked for call ${callSid} - rate limit exceeded`);
          // Return a safe default to avoid blocking the call
          return { 
            isVoice: false, 
            consecutiveFrames: 0, 
            frameInfo: { skipped: true, reason: 'rate_limited' },
            timestamp: Date.now() 
          };
        }
        
        // **This includes the fix from Task 1**
        const vadUrl = `${audioConfig.vad.endpoint}?callSid=${encodeURIComponent(callSid)}&connectionType=${connectionType}`;
        
        const response = await axios.post(vadUrl, frameToSend, {
          headers: { 'Content-Type': 'application/octet-stream' },
          timeout: audioConfig.vad.timeout,
          validateStatus: (status) => status === 200,
        });

        // Smart VAD diagnostics - only log highest probability per second
        const currentTime = Date.now();
        const vadData = response.data;
        const probability = vadData.probability || 0;
        
        // *** RESTORED VAD PROCESSING LOGIC (LIVEKIT PATH) ***
        // Process VAD decision for interruption and turn management
        const interruptThreshold = getInterruptionThreshold(connectionType);
        
        // Initialize diagnostics for this call if needed
        if (!vadDiagnostics[callSid]) {
          vadDiagnostics[callSid] = { highestProb: 0, bestSamples: '', lastLogTime: 0 };
        }
        
        const diag = vadDiagnostics[callSid];
        
        // Track the highest probability in this second
        if (probability > diag.highestProb) {
          diag.highestProb = probability;
          diag.bestSamples = `Max sample: ${maxSample}, First 10: [${samples.slice(0, 10).join(',')}]`;
        }
        
        // Log once per second with the highest probability
        if (currentTime - diag.lastLogTime >= 1000) {
          if (diag.highestProb > 0) {
            // DEBUG: Show VAD probabilities to help debug interruption issues
            console.log(`🎤 [VAD-PROBABILITY-DEBUG] ${callSid}: Highest VAD probability in last second: ${diag.highestProb.toFixed(3)} (threshold: ${interruptThreshold}, isModelSpeaking: ${state.isModelSpeaking})`);
          }
          // Reset for next second
          diag.highestProb = 0;
          diag.bestSamples = '';
          diag.lastLogTime = currentTime;
        }
        
        // Track threshold effectiveness
        if (!thresholdDiagnostics[callSid]) {
          thresholdDiagnostics[callSid] = {
            connectionType,
            totalFrames: 0,
            speechFrames: 0,
            interruptionAttempts: 0,
            successfulInterruptions: 0,
            thresholdUsed: interruptThreshold
          };
        }
        thresholdDiagnostics[callSid].totalFrames++;
        
        // CRITICAL FIX: Set hasSpeechDetected for ANY speech detection, not just interruption-level
        if (vadData.speech) {
          state.speechFramesInCurrentTurn++;
          state.consecutiveSilenceFrames = 0;
          state.hasSpeechDetected = true;
          
          // DEBUG: Log all speech detection for interruption debugging
          if (state.isModelSpeaking) {
            console.log(`🔍 [VAD-SPEECH-DEBUG] ${callSid}: Speech detected while model speaking - probability: ${probability.toFixed(3)}, threshold: ${interruptThreshold}, above threshold: ${probability > interruptThreshold}`);
          }
          
          // CRITICAL FIX: Send SPEECH_FRAME event to XState machine to sync context
          sendSpeechFrame(callSid);
          
          // Send activity detection event to XState for inactivity tracking
          const controller = callControllers[callSid];
          if (controller?.machine) {
            controller.machine.send({
              type: 'ACTIVITY_DETECTED',
              source: 'speech',
              callSid: callSid,
              timestamp: Date.now()
            });
          }
          
          // Reset inactivity timer on speech detection (user is actively speaking)
          resetInactivityTimer(callSid);
          
          // Only count as significant speech for interruption purposes if above threshold
          if (probability > interruptThreshold) {
            thresholdDiagnostics[callSid].speechFrames++;
            
            // Check for interruption (user speaking while model is speaking) - focused logging
            if (state.isModelSpeaking) {
              // ===== VAD FALSE POSITIVE DETECTION DURING TTS =====
              const vadDetectionDetails = {
                callSid,
                timestamp: Date.now(),
                timestampFormatted: new Date().toISOString(),
                vadProbability: probability,
                interruptThreshold,
                connectionType,
                ttsContext: {
                  isModelSpeaking: state.isModelSpeaking,
                  lastTtsStartTime: state.lastTtsStartTime,
                  currentlyPlayingTurnIndex: state.currentlyPlayingTurnIndex,
                  ttsPlaybackDuration: state.lastTtsStartTime ? Date.now() - state.lastTtsStartTime : null
                },
                audioSample: {
                  maxSampleValue: maxSample,
                  frameLength: frameToSend.length,
                  firstSamples: samples.slice(0, 5)
                }
              };
              
              console.log(`🎤 [INTERRUPT-ATTEMPT] ${callSid}: Speech detected (p=${probability.toFixed(3)}, thresh=${interruptThreshold}) while model speaking`);
              console.log(`🔍 [VAD-TTS-ANALYSIS] ${JSON.stringify(vadDetectionDetails, null, 2)}`);
              
              
              // CRITICAL FIX: IMMEDIATE audio stopping - only if audio is actually playing  
              const isActuallyPlayingAudio = state.currentlyPlayingTurnIndex !== undefined && state.livekitRTCHandler;
              if (isActuallyPlayingAudio) {
                console.log(`🚨 [IMMEDIATE-STOP] ${callSid}: Audio is playing (turn ${state.currentlyPlayingTurnIndex}), stopping IMMEDIATELY (VAD path)`);
                try {
                  // Stop audio immediately without waiting for validation
                  state.livekitRTCHandler.stopAudio();
                  console.log(`🚨 [IMMEDIATE-STOP] LiveKit audio stop called immediately for ${callSid} (VAD path)`);
                } catch (error) {
                  console.error(`❌ [IMMEDIATE-STOP] Error in immediate VAD audio stop:`, error);
                }
              } else {
                console.log(`🔍 [IMMEDIATE-STOP] ${callSid}: TTS generating but no audio playing yet (turn ${state.currentlyPlayingTurnIndex}), using normal interruption (VAD path)`);
              }
              
              // PHASE 3: Clean event-based interruption - Send XState event directly
              console.log(`🎤 [VAD-INTERRUPT-DETECTED] ${callSid}: Sending USER_INTERRUPTION_DETECTED event to XState (p=${probability.toFixed(3)})`);
              
              thresholdDiagnostics[callSid].interruptionAttempts++;
              
              // Send clean XState event - let the state machine handle the logic
              const controller = callControllers[callSid];
              if (controller?.machine) {
                controller.machine.send({
                  type: 'USER_INTERRUPTION_DETECTED',
                  callSid: callSid,
                  timestamp: Date.now(),
                  vadProbability: probability,
                  currentTurnIndex: state.currentlyPlayingTurnIndex
                });
                
                console.log(`✅ [VAD-INTERRUPT-SENT] ${callSid}: USER_INTERRUPTION_DETECTED event sent to XState machine`);
                thresholdDiagnostics[callSid].successfulInterruptions++;
              } else {
                console.log(`❌ [VAD-INTERRUPT-NO-MACHINE] ${callSid}: No XState machine found for interruption event`);
              }
            } else {
              // Check interruption capability when model isn't speaking (only for legacy mode)
              if (!ENABLE_CENTRALIZED_TURNS) {
                validateInterruptionCapability(callSid, connectionType);
              }
            }
          }
        } else {
          // Silence detected
          state.consecutiveSilenceFrames++;
          
          // Removed excessive per-frame logging for cleaner output
          
          // Log feature health diagnostics periodically
          logFeatureHealthDiagnostics(callSid, connectionType);
          
          if (state.hasSpeechDetected) {
            // Check for grace period trigger (33 frames = 0.66s)
            if (state.consecutiveSilenceFrames === SILENCE_FRAMES_THRESHOLD) {
              // PHASE 1: STT Processing Timeline Analysis - Track timing at silence threshold
              const silenceDetectionTime = Date.now();
              state.lastSilenceDetectionTime = silenceDetectionTime;
              
              // Calculate approximate speech end time (threshold frames ago)
              const frameDurationMs = 20; // 20ms per frame
              const speechEndTime = silenceDetectionTime - (SILENCE_FRAMES_THRESHOLD * frameDurationMs);
              state.lastSpeechEndTime = speechEndTime;
              
              // DIAGNOSTIC: Track VAD silence detection timing
              logDiagnostic(callSid, 'VAD-SILENCE-DETECTION', 'Silence threshold reached', {
                silenceDetectionTime,
                speechEndTime,
                silenceFrames: state.consecutiveSilenceFrames,
                thresholdFrames: SILENCE_FRAMES_THRESHOLD,
                frameDurationMs,
                calculatedDelay: silenceDetectionTime - speechEndTime,
                hasSpeechDetected: state.hasSpeechDetected,
                lastSpeechFrame: state.lastSpeechFrameTime || 'unknown'
              });
              
              console.log(`[STT-TIMING] ${callSid}: Turn meaningful silence reached at ${silenceDetectionTime}, estimated speech end at ${speechEndTime} (${SILENCE_FRAMES_THRESHOLD} frames ago)`);
              
              // NEW: Use barrier synchronization pattern instead of direct processing
              const { turnCompletionCoordinator } = require('./turnCompletionCoordinator');
              
              console.log(`[VAD-COORDINATOR] ${callSid}: Notifying coordinator of silence detection after ${state.consecutiveSilenceFrames} frames`);
              
              // DIAGNOSTIC: Track coordinator notification
              logDiagnostic(callSid, 'COORDINATOR-NOTIFY', 'Calling onSilenceDetected', {
                coordinatorCallTime: Date.now(),
                timeSinceSilenceDetection: Date.now() - silenceDetectionTime,
                consecutiveSilenceFrames: state.consecutiveSilenceFrames
              });
              
              turnCompletionCoordinator.onSilenceDetected(callSid);
              
              // DIAGNOSTIC: Track coordinator notification completion
              logDiagnostic(callSid, 'COORDINATOR-NOTIFY-COMPLETE', 'onSilenceDetected completed', {
                coordinatorCompleteTime: Date.now(),
                totalNotificationTime: Date.now() - silenceDetectionTime
              });
            }
            // Check for immediate turn finalization after minimal silence for interruption reset
            else if (state.consecutiveSilenceFrames >= RESET_INTERRUPT_SILENCE_THRESHOLD && state.consecutiveSilenceFrames < SILENCE_FRAMES_THRESHOLD) {
              // Only for interruption reset, not turn finalization
              // Note: Interruption state reset happens silently here
              // Don't finalize turn here - let the grace period logic handle it
            }
          } else {
            // Silence frame with no prior speech detected - this is normal, no logging needed
          }
        }

        // *** CRITICAL FIX: Send audio to STT provider (same as Twilio path) ***
        // This was missing in the LiveKit path, causing STT to never receive audio
        if (state.sttProvider && state.sttProvider.sendAudio) {
          state.sttProvider.sendAudio(frameToSend);
        }

      } catch (err) {
        console.error(`VAD call failed for callSid=${callSid}:`, err);
      }
    } else {
      // Not enough data for a full frame yet, just store it and wait for the next chunk
      // Buffer waiting - only log if we're waiting for too long
      if (state.vadBuffer.length > audioConfig.output.frameSize * 2) {
        console.log(`[LIVEKIT-BUFFER-WAITING] ${callSid}: Buffer size=${combinedBuffer.length}b, need ${audioConfig.output.frameSize}b, waiting for more data`);
      }
      state.vadBuffer = combinedBuffer;
    }
  } else {
    // This is the OLD logic for Twilio/Direct, which works correctly. It is preserved here.
    let leftover = state.leftover;
    const framesPlusLeftover = splitTo20msFrames(chunk, leftover, audioConfig.output.frameSize);
    leftover = framesPlusLeftover.pop()!;
    const frames = framesPlusLeftover;
    state.leftover = leftover;

    for (const frame of frames) {
      try {
        // **CRITICAL: Check VAD rate limit to prevent flooding and call unresponsiveness**
        if (!vadRateLimiter.shouldAllowRequest(callSid)) {
          console.warn(`[VAD Rate Limit] ⚠️ VAD request blocked for call ${callSid} - rate limit exceeded (Twilio/Direct path)`);
          // Continue to next frame without processing this one
          continue;
        }
        
        // **This also includes the fix from Task 1**
        const vadUrl = `${audioConfig.vad.endpoint}?callSid=${encodeURIComponent(callSid)}&connectionType=${connectionType}`;
        const response = await axios.post(vadUrl, frame, {
            headers: { 'Content-Type': 'application/octet-stream' },
            timeout: audioConfig.vad.timeout,
            validateStatus: (status) => status === 200,
        });
        
        // Smart VAD diagnostics - only log highest probability per second (Twilio/Direct path)
        const currentTime = Date.now();
        const vadData = response.data;
        const probability = vadData.probability || 0;
        
        // *** RESTORED VAD PROCESSING LOGIC (TWILIO PATH) ***
        // Process VAD decision for interruption and turn management
        const interruptThreshold = getInterruptionThreshold(connectionType);
        
        // Initialize diagnostics for this call if needed
        if (!vadDiagnostics[callSid]) {
          vadDiagnostics[callSid] = { highestProb: 0, bestSamples: 'N/A for Twilio', lastLogTime: 0 };
        }
        
        const diag = vadDiagnostics[callSid];
        
        // Track the highest probability in this second
        if (probability > diag.highestProb) {
          diag.highestProb = probability;
        }
        
        // Log once per second with the highest probability
        if (currentTime - diag.lastLogTime >= 1000) {
          if (diag.highestProb > 0) {
            // DEBUG: Show VAD probabilities to help debug interruption issues (Twilio path)
            console.log(`🎤 [VAD-PROBABILITY-DEBUG-TWILIO] ${callSid}: Highest VAD probability in last second: ${diag.highestProb.toFixed(3)} (threshold: ${interruptThreshold}, isModelSpeaking: ${state.isModelSpeaking})`);
          }
          // Reset for next second
          diag.highestProb = 0;
          diag.lastLogTime = currentTime;
        }
        
        // Track threshold effectiveness
        if (!thresholdDiagnostics[callSid]) {
          thresholdDiagnostics[callSid] = {
            connectionType,
            totalFrames: 0,
            speechFrames: 0,
            interruptionAttempts: 0,
            successfulInterruptions: 0,
            thresholdUsed: interruptThreshold
          };
        }
        thresholdDiagnostics[callSid].totalFrames++;
        
        // CRITICAL FIX: Set hasSpeechDetected for ANY speech detection, not just interruption-level
        if (vadData.speech) {
          state.speechFramesInCurrentTurn++;
          state.consecutiveSilenceFrames = 0;
          state.hasSpeechDetected = true;
          
          // DEBUG: Log all speech detection for interruption debugging (Twilio path)
          if (state.isModelSpeaking) {
            console.log(`🔍 [VAD-SPEECH-DEBUG-TWILIO] ${callSid}: Speech detected while model speaking - probability: ${probability.toFixed(3)}, threshold: ${interruptThreshold}, above threshold: ${probability > interruptThreshold}`);
          }
          
          // CRITICAL FIX: Send SPEECH_FRAME event to XState machine to sync context
          sendSpeechFrame(callSid);
          
          // Send activity detection event to XState for inactivity tracking
          const controller = callControllers[callSid];
          if (controller?.machine) {
            controller.machine.send({
              type: 'ACTIVITY_DETECTED',
              source: 'speech',
              callSid: callSid,
              timestamp: Date.now()
            });
          }
          
          // Only count as significant speech for interruption purposes if above threshold
          if (probability > interruptThreshold) {
            thresholdDiagnostics[callSid].speechFrames++;
            
            // Validate TTS state before interruption check
            validateTTSState(callSid, connectionType);
            
            // Check for interruption (user speaking while model is speaking)
            console.log(`🔍 [INTERRUPT-DEBUG] ${callSid}: VAD check - isModelSpeaking=${state.isModelSpeaking}, probability=${probability.toFixed(3)}, threshold=${interruptThreshold}`);
            
            // PHASE 3: Clean event-based interruption - Send XState event directly (Twilio path)
            if (state.isModelSpeaking) {
              console.log(`🎤 [VAD-INTERRUPT-DETECTED-TWILIO] ${callSid}: Sending USER_INTERRUPTION_DETECTED event to XState (p=${probability.toFixed(3)})`);
              
              thresholdDiagnostics[callSid].interruptionAttempts++;
              
              // Send clean XState event - let the state machine handle the logic
              const controller = callControllers[callSid];
              if (controller?.machine) {
                controller.machine.send({
                  type: 'USER_INTERRUPTION_DETECTED',
                  callSid: callSid,
                  timestamp: Date.now(),
                  vadProbability: probability,
                  currentTurnIndex: state.currentlyPlayingTurnIndex
                });
                
                console.log(`✅ [VAD-INTERRUPT-SENT-TWILIO] ${callSid}: USER_INTERRUPTION_DETECTED event sent to XState machine`);
                thresholdDiagnostics[callSid].successfulInterruptions++;
              } else {
                console.log(`❌ [VAD-INTERRUPT-NO-MACHINE-TWILIO] ${callSid}: No XState machine found for interruption event`);
              }
            } else {
              // Model is not speaking - just track the VAD data for debugging
              console.log(`🔍 [VAD-NO-INTERRUPT] ${callSid}: Speech detected but model not speaking (p=${probability.toFixed(3)})`);
            }
          }
        } else {
          // Silence detected
          state.consecutiveSilenceFrames++;
          
          // Removed excessive per-frame logging for cleaner output
          
          // Log feature health diagnostics periodically
          logFeatureHealthDiagnostics(callSid, connectionType);
          
          if (state.hasSpeechDetected) {
            // Check for grace period trigger (33 frames = 0.66s)
            if (state.consecutiveSilenceFrames === SILENCE_FRAMES_THRESHOLD) {
              // PHASE 1: STT Processing Timeline Analysis - Track timing at silence threshold
              const silenceDetectionTime = Date.now();
              state.lastSilenceDetectionTime = silenceDetectionTime;
              
              // Calculate approximate speech end time (threshold frames ago)
              const frameDurationMs = 20; // 20ms per frame
              const speechEndTime = silenceDetectionTime - (SILENCE_FRAMES_THRESHOLD * frameDurationMs);
              state.lastSpeechEndTime = speechEndTime;
              
              // DIAGNOSTIC: Track VAD silence detection timing
              logDiagnostic(callSid, 'VAD-SILENCE-DETECTION', 'Silence threshold reached', {
                silenceDetectionTime,
                speechEndTime,
                silenceFrames: state.consecutiveSilenceFrames,
                thresholdFrames: SILENCE_FRAMES_THRESHOLD,
                frameDurationMs,
                calculatedDelay: silenceDetectionTime - speechEndTime,
                hasSpeechDetected: state.hasSpeechDetected,
                lastSpeechFrame: state.lastSpeechFrameTime || 'unknown'
              });
              
              console.log(`[STT-TIMING] ${callSid}: Turn meaningful silence reached at ${silenceDetectionTime}, estimated speech end at ${speechEndTime} (${SILENCE_FRAMES_THRESHOLD} frames ago)`);
              
              // NEW: Use barrier synchronization pattern instead of direct processing
              const { turnCompletionCoordinator } = require('./turnCompletionCoordinator');
              
              console.log(`[VAD-COORDINATOR] ${callSid}: Notifying coordinator of silence detection after ${state.consecutiveSilenceFrames} frames`);
              
              // DIAGNOSTIC: Track coordinator notification
              logDiagnostic(callSid, 'COORDINATOR-NOTIFY', 'Calling onSilenceDetected', {
                coordinatorCallTime: Date.now(),
                timeSinceSilenceDetection: Date.now() - silenceDetectionTime,
                consecutiveSilenceFrames: state.consecutiveSilenceFrames
              });
              
              turnCompletionCoordinator.onSilenceDetected(callSid);
              
              // DIAGNOSTIC: Track coordinator notification completion
              logDiagnostic(callSid, 'COORDINATOR-NOTIFY-COMPLETE', 'onSilenceDetected completed', {
                coordinatorCompleteTime: Date.now(),
                totalNotificationTime: Date.now() - silenceDetectionTime
              });
            }
            // Check for immediate turn finalization after minimal silence for interruption reset
            else if (state.consecutiveSilenceFrames >= RESET_INTERRUPT_SILENCE_THRESHOLD && state.consecutiveSilenceFrames < SILENCE_FRAMES_THRESHOLD) {
              // Only for interruption reset, not turn finalization
              // Note: Interruption state reset happens silently here
              // Don't finalize turn here - let the grace period logic handle it
            }
          } else {
            // Silence frame with no prior speech detected - this is normal, no logging needed
          }
        }
        
      } catch (err) { 
        console.error(`VAD call failed for callSid=${callSid}:`, err);
      }
    }
  }
  // --- END OF NEW LOGIC ---
}

// Add interruption handler (No changes needed here for STT provider switch)
export async function handleInterruption(callSid: string, connectionType: ConnectionType = ConnectionType.TWILIO) {
  const state = calls[callSid];
  if (!state) return;
  
  
  // ===== ENHANCED TTS INTERRUPTION TRACKING =====
  const interruptTimestamp = Date.now();
  const interruptionDetails = {
    callSid,
    timestamp: interruptTimestamp,
    timestampFormatted: new Date(interruptTimestamp).toISOString(),
    connectionType,
    reason: 'user_speech_detected',
    preInterruptionState: {
      isModelSpeaking: state.isModelSpeaking,
      hasTtsProcess: !!state.currentFfmpegProcess,
      hasTtsTimeout: !!state.currentSpeakingTimeout,
      hasElevenLabsWs: !!state.currentElevenLabsWebSocket,
      hasLivekitRtc: !!state.livekitRTCHandler,
      activeTtsOperation: state.activeTtsOperation,
      currentlyPlayingTurnIndex: state.currentlyPlayingTurnIndex,
      lastTtsStartTime: state.lastTtsStartTime,
      audioStreamingStartAt: state.turnTimings?.[state.currentTurnIndex]?.audioStreamingStartAt
    }
  };
  
  console.log(`🔴 [TTS-INTERRUPT-EVENT] ${JSON.stringify(interruptionDetails, null, 2)}`);
  
  // Calculate TTS playback duration if available
  if (state.lastTtsStartTime && state.isModelSpeaking) {
    const ttsPlaybackDuration = interruptTimestamp - state.lastTtsStartTime;
    console.log(`📊 [TTS-INTERRUPT-TIMING] Call ${callSid}: TTS was playing for ${ttsPlaybackDuration}ms before interruption`);
    
    // Log if this was a very quick interruption (potential false positive)
    if (ttsPlaybackDuration < 500) {
      console.warn(`⚠️ [TTS-INTERRUPT-QUICK] Call ${callSid}: Very quick interruption after only ${ttsPlaybackDuration}ms - potential VAD false positive!`);
    }
  }

  console.log(`[INTERRUPT-DEBUG-${connectionType}] Handling user interruption for call ${callSid}`);
  console.log(`[INTERRUPT-DEBUG-${connectionType}] Current model speaking state: ${state.isModelSpeaking}`);
  console.log(`[INTERRUPT-DEBUG-${connectionType}] TTS process exists: ${!!state.currentFfmpegProcess}, TTS timeout exists: ${!!state.currentSpeakingTimeout}`);
  
  // Send INTERRUPT event to XState machine if available
  const controller = callControllers[callSid];
  if (controller && controller.machine) {
    try {
      console.log(`[XState:${callSid}] Sending INTERRUPT event to machine (${connectionType}) with timestamp: ${interruptTimestamp}`);
      // SINGLE SOURCE OF TRUTH: Get transcript from XState infrastructure
      const { getTranscriptState } = require('./infrastructureHelpers');
      const transcriptState = getTranscriptState(callSid);
      const currentTranscript = transcriptState?.currentText || '';
      sendInterrupt(callSid, currentTranscript, interruptTimestamp);
    } catch (err) {
      console.error(`[XState:${callSid}] Error sending INTERRUPT event:`, err);
      // Continue with traditional interruption handling as fallback
    }
  }
  
  // Stop audio playback if in progress
  if (state.isModelSpeaking) {
    try {
      // CRITICAL: Clear any duration-based timeout that would automatically end speaking
      if (state.currentSpeakingTimeout) {
        console.log(`[INTERRUPT-DEBUG-${connectionType}] Clearing duration-based speaking timeout`);
        clearTimeout(state.currentSpeakingTimeout);
        state.currentSpeakingTimeout = undefined;
      } else {
        console.log(`[INTERRUPT-DEBUG-${connectionType}] No duration-based speaking timeout to clear`);
      }
      
      // CRITICAL FIX: Make sure we properly kill the ffmpeg process FIRST before anything else
      if (state.currentFfmpegProcess) {
        const pid = state.currentFfmpegProcess.pid;
        const processStartTime = state.currentFfmpegProcess.spawnargs ? 'unknown' : 'unavailable';
        
        console.log(`🎯 [TTS-PROCESS-KILL] Call ${callSid}: Terminating FFmpeg process PID ${pid} for ${connectionType}`);
        console.log(`🎯 [TTS-PROCESS-KILL] Process details: PID=${pid}, startTime=${processStartTime}, killed=${state.currentFfmpegProcess.killed}`);
        
        // Properly kill the process - first close all streams to prevent hanging
        try {
          const streamCloseStart = Date.now();
          
          if (state.currentFfmpegProcess.stdin) {
            state.currentFfmpegProcess.stdin.end();
            state.currentFfmpegProcess.stdin.destroy();
            console.log(`🎯 [TTS-PROCESS-KILL] stdin closed and destroyed`);
          }
          if (state.currentFfmpegProcess.stdout) {
            state.currentFfmpegProcess.stdout.destroy();
            console.log(`🎯 [TTS-PROCESS-KILL] stdout destroyed`);
          }
          if (state.currentFfmpegProcess.stderr) {
            state.currentFfmpegProcess.stderr.destroy();
            console.log(`🎯 [TTS-PROCESS-KILL] stderr destroyed`);
          }
          
          const streamCloseTime = Date.now() - streamCloseStart;
          console.log(`🎯 [TTS-PROCESS-KILL] Stream cleanup completed in ${streamCloseTime}ms`);
          
          // Kill with SIGKILL immediately - don't wait
          const killStart = Date.now();
          state.currentFfmpegProcess.kill('SIGKILL');
          console.log(`🎯 [TTS-PROCESS-KILL] SIGKILL sent to PID ${pid}`);
          
          // Also try the system kill command as a backup
          try {
            console.log(`🎯 [TTS-PROCESS-KILL] Also using system kill for PID: ${pid}`);
            // Use process.kill as a direct way to send the signal
            if(pid) process.kill(pid, 'SIGKILL');
            const killTime = Date.now() - killStart;
            console.log(`🎯 [TTS-PROCESS-KILL] System kill completed in ${killTime}ms`);
          } catch (killErr) {
            console.warn(`🎯 [TTS-PROCESS-KILL] System kill failed (process may already be dead): ${killErr.message}`);
          }
        } catch (err) {
          console.error(`🔴 [TTS-PROCESS-KILL-ERROR] Failed to kill ffmpeg PID ${pid}:`, err);
        }
        
        // Immediately clear the reference
        state.currentFfmpegProcess = undefined;
        console.log(`✅ [TTS-PROCESS-KILL] FFmpeg process reference cleared for call ${callSid}`);
      } else {
        console.log(`ℹ️ [TTS-PROCESS-KILL] No ffmpeg process found to kill for call ${callSid}`);
      }

      // CRITICAL: Handle ElevenLabs streaming WebSocket interruption
      if (state.currentElevenLabsWebSocket) {
        console.log(`[INTERRUPT-DEBUG-${connectionType}] CLOSING ElevenLabs WebSocket for call ${callSid}`);
        try {
          // Close the ElevenLabs WebSocket to stop audio generation
          state.currentElevenLabsWebSocket.close();
          console.log(`[INTERRUPT-DEBUG-${connectionType}] ElevenLabs WebSocket close() called`);
          
          // Clear the reference immediately
          state.currentElevenLabsWebSocket = undefined;
          console.log(`[INTERRUPT-DEBUG-${connectionType}] Cleared ElevenLabs WebSocket reference`);
        } catch (err) {
          console.error(`[INTERRUPT-DEBUG-${connectionType}] Error closing ElevenLabs WebSocket:`, err);
          // Clear reference even on error
          state.currentElevenLabsWebSocket = undefined;
        }
      } else {
        console.log(`[INTERRUPT-DEBUG-${connectionType}] No ElevenLabs WebSocket found to close`);
      }
      
      // Find the WebSocket connection for this call
      const wsConnection = Array.from(wss.clients).find((client) => {
        const typedClient = client as any; // Use any for ws WebSocket which has different typing than browser WebSocket
        return typedClient.callSid === callSid && typedClient.readyState === WebSocket.OPEN;
      }) as any;
      
      if (wsConnection) {
        console.log(`[INTERRUPT-DEBUG] Found active WebSocket connection for call ${callSid}`);
        console.log(`[INTERRUPT-DEBUG] WebSocket readyState: ${wsConnection.readyState} (1=OPEN)`);
        
        // Get the streamSid from call state
        const streamSid = state.streamSid || callSid;
        console.log(`[INTERRUPT-DEBUG] Using streamSid: ${streamSid}`);
        
        console.log(`[INTERRUPT-CLEAR] Attempting to clear audio buffer for call ${callSid}, stream ${streamSid}`);
        
        try {
          // Step 1: Send clear command
          const clearMessage = JSON.stringify({
          event: 'clear',
          streamSid: streamSid
        });
        
          console.log(`[INTERRUPT-CLEAR] Sending clear command: ${clearMessage}`);
          wsConnection.send(clearMessage);
          
          // Step 2: Immediately follow with a very short silence to force-replace current audio
          // Create a minimal audio packet that's just silence
          const silencePacket = Buffer.alloc(320, 0); // 320 bytes of silence (20ms at 8kHz µ-law)
          const silenceBase64 = silencePacket.toString('base64');
          
          const silenceMessage = JSON.stringify({
            event: 'media',
            streamSid: streamSid,
            media: {
              payload: silenceBase64
            }
          });
          
          console.log(`[INTERRUPT-CLEAR] Sending silence packet to force-stop current audio`);
          wsConnection.send(silenceMessage);
          
          // Step 3: Send another clear command to make sure
          setTimeout(() => {
            try {
              if (wsConnection.readyState === WebSocket.OPEN) {
                console.log(`[INTERRUPT-CLEAR] Sending second clear command for redundancy`);
                wsConnection.send(clearMessage);
              }
            } catch (err) {
              console.error(`[INTERRUPT-DEBUG] Error sending second clear:`, err);
            }
          }, 50);
          
        } catch (err) {
          console.error(`[INTERRUPT-DEBUG] Error sending clear commands:`, err);
        }
        
      } else {
        console.error(`[INTERRUPT-DEBUG] No active WebSocket connection found for call ${callSid}`);
      }
    } catch (err) {
      console.error(`[INTERRUPT-DEBUG] Error stopping audio playback for call ${callSid}:`, err);
    }

    // CRITICAL: Handle LiveKit audio stopping (equivalent to Twilio's clear command)
    if (state.livekitRTCHandler) {
      const livekitStopStart = Date.now();
      console.log(`🔴 [LIVEKIT-INTERRUPT] Call ${callSid}: Initiating LiveKit audio interruption`);
      console.log(`🔴 [LIVEKIT-INTERRUPT] Handler available: ${!!state.livekitRTCHandler}`);
      
      try {
        console.log(`🔴 [LIVEKIT-INTERRUPT] Calling stopAudio() on LiveKit RTC handler (INTERRUPTION TRIGGER)...`);
        await state.livekitRTCHandler.stopAudio();
        console.log(`🔴 [LIVEKIT-INTERRUPT] stopAudio() completed from interruption handler`);
        
        // CRITICAL FIX: Don't reset immediately - let streamingCancelled flag stay true to stop ongoing chunks
        // resetForNewTurn() should only be called when starting new audio, not during interruption
        console.log(`🔄 [LIVEKIT-INTERRUPT] Preserving streamingCancelled=true to stop ongoing chunks`);
        
        const livekitStopTime = Date.now() - livekitStopStart;
        console.log(`✅ [LIVEKIT-INTERRUPT] LiveKit audio stopped successfully in ${livekitStopTime}ms`);
        
        // Verify the interruption was effective
        if (state.isModelSpeaking) {
          console.warn(`⚠️ [LIVEKIT-INTERRUPT] Warning: isModelSpeaking still true after LiveKit stop`);
        }
        
      } catch (err) {
        const livekitStopTime = Date.now() - livekitStopStart;
        console.error(`🔴 [LIVEKIT-INTERRUPT-ERROR] Failed to stop LiveKit audio after ${livekitStopTime}ms:`, err);
        console.error(`🔴 [LIVEKIT-INTERRUPT-ERROR] Error details: ${err.message}`);
        console.error(`🔴 [LIVEKIT-INTERRUPT-ERROR] Stack trace: ${err.stack}`);
      }
    } else {
      console.log(`ℹ️ [LIVEKIT-INTERRUPT] No LiveKit RTC handler found for call ${callSid} - audio interruption not available`);
      
      // This could indicate a configuration issue
      if (connectionType === ConnectionType.LIVEKIT) {
        console.warn(`⚠️ [LIVEKIT-INTERRUPT] Warning: ConnectionType is LIVEKIT but no RTC handler found!`);
      }
    }
    
    // CRITICAL FIX: Force reset all streaming flags
    setModelSpeaking(callSid, false, "Interruption handled", connectionType);
    
    // CRITICAL FIX: Reset TTS operation lock to allow future TTS
    state.activeTtsOperation = false;
    console.log(`[INTERRUPT-TTS-RESET] Reset activeTtsOperation to false for ${callSid}`);
    
    // Record interruption time in the current turn's timings
    try {
      const playingTurnIndex = state.currentlyPlayingTurnIndex;
      const currentTurnIndex = await getCallTurnIndex(callSid, calls);
      const interruptTime = Date.now();
      
      let targetTurnIndex = playingTurnIndex;
      
      // If currentlyPlayingTurnIndex is undefined (audio finished), find the most recent turn with audio
      if (targetTurnIndex === undefined && state.turnTimings && state.turnTimings.length > 0) {
        // Find the most recent turn that has audio streaming start time
        const recentTurnsWithAudio = state.turnTimings
          .filter(t => t.audioStreamingStartAt)
          .sort((a, b) => (b.audioStreamingStartAt || 0) - (a.audioStreamingStartAt || 0));
        
        if (recentTurnsWithAudio.length > 0) {
          const mostRecentTurn = recentTurnsWithAudio[0];
          const timeSinceAudioStart = interruptTime - (mostRecentTurn.audioStreamingStartAt || 0);
          
          // Only use this turn if interruption is within a reasonable time window (e.g., 5 seconds)
          if (timeSinceAudioStart <= 5000) {
            targetTurnIndex = mostRecentTurn.turnIndex;
            console.log(`[INTERRUPT-DEBUG] Using most recent audio turn ${targetTurnIndex} (${timeSinceAudioStart}ms ago)`);
          }
        }
      }
      
      if (targetTurnIndex !== undefined && targetTurnIndex >= 0) {
        // Get the current timing before update for logging
        const currentTiming = state.turnTimings?.find(t => t.turnIndex === targetTurnIndex);
        
        // Log detailed info about the interruption
        console.log(`[INTERRUPT-DEBUG] ****************************************`);
        console.log(`[INTERRUPT-DEBUG] INTERRUPTION DETECTED`);
        console.log(`[INTERRUPT-DEBUG]   Target turn index: ${targetTurnIndex}`);
        console.log(`[INTERRUPT-DEBUG]   Currently playing turn index: ${playingTurnIndex}`);
        console.log(`[INTERRUPT-DEBUG]   Current turn index: ${currentTurnIndex}`);
        console.log(`[INTERRUPT-DEBUG]   Interruption time: ${interruptTime}`);
        
        if (currentTiming) {
          console.log(`[INTERRUPT-DEBUG]   Current timing for turn ${targetTurnIndex}:`);
          console.log(`[INTERRUPT-DEBUG]     TTS file: ${currentTiming.ttsFilename || 'N/A'}`);
          console.log(`[INTERRUPT-DEBUG]     Audio started at: ${currentTiming.audioStreamingStartAt || 'N/A'}`);
          console.log(`[INTERRUPT-DEBUG]     Duration before interrupt: ${currentTiming.audioStreamingStartAt ? 
            (interruptTime - currentTiming.audioStreamingStartAt) + 'ms' : 'N/A'}`);
          console.log(`[INTERRUPT-DEBUG]     Existing interruption: ${currentTiming.interruptionTimestamp || 'None'}`);
        } else {
          console.log(`[INTERRUPT-DEBUG]   No timing found for turn ${targetTurnIndex}`);
        }
        
        // CRITICAL FIX: Use XState action instead of direct turnTimings update
        // This ensures synchronization between XState context and global calls object
        const controller = callControllers[callSid];
        if (controller?.machine) {
          controller.machine.send({
            type: 'UPDATE_INTERRUPTION_TIMESTAMP',
            timestamp: interruptTime,
            turnIndex: targetTurnIndex,
            callSid: callSid
          });
        }
        
        // Log the updated timing
        const updatedTiming = state.turnTimings.find(t => t.turnIndex === targetTurnIndex);
        if (updatedTiming) {
          console.log(`[INTERRUPT-DEBUG]   Updated interruption timestamp: ${updatedTiming.interruptionTimestamp}`);
        }
        
        console.log(`[INTERRUPT-DEBUG] ****************************************`);
      } else {
        console.warn(`[INTERRUPT-DEBUG] Could not find suitable turn for interruption timestamp - currentlyPlayingTurnIndex: ${state.currentlyPlayingTurnIndex}, currentTurnIndex: ${state.currentTurnIndex}, turnTimings length: ${state.turnTimings?.length || 0}`);
      }
    } catch (err) {
      console.error(`[INTERRUPT-DEBUG] Error recording interruption timestamp:`, err);
    }
    
    // Clear any path to the current audio file to prevent re-use
    state.audioResponsePath = undefined;
  
  // If there was a partial model response, save it to conversation history
  if (state.currentModelResponse) {
      console.log(`[INTERRUPT-DEBUG] Saving interrupted response to conversation history`);
      
      // Get current XState machine state
      const currentStateInfo = getCurrentState(callSid);
      const currentXStateState = currentStateInfo?.state || 'unknown';
      
      // REMOVED: Conversation management moved to XState as single source of truth
      // XState interruption handlers now manage all conversation updates including interruptions
      state.currentModelResponse = undefined;
      
      // Sync server state from XState and save enhanced conversation after interruption
      syncConversationFromXState(callSid);
      saveEnhancedConversation(callSid);
    }
    
    // Reset user speech counts to prepare for new turn
    state.speechFramesInCurrentTurn = 0;
    // SINGLE SOURCE OF TRUTH: XState infrastructure handles transcript state
    // Legacy transcript fields are no longer used
    
    // Clear currentlyPlayingTurnIndex after interruption processing is complete
    console.log(`[INTERRUPT-DEBUG] Clearing currentlyPlayingTurnIndex after interruption (was ${state.currentlyPlayingTurnIndex})`);
    state.currentlyPlayingTurnIndex = undefined;
    
    console.log(`[INTERRUPT-DEBUG] Interruption handling complete for call ${callSid}`);
  } else {
    console.log(`[INTERRUPT-DEBUG] Model not speaking for call ${callSid}, no interruption needed`);
  }
}

// Function to generate audio from text using Azure Speech SDK (TTS)
// Updated to include turnIndex in filename
// Original generateAudioResponse moved to generateAudioResponseOriginal above

// Function to validate TTS state consistency across connection types
function validateTTSState(callSid: string, connectionType: ConnectionType = ConnectionType.TWILIO): void {
  const state = calls[callSid];
  if (!state) return;
  
  const ttsProcessExists = !!state.currentFfmpegProcess;
  const ttsTimeoutExists = !!state.currentSpeakingTimeout;
  const elevenLabsWSExists = !!state.currentElevenLabsWebSocket;
  const modelSpeakingState = state.isModelSpeaking;
  
  // Skip detailed TTS state logging to reduce verbosity - only warnings for actual problems
  
  // Create current state signature for comparison (only track the problematic state)
  const currentWarningState = {
    modelSpeakingNoProcess: modelSpeakingState && !ttsProcessExists && !elevenLabsWSExists
  };
  
  // Check if this warning state is different from the last one
  const lastWarningState = state.lastTtsStateWarning;
  const isStateChanged = !lastWarningState || 
    lastWarningState.modelSpeakingNoProcess !== currentWarningState.modelSpeakingNoProcess;
  
  // Only log warnings when state changes, not every frame
  if (isStateChanged) {
    // Only warn about the problematic case: model marked as speaking but no TTS process
    if (currentWarningState.modelSpeakingNoProcess) {
      console.warn(`[TTS-STATE-WARNING-${connectionType}] ${callSid} - Model marked as speaking but no TTS process found!`);
    }
    
    // Update the last warning state
    state.lastTtsStateWarning = currentWarningState;
  }
}

// Rate limiting for debug logging to prevent log spam
const INTERRUPT_DEBUG_LOG_INTERVAL = 250; // Log every 250 calls (5 seconds at 50 calls/sec)
const interruptDebugCounters: Record<string, number> = {};

// Function to validate interruption detection capabilities
export function validateInterruptionCapability(callSid: string, connectionType: ConnectionType): boolean {
  const state = calls[callSid];
  if (!state) return false;
  
  // CRITICAL: Check XState machine meta properties for interruptibility
  const controller = callControllers[callSid];
  if (controller?.machine) {
    const machineSnapshot = controller.machine.getSnapshot();
    
    // CRITICAL FIX: Get the full nested state path from XState machine snapshot
    // The context.currentState can be stale/inconsistent with the actual machine state
    const actualMachineState = machineSnapshot.value;
    
    // Helper function to extract full nested state path
    const getFullStatePath = (stateValue: any): string => {
      if (typeof stateValue === 'string') {
        return stateValue;
      }
      if (typeof stateValue === 'object' && stateValue !== null) {
        // Recursively build the full path for nested states
        const buildPath = (obj: any, path: string[] = []): string[] => {
          const keys = Object.keys(obj);
          if (keys.length === 0) return path;
          
          const key = keys[0];
          path.push(key);
          
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            return buildPath(obj[key], path);
          }
          return path;
        };
        
        return buildPath(stateValue).join('.');
      }
      return 'unknown';
    };
    
    const currentState = getFullStatePath(actualMachineState);
    
    // RATE LIMITED DEBUG: Only log periodically to prevent spam (was logging 50x/sec per call)
    const debugKey = `${callSid}-state-debug`;
    if (!interruptDebugCounters[debugKey]) interruptDebugCounters[debugKey] = 0;
    interruptDebugCounters[debugKey]++;
    
    if (interruptDebugCounters[debugKey] % INTERRUPT_DEBUG_LOG_INTERVAL === 0) {
      const contextState = machineSnapshot.context.currentState;
      console.log(`[INTERRUPT-STATE-DEBUG] ${callSid}: machineSnapshot.value="${JSON.stringify(actualMachineState)}", derived="${currentState}", context.currentState="${contextState}" (logged every ${INTERRUPT_DEBUG_LOG_INTERVAL} calls, count: ${interruptDebugCounters[debugKey]})`);
    }
    
    // Check the current state's meta properties using proper XState API
    if (currentState) {
      try {
        const metaData = machineSnapshot.getMeta();
        
        // CRITICAL FIX: Use XState's configuration to check all active states
        let isInterruptible = true; // Default to interruptible
        
        // Approach 1: Use XState's built-in state configuration (most reliable)
        try {
          const activeStates = machineSnapshot.configuration || new Set();
          for (const stateNode of activeStates) {
            if (stateNode.meta?.interruptible === false) {
              isInterruptible = false;
              console.log(`🔒 [INTERRUPT-BLOCKED-STATENODE] ${callSid}: ${stateNode.id} (via configuration)`);
              break;
            }
          }
        } catch (configError) {
          console.warn(`[META-CONFIG-ERROR] ${callSid}: Failed to check configuration:`, configError);
        }
        
        // Approach 2: Exact match with full state path
        if (isInterruptible && metaData[currentState] && metaData[currentState].interruptible === false) {
          isInterruptible = false;
          console.log(`🔒 [INTERRUPT-BLOCKED-EXACT] ${callSid}: ${currentState}`);
        }
        
        // Approach 3: Check the deepest (rightmost) part of the state path
        if (isInterruptible && currentState.includes('.')) {
          const stateParts = currentState.split('.');
          const deepestState = stateParts[stateParts.length - 1];
          if (metaData[deepestState] && metaData[deepestState].interruptible === false) {
            isInterruptible = false;
            console.log(`🔒 [INTERRUPT-BLOCKED-DEEPEST] ${callSid}: ${currentState} (deepest: ${deepestState})`);
          }
        }
        
        // Approach 4: Fallback to context.currentState if available
        if (isInterruptible) {
          const contextState = machineSnapshot.context.currentState;
          if (contextState && metaData[contextState] && metaData[contextState].interruptible === false) {
            isInterruptible = false;
            console.log(`🔒 [INTERRUPT-BLOCKED-CONTEXT] ${callSid}: ${contextState} (via context)`);
          }
        }
        
        // Approach 5: Check all meta keys for exact matches to any part of the state path
        if (isInterruptible && currentState.includes('.')) {
          const stateParts = currentState.split('.');
          for (const part of stateParts) {
            if (metaData[part] && metaData[part].interruptible === false) {
              isInterruptible = false;
              console.log(`🔒 [INTERRUPT-BLOCKED-PART] ${callSid}: ${currentState} (part: ${part})`);
              break;
            }
          }
        }
        
        if (!isInterruptible) {
          return false;
        }
        
      } catch (err) {
        console.error(`[META-ERROR] ${callSid}: Failed to check meta properties:`, err);
        // Silent fallback to TTS validation
      }
    }
  }
  
  // XState meta properties already handled above - if we get here, interruption is allowed
  // Only check if there's actually something to interrupt
  let hasActiveTTS = false;
  
  if (connectionType === ConnectionType.LIVEKIT) {
    // For LiveKit: Check if RTC handler exists and is connected
    hasActiveTTS = !!(state.livekitRTCHandler?.isConnected) || !!(state.currentFfmpegProcess || state.currentElevenLabsWebSocket);
    
    // CRITICAL FIX: For LiveKit, also check if we're currently playing a turn
    // This prevents interruption failures when isModelSpeaking state gets out of sync
    const hasPlayingTurn = state.currentlyPlayingTurnIndex !== undefined;
    
    // RATE LIMITED DEBUG: Only log periodically to prevent spam (was logging 50x/sec per call)
    const livekitDebugKey = `${callSid}-livekit-debug`;
    if (!interruptDebugCounters[livekitDebugKey]) interruptDebugCounters[livekitDebugKey] = 0;
    interruptDebugCounters[livekitDebugKey]++;
    
    if (interruptDebugCounters[livekitDebugKey] % INTERRUPT_DEBUG_LOG_INTERVAL === 0) {
      console.log(`[INTERRUPT-VALIDATE-LIVEKIT] ${callSid}: isModelSpeaking=${state.isModelSpeaking}, hasActiveTTS=${hasActiveTTS}, hasPlayingTurn=${hasPlayingTurn}, livekitConnected=${!!state.livekitRTCHandler?.isConnected} (logged every ${INTERRUPT_DEBUG_LOG_INTERVAL} calls, count: ${interruptDebugCounters[livekitDebugKey]})`);
    }
    
    // Allow interruption if either the model is speaking OR we have an active playing turn
    // This handles cases where isModelSpeaking gets reset but audio is still playing
    return (state.isModelSpeaking || hasPlayingTurn) && hasActiveTTS;
  } else {
    // For Twilio/Direct: Use existing logic
    hasActiveTTS = !!(state.currentFfmpegProcess || state.currentElevenLabsWebSocket);
    
    console.log(`[INTERRUPT-VALIDATE-TWILIO] ${callSid}: isModelSpeaking=${state.isModelSpeaking}, hasActiveTTS=${hasActiveTTS}`);
    
    return state.isModelSpeaking && hasActiveTTS;
  }
}

// Function to get connection-specific interruption threshold
function getInterruptionThreshold(connectionType: ConnectionType): number {
  switch (connectionType) {
    case ConnectionType.LIVEKIT:
      return INTERRUPT_SPEECH_THRESHOLD_LIVEKIT;
    case ConnectionType.DIRECT:
      return INTERRUPT_SPEECH_THRESHOLD_WEBRTC; // Use WebRTC threshold for direct connections
    case ConnectionType.TWILIO:
    default:
      return INTERRUPT_SPEECH_THRESHOLD_TWILIO;
  }
}

// Diagnostic function to monitor interruption and grace period feature health
function logFeatureHealthDiagnostics(callSid: string, connectionType: ConnectionType): void {
  const state = calls[callSid];
  if (!state) return;
  
  const controller = callControllers[callSid];
  const machineSnapshot = controller?.machine?.getSnapshot();
  const context = machineSnapshot?.context as any;
  
  // Minimal health check every 500 frames (10s)
  if (state.consecutiveSilenceFrames % 500 === 0 && state.consecutiveSilenceFrames > 0) {
    console.log(`[HEALTH] ${callSid}: ${connectionType} - ${JSON.stringify(machineSnapshot?.value)}`);
  }
}

// Function to log threshold effectiveness summary
function logThresholdEffectiveness(callSid: string): void {
  const stats = thresholdDiagnostics[callSid];
  if (!stats) return;
  
  const speechRate = stats.speechFrames / Math.max(stats.totalFrames, 1);
  const interruptSuccessRate = stats.successfulInterruptions / Math.max(stats.interruptionAttempts, 1);
  
  console.log(`[THRESHOLD-STATS-${stats.connectionType}] ${callSid} - Threshold: ${stats.thresholdUsed}, Speech Rate: ${(speechRate * 100).toFixed(1)}%, Interrupt Success: ${(interruptSuccessRate * 100).toFixed(1)}% (${stats.successfulInterruptions}/${stats.interruptionAttempts})`);
}

// Function to log grace period diagnostics
function logGracePeriodDiagnostics(callSid: string): void {
  const state = calls[callSid];
  if (!state) return;
  
  const latestTurnTiming = state.turnTimings[state.turnTimings.length - 1];
  if (latestTurnTiming) {
    console.log(`[GRACE-PERIOD-DIAG] ${callSid} - Latest turn: ${latestTurnTiming.turnIndex}, audioStart: ${latestTurnTiming.audioStreamingStartAt}, interruption: ${latestTurnTiming.interruptionTimestamp}`);
  }
  
  // Check XState machine context for turnFinalizedAt
  const controller = callControllers[callSid];
  if (controller?.machine) {
    try {
      const snapshot = controller.machine.getSnapshot();
      const context = snapshot.context as any;
      console.log(`[GRACE-PERIOD-DIAG] ${callSid} - XState turnFinalizedAt: ${context.turnFinalizedAt}, audioPlaybackStartTime: ${context.audioPlaybackStartTime}`);
    } catch (err) {
      console.error(`[GRACE-PERIOD-DIAG] Error accessing XState context for ${callSid}:`, err);
    }
  }
}

// Helper function to convert string connection type to ConnectionType enum
function getConnectionTypeEnum(connectionType: string | ConnectionType | undefined): ConnectionType {
  if (typeof connectionType === 'string') {
    switch (connectionType.toLowerCase()) {
      case 'twilio':
        return ConnectionType.TWILIO;
      case 'livekit':
        return ConnectionType.LIVEKIT;
      case 'direct':
        return ConnectionType.DIRECT;
      default:
        console.warn(`Unknown connection type string: ${connectionType}, defaulting to TWILIO`);
        return ConnectionType.TWILIO;
    }
  }
  return connectionType || ConnectionType.TWILIO;
}

// Function to track when model speaking state changes
function setModelSpeaking(callSid: string, value: boolean, reason: string, connectionType: ConnectionType = ConnectionType.TWILIO) {
  const state = calls[callSid]
  if (!state) return;
  
  // Store previous value before changing it
  const wasModelSpeaking = state.isModelSpeaking;
  
  console.log(`[MODEL-SPEAKING-${connectionType}] Setting isModelSpeaking=${value} for ${callSid} - Reason: ${reason}`);
  console.log(`🔍 [MODEL-SPEAKING-DEBUG] ${callSid}: Changed from ${wasModelSpeaking} to ${value} - Reason: ${reason}`);
  state.isModelSpeaking = value;
  
  // Validate TTS state after change
  validateTTSState(callSid, connectionType);
  
  // When transitioning from not-speaking to speaking, record which turn is playing
  if (!wasModelSpeaking && value === true) {
    state.currentlyPlayingTurnIndex = state.currentTurnIndex;
    console.log(`[MODEL-SPEAKING] Now playing turn ${state.currentlyPlayingTurnIndex}`);
  }
}

// Update the streamAudioToCall function to use our tracking function (TTS playback - remains the same)
export async function streamAudioToCall(audioPath: string, callSid: string, actualTurnNumber: number) {
  // Record audio file buffer preparation
  latencyTracker.recordStep(callSid, actualTurnNumber, LatencySteps.AUDIO_FILE_BUFFER_PREPARED, {
    audioPath: path.basename(audioPath),
    fileExists: fs.existsSync(audioPath)
  });
  
  // Record audio streaming start
  latencyTracker.recordStep(callSid, actualTurnNumber, LatencySteps.AUDIO_STREAMING_STARTED, {
    audioPath: path.basename(audioPath)
  });
  
  const state = calls[callSid];
  if (!state) {
    console.error(`No call state found for ${callSid} when attempting to stream audio.`);
    return false;
  }
  
  // Backup mechanism: Ensure currentlyPlayingTurnIndex is set
  if (state.currentlyPlayingTurnIndex === undefined) {
    state.currentlyPlayingTurnIndex = state.currentTurnIndex;
    if (LOGGING.STREAM_DEBUG) {
      console.log(`[STREAM-DEBUG] Setting missing currentlyPlayingTurnIndex to ${state.currentlyPlayingTurnIndex}`);
    }
  }
  
  // Check connection type - Direct WebSocket calls don't need streamSid
  const connectionType = state.connectionType || 'twilio';
  const streamSid = state.streamSid;
  
  // Record streaming connection validation
  latencyTracker.recordStep(callSid, actualTurnNumber, LatencySteps.STREAMING_CONNECTION_VALIDATED, {
    connectionType: connectionType,
    hasStreamSid: !!streamSid,
    hasDirectWebSocket: !!state.directWebSocket
  });
  
  if (connectionType === 'twilio' && !streamSid) {
    console.error(`No streamSid found for Twilio call ${callSid}`);
    return false;
  }
  
  if (connectionType === 'direct' && !state.directWebSocket) {
    console.error(`No directWebSocket found for direct call ${callSid}`);
    return false;
  }

  // Check if the call has been interrupted before starting streaming
  const turnTiming = state.turnTimings?.find(t => t.turnIndex === actualTurnNumber);
  const wasInterrupted = turnTiming?.interruptionTimestamp !== undefined;

  if (wasInterrupted) {
    console.log(`[TTS-STREAMING] Skipping audio streaming for ${callSid} turn ${actualTurnNumber} - call was interrupted at ${turnTiming?.interruptionTimestamp}`);
    // Record that streaming was skipped
    latencyTracker.recordStep(callSid, actualTurnNumber, 'AUDIO_STREAMING_SKIPPED' as any, {
      reason: 'call_interrupted',
      interruptionTime: turnTiming?.interruptionTimestamp
    });
    return false;
  }


  console.log(`[TTS-STREAMING] Starting audio streaming for ${callSid} turn ${actualTurnNumber} - no interruption detected`);

  try {
    // Use ffprobe to get the duration of the audio file
    let audioDurationSec = 0;
    let ffprobeOutput = ''; // To store ffprobe stdout
    try {
      const ffprobeProcess = spawn('ffprobe', [
        '-v', 'error',
        '-show_entries', 'format=duration',
        '-of', 'default=noprint_wrappers=1:nokey=1',
        audioPath
      ]);

      // let ffmpegStderr = ''; // We need stdout for duration
      ffprobeProcess.stdout.on('data', (data) => { // Capture stdout for duration
         ffprobeOutput += data.toString();
      });
      // Optionally, still capture stderr for error logging if needed
      let ffprobeErrOutput = '';
      ffprobeProcess.stderr.on('data', (data) => { ffprobeErrOutput += data.toString(); });

      await new Promise<void>((resolve, reject) => {
        ffprobeProcess.on('close', (code) => {
          if (code === 0) {
            try {
              audioDurationSec = parseFloat(ffprobeOutput.trim());
              if (!isNaN(audioDurationSec) && ffprobeErrOutput.length > 0 && LOGGING.FFMPEG_STDERR) {
                console.warn(`[FFPROBE-WARN ${callSid}] ffprobe for ${path.basename(audioPath)} got duration ${audioDurationSec}s but also had stderr output: ${ffprobeErrOutput}`);
              }
              if (isNaN(audioDurationSec) && ffprobeErrOutput.length > 0) {
                console.error(`[FFPROBE-ERROR ${callSid}] ffprobe for ${path.basename(audioPath)} failed to parse duration. Stderr: ${ffprobeErrOutput}`);
              }
              if (LOGGING.STREAM_DEBUG) {
                console.log(`[STREAM-DEBUG] Audio duration for ${path.basename(audioPath)}: ${audioDurationSec} seconds`);
              }
              resolve();
            } catch (e) {
              console.error(`[STREAM-ERROR] Failed to parse ffprobe duration output for ${callSid}:`, e);
              audioDurationSec = 0; // Default to 0 on error
              resolve();
            }
          } else {
            console.error(`[STREAM-ERROR] ffprobe process for ${path.basename(audioPath)} exited with code ${code} for ${callSid}. Stderr: ${ffprobeErrOutput}`);
            audioDurationSec = 0; // Default to 0 on error
            resolve(); // Resolve anyway to continue with default duration
          }
        });

        ffprobeProcess.on('error', (err) => {
          console.error(`[STREAM-ERROR] Failed to start ffprobe process for ${path.basename(audioPath)} for ${callSid}:`, err);
          audioDurationSec = 0; // Default to 0 on error
          resolve(); // Resolve anyway to continue with default duration
        });
      });
    } catch (err) {
      console.error(`[STREAM-ERROR] Error getting audio duration for ${callSid}:`, err);
      audioDurationSec = 0; // Default to 0 on error
    }

    // Limit the duration to a maximum value to prevent very long files
    const maxDurationSec = 60; // 60 seconds maximum
    const effectiveDurationSec = Math.min(audioDurationSec || 30, maxDurationSec);
    
    // CRITICAL FIX: Use much longer safety timeout instead of predicted duration
    // The timeout should only trigger if FFmpeg process hangs, not during normal streaming
    // Previous logic: (effectiveDurationSec * 1000) + 2000 caused premature cutoffs
    const timeoutDuration = Math.max(
      (effectiveDurationSec * 1000) * 2.5, // 2.5x predicted duration for streaming delays
      10000 // Minimum 10 seconds to prevent immediate timeouts
    );

    // Mark that model is speaking
    // CRITICAL: Set the currently playing turn index before we start streaming
    // This ensures any interruption will be recorded against the correct turn
    const previousTurnIndex = state.currentlyPlayingTurnIndex;
    state.currentlyPlayingTurnIndex = actualTurnNumber;
    
    console.log(`[CURRENT-PLAYING-DEBUG] Set currentlyPlayingTurnIndex to ${actualTurnNumber} for call ${callSid} (was ${previousTurnIndex})`);
    
    if (LOGGING.STREAM_DEBUG) {
      console.log(`[STREAM-DEBUG ${callSid}] Set currentlyPlayingTurnIndex to ${actualTurnNumber} for TTS playback of ${path.basename(audioPath)}`);
    }

    // --- Record Turn Timing Information --- 
    const ttsFilename = path.basename(audioPath);
    const audioStreamingStartAt = Date.now();
    const turnIndexToRecord = actualTurnNumber; // Use the passed-in actualTurnNumber
    const audioDurationMs = Math.round(effectiveDurationSec * 1000);

    // ===== AUDIO DURATION PREDICTION VS ACTUAL TRACKING =====
    const durationPredictionDetails = {
      callSid,
      turnIndex: turnIndexToRecord,
      filename: ttsFilename,
      timestamp: audioStreamingStartAt,
      timestampFormatted: new Date(audioStreamingStartAt).toISOString(),
      connectionType: state.connectionType,
      audioDurationPrediction: {
        ffprobeRawSec: audioDurationSec,
        effectiveDurationSec: effectiveDurationSec,
        predictedDurationMs: audioDurationMs,
        timeoutDurationMs: timeoutDuration,
        maxDurationCapSec: maxDurationSec
      },
      playbackTracking: {
        expectedEndTime: audioStreamingStartAt + audioDurationMs,
        expectedEndTimeFormatted: new Date(audioStreamingStartAt + audioDurationMs).toISOString(),
        timeoutEndTime: audioStreamingStartAt + timeoutDuration,
        timeoutEndTimeFormatted: new Date(audioStreamingStartAt + timeoutDuration).toISOString()
      }
    };
    
    console.log(`🎵 [AUDIO-DURATION-PREDICTION] ${JSON.stringify(durationPredictionDetails, null, 2)}`);
    
    // Store prediction for later comparison
    if (!state.audioDurationPredictions) {
      state.audioDurationPredictions = {};
    }
    state.audioDurationPredictions[turnIndexToRecord] = durationPredictionDetails;
    
    // Schedule tracking of actual vs predicted duration
    setTimeout(() => {
      const actualEndTime = Date.now();
      const actualDurationMs = actualEndTime - audioStreamingStartAt;
      const durationDifference = actualDurationMs - audioDurationMs;
      const accuracyPercent = ((audioDurationMs / actualDurationMs) * 100).toFixed(1);
      
      const actualTrackingDetails = {
        callSid,
        turnIndex: turnIndexToRecord,
        actualEndTime,
        actualEndTimeFormatted: new Date(actualEndTime).toISOString(),
        actualDurationMs,
        predictedDurationMs: audioDurationMs,
        durationDifferenceMs: durationDifference,
        accuracyPercent: `${accuracyPercent}%`,
        wasInterrupted: !state.isModelSpeaking || state.currentlyPlayingTurnIndex !== turnIndexToRecord
      };
      
      console.log(`📊 [AUDIO-DURATION-ACTUAL] ${JSON.stringify(actualTrackingDetails, null, 2)}`);
      
      // Alert on significant duration mismatches
      if (Math.abs(durationDifference) > 1000) {
        console.warn(`⚠️ [AUDIO-DURATION-MISMATCH] Call ${callSid} Turn ${turnIndexToRecord}: Significant duration mismatch! Predicted: ${audioDurationMs}ms, Actual: ${actualDurationMs}ms, Difference: ${durationDifference}ms`);
      }
      
    }, timeoutDuration + 1000); // Check shortly after timeout

    // Initialize turnTimings if it doesn't exist
    if (!state.turnTimings) {
      state.turnTimings = [];
    }

    // CRITICAL FIX: Use XState action instead of direct turnTimings update
    // This ensures synchronization between XState context and global calls object
    const timingController = callControllers[callSid];
    if (timingController?.machine) {
      timingController.machine.send({
        type: 'UPDATE_AUDIO_STREAMING_START',
        timestamp: audioStreamingStartAt,
        turnIndex: turnIndexToRecord,
        audioDurationMs,
        ttsFilename,
        callSid: callSid
      });
    }

    if (LOGGING.TIMING_EVENTS) {
      console.log(`[TIMING ${callSid}] Recorded TTS timing for turn ${turnIndexToRecord}: ${ttsFilename}, start: ${audioStreamingStartAt}, duration: ${audioDurationMs}ms`);
    }
    // --- End Record Turn Timing Information ---

    // NOTE: Moved setModelSpeaking to when audio actually starts streaming (in FFmpeg data handler)
    // This prevents timing issues where isModelSpeaking=true but audio hasn't started yet
    
    // Update conversation metadata: mark audio playback started
    updateLatestAITurnMetadata(callSid, { audioStartedAt: Date.now() });
    
    // Set a timeout to end speaking mode after the audio duration plus buffer
    // This is a safety mechanism in case the ffmpeg process doesn't end properly
    if (state.currentSpeakingTimeout) {
      clearTimeout(state.currentSpeakingTimeout);
    }
    
    // ===== TIMEOUT-BASED AUDIO CUTOFF LOGGING =====
    const timeoutSetupDetails = {
      callSid,
      turnIndex: turnIndexToRecord,
      timestamp: Date.now(),
      timestampFormatted: new Date().toISOString(),
      timeoutConfig: {
        audioDurationMs,
        bufferMs: 2000,
        totalTimeoutMs: timeoutDuration,
        expectedCutoffTime: Date.now() + timeoutDuration,
        expectedCutoffTimeFormatted: new Date(Date.now() + timeoutDuration).toISOString()
      },
      audioFileInfo: {
        filename: ttsFilename,
        effectiveDurationSec: effectiveDurationSec,
        originalDurationSec: audioDurationSec
      }
    };
    
    console.log(`⏰ [TIMEOUT-SETUP] ${JSON.stringify(timeoutSetupDetails, null, 2)}`);
    
    state.currentSpeakingTimeout = setTimeout(() => {
      // ===== TIMEOUT-BASED AUDIO CUTOFF DETECTED =====
      const timeoutTriggerDetails = {
        callSid,
        turnIndex: turnIndexToRecord,
        timestamp: Date.now(),
        timestampFormatted: new Date().toISOString(),
        timeoutReason: 'safety_mechanism_triggered',
        cutoffContext: {
          wasStillSpeaking: state.isModelSpeaking,
          currentlyPlayingTurn: state.currentlyPlayingTurnIndex,
          expectedTurn: turnIndexToRecord,
          turnMatch: state.currentlyPlayingTurnIndex === turnIndexToRecord,
          elapsedTimeMs: timeoutDuration
        },
        audioState: {
          hasActiveFfmpeg: !!state.currentFfmpegProcess,
          hasActiveTts: state.activeTtsOperation,
          hasLivekitRtc: !!state.livekitRTCHandler
        }
      };
      
      console.warn(`🔴 [TIMEOUT-CUTOFF] ${JSON.stringify(timeoutTriggerDetails, null, 2)}`);
      
      // Check if this is a premature cutoff (audio should still be playing)
      const expectedEndTime = audioStreamingStartAt + audioDurationMs;
      const currentTime = Date.now();
      const prematureCutoff = currentTime < expectedEndTime - 500; // 500ms tolerance
      
      if (prematureCutoff) {
        const timingError = expectedEndTime - currentTime;
        console.error(`🚨 [PREMATURE-TIMEOUT-CUTOFF] Call ${callSid}: Timeout triggered ${timingError}ms too early! This may indicate a timing calculation error.`);
      }
      
      if (LOGGING.STREAM_DEBUG) {
        console.log(`[STREAM-DEBUG] Speaking timeout of ${timeoutDuration}ms elapsed for call ${callSid} - ending speaking mode`);
      }
      setModelSpeaking(callSid, false, "Speaking timeout elapsed", getConnectionTypeEnum(state.connectionType) || ConnectionType.TWILIO);
    }, timeoutDuration);

    // For Twilio calls, we don't need to explicitly check the WebSocket connection here
    // The existing logic already handles streaming to the correct connection
    // Just ensure we're not trying to use direct WebSocket logic for Twilio calls
    // const connectionType = state.connectionType || 'twilio';

    // Kill any existing ffmpeg process for this call
    if (state.currentFfmpegProcess) {
      try {
        if (LOGGING.STREAM_DEBUG) {
          console.log(`[STREAM-DEBUG] Killing existing ffmpeg process for call ${callSid}`);
        }
        state.currentFfmpegProcess.kill('SIGKILL');
      } catch (e) {
        console.error(`[STREAM-ERROR] Error killing existing ffmpeg process: ${e}`);
      }
      state.currentFfmpegProcess = undefined;
    }

    // Record FFmpeg command preparation
    latencyTracker.recordStep(callSid, actualTurnNumber, LatencySteps.FFMPEG_COMMAND_PREPARED, {
      audioFormat: path.extname(audioPath),
      outputFormat: 'pcm_mulaw'
    });
    
    // Set up ffmpeg command to stream audio to Twilio (supports MP3 and OGG/Opus)
    if (LOGGING.STREAM_DEBUG) {
      console.log(`[STREAM-DEBUG] Streaming audio ${audioPath} to call ${callSid}`);
    }
    const ffmpegArgs = [
      '-re', // Read at native frame rate (real-time)
      '-i', audioPath, // Auto-detect input format (works for both MP3 and OGG)
      '-acodec', 'pcm_mulaw',
      '-ar', '8000',
      '-ac', '1',
      '-f', 'mulaw',
      '-'
    ];

    const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);
    state.currentFfmpegProcess = ffmpegProcess;
    
    // Record FFmpeg stdin ready
    latencyTracker.recordStep(callSid, actualTurnNumber, LatencySteps.FFMPEG_STDIN_READY, {
      pid: ffmpegProcess.pid
    });
    
    // Record ffmpeg process start
    latencyTracker.recordStep(callSid, actualTurnNumber, LatencySteps.FFMPEG_PROCESS_STARTED);
    
    // CRITICAL: Send AUDIO_PLAYBACK_STARTED event to XState as soon as ffmpeg starts
    // This ensures grace period timing starts immediately, even if interrupted before first chunk
    const playbackController = callControllers[callSid];
    if (playbackController?.machine) {
      try {
        console.log(`[AUDIO-PLAYBACK] Sending AUDIO_PLAYBACK_STARTED event to XState for ${callSid}`);
        playbackController.machine.send({
          type: 'AUDIO_PLAYBACK_STARTED',
          callSid: callSid,
          turnIndex: actualTurnNumber,
          timestamp: Date.now()
        });
      } catch (error) {
        console.error(`[AUDIO-PLAYBACK] Error sending AUDIO_PLAYBACK_STARTED event for ${callSid}:`, error);
      }
    }
    
    let firstChunkSent = false;

    // Send chunks of audio data to Twilio WebSocket
    ffmpegProcess.stdout.on('data', (chunk) => {
      try {
        // Handle direct WebSocket connections (web/iOS clients)
        if (state.directWebSocket && state.connectionType === 'direct') {
          if (state.directWebSocket.readyState === 1) { // WebSocket.OPEN = 1
            // Record first audio chunk sent
            if (!firstChunkSent) {
              latencyTracker.recordStep(callSid, actualTurnNumber, LatencySteps.NETWORK_SEND_INITIATED, {
                connectionType: 'direct',
                chunkSize: chunk.length
              });
              latencyTracker.recordStep(callSid, actualTurnNumber, LatencySteps.FIRST_AUDIO_CHUNK_SENT);
              latencyTracker.recordStep(callSid, actualTurnNumber, LatencySteps.AUDIO_PLAYBACK_STARTED);
              
              // Complete latency tracking - we've reached the end goal (audio playback start)
              latencyTracker.completeTracking(callSid, actualTurnNumber);
              
              // CRITICAL FIX: Set isModelSpeaking=true when audio actually starts streaming
              setModelSpeaking(callSid, true, "Audio streaming started (direct WebSocket)", getConnectionTypeEnum(state.connectionType));
              
              firstChunkSent = true;
              console.log(`[TTS-WEBSOCKET] First TTS chunk sent to direct WebSocket for ${callSid}`);
            }
            
            // For direct WebSocket connections, send as TTS message
            const ttsMessage = JSON.stringify({
              type: 'tts',
              callId: callSid,
              audio: chunk.toString('base64'),
              timestamp: new Date().toISOString()
            });
            
            // Record WebSocket frame sent
            latencyTracker.recordStep(callSid, actualTurnNumber, LatencySteps.WEBSOCKET_FRAME_SENT, {
              messageSize: ttsMessage.length,
              audioDataSize: chunk.length
            });
            
            state.directWebSocket.send(ttsMessage);
          } else {
            console.warn(`[TTS-WEBSOCKET] Direct WebSocket not open for ${callSid}, readyState: ${state.directWebSocket.readyState}`);
          }
        }
        // Handle Twilio WebSocket connections (existing logic)
        else {
          // Find the correct WebSocket connection for this call
          const clients = [...wss.clients].filter(client => (client as any).callSid === callSid);
          
          if (clients.length > 0) {
            const ws = clients[0];
            if (ws.readyState === WebSocket.OPEN) {
            // Record first audio chunk sent
            if (!firstChunkSent) {
              latencyTracker.recordStep(callSid, actualTurnNumber, LatencySteps.NETWORK_SEND_INITIATED, {
                connectionType: 'twilio',
                chunkSize: chunk.length
              });
              latencyTracker.recordStep(callSid, actualTurnNumber, LatencySteps.FIRST_AUDIO_CHUNK_SENT);
              latencyTracker.recordStep(callSid, actualTurnNumber, LatencySteps.AUDIO_PLAYBACK_STARTED);
              
              // Complete latency tracking - we've reached the end goal (audio playback start)
              latencyTracker.completeTracking(callSid, actualTurnNumber);
              
              // CRITICAL FIX: Set isModelSpeaking=true when audio actually starts streaming
              setModelSpeaking(callSid, true, "Audio streaming started (Twilio WebSocket)", getConnectionTypeEnum(state.connectionType));
              
              firstChunkSent = true;
              console.log(`[AUDIO-SUCCESS] First audio chunk sent for ${callSid}, streamSid: ${state.streamSid}, chunkSize: ${chunk.length} bytes`);
            }
            
              // For Twilio connections, use Twilio's media format
              // CRITICAL FIX: Get streamSid from state
            const msg = JSON.stringify({
              event: 'media',
              streamSid: state.streamSid,
              media: {
                payload: chunk.toString('base64')
              }
            });
            
            // Record WebSocket frame sent
            if (!firstChunkSent) {
              latencyTracker.recordStep(callSid, actualTurnNumber, LatencySteps.WEBSOCKET_FRAME_SENT, {
                messageSize: msg.length,
                audioDataSize: chunk.length,
                streamSid: state.streamSid
              });
            }
            
            ws.send(msg);
            }
          } else if (state.isModelSpeaking === false && firstChunkSent) {
            // Only kill FFmpeg if we've started sending chunks but then got interrupted
            if (LOGGING.STREAM_DEBUG) {
              console.log(`[STREAM-DEBUG] Stopping data send for call ${callSid} - model was interrupted after first chunk sent`);
            }
            ffmpegProcess.kill('SIGKILL');
          }
        }
      } catch (err) {
        console.error(`[STREAM-ERROR] Failed to send audio chunk for call ${callSid}:`, err);
      }
    });

    // Log errors from ffmpeg
    ffmpegProcess.stderr.on('data', (data) => {
      if (LOGGING.FFMPEG_STDERR) {
        console.error(`[STREAM-DEBUG] ffmpeg stderr for ${callSid}: ${data.toString().substr(0, 150)}...`);
      }
    });

    // Return a promise that resolves when ffmpeg finishes
    return new Promise<boolean>((resolve) => {
      ffmpegProcess.on('close', (code) => {
        if (LOGGING.STREAM_DEBUG) {
          console.log(`[STREAM-DEBUG] ffmpeg process for call ${callSid} closed with code ${code}`);
        }
        
        // Clear the speaking timeout since FFmpeg completed naturally
        if (state.currentSpeakingTimeout) {
          clearTimeout(state.currentSpeakingTimeout);
          state.currentSpeakingTimeout = undefined;
          console.log(`[FFMPEG-COMPLETE] Cleared timeout for ${callSid} - FFmpeg finished naturally`);
        }
        
        // CRITICAL FIX: Only set speaking to false if FFmpeg completed successfully
        // This prevents premature timeout cutoffs from overriding natural completion
        if (code === 0 || code === null) {
          console.log(`[FFMPEG-COMPLETE] FFmpeg completed successfully for ${callSid}, setting isModelSpeaking=false`);
          setModelSpeaking(callSid, false, "FFmpeg process completed", getConnectionTypeEnum(state.connectionType));
        } else {
          console.warn(`[FFMPEG-ERROR] FFmpeg failed for ${callSid} with code ${code}, but maintaining speaking state for timeout handling`);
          // Don't change speaking state on FFmpeg errors - let timeout handle it
        }
        
        // NOTE: Latency tracking completion moved to when audio playback starts, not ends
        
        // Reset currentlyPlayingTurnIndex when playback completes normally (not interrupted)
        // CRITICAL: Only reset if this was a normal completion, not an interruption
        // Check if the turn timing has an interruption timestamp - if so, don't reset
        const turnTiming = state.turnTimings?.find(t => t.turnIndex === actualTurnNumber);
        const wasInterrupted = turnTiming?.interruptionTimestamp !== undefined;
        
        // Update conversation metadata based on completion type
        if (!wasInterrupted) {
          // Normal completion
          console.log(`[STREAM-DEBUG ${callSid}] Audio completed normally for turn ${actualTurnNumber}`);
          updateLatestAITurnMetadata(callSid, { 
            interrupted: false,
            audioFinishedAt: Date.now(),
            completionPercent: 100 
          });
          
          if (state.currentlyPlayingTurnIndex === actualTurnNumber) {
            state.currentlyPlayingTurnIndex = undefined;
          }
        } else {
          // Interrupted completion
          console.log(`[STREAM-DEBUG ${callSid}] Audio was interrupted for turn ${actualTurnNumber}`);
          
          // Calculate completion percentage if we have timing data
          let completionPercent = 0;
          if (turnTiming?.audioStreamingStartAt && turnTiming?.interruptionTimestamp) {
            const audioStartTime = turnTiming.audioStreamingStartAt;
            const interruptTime = turnTiming.interruptionTimestamp;
            const totalPlayedTime = interruptTime - audioStartTime;
            
            // Estimate total duration from turn timing or use a reasonable estimate
            const estimatedTotalDuration = turnTiming?.audioDurationMs || (state.responseText?.length || 100) * 50; // 50ms per character estimate
            completionPercent = Math.min(100, Math.max(0, (totalPlayedTime / estimatedTotalDuration) * 100));
          }
          
          updateLatestAITurnMetadata(callSid, { 
            interrupted: true,
            audioFinishedAt: undefined, // Not finished, was interrupted
            interruptedAt: turnTiming?.interruptionTimestamp || Date.now(),
            completionPercent: Math.round(completionPercent)
          });
        }
        
        // Send TTS_FINISHED event to XState machine
        const ttsFinishController = callControllers[callSid]; 
        if (ttsFinishController?.machine) { 
          try { 
            if (!callControllers[callSid]) { 
              console.log(`[XState:${callSid}] Skipping TTS_FINISHED event: Call already cleaned up`);  
            } else { 
              console.log(`[XState:${callSid}] Sending TTS_FINISHED event to machine ID: ${ttsFinishController.machine.id}`); 
              ttsFinishController.machine.send({ 
                type: 'TTS_FINISHED', 
                callSid: callSid
              });
            } 
          } catch (err) { 
            console.error(`[XState:${callSid}] Error sending TTS_FINISHED event:`, err); 
          } 
        } else { 
          console.log(`[XState:${callSid}] Skipping TTS_FINISHED event: Controller not found or machine stopped`); 
        } 
        
        resolve(code === 0); // Resolve promise with success (true) if code is 0
      });

      // Specify timeout duration (fixing the undefined variable error)
      const timeoutDuration = 15000; // 15 seconds
      
      // Set a timeout for the maximum duration just in case
      setTimeout(() => {
        if (LOGGING.STREAM_DEBUG) {
          console.log(`[STREAM-DEBUG] Setting timeout for ${timeoutDuration}ms for audio playback for call ${callSid}`);
        }
      }, timeoutDuration);
    });
  } catch (err) {
    console.error(`[STREAM-ERROR] Failed to stream audio to call ${callSid}:`, err);
    state.isModelSpeaking = false;
    return false;
  }
}

// We use the finalizeTurn function defined earlier in the file

// Global error handlers to prevent crashes (remains the same)
process.on('uncaughtException', (err) => {
  console.error('UNCAUGHT EXCEPTION - server will continue running:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('UNHANDLED REJECTION - server will continue running:', reason);
});

// For WebSocket error handling we use the safeJSONParse function defined earlier

// We use the getCallData function defined earlier in the file

// We use the saveConversation function defined earlier in the file

// Function to save conversation to Supabase database (remains the same)
async function saveConversationToDb(callSid: string, conversation: Array<{ speaker: string; text: string }>) {
  try {
    // 🔍 DEBUG: Save what's being sent to database
    const debugDir = path.join(__dirname, '../conversations', callSid);
    try {
      if (!fs.existsSync(debugDir)) {
        fs.mkdirSync(debugDir, { recursive: true });
      }
      const dbPath = path.join(debugDir, '5_database_save.json');
      fs.writeFileSync(dbPath, JSON.stringify(conversation, null, 2));
      console.log(`[DEBUG] Saved database payload: ${dbPath} (${conversation.length} turns)`);
      
      // Count speakers for debugging
      const callerCount = conversation.filter(turn => turn.speaker === 'caller').length;
      const assistantCount = conversation.filter(turn => turn.speaker === 'assistant').length;
      console.log(`[DEBUG] Database save breakdown: ${callerCount} caller + ${assistantCount} assistant = ${conversation.length} total`);
    } catch (debugError) {
      console.error(`[DEBUG] Failed to save database payload:`, debugError);
    }

    // Update the call_history table with the conversation
    const { error } = await supabase
      .from('call_history')
      .update({ 
        conversation_history: conversation,
        // Include a timestamp of when this was updated
        updated_at: new Date().toISOString()
      })
      .eq('call_sid', callSid);
    
    if (error) {
      console.error(`[DB] Supabase error updating conversation for ${callSid}:`, error); // Changed error.message to error
    } else {
      console.log(`[DB] Successfully updated conversation history for call ${callSid}`);
    }
  } catch (err) {
    console.error(`[DB] Error in database operation for ${callSid}:`, err);
  }
}


// We already initialized Supabase earlier in the file

// Function to create initial call record
export async function insertInitialCallRecord(callSid: string, callStartTime: Date, fromNumber: string | null, toNumber: string | null, userId: string | null, sessionId: string | null) {
  try {
    const { data, error } = await supabase
      .from('call_history') // Changed table name to call_history
      .insert([
        { 
          call_sid: callSid, 
          user_id: userId,
          session_id: sessionId,
          from_number: fromNumber,
          to_number: toNumber,
          call_start_time: callStartTime.toISOString(), 
          call_status: 'in-progress' 
        }
      ]);
    
    if (error) {
      console.error(`[DB] Error inserting call record for ${callSid}:`, error);
    } else {
      console.log(`[DB] Successfully inserted initial call record for ${callSid}`);
    }
  } catch (err) {
    console.error(`[DB] Exception inserting call record for ${callSid}:`, err);
  }
}

// Function to update call record on end
async function updateCallRecordOnEnd(callSid: string, callEndTime: Date, durationSeconds: number, recordingPath: string | null) {
  try {
    const { data, error } = await supabase
      .from('call_history') // Changed table name to call_history
      .update({ 
        call_end_time: callEndTime.toISOString(), 
        call_duration: durationSeconds, 
        call_recording_path: recordingPath, 
        call_status: 'completed' 
      })
      .eq('call_sid', callSid);
    
    if (error) {
      console.error(`[DB] Error updating call record on end for ${callSid}:`, error);
    } else {
      console.log(`[DB] Successfully updated call record on end for ${callSid}`);
    }
  } catch (err) {
    console.error(`[DB] Exception updating call record on end for ${callSid}:`, err);
  }
}

// Function to update recording path
async function updateCallRecordingPath(callSid: string, recordingPath: string, updateTime: Date) {
  try {
    const { data, error } = await supabase
      .from('call_history') // Changed 'calls' to 'call_history'
      .update({ 
        call_recording_path: recordingPath,
        updated_at: updateTime.toISOString()
      })
      .eq('call_sid', callSid);
    
    if (error) {
      console.error(`[DB] Error updating recording path for ${callSid}:`, error); // Changed error.message to error
    } else {
      console.log(`[DB] Successfully updated recording path for ${callSid} to ${recordingPath}`);
    }
  } catch (err) {
    console.error(`[DB] Exception updating recording path for ${callSid}:`, err);
  }
}

//-----------------------------------------------
// Initialize WebRTC Integration
//-----------------------------------------------

// Initialize WebRTC Integration for audio processing pipeline
// COMMENTED OUT: WebRTC P2P connection path
// let webrtcIntegration: WebRTCIntegration | null = null;

// WebRTC to CallSid mapping for audio processing integration
// const webrtcCallMapping: Map<string, string> = new Map(); // webrtcCallId -> callSid

// Helper function to create call state for WebRTC connections
// COMMENTED OUT: WebRTC P2P connection path
// function createWebRTCCallState(webrtcCallId: string, userId: string): string {
//   // Generate unique callSid for WebRTC calls
//   const callSid = `webrtc-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  
//   // Store mapping
//   webrtcCallMapping.set(webrtcCallId, callSid);
  
//   // Initialize call state similar to Twilio/Direct calls
//   const callState: CallState = {
//     callSid,
//     userId,
//     leftover: Buffer.alloc(0),
//     consecutiveSilenceFrames: 0,
//     silenceFrameCounter: 0,
//     speechFrameCounter: 0,
//     speechFramesInCurrentTurn: 0,
//     hasSpeechDetected: false,
//     isModelSpeaking: false,
//     finalizationInProgress: false,
//     currentTurnText: '',
//     conversation: [],
//     turnTimings: [],
//     currentTurnIndex: 0,
//     callStartTime: Date.now(),
//     connectionType: 'webrtc' as any, // Add webrtc to union type
//     audioQuality: 'high',
//     lastUpdated: Date.now(),
//     startTime: new Date().toISOString(),
//     silenceStart: null,
//     recording: false,
//     recordingStartTime: null,
//     recordingPath: null,
//     recordingWriter: null,
//     activeTtsOperation: false,
//   };
  
//   // Store in calls object
//   calls[callSid] = callState;
  
//   // Initialize call state machine if needed
//   try {
//     const initialAudioState = {
//       leftover: Buffer.alloc(0),
//       silenceStart: null,
//       recording: false,
//       recordingStartTime: null,
//       recordingPath: null,
//       recordingWriter: null,
//     };
    
//     initializeCallStateMachine(
//       callSid,
//       initialAudioState,
//       userId,
//       null, // sessionId
//       {}, // pendingCallDetailsMap
//     );
//   } catch (error) {
//     console.error(`[WebRTC] Failed to initialize state machine for ${callSid}:`, error);
//   }
  
//   console.log(`[WebRTC] Created call state for WebRTC call ${webrtcCallId} -> ${callSid}`);
//   return callSid;
// }

// Helper function to send TTS audio to WebRTC connections
// COMMENTED OUT: WebRTC P2P connection path
// async function sendTTSToWebRTC(callSid: string, audioPath: string, turnIndex: number): Promise<boolean> {
//   try {
//     // Find WebRTC callId from callSid mapping
//     let webrtcCallId: string | null = null;
//     for (const [wCallId, cSid] of webrtcCallMapping.entries()) {
//       if (cSid === callSid) {
//         webrtcCallId = wCallId;
//         break;
//       }
//     }
    
//     if (!webrtcCallId) {
//       console.warn(`[WebRTC-TTS] No WebRTC callId found for callSid ${callSid}`);
//       return false;
//     }
    
//     if (!webrtcIntegration) {
//       console.warn(`[WebRTC-TTS] WebRTC integration not available`);
//       return false;
//     }
    
//     // Read audio file
//     const fs = require('fs');
//     if (!fs.existsSync(audioPath)) {
//       console.error(`[WebRTC-TTS] Audio file not found: ${audioPath}`);
//       return false;
//     }
    
//     const audioBuffer = fs.readFileSync(audioPath);
    
//     // Send TTS audio to WebRTC connection
//     const success = await webrtcIntegration.handleTTSAudio(webrtcCallId, audioBuffer, {
//       turnIndex,
//       priority: 'normal',
//     });
    
//     if (success) {
//       console.log(`[WebRTC-TTS] Successfully sent TTS audio to WebRTC call ${webrtcCallId} (${audioBuffer.length} bytes)`);
//     } else {
//       console.error(`[WebRTC-TTS] Failed to send TTS audio to WebRTC call ${webrtcCallId}`);
//     }
    
//     return success;
    
//   } catch (error) {
//     console.error(`[WebRTC-TTS] Error sending TTS audio for ${callSid}:`, error);
//     return false;
//   }
// }

// Helper function to send TTS audio to LiveKit rooms via RTC handler
export async function sendTTSToLiveKit(callSid: string, audioPath: string, turnIndex: number): Promise<boolean> {
  const callState = calls[callSid];
  
  try {
    let rtcHandler = callState?.livekitRTCHandler;

    // Note: LiveKit TTS cancellation is now handled by the central TTS race condition logic
    // in generateAudioResponse(), following the same pattern as Twilio/Azure cancellation

    // If RTC handler not ready, wait a bit and retry (connection might still be establishing)
    if (!callState || !rtcHandler) {
      console.log(`[LiveKit-TTS] RTC handler not ready for ${callSid}, waiting for connection...`);
      
      // Wait up to 3 seconds for RTC handler to be ready, checking every 50ms for responsiveness
      const startTime = Date.now();
      const maxWaitTime = 1000; // 1 second max (optimized for responsiveness)
      
      while (Date.now() - startTime < maxWaitTime) {
        await new Promise(resolve => setTimeout(resolve, 50)); // Check every 50ms
        rtcHandler = calls[callSid]?.livekitRTCHandler;
        if (rtcHandler) {
          const waitTime = Date.now() - startTime;
          console.log(`[LiveKit-TTS] RTC handler ready after ${waitTime}ms for ${callSid}`);
          break;
        }
      }
      
      if (!calls[callSid] || !rtcHandler) {
        console.warn(`[LiveKit-TTS] No call state or RTC handler for callSid ${callSid} after waiting ${maxWaitTime}ms`);
        return false;
      }
    }
    
    if (!fs.existsSync(audioPath)) {
      console.error(`[LiveKit-TTS] Audio file not found: ${audioPath}`);
      return false;
    }
    
    console.log(`[LiveKit-TTS] Processing TTS audio file: ${audioPath} for call ${callSid}`);
    
    // Use optimized FFmpeg pool for FANG-level performance
    console.log(`[LiveKit-TTS] Using optimized FFmpeg pool for conversion: ${audioPath}`);
    
    // Read audio file into memory for pool processing (supports MP3 and OGG formats)
    const audioBuffer = fs.readFileSync(audioPath);
    
    // CRITICAL FIX: Set up callbacks to synchronize isModelSpeaking with actual LiveKit audio streaming
    rtcHandler.onTTSPlaybackStarted = (handlerCallSid: string) => {
      console.log(`[LiveKit-TTS-CALLBACK] TTS playback started for ${handlerCallSid}`);
      setModelSpeaking(handlerCallSid, true, "Audio streaming started (LiveKit RTC)", ConnectionType.LIVEKIT);
    };
    
    rtcHandler.onTTSPlaybackFinished = (handlerCallSid: string) => {
      console.log(`[LiveKit-TTS-CALLBACK] TTS playback finished for ${handlerCallSid}`);
      setModelSpeaking(handlerCallSid, false, "Audio streaming finished (LiveKit RTC)", ConnectionType.LIVEKIT);
    };
    
    // Send the audio file path to the RTC handler for processing
    // The RTC handler will now manage isModelSpeaking state via callbacks
    await rtcHandler.sendTTSAudio(audioPath);

    console.log(`[LiveKit-TTS] ✅ Successfully sent audio file ${audioPath} to room ${callSid}`);
    
    return true;
    
  } catch (error) {
    console.error(`[LiveKit-TTS] Error sending TTS audio for ${callSid}:`, error);
    
    // Note: TTS state will be reset via RTC handler callback even on error
    return false;
  }
}

/**
 * Stream audio chunk directly to LiveKit (no file I/O)
 */
export async function streamAudioChunkToLiveKit(
  callId: string, 
  chunk: Buffer, 
  turnIndex: number, 
  metadata?: StreamingMetadata
): Promise<void> {
  try {
    const call = calls[callId];
    if (!call) {
      throw new Error(`Call ${callId} not found for LiveKit chunk streaming`);
    }
    
    // Get LiveKit RTC handler
    const livekitHandler = call.livekitRTCHandler;
    if (!livekitHandler || !livekitHandler.publishAudioChunk) {
      throw new Error(`LiveKit handler not available for call ${callId}`);
    }
    
    // Stream chunk directly to LiveKit room
    await livekitHandler.publishAudioChunk(chunk, metadata);
    
    console.log(`[LiveKit-Chunk] Streamed chunk ${metadata?.chunkIndex} to room for ${callId} (${chunk.length} bytes)`);
    
  } catch (error) {
    console.error(`[LiveKit-Chunk] Error streaming chunk for ${callId}:`, error);
    throw error;
  }
}

/**
 * Stream audio chunk to direct WebSocket connection
 */
export async function streamAudioChunkToDirect(
  callId: string, 
  chunk: Buffer, 
  turnIndex: number, 
  metadata?: StreamingMetadata
): Promise<void> {
  try {
    const call = calls[callId];
    if (!call || !call.directWebSocket) {
      throw new Error(`Direct WebSocket connection not found for call ${callId}`);
    }
    
    // Send chunk as binary WebSocket message
    const chunkMessage = {
      type: 'audio_chunk',
      callId,
      turnIndex,
      chunk: chunk.toString('base64'),
      metadata
    };
    
    call.directWebSocket.send(JSON.stringify(chunkMessage));
    
    console.log(`[Direct-Chunk] Streamed chunk ${metadata?.chunkIndex} to WebSocket for ${callId} (${chunk.length} bytes)`);
    
  } catch (error) {
    console.error(`[Direct-Chunk] Error streaming chunk for ${callId}:`, error);
    throw error;
  }
}

/**
 * Stream audio chunk to Twilio connection
 */
export async function streamAudioChunkToTwilio(
  callId: string, 
  chunk: Buffer, 
  turnIndex: number, 
  metadata?: StreamingMetadata
): Promise<void> {
  try {
    const call = calls[callId];
    if (!call || !call.websocket) {
      throw new Error(`Twilio WebSocket not found for call ${callId}`);
    }
    
    // Convert chunk to Twilio media format
    const twilioMedia = {
      event: 'media',
      streamSid: call.streamSid,
      media: {
        payload: chunk.toString('base64')
      }
    };
    
    call.websocket.send(JSON.stringify(twilioMedia));
    
    console.log(`[Twilio-Chunk] Streamed chunk ${metadata?.chunkIndex} to Twilio for ${callId} (${chunk.length} bytes)`);
    
  } catch (error) {
    console.error(`[Twilio-Chunk] Error streaming chunk for ${callId}:`, error);
    throw error;
  }
}

// COMMENTED OUT: WebRTC P2P connection path
// (async () => {
//   try {
//     webrtcIntegration = new WebRTCIntegration({
//       enableSignaling: true,
//       enableStreaming: true,
//       enableAudioProcessing: true,
//       enableCallStateSync: true,
//       enableLifecycleManagement: true,
//       enableHealthMonitoring: true,
//       enableErrorRecovery: true,
//       enableAutoReconnection: true,
//     });

//     // Setup WebRTC event handlers for audio processing pipeline integration
//     webrtcIntegration.on('vadRequest', async (data) => {
//       const { callId, audioFrame, source } = data;
      
//       if (source === 'webrtc' && callId) {
//         // Map WebRTC callId to server callSid
//         const callSid = webrtcCallMapping.get(callId);
        
//         if (callSid) {
//           try {
//             // Process WebRTC audio through existing VAD pipeline using Twilio config for compatibility
//             await handleVadAndTurns(callSid, audioFrame, ConnectionType.TWILIO);
//           } catch (error) {
//             console.error(`[WebRTC-VAD] Error processing audio for ${callSid}:`, error);
//           }
//         } else {
//           console.warn(`[WebRTC-VAD] No callSid mapping found for WebRTC call ${callId}`);
//         }
//       }
//     });

//     webrtcIntegration.on('sttRequest', async (data) => {
//       const { callId, audioFrame, source } = data;
      
//       if (source === 'webrtc' && callId) {
//         // Map WebRTC callId to server callSid  
//         const callSid = webrtcCallMapping.get(callId);
        
//         if (callSid) {
//           // STT is handled through the same pipeline as VAD
//           // The handleVadAndTurns function will process STT automatically
//         } else {
//           console.warn(`[WebRTC-STT] No callSid mapping found for WebRTC call ${callId}`);
//         }
//       }
//     });

//     webrtcIntegration.on('callInitialized', (data) => {
//       console.log(`[WebRTC] Call initialized: ${data.callId} for user ${data.userId}`);
      
//       // Create call state for WebRTC connection
//       const callSid = createWebRTCCallState(data.callId, data.userId);
//       console.log(`[WebRTC] Created call state mapping: ${data.callId} -> ${callSid}`);
//     });

//     webrtcIntegration.on('connectionEstablished', (data) => {
//       console.log(`[WebRTC] Connection established for call: ${data.callId}`);
//     });

//     webrtcIntegration.on('connectionFailed', (data) => {
//       console.error(`[WebRTC] Connection failed for call: ${data.callId}`, data.error);
//     });

//     webrtcIntegration.on('callCleanup', (data) => {
//       const { callId } = data;
//       const callSid = webrtcCallMapping.get(callId);
      
//       if (callSid) {
//         console.log(`[WebRTC] Cleaning up call: ${callId} -> ${callSid}`);
        
//         // Clean up call state
//         try {
//           cleanupCall(callSid);
//         } catch (error) {
//           console.error(`[WebRTC] Error cleaning up call ${callSid}:`, error);
//         }
        
//         // Remove mapping
//         webrtcCallMapping.delete(callId);
//       }
//     });

//     webrtcIntegration.on('error', (error) => {
//       console.error('[WebRTC] Integration error:', error);
//     });

//     // Initialize the WebRTC integration
//     await webrtcIntegration.initialize();
//     console.log('[WebRTC] Integration initialized successfully');

//   } catch (error) {
//     console.error('[WebRTC] Failed to initialize WebRTC integration:', error);
//     webrtcIntegration = null;
//   }
// })();

//-----------------------------------------------
// Start the Servers
//-----------------------------------------------
const HTTPS_PORT = process.env.HTTPS_PORT || 5443;

// Start HTTP server (for WS development connections)
server.listen(PORT, async () => {
  console.log(`HTTP Server running on port ${PORT} (WS support for development)`)
  console.log(`WebRTC Integration: DISABLED (commented out)`) // COMMENTED OUT: WebRTC P2P connection path
  
  // 🚀 PHASE 2: Initialize Audio Plugin System
  try {
    const { initializeAudioPlugins, validateAudioPlugins } = await import('./services/audio-plugins');
    
    console.log('🚀 Initializing audio plugin system...');
    initializeAudioPlugins();
    
    console.log('🚀 Validating audio plugins...');
    await validateAudioPlugins();
    
    console.log('✅ Audio plugin system initialized and validated successfully');
  } catch (error) {
    console.error('❌ Failed to initialize audio plugin system:', error);
    console.error('   ⚠️  Audio providers may not work correctly - check environment configuration');
    // Don't exit - let the app continue with potential fallbacks
  }
  
  // 🚀 Initialize Phantom Call Detection System
  try {
    const { startPhantomCallDetection } = await import('./xstateIntegration');
    startPhantomCallDetection();
    console.log('✅ Phantom call detection system started successfully');
  } catch (error) {
    console.error('❌ Failed to initialize phantom call detection system:', error);
    // Don't exit - this is not critical for basic functionality
  }
  
  // 🚀 Initialize Knowledge Area Content System
  try {
    const { knowledgeAreaIntegration } = await import('./tutor/knowledge-area-integration-service');
    await knowledgeAreaIntegration.initializeSystem();
    console.log('✅ Knowledge Area Content System initialized successfully');
    
    // Setup test endpoints for knowledge area integration
    const { setupKnowledgeAreaTestRoutes } = await import('./tutor/test-knowledge-integration');
    setupKnowledgeAreaTestRoutes(app);
  } catch (error) {
    console.error('❌ Failed to initialize Knowledge Area Content System:', error);
    console.error('   Tutoring will continue but without expert learning objectives');
  }

  // 🚀 Initialize Enhanced Intelligence Systems (Neo4j + PostgreSQL)
  try {
    const { sessionManager } = await import('./tutor/session/session-manager');
    const result = await sessionManager.initializeIntelligenceSystems();
    
    if (result.neo4jInitialized) {
      console.log('✅ Enhanced Intelligence Systems initialized successfully');
      console.log(`   📊 Investigation pairs: ${result.investigationPairs}`);
      console.log('   🧠 Neo4j graph intelligence: ENABLED');
    } else {
      console.log('⚠️  Intelligence Systems initialized (PostgreSQL-only mode)');
      console.log(`   📊 Investigation pairs: ${result.investigationPairs}`);
      console.log('   🧠 Neo4j graph intelligence: DISABLED (fallback mode)');
    }
    
    if (result.errors.length > 0) {
      console.log('   ⚠️  Initialization warnings:');
      result.errors.forEach(error => console.log(`      - ${error}`));
    }
  } catch (error) {
    console.error('❌ Failed to initialize Intelligence Systems:', error);
    console.error('   Tutoring will continue but with reduced intelligent recommendations');
  }
  
  // Set up periodic cleanup
  setupPeriodicCleanup();
});

// Start HTTPS server if available (for WSS production connections)
if (httpsServer) {
  httpsServer.listen(HTTPS_PORT, () => {
    console.log(`HTTPS Server running on port ${HTTPS_PORT} (WSS support for production)`);
  });
} else {
  console.log(`HTTPS Server not started - SSL certificates not available`);
  console.log(`For production WSS support, ensure SSL certificates are available at:`);
  console.log(`  - /etc/ssl/certs/server.crt`);
  console.log(`  - /etc/ssl/private/server.key`);
}

//-----------------------------------------------
// Direct WebSocket for Web/iOS clients
// Separate endpoint from Twilio's /media
//-----------------------------------------------
// Note: DirectWebSocketHandler will be created after LiveKitServerHandler

// console.log('Direct WebSocket server created and listening on path: /direct-call');

// Handle direct WebSocket connections from web/iOS clients
directWss.on('connection', async (ws, request) => { // Made handler async
  console.log('[DIRECT_WSS_CONNECTION] Direct WebSocket connection established for /direct-call from:', request.socket.remoteAddress);
  if (directWebSocketHandler) { // Check if it's initialized
    directWebSocketHandler.handleConnection(ws, request);
  } else {
    console.error('[DIRECT_WSS_CONNECTION] directWebSocketHandler is not initialized');
    ws.close(1011, 'Server error: directWebSocketHandler not initialized');
  }
});

//-----------------------------------------------
// WebRTC Signaling WebSocket for Real-time Audio
// Handles WebRTC peer connections for iOS/Web clients
//-----------------------------------------------
// COMMENTED OUT: WebRTC P2P connection path
// const webrtcSignalingHandler = new WebRTCSignalingHandler();
const webSocketAuthService = new WebSocketAuthService();

// Create WebRTC signaling WebSocket server
// const webrtcWss = new WebSocketServer({
//   noServer: true // We will handle upgrades manually
// });

// console.log('WebRTC signaling WebSocket server created and listening on path: /webrtc-signaling');

// Handle WebRTC signaling connections
// webrtcWss.on('connection', async (ws, request) => {
//   console.log('[WEBRTC_WSS_CONNECTION] WebRTC signaling connection established for /webrtc-signaling from:', request.socket.remoteAddress);
//   
//   try {
//     // Extract and validate authentication token
//     const token = webSocketAuthService.extractTokenFromRequest(request);
//     if (!token) {
//       console.warn('[WEBRTC_WSS_CONNECTION] No authentication token found');
//       ws.close(4001, 'Authentication required');
//       return;
//     }

//     // Authenticate user
//     const user = await webSocketAuthService.authenticate(token);
//     if (!user) {
//       console.warn('[WEBRTC_WSS_CONNECTION] Authentication failed');
//       ws.close(4001, 'Authentication failed');
//       return;
//     }

//     console.log(`[WEBRTC_WSS_CONNECTION] Authenticated user: ${user.id}`);

//     // Generate unique call ID for WebRTC session
//     const callId = `webrtc-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
    
//     // Initialize WebRTC signaling session
//     await webrtcSignalingHandler.handleWebSocketUpgrade(ws, request, callId, user.id);
    
//   } catch (error) {
//     console.error('[WEBRTC_WSS_CONNECTION] Error handling WebRTC signaling connection:', error);
//     ws.close(1011, 'Internal server error');
//   }
// });


// Export the direct handler for use in other parts of the application
export { directWebSocketHandler };

// Export the WebRTC signaling handler for use in other parts of the application
// export { webrtcSignalingHandler }; // COMMENTED OUT: WebRTC P2P connection path

// --------------------------------------------------------------------------------------
// XState Integration API Endpoints
// --------------------------------------------------------------------------------------

// API to request state transition
app.post('/api/state/transition', function(req: Request, res: Response) {
  const { callSid, targetState, reason } = req.body;
  
  // The rest of the function can be async
  (async () => {
    try {
      // More comprehensive validation
      if (!callSid) {
        console.error(`[API] /api/state/transition - Missing callSid parameter`);
        return res.status(400).send({ 
          success: false, 
          error: 'Missing callSid parameter',
          timestamp: new Date().toISOString()
        });
      }
      
      if (!targetState) {
        console.error(`[API] /api/state/transition - Missing targetState parameter for call ${callSid}`);
        return res.status(400).send({ 
          success: false, 
          error: 'Missing targetState parameter',
          timestamp: new Date().toISOString()
        });
      }
      
      // Check if controller exists for this call
      if (!callControllers[callSid]) {
        console.error(`[API] /api/state/transition - Call not found: ${callSid}`);
        return res.status(404).send({ 
          success: false, 
          error: 'Call not found',
          timestamp: new Date().toISOString()
        });
      }
      
      // Check the current state to ensure we're not already in a transition
      const currentState = getCurrentState(callSid);
      if (!currentState) {
        console.error(`[API] /api/state/transition - Unable to get current state for call ${callSid}`);
        return res.status(500).send({ 
          success: false, 
          error: 'Unable to determine current state',
          timestamp: new Date().toISOString()
        });
      }
      
      // Don't allow new transitions if already in transition state
      if (currentState.inTransition) {
        console.warn(`[API] /api/state/transition - Call ${callSid} is already in transition state`);
        return res.status(409).send({ 
          success: false, 
          error: 'Already in transition state',
          currentState: currentState.state,
          timestamp: new Date().toISOString()
        });
      }
      
      // Request transition with validation
      const success = requestTransition(callSid, targetState, reason || 'user_request'); // Use string directly
      
      if (!success) {
        console.error(`[API] /api/state/transition - Invalid transition requested for call ${callSid}: ${currentState.state} -> ${targetState}`);
        return res.status(400).send({ 
          success: false, 
          error: `Invalid transition from '${currentState.state}' to '${targetState}'`,
          timestamp: new Date().toISOString()
        });
      }
      
      // Log successful request
      console.log(`[API] /api/state/transition - Successfully requested transition for call ${callSid}: ${currentState.state} -> ${targetState}`);
      
      // Get updated state (will reflect pending transition)
      const updatedState = getCurrentState(callSid);
      
      res.status(200).send({ 
        success: true, 
        previousState: currentState.state,
        currentState: updatedState?.state || 'unknown',
        inTransition: updatedState?.inTransition || false,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error(`[API] /api/state/transition - Unexpected error for call ${callSid}:`, error);
      res.status(500).send({ 
        success: false, 
        error: 'Internal server error',
        message: error.message || 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  })();
});

// API to accept transition
app.post('/api/state/accept-transition', function(req: Request, res: Response) {
  const { callSid } = req.body;
  
  // The rest of the function can be async
  (async () => {
    try {
      if (!callSid) {
        console.error(`[API] /api/state/accept-transition - Missing callSid parameter`);
        return res.status(400).send({ 
          success: false, 
          error: 'Missing callSid parameter',
          timestamp: new Date().toISOString()
        });
      }
      
      // Check if controller exists for this call
      if (!callControllers[callSid]) {
        console.error(`[API] /api/state/accept-transition - Call not found: ${callSid}`);
        return res.status(404).send({ 
          success: false, 
          error: 'Call not found',
          timestamp: new Date().toISOString()
        });
      }
      
      // Get current state to ensure we're actually in a transition
      const currentState = getCurrentState(callSid);
      if (!currentState) {
        console.error(`[API] /api/state/accept-transition - Unable to get current state for call ${callSid}`);
        return res.status(500).send({ 
          success: false, 
          error: 'Unable to determine current state',
          timestamp: new Date().toISOString()
        });
      }
      
      // Ensure we're in transition state
      if (!currentState.inTransition) {
        console.warn(`[API] /api/state/accept-transition - Call ${callSid} is not in transition state (current: ${currentState.state})`);
        return res.status(409).send({ 
          success: false, 
          error: 'Not in transition state',
          currentState: currentState.state,
          timestamp: new Date().toISOString()
        });
      }
      
      // Accept the transition
      const accepted = sendAcceptTransition(callSid);
      
      if (accepted) {
        // Log successful action
        console.log(`[API] /api/state/accept-transition - Successfully accepted transition for call ${callSid}`);
        
        // Get updated state after accepting transition
        const updatedState = getCurrentState(callSid);
        
        res.status(200).send({ 
          success: true, 
          previousState: 'transition_agent',
          currentState: updatedState?.state || 'unknown',
          timestamp: new Date().toISOString()
        });
      } else {
        console.error(`[API] /api/state/accept-transition - Failed to accept transition for call ${callSid}`);
        res.status(500).send({ 
          success: false, 
          error: 'Failed to accept transition',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error(`[API] /api/state/accept-transition - Unexpected error for call ${callSid}:`, error);
      res.status(500).send({ 
        success: false, 
        error: 'Internal server error',
        message: error.message || 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  })();
});

// API to reject transition
app.post('/api/state/reject-transition', function(req: Request, res: Response) {
  const { callSid, reason } = req.body;
  
  // The rest of the function can be async
  (async () => {
    try {
      if (!callSid) {
        console.error(`[API] /api/state/reject-transition - Missing callSid parameter`);
        return res.status(400).send({ 
          success: false, 
          error: 'Missing callSid parameter',
          timestamp: new Date().toISOString()
        });
      }
      
      // Check if controller exists for this call
      if (!callControllers[callSid]) {
        console.error(`[API] /api/state/reject-transition - Call not found: ${callSid}`);
        return res.status(404).send({ 
          success: false, 
          error: 'Call not found',
          timestamp: new Date().toISOString()
        });
      }
      
      // Get current state to ensure we're actually in a transition
      const currentState = getCurrentState(callSid);
      if (!currentState) {
        console.error(`[API] /api/state/reject-transition - Unable to get current state for call ${callSid}`);
        return res.status(500).send({ 
          success: false, 
          error: 'Unable to determine current state',
          timestamp: new Date().toISOString()
        });
      }
      
      // Ensure we're in transition state
      if (!currentState.inTransition) {
        console.warn(`[API] /api/state/reject-transition - Call ${callSid} is not in transition state (current: ${currentState.state})`);
        return res.status(409).send({ 
          success: false, 
          error: 'Not in transition state',
          currentState: currentState.state,
          timestamp: new Date().toISOString()
        });
      }
      
      // Track the rejection reason in XState context if provided
      if (reason) {
        try {
          const controller = callControllers[callSid];
          if (controller?.machine) {
            // @ts-ignore - Context access
            controller.machine.state.context.rejectionReason = reason;
          }
        } catch (err) {
          console.warn(`[API] /api/state/reject-transition - Failed to set rejection reason for call ${callSid}:`, err);
          // Non-critical error, continue with rejection
        }
      }
      
      // Reject the transition
      const rejected = sendRejectTransition(callSid);
      
      if (rejected) {
        // Log successful action
        console.log(`[API] /api/state/reject-transition - Successfully rejected transition for call ${callSid}${reason ? ` (reason: ${reason})` : ''}`);
        
        // Get updated state after rejecting transition
        const updatedState = getCurrentState(callSid);
        
        res.status(200).send({ 
          success: true, 
          previousState: 'transition_agent',
          currentState: updatedState?.state || 'unknown',
          timestamp: new Date().toISOString()
        });
      } else {
        console.error(`[API] /api/state/reject-transition - Failed to reject transition for call ${callSid}`);
        res.status(500).send({ 
          success: false, 
          error: 'Failed to reject transition',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error(`[API] /api/state/reject-transition - Unexpected error for call ${callSid}:`, error);
      res.status(500).send({ 
        success: false, 
        error: 'Internal server error',
        message: error.message || 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  })();
});

// API to get current state
app.get('/api/state/current/:callSid', function(req: Request, res: Response) {
  const { callSid } = req.params;
  
  // The rest of the function can be async
  (async () => {
    try {
      if (!callSid) {
        console.error(`[API] /api/state/current - Missing callSid parameter`);
        return res.status(400).send({ 
          success: false, 
          error: 'Missing callSid parameter',
          timestamp: new Date().toISOString()
        });
      }
      
      // Check if controller exists for this call
      if (!callControllers[callSid]) {
        console.error(`[API] /api/state/current - Call not found: ${callSid}`);
        return res.status(404).send({ 
          success: false, 
          error: 'Call not found',
          timestamp: new Date().toISOString()
        });
      }
      
      // Get current state
      const state = getCurrentState(callSid);
      
      if (state) {
        // Add additional context for monitoring
        const controller = callControllers[callSid];
        let additionalContext = {};
        
        if (controller?.machine) {
          try {
            // @ts-ignore - Context access
            const context = controller.machine.state.context;
            
            // Include transition context if available
            if (state.inTransition) {
              additionalContext = {
                fromState: context.fromState,
                toState: context.toState,
                transitionReason: context.transitionReason
              };
            }
            
            // Include error context if available
            if (context.transitionError) {
              additionalContext = {
                ...additionalContext,
                transitionError: context.transitionError
              };
            }
          } catch (err) {
            console.warn(`[API] /api/state/current - Error accessing context for call ${callSid}:`, err);
            // Non-critical error, continue with basic state info
          }
        }
        
        res.status(200).send({ 
          success: true, 
          currentState: state.state,
          inTransition: state.inTransition,
          turnCompleted: state.turnCompleted,
          ...additionalContext,
          timestamp: new Date().toISOString()
        });
      } else {
        console.error(`[API] /api/state/current - Failed to get current state for call ${callSid}`);
        res.status(500).send({ 
          success: false, 
          error: 'Failed to get current state',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error(`[API] /api/state/current - Unexpected error for call ${callSid}:`, error);
      res.status(500).send({ 
        success: false, 
        error: 'Internal server error',
        message: error.message || 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  })();
});

// API endpoint for LLM tool calling to trigger state transitions via DTMF
app.post('/api/dtmf-input', function(req: Request, res: Response) {
  const { callSid, digit, source } = req.body;
  
  // The rest of the function can be async
  (async () => {
    try {
    if (!callSid) {
      console.error(`[API] /api/dtmf-input - Missing callSid parameter`);
      return res.status(400).send({ 
        success: false, 
        error: 'Missing callSid parameter',
        timestamp: new Date().toISOString()
      });
    }
    
    if (!digit || typeof digit !== 'string') {
      console.error(`[API] /api/dtmf-input - Missing or invalid digit parameter for call ${callSid}`);
      return res.status(400).send({ 
        success: false, 
        error: 'Missing or invalid digit parameter',
        timestamp: new Date().toISOString()
      });
    }
    
    // Check if controller exists for this call
    if (!callControllers[callSid]) {
      console.error(`[API] /api/dtmf-input - Call not found: ${callSid}`);
      return res.status(404).send({ 
        success: false, 
        error: 'Call not found',
        timestamp: new Date().toISOString()
      });
    }
    
    // Use the sendDtmfInput function directly to ensure XState machine gets the event
    console.log(`[API] /api/dtmf-input - Sending DTMF digit '${digit}' to XState machine for call ${callSid} (source: ${source || 'unknown'})`);
    const success = sendDtmfInput(callSid, digit);
    
    if (success) {
      console.log(`[API] /api/dtmf-input - Successfully sent DTMF digit '${digit}' to XState machine for call ${callSid}`);
      res.status(200).send({ 
        success: true, 
        message: `DTMF digit '${digit}' sent to state machine`,
        callSid,
        digit,
        source: source || 'unknown',
        timestamp: new Date().toISOString()
      });
    } else {
      console.error(`[API] /api/dtmf-input - Failed to send DTMF digit '${digit}' to XState machine for call ${callSid}`);
      res.status(500).send({ 
        success: false, 
        error: 'Failed to send DTMF digit to state machine',
        timestamp: new Date().toISOString()
      });
    }
      } catch (error) {
      console.error(`[API] /api/dtmf-input - Unexpected error for call ${callSid}:`, error);
      res.status(500).send({ 
        success: false, 
        error: 'Internal server error',
        message: error.message || 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  })();
});

// API endpoint for LLM tool calling to trigger direct state transitions
app.post('/api/transition', function(req: Request, res: Response) {
  const { callSid, eventType, source } = req.body;
  
  // The rest of the function can be async
  (async () => {
    try {
      if (!callSid) {
        console.error(`[API] /api/transition - Missing callSid parameter`);
        return res.status(400).send({ 
          success: false, 
          error: 'Missing callSid parameter',
          timestamp: new Date().toISOString()
        });
      }
      
      const validEvents = ['TRANSITION_TO_EXAMINATION', 'TRANSITION_TO_MARKING', 'TRANSITION_TO_CLOSING'];
      if (!eventType || !validEvents.includes(eventType)) {
        console.error(`[API] /api/transition - Invalid or missing eventType parameter for call ${callSid}. Must be one of: ${validEvents.join(', ')}`);
        return res.status(400).send({ 
          success: false, 
          error: `Invalid or missing eventType parameter. Must be one of: ${validEvents.join(', ')}`,
          timestamp: new Date().toISOString()
        });
      }
      
      // Check if controller exists for this call
      if (!callControllers[callSid]) {
        console.error(`[API] /api/transition - Call not found: ${callSid}`);
        return res.status(404).send({ 
          success: false, 
          error: 'Call not found',
          timestamp: new Date().toISOString()
        });
      }
      
      // Send direct transition event to XState machine
      console.log(`[API] /api/transition - Sending ${eventType} event to XState machine for call ${callSid} (source: ${source || 'unknown'})`);
      
             try {
         // Send the event directly to the XState machine
         callControllers[callSid].machine.send({
           type: eventType,
           callSid: callSid
         });
        
        console.log(`[API] /api/transition - Successfully sent ${eventType} event to XState machine for call ${callSid}`);
        res.status(200).send({ 
          success: true, 
          message: `${eventType} event sent to state machine`,
          callSid,
          eventType,
          source: source || 'unknown',
          timestamp: new Date().toISOString()
        });
      } catch (xstateError) {
        console.error(`[API] /api/transition - Failed to send ${eventType} event to XState machine for call ${callSid}:`, xstateError);
        res.status(500).send({ 
          success: false, 
          error: 'Failed to send transition event to state machine',
          details: xstateError.message || 'Unknown XState error',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error(`[API] /api/transition - Unexpected error for call ${callSid}:`, error);
      res.status(500).send({ 
        success: false, 
        error: 'Internal server error',
        message: error.message || 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  })();
});

//-----------------------------------------------
// Medical AI Tutoring API Routes
//-----------------------------------------------

// Handle preflight requests for tutoring endpoints
app.options('/api/tutoring/*', function(req: Request, res: Response) {
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, ngrok-skip-browser-warning, X-Requested-With, Accept, Origin');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.status(200).end();
});

/**
 * POST /api/tutoring/sessions
 * Start a new tutoring session
 */
app.post('/api/tutoring/sessions', function(req: Request, res: Response) {
  (async () => {
    try {
      const { userId, specialtyId, conditionId, presentationId, difficultyLevel, targetDurationMinutes, learningObjectives } = req.body;
      
      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'Missing userId parameter'
        });
      }

      // Import tutoring functions dynamically
      const { startTutoringSession } = await import('./tutor');
      const { selectOptimalTopicForUser } = await import('./tutor/core/topic-selector');
      
      let finalSpecialtyId = specialtyId;
      let finalConditionId = conditionId;
      let finalPresentationId = presentationId;
      
      let config: any = {
        userId,
        specialtyId: finalSpecialtyId,
        conditionId: finalConditionId,
        presentationId: finalPresentationId,
        difficultyLevel: difficultyLevel || 'intermediate',
        targetDurationMinutes: targetDurationMinutes || 30,
        learningObjectives: learningObjectives || []
      };
      
      // If no specialty provided by UI, use intelligent topic selection from database
      if (!specialtyId) {
        console.log(`[SESSION-START] No specialty provided by UI, using intelligent topic selection from user preferences/study profile for user: ${userId}`);
        
        try {
          const optimalTopic = await selectOptimalTopicForUser(userId);
          finalSpecialtyId = optimalTopic.specialtyId;
          finalConditionId = optimalTopic.conditionId || conditionId;
          finalPresentationId = optimalTopic.presentationId || presentationId;
          
          // Update config with selected values including knowledge area
          config.specialtyId = finalSpecialtyId;
          config.conditionId = finalConditionId;
          config.presentationId = finalPresentationId;
          config.knowledgeAreaId = optimalTopic.knowledgeAreaId; // Include knowledge area for proper spaced repetition
          
          console.log(`[SESSION-START] Selected optimal topic:`, {
            specialty: finalSpecialtyId,
            condition: finalConditionId,
            presentation: finalPresentationId,
            knowledgeArea: optimalTopic.knowledgeAreaId,
            reason: optimalTopic.reason,
            confidence: optimalTopic.confidence,
            configurationStatus: optimalTopic.configurationStatus
          });
          
          // Store the configuration status for the response
          config.topicSelectionInfo = {
            reason: optimalTopic.reason,
            topicName: optimalTopic.topicName,
            configurationStatus: optimalTopic.configurationStatus
          };
        } catch (topicError) {
          console.error(`[SESSION-START] Topic selection failed completely:`, topicError);
          return res.status(500).json({
            success: false,
            error: 'Failed to select study topics from database. Please try again.',
            technicalError: topicError.message
          });
        }
      }

      const result = await startTutoringSession(config);
      
      // Auto-generate the first welcome message as a conversation turn
      try {
        const { handleConversationTurn } = await import('./tutor');
        
        // Send a special "session_start" message to trigger our enhanced welcome
        const welcomeResponse = await handleConversationTurn({
          sessionId: result.sessionId,
          userMessage: "Hello", // This will trigger generateProactiveWelcome
          responseTime: 1000,
          timestamp: new Date(),
          metadata: { 
            source: 'session_start',
            isInitialWelcome: true
          }
        });
        
        console.log(`[SESSION-START] Generated welcome message for session ${result.sessionId}`);
        
        res.json({
          success: true,
          sessionId: result.sessionId,
          session: result.session,
          hasExistingProgress: result.hasExistingProgress,
          progressData: result.progressData,
          welcomeMessage: welcomeResponse.tutorMessage, // Use the enhanced welcome
          initialWelcomeGenerated: true,
          topicSelectionInfo: config.topicSelectionInfo // Include topic selection info
        });
        
      } catch (welcomeError) {
        console.warn('[SESSION-START] Failed to generate initial welcome, using fallback:', welcomeError);
        
        res.json({
          success: true,
          sessionId: result.sessionId,
          session: result.session,
          hasExistingProgress: result.hasExistingProgress,
          progressData: result.progressData,
          welcomeMessage: 'Welcome to your personalized medical learning session!',
          initialWelcomeGenerated: false,
          topicSelectionInfo: config.topicSelectionInfo // Include topic selection info
        });
      }
      
    } catch (error) {
      console.error('[API] /api/tutoring/sessions error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to start tutoring session'
      });
    }
  })();
});

/**
 * POST /api/tutoring/sessions/:sessionId/messages
 * Send a message in a tutoring session
 */
app.post('/api/tutoring/sessions/:sessionId/messages', function(req: Request, res: Response) {
  (async () => {
    try {
      const { sessionId } = req.params;
      const { message, metadata } = req.body;
      
      if (!sessionId || !message) {
        return res.status(400).json({
          success: false,
          error: 'Missing sessionId or message'
        });
      }

      const { handleConversationTurn } = await import('./tutor');
      
      const request = {
        sessionId,
        userMessage: message,
        responseTime: metadata?.responseTime || 5000,
        timestamp: new Date(),
        metadata: {
          source: 'webapp',
          userAgent: req.get('User-Agent'),
          ...metadata
        }
      };

      const response = await handleConversationTurn(request);
      
      res.json({
        success: true,
        tutorMessage: response.tutorMessage,
        sessionPhase: response.sessionPhase,
        progressUpdate: response.progressUpdate,
        teachingStrategy: response.teachingStrategy,
        conceptsIntroduced: response.conceptsIntroduced,
        assessmentRequired: response.assessmentRequired,
        sessionShouldEnd: response.sessionShouldEnd,
        nextActions: response.nextActions
      });
      
    } catch (error) {
      console.error('[API] /api/tutoring/sessions/:sessionId/messages error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to process message',
        fallbackMessage: "I'm sorry, I had trouble processing that. Could you please try again?"
      });
    }
  })();
});

/**
 * GET /api/tutoring/sessions/:sessionId
 * Get tutoring session status
 */
app.get('/api/tutoring/sessions/:sessionId', function(req: Request, res: Response) {
  (async () => {
    try {
      const { sessionId } = req.params;
      
      if (!sessionId) {
        return res.status(400).json({
          success: false,
          error: 'Missing sessionId parameter'
        });
      }

      const { getTutoringSessionStatus } = await import('./tutor');
      
      const status = await getTutoringSessionStatus(sessionId);
      
      res.json({
        success: true,
        session: status.session,
        health: status.health,
        summary: status.summary,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('[API] /api/tutoring/sessions/:sessionId error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to get session status'
      });
    }
  })();
});

/**
 * POST /api/tutoring/sessions/:sessionId/complete
 * Complete a tutoring session
 */
app.post('/api/tutoring/sessions/:sessionId/complete', function(req: Request, res: Response) {
  (async () => {
    try {
      const { sessionId } = req.params;
      const { feedback, concepts, score } = req.body;
      
      if (!sessionId) {
        return res.status(400).json({
          success: false,
          error: 'Missing sessionId parameter'
        });
      }

      const { finalizeTutoringSession } = await import('./tutor');
      
      const result = await finalizeTutoringSession(sessionId, {
        conceptsLearned: concepts || [],
        objectivesAchieved: [],
        finalScore: score || 75,
        timeSpent: 30,
        userFeedback: feedback
      });
      
      res.json({
        success: true,
        session: result.session,
        experienceGained: result.experienceGained,
        achievementsUnlocked: result.achievementsUnlocked,
        progressUpdated: result.progressUpdated,
        completedAt: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('[API] /api/tutoring/sessions/:sessionId/complete error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to complete session'
      });
    }
  })();
});

/**
 * GET /api/tutoring/users/:userId/progress
 * Get user learning progress
 */
app.get('/api/tutoring/users/:userId/progress', function(req: Request, res: Response) {
  (async () => {
    try {
      const { userId } = req.params;
      const { specialtyId } = req.query;
      
      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'Missing userId parameter'
        });
      }

      const { getUserMasteryBySpecialty, getProgressForReview } = await import('./tutor');
      
      const [masteryProgress, reviewItems] = await Promise.all([
        specialtyId ? getUserMasteryBySpecialty(userId, Number(specialtyId)) : [],
        getProgressForReview(userId, 10)
      ]);

      res.json({
        success: true,
        mastery: masteryProgress,
        reviewDue: reviewItems,
        totalMastered: masteryProgress.filter(p => p.masteryLevel >= 4).length,
        totalLearning: masteryProgress.length
      });
      
    } catch (error) {
      console.error('[API] /api/tutoring/users/:userId/progress error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to get user progress'
      });
    }
  })();
});

/**
 * GET /api/tutoring/users/:userId/prestige
 * Get user prestige and gamification data
 */
app.get('/api/tutoring/users/:userId/prestige', function(req: Request, res: Response) {
  (async () => {
    try {
      const { userId } = req.params;
      const { specialtyId } = req.query;
      
      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'Missing userId parameter'
        });
      }

      const { getUserPrestige, getPrestigeBadge } = await import('./tutor');
      
      const prestige = await getUserPrestige(userId, specialtyId ? Number(specialtyId) : undefined);
      
      if (!prestige) {
        return res.json({
          success: true,
          prestige: null,
          message: 'No prestige data found. Complete your first session to start earning experience!'
        });
      }

      const badge = getPrestigeBadge(prestige.prestigeLevel);
      
      res.json({
        success: true,
        prestige: {
          level: prestige.prestigeLevel,
          badge,
          experiencePoints: prestige.experiencePoints,
          sessionsCompleted: prestige.sessionsCompleted,
          conceptsMastered: prestige.conceptsMastered,
          streakDays: prestige.streakDays,
          longestStreak: prestige.longestStreak,
          rank: prestige.rankWithinSpecialty,
          percentile: prestige.percentile,
          achievements: Object.keys(prestige.achievements).length
        }
      });
      
    } catch (error) {
      console.error('[API] /api/tutoring/users/:userId/prestige error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to get user prestige'
      });
    }
  })();
});

/**
 * GET /api/tutoring/specialties
 * Get available medical specialties for selection
 */
app.get('/api/tutoring/specialties', function(req: Request, res: Response) {
  (async () => {
    try {
      const { data, error } = await supabase
        .from('specialties')
        .select('id, name, display_order, is_active')
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) {
        throw new Error(`Failed to fetch specialties: ${error.message}`);
      }

      res.json({
        success: true,
        specialties: data || [],
        count: data?.length || 0
      });
      
    } catch (error) {
      console.error('[API] /api/tutoring/specialties error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to fetch specialties'
      });
    }
  })();
});

/**
 * GET /api/tutoring/specialties/:specialtyId/conditions
 * Get conditions for a specific specialty
 */
app.get('/api/tutoring/specialties/:specialtyId/conditions', function(req: Request, res: Response) {
  (async () => {
    try {
      const { specialtyId } = req.params;
      
      if (!specialtyId) {
        return res.status(400).json({
          success: false,
          error: 'Missing specialtyId parameter'
        });
      }

      const { data, error } = await supabase
        .from('specialty_conditions')
        .select('id, specialty_id, condition_name')
        .eq('specialty_id', parseInt(specialtyId))
        .order('condition_name', { ascending: true });

      if (error) {
        throw new Error(`Failed to fetch conditions: ${error.message}`);
      }

      res.json({
        success: true,
        conditions: data || [],
        specialtyId: parseInt(specialtyId),
        count: data?.length || 0
      });
      
    } catch (error) {
      console.error('[API] /api/tutoring/specialties/:specialtyId/conditions error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to fetch conditions'
      });
    }
  })();
});

/**
 * GET /api/tutoring/specialties/:specialtyId/presentations
 * Get presentations for a specific specialty
 */
app.get('/api/tutoring/specialties/:specialtyId/presentations', function(req: Request, res: Response) {
  (async () => {
    try {
      const { specialtyId } = req.params;
      
      if (!specialtyId) {
        return res.status(400).json({
          success: false,
          error: 'Missing specialtyId parameter'
        });
      }

      const { data, error } = await supabase
        .from('specialty_presentations')
        .select('id, specialty_id, presentation_name')
        .eq('specialty_id', parseInt(specialtyId))
        .order('presentation_name', { ascending: true });

      if (error) {
        throw new Error(`Failed to fetch presentations: ${error.message}`);
      }

      res.json({
        success: true,
        presentations: data || [],
        specialtyId: parseInt(specialtyId),
        count: data?.length || 0
      });
      
    } catch (error) {
      console.error('[API] /api/tutoring/specialties/:specialtyId/presentations error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to fetch presentations'
      });
    }
  })();
});

/**
 * GET /api/tutoring/hierarchy
 * Get complete specialty hierarchy (specialties with their conditions and presentations)
 */
app.get('/api/tutoring/hierarchy', function(req: Request, res: Response) {
  (async () => {
    try {
      // Get specialties
      const { data: specialties, error: specialtiesError } = await supabase
        .from('specialties')
        .select('id, name, display_order, is_active')
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (specialtiesError) {
        throw new Error(`Failed to fetch specialties: ${specialtiesError.message}`);
      }

      // Get all conditions
      const { data: conditions, error: conditionsError } = await supabase
        .from('specialty_conditions')
        .select('id, specialty_id, condition_name')
        .order('condition_name', { ascending: true });

      if (conditionsError) {
        throw new Error(`Failed to fetch conditions: ${conditionsError.message}`);
      }

      // Get all presentations  
      const { data: presentations, error: presentationsError } = await supabase
        .from('specialty_presentations')
        .select('id, specialty_id, presentation_name')
        .order('presentation_name', { ascending: true });

      if (presentationsError) {
        throw new Error(`Failed to fetch presentations: ${presentationsError.message}`);
      }

      // Build hierarchy
      const hierarchy = specialties.map(specialty => ({
        id: specialty.id,
        name: specialty.name,
        displayOrder: specialty.display_order,
        conditions: conditions.filter(c => c.specialty_id === specialty.id),
        presentations: presentations.filter(p => p.specialty_id === specialty.id)
      }));

      res.json({
        success: true,
        hierarchy,
        summary: {
          totalSpecialties: specialties.length,
          totalConditions: conditions.length,
          totalPresentations: presentations.length
        }
      });
      
    } catch (error) {
      console.error('[API] /api/tutoring/hierarchy error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to fetch hierarchy'
      });
    }
  })();
});

/**
 * GET /api/tutoring/health
 * Health check for tutoring system
 */
app.get('/api/tutoring/health', function(req: Request, res: Response) {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    features: {
      spaced_repetition: true,
      anti_false_mastery: true,
      gamification: true,
      multi_llm_orchestration: true
    }
  });
});

//-----------------------------------------------
// LiveKit API Routes
//-----------------------------------------------

/**
 * POST /api/livekit/create-room
 * iOS client requests room creation and token generation
 * REQUIRES: Authorization header with Supabase JWT token
 */
app.post('/api/livekit/create-room', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token from Authorization header
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        console.warn('[LiveKit API] No authentication token provided');
        return res.status(401).json({
          success: false,
          error: 'Authentication required. Include Authorization: Bearer <token> header.'
        });
      }

      // Authenticate user using the same system as WebSocket connections
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        console.warn('[LiveKit API] Authentication failed - invalid or expired token');
        return res.status(401).json({
          success: false,
          error: 'Authentication failed. Invalid or expired token.'
        });
      }

      console.log(`[LiveKit API] Authenticated user: ${user.id}`);

      const { userId, sessionId } = req.body;

      // Verify userId matches authenticated user (prevent impersonation)
      if (userId && userId !== user.id) {
        console.warn(`[LiveKit API] User ID mismatch: token=${user.id}, requested=${userId}`);
        return res.status(403).json({
          success: false,
          error: 'User ID mismatch. Cannot create room for different user.'
        });
      }

      // Use authenticated user ID (ignore any userId in request body)
      const authenticatedUserId = user.id;

      console.log(`[LiveKit API] Room creation request from authenticated user: ${authenticatedUserId}`);

      // Request room creation from liveserver
      const roomResult = await livekitRoomService.createRoom({
        userId: authenticatedUserId,
        sessionId,
        audioFormat: {
          sampleRate: 8000,  // Target format for our pipeline
          channels: 1,       // Mono
          bitDepth: 16       // 16-bit
        }
      });

      if (!roomResult.success) {
        console.error(`[LiveKit API] Room creation failed: ${roomResult.error}`);
        return res.status(500).json({
          success: false,
          error: roomResult.error || 'Failed to create room'
        });
      }

      // Use room name as callSid for consistency
      const callSid = roomResult.roomName;
      
      console.log(`[LiveKit API] ✅ Room created: ${callSid} for user: ${authenticatedUserId}`);

      // Return room details to iOS client
      res.json({
        success: true,
        roomName: roomResult.roomName,
        token: roomResult.token,
        wsUrl: roomResult.wsUrl,
        callSid: callSid, // For iOS client reference
        sessionId: sessionId,
        userId: authenticatedUserId // Return the authenticated user ID
      });

    } catch (error) {
      console.error(`[LiveKit API] Unexpected error:`, error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  })();
});

/**
 * POST /api/livekit/end-session
 * iOS client notifies session end for cleanup
 * REQUIRES: Authorization header with Supabase JWT token
 */
app.post('/api/livekit/end-session', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token from Authorization header
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        console.warn('[LiveKit API] No authentication token provided for end-session');
        return res.status(401).json({
          success: false,
          error: 'Authentication required. Include Authorization: Bearer <token> header.'
        });
      }

      // Authenticate user using the same system as WebSocket connections
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        console.warn('[LiveKit API] Authentication failed for end-session - invalid or expired token');
        return res.status(401).json({
          success: false,
          error: 'Authentication failed. Invalid or expired token.'
        });
      }

      console.log(`[LiveKit API] End-session request from authenticated user: ${user.id}`);

      const { roomName, callSid } = req.body;

      if (!roomName || !callSid) {
        return res.status(400).json({
          success: false,
          error: 'roomName and callSid are required'
        });
      }

      // Verify the room/call belongs to the authenticated user
      // Room names are formatted as: room-{userId}-{timestamp}
      if (!roomName.startsWith(`room-${user.id}-`)) {
        console.warn(`[LiveKit API] User ${user.id} attempted to end session for room ${roomName} (not owned)`);
        return res.status(403).json({
          success: false,
          error: 'Cannot end session for room not owned by authenticated user.'
        });
      }

      console.log(`[LiveKit API] Session end request for room: ${roomName} by user: ${user.id}`);

      // Clean up room resources
      const cleanupResult = await livekitRoomService.cleanupRoom(roomName, callSid);

      res.json({
        success: cleanupResult,
        message: cleanupResult ? 'Session ended successfully' : 'Cleanup completed with warnings'
      });

    } catch (error) {
      console.error(`[LiveKit API] Session end error:`, error);
      res.status(500).json({
        success: false,
        error: 'Failed to end session'
      });
    }
  })();
});

/**
 * GET /api/livekit/health
 * Health check for LiveKit integration
 */
app.get('/api/livekit/health', function(req, res) {
  (async () => {
    try {
      const connectivity = await livekitRoomService.testConnectivity();

      res.json({
        success: true,
        liveserver: connectivity.liveserverReachable,
        livekitServer: connectivity.livekitServerReachable,
        status: connectivity.liveserverReachable && connectivity.livekitServerReachable ? 'healthy' : 'degraded',
        error: connectivity.error
      });

    } catch (error) {
      console.error(`[LiveKit API] Health check error:`, error);
      res.status(500).json({
        success: false,
        error: 'Health check failed'
      });
    }
  })();
});

/**
 * LiveKit Server Handler - moved to top level
 */
// Import the server handler
import { LiveKitServerHandler } from './services/livekit/livekit-server-handler';
import { LiveKitWebhookListener } from './services/livekit/livekit-webhook-listener';
import { createLiveKitRTCHandler, LiveKitRTCHandler } from './services/livekit/livekit-rtc-handler-working';
const livekitServerHandler = new LiveKitServerHandler();
const livekitWebhookListener = new LiveKitWebhookListener();

// Create DirectWebSocketHandler with shared LiveKitServerHandler instance
const directWebSocketHandler = new DirectWebSocketHandler(livekitServerHandler);

// LiveKit webhook endpoint is now defined earlier in the file before body parsing middleware

// Manual Track Egress trigger endpoint (for debugging)
app.post('/api/livekit/manual-egress', function(req, res) {
  (async () => {
    try {
      const { roomName, callSid, participantIdentity, trackSid } = req.body;
      
      if (!roomName || !callSid || !participantIdentity || !trackSid) {
        return res.status(400).json({ 
          error: 'Missing required fields: roomName, callSid, participantIdentity, trackSid' 
        });
      }
      
      console.log(`[Manual Egress] Triggering Track Egress for ${callSid}`);
      console.log(`[Manual Egress] Room: ${roomName}, Track: ${trackSid}, Participant: ${participantIdentity}`);
      
      const success = await livekitServerHandler.createTrackEgressForParticipant(
        roomName,
        callSid,
        participantIdentity,
        trackSid
      );
      
      if (success) {
        res.json({ success: true, message: 'Track Egress created successfully' });
      } else {
        res.status(500).json({ success: false, error: 'Failed to create Track Egress' });
      }
      
    } catch (error) {
      console.error('[Manual Egress] Error:', error);
      res.status(500).json({ success: false, error: error.message });
    }
  })();
});

/**
 * WebSocket endpoint for LiveKit Track Egress
 * Handle incoming audio stream from LiveKit egress via WebSocket
 */
const egressWss = new WebSocketServer({ noServer: true });

egressWss.on('connection', (ws: any, request: any) => {
  // Extract callSid from URL path
  const urlParts = request.url.split('/');
  const callSid = urlParts[urlParts.length - 1];
  
  console.log(`[LiveKit Egress WS] Connection established for callSid: ${callSid}`);
  
  if (callSid) {
    livekitServerHandler.handleEgressWebSocket(ws, callSid);
  } else {
    console.warn(`[LiveKit Egress WS] Could not extract callSid from URL: ${request.url}`);
    ws.close(1011, 'Invalid URL path');
  }
});

// --- State Machine Interaction Functions ---
// Fetches case data (currently hardcoded, replace with DB logic later)
export async function fetchRandomCaseData(callSid: string): Promise<CaseData | null> {
  console.log(`[SERVER:${callSid}] Fetching case data...`);
  // TODO: Replace with actual database fetch logic
  const cases = [
    {
      id: 'case1',
      scenario: 'Headache', 
      initial_question: 'Okay, please tell me about this headache. When did it start?',
      questions: [], // Add empty questions array
      data: {} // Add an empty data property to ensure compatibility with code expectations
    },
    {
      id: 'case2',
      scenario: 'Chest Pain', 
      initial_question: 'Got it. Can you describe the chest pain for me?',
      questions: [], // Add empty questions array
      data: {} // Add an empty data property to ensure compatibility with code expectations
    }
  ];
  const selectedCase = cases[Math.floor(Math.random() * cases.length)];
  console.log(`[SERVER:${callSid}] Selected case: ${selectedCase.scenario}`);
  return selectedCase;
}

function updateGlobalContext(callSid: string, contextUpdate: any) {
  return global.contextCache.updateContext(callSid, contextUpdate);
}

function getGlobalContext(callSid: string) {
  return global.contextCache.getContext(callSid);
}

// ============================================================================
// DIAGNOSTIC CONFIGURATION - Control verbose logging for debugging
// ============================================================================
const DIAGNOSTIC_LOGGING = {
  // Turn finalization and race condition debugging
  TURN_FINALIZATION: true,
  TTS_RACE_CONDITIONS: true,
  VAD_VERBOSE: process.env.VERBOSE_VAD_LOGGING === 'true', // Enable via env var
  
  // State machine debugging
  STATE_TRANSITIONS: true,
  CONTEXT_CHANGES: false, // Reduced to prevent spam
  
  // Audio processing debugging
  AUDIO_BUFFER_DETAILS: false,
  INTERRUPT_DETAILS: true
};

// Log diagnostic configuration on startup
console.log('[DIAGNOSTIC] Logging configuration:', DIAGNOSTIC_LOGGING);



function getOrCreateTtsSynthesizer(callSid: string): sdk.SpeechSynthesizer {
  const existing = ttsConnectionPool.get(callSid);
  const now = Date.now();
  
  // Reuse existing connection if still valid
  if (existing && (now - existing.lastUsed) < TTS_CONNECTION_TIMEOUT) {
    existing.lastUsed = now;
    console.log(`[TTS-POOL] Reusing connection for ${callSid}`);
    return existing.synthesizer;
  }
  
  // Create new connection
  const speechConfig = sdk.SpeechConfig.fromSubscription(AZURE_SPEECH_KEY, AZURE_SPEECH_REGION);
  speechConfig.speechSynthesisVoiceName = AZURE_SPEECH_VOICE;
  
  // Enable compressed audio for faster transmission
  speechConfig.speechSynthesisOutputFormat = sdk.SpeechSynthesisOutputFormat.Audio24Khz48KBitRateMonoMp3;
  
  const synthesizer = new sdk.SpeechSynthesizer(speechConfig, null);
  
  // Clean up old connection if it exists
  if (existing) {
    existing.synthesizer.close();
  }
  
  ttsConnectionPool.set(callSid, { synthesizer, lastUsed: now });
  console.log(`[TTS-POOL] Created new connection for ${callSid}`);
  
  return synthesizer;
}

/**
 * SAFE STREAMING TTS IMPLEMENTATION
 * 
 * This provides faster time-to-first-audio while maintaining all existing contracts:
 * - Still produces one file per turn for recording/interruption systems
 * - Streams sentences as they're ready for lower latency
 * - Safe fallback to existing implementation
 */

interface SentenceSegment {
  text: string;
  index: number;
  audioPath?: string;
  startTime?: number;
  endTime?: number;
}

// PHASE 1: splitIntoSentences function moved to AzureTTSService

/**
 * Create a proper WAV file from raw PCM audio data
 * Deepgram returns raw linear16 PCM, but we need WAV headers for FFmpeg compatibility
 */
function createWavFile(audioBuffer: Buffer, sampleRate: number): Buffer {
  const channels = 1; // Mono
  const bitsPerSample = 16; // 16-bit PCM
  const byteRate = sampleRate * channels * bitsPerSample / 8;
  const blockAlign = channels * bitsPerSample / 8;
  const dataSize = audioBuffer.length;
  const fileSize = 36 + dataSize;

  // Create WAV header (44 bytes)
  const header = Buffer.alloc(44);
  
  // RIFF header
  header.write('RIFF', 0);
  header.writeUInt32LE(fileSize, 4);
  header.write('WAVE', 8);
  
  // fmt chunk
  header.write('fmt ', 12);
  header.writeUInt32LE(16, 16); // PCM chunk size
  header.writeUInt16LE(1, 20);  // Audio format (PCM)
  header.writeUInt16LE(channels, 22); // Number of channels
  header.writeUInt32LE(sampleRate, 24); // Sample rate
  header.writeUInt32LE(byteRate, 28); // Byte rate
  header.writeUInt16LE(blockAlign, 32); // Block align
  header.writeUInt16LE(bitsPerSample, 34); // Bits per sample
  
  // data chunk
  header.write('data', 36);
  header.writeUInt32LE(dataSize, 40);
  
  // Combine header and audio data
  return Buffer.concat([header, audioBuffer]);
}

/**
 * Convert PCM buffer to µ-law format for Twilio using ITU-T G.711 compliant encoding
 * Twilio expects µ-law encoded audio at 8kHz sample rate
 * Uses alawmulaw library for proper standards-compliant conversion
 */
function pcmToUlaw(pcmBuffer: Buffer): Buffer {
  // Convert Buffer to Int16Array for alawmulaw library
  const pcmSamples = new Int16Array(pcmBuffer.buffer, pcmBuffer.byteOffset, pcmBuffer.length / 2);
  
  // Encode PCM to µ-law using ITU-T G.711 compliant library
  const ulawSamples = alawmulaw.mulaw.encode(pcmSamples);
  
  // Convert Uint8Array back to Buffer
  return Buffer.from(ulawSamples);
}

// PHASE 1 CLEANUP: concatenateAudioFiles function moved to AzureTTSService

// PHASE 1 CLEANUP: synthesizeSentence function moved to AzureTTSService

// PHASE 1 CLEANUP: streamSentenceToCall function moved to AzureTTSService

/**
 * Make streaming RAG request to Python LLM server
 */
async function makeStreamingRagRequest(
  userMessage: string, 
  callSid: string, 
  conversationHistory: Array<{ speaker: string; text: string }> = [],
  caseData?: any
): Promise<NodeJS.ReadableStream | null> {
  try {
    const currentStateObj = getCurrentState(callSid);
    let role: 'patient' | 'examiner' | 'mark' = 'patient';
    
    // Determine role based on current state
    if (currentStateObj?.state) {
      const currentState = currentStateObj.state;
      if (currentState === 'patient_interaction' || currentState === 'select_case' || currentState === 'readiness_check') {
        role = 'patient';
      } else if (currentState === 'examination_phase') {
        role = 'examiner';
      }
    }
    
    console.log(`[STREAMING-RAG] Using LLM router for streaming request: ${role}`);
    
    return await routeStreamingRAG(
      callSid,
      userMessage,
      role,
      conversationHistory,
      caseData
    );
  } catch (error) {
    console.error(`[STREAMING-RAG] Error in streaming RAG request for ${callSid}:`, error);
    return null;
  }
}

/**
 * Full end-to-end streaming: LLM → ElevenLabs → Twilio
 * This is the ultra-low latency pathway
 */
export async function generateStreamingResponse(
  userMessage: string, 
  callSid: string, 
  turnIndex: number,
  conversationHistory: Array<{ speaker: string; text: string }> = [],
  caseData?: any
): Promise<{ audioPath: string; responseText: string } | null> {
  try {
    console.log(`[FULL-STREAMING] Starting end-to-end streaming for ${callSid} turn ${turnIndex}`);
    
    // Setup latency tracking
    const existingSession = latencyTracker.getCurrentSession(callSid, turnIndex);
    if (!existingSession) {
      latencyTracker.startTracking(callSid, turnIndex);
      latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TURN_FINALIZED);
    }
    
    latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TTS_GENERATION_STARTED, {
      mode: 'full_streaming'
    });
    
    // Create audio file path for final recording
    const audioPath = `/tmp/response_${callSid}_${turnIndex}_full_stream.wav`;
    
    // Get streaming LLM response
    const llmStream = await makeStreamingRagRequest(userMessage, callSid, conversationHistory, caseData);
    if (!llmStream) {
      console.error(`[FULL-STREAMING] Failed to get LLM stream for ${callSid}`);
      return null;
    }
    
    // Setup ElevenLabs WebSocket
    const wsUrl = `wss://api.elevenlabs.io/v1/text-to-speech/${ELEVENLABS_VOICE_ID}/stream-input?model_id=${ELEVENLABS_MODEL_ID}&output_format=ulaw_8000`;
    const ws = new WebSocket(wsUrl, {
      headers: {
        'xi-api-key': ELEVENLABS_API_KEY
      }
    });
    
    // CRITICAL: Store WebSocket in call state for cancellation
    const callState = calls[callSid];
    if (callState) {
      callState.currentElevenLabsWebSocket = ws;
      console.log(`[STREAMING-TTS] Stored ElevenLabs WebSocket for ${callSid} cancellation tracking`);
    }
    
    const audioChunks: Buffer[] = [];
    let fullText = '';
    let isComplete = false;
    
    return new Promise((resolve, reject) => {
      ws.on('open', () => {
        console.log(`[FULL-STREAMING] ElevenLabs WebSocket connected for ${callSid}`);
        
        // Pipe LLM stream directly to ElevenLabs
        llmStream.on('data', (chunk: Buffer) => {
          const text = chunk.toString();
          fullText += text;
          
          // Send each chunk to ElevenLabs immediately
          ws.send(JSON.stringify({
            text: text,
            try_trigger_generation: true
          }));
          
          console.log(`[FULL-STREAMING] Sent to ElevenLabs: "${text}"`);
        });
        
        llmStream.on('end', () => {
          console.log(`[FULL-STREAMING] LLM stream ended, sending EOS to ElevenLabs`);
          
          // Send end of stream to ElevenLabs
          ws.send(JSON.stringify({
            text: ""
          }));
        });
        
        llmStream.on('error', (error) => {
          console.error(`[FULL-STREAMING] LLM stream error:`, error);
          ws.close();
          reject(error);
        });
      });
      
      ws.on('message', (data: Buffer) => {
        try {
          const response = JSON.parse(data.toString());
          
          if (response.audio) {
            // Decode base64 audio and collect chunks
            const audioBuffer = Buffer.from(response.audio, 'base64');
            audioChunks.push(audioBuffer);
            
            // Stream directly to Twilio WebSocket for ultra-low latency
            streamAudioDirectlyToTwilio(callSid, audioBuffer);
            
            console.log(`[FULL-STREAMING] Streamed ${audioBuffer.length} bytes directly to Twilio for ${callSid}`)
          }
          
          if (response.isFinal) {
            console.log(`[FULL-STREAMING] ElevenLabs stream complete for ${callSid}`);
            isComplete = true;
            ws.close();
            
            // Clear WebSocket reference from call state
            if (callState) {
              callState.currentElevenLabsWebSocket = undefined;
              console.log(`[STREAMING-TTS] Cleared ElevenLabs WebSocket reference for ${callSid}`);
            }
          }
        } catch (error) {
          console.error(`[FULL-STREAMING] Error parsing ElevenLabs response:`, error);
        }
      });
      
      ws.on('close', () => {
        console.log(`[FULL-STREAMING] ElevenLabs WebSocket closed for ${callSid}`);
        
        // Clear WebSocket reference from call state
        if (callState) {
          callState.currentElevenLabsWebSocket = undefined;
          console.log(`[STREAMING-TTS] Cleared ElevenLabs WebSocket reference on close for ${callSid}`);
        }
        
        if (audioChunks.length > 0) {
          // Combine all audio chunks and write to file
          const finalAudio = Buffer.concat(audioChunks);
          fs.writeFileSync(audioPath, finalAudio);
          
          latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TTS_SYNTHESIS_COMPLETED);
          
          console.log(`[FULL-STREAMING] Complete! Audio: ${audioPath} (${finalAudio.length} bytes), Text: "${fullText}"`);
          resolve({ audioPath, responseText: fullText });
        } else {
          console.error(`[FULL-STREAMING] No audio data received for ${callSid}`);
          reject(new Error('No audio data received from full streaming'));
        }
      });
      
      ws.on('error', (error) => {
        console.error(`[FULL-STREAMING] ElevenLabs WebSocket error:`, error);
        
        // Clear WebSocket reference from call state on error
        if (callState) {
          callState.currentElevenLabsWebSocket = undefined;
          console.log(`[STREAMING-TTS] Cleared ElevenLabs WebSocket reference on error for ${callSid}`);
        }
        
        reject(error);
      });
      
      // Intelligent timeout based on estimated response length
      // For streaming responses, estimate based on user message + generous buffer
      const estimatedResponseText = userMessage.repeat(3); // Assume AI response ~3x user input length
      const intelligentTimeout = calculateTTSTimeout(estimatedResponseText);
      setTimeout(() => {
        if (!isComplete) {
          console.error(`[FULL-STREAMING] Timeout for ${callSid} after ${intelligentTimeout/1000}s`);
          
          // Clear WebSocket reference from call state on timeout
          if (callState) {
            callState.currentElevenLabsWebSocket = undefined;
            console.log(`[STREAMING-TTS] Cleared ElevenLabs WebSocket reference on timeout for ${callSid}`);
          }
          
          ws.close();
          reject(new Error('Full streaming timeout'));
        }
      }, intelligentTimeout);
    });
    
  } catch (error) {
    console.error(`[FULL-STREAMING] Error in full streaming for ${callSid}:`, error);
    return null;
  }
}

/**
 * Stream audio directly to Twilio WebSocket (bypassing files and ffmpeg)
 */
function streamAudioDirectlyToTwilio(callSid: string, audioBuffer: Buffer): void {
  try {
    const callState = calls[callSid];
    if (!callState?.streamSid) {
      console.error(`[DIRECT-STREAM] No WebSocket connection for ${callSid}`);
      return;
    }

    // Find the WebSocket connection for this call
    const wsConnections = Array.from(wss.clients);
    console.log(`[DIRECT-STREAM] Searching ${wsConnections.length} WebSocket connections for ${callSid}`);
    
    const targetWS = wsConnections.find((ws: any) => {
      const isTargetCall = ws.callSid === callSid;
      const isOpen = ws.readyState === WebSocket.OPEN;
      console.log(`[DIRECT-STREAM] WS check: callSid=${ws.callSid}, target=${callSid}, match=${isTargetCall}, open=${isOpen}`);
      return isTargetCall && isOpen;
    });

    if (!targetWS) {
      console.error(`[DIRECT-STREAM] No active WebSocket found for ${callSid} among ${wsConnections.length} connections`);
      return;
    }

    // Send audio directly to Twilio WebSocket
    // The audioBuffer should already be in ulaw_8000 format from ElevenLabs
    const mediaMessage = {
      event: 'media',
      streamSid: callState.streamSid,
      media: {
        payload: audioBuffer.toString('base64')
      }
    };

    try {
      (targetWS as any).send(JSON.stringify(mediaMessage));
      console.log(`[DIRECT-STREAM] ✅ Sent ${audioBuffer.length} bytes directly to Twilio for ${callSid}`);
    } catch (error) {
      console.error(`[DIRECT-STREAM] Error sending to WebSocket for ${callSid}:`, error);
    }
  } catch (error) {
    console.error(`[DIRECT-STREAM] Error streaming to Twilio for ${callSid}:`, error);
  }
}

/**
 * ElevenLabs WebSocket streaming TTS implementation for ultra-low latency
 * This version streams directly to Twilio WebSocket (no files/ffmpeg)
 */
async function generateAudioResponseElevenLabsStream(text: string, callSid: string, turnIndex: number): Promise<string | null> {
  try {
    // Ensure latency tracking session exists
    const existingSession = latencyTracker.getCurrentSession(callSid, turnIndex);
    if (!existingSession) {
      console.log(`[LATENCY-INIT] Starting latency tracking for ${callSid} turn ${turnIndex} at ElevenLabs streaming TTS generation`);
      latencyTracker.startTracking(callSid, turnIndex);
      latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TURN_FINALIZED, {
        note: 'Started at TTS - missed silence detection'
      });
    }
    
    // Record TTS generation start
    const hasGenerationStarted = existingSession?.steps.some(step => step.name === LatencySteps.TTS_GENERATION_STARTED);
    if (!hasGenerationStarted) {
      latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TTS_GENERATION_STARTED);
    }
    
    console.log(`[ElevenLabs-Stream] Starting streaming TTS for ${callSid} turn ${turnIndex}`);
    
    // Create audio file path
    const audioPath = `/tmp/response_${callSid}_${turnIndex}_stream.wav`;
    
    // Connect to ElevenLabs WebSocket with ulaw_8000 output format (Twilio compatible)
    const wsUrl = `wss://api.elevenlabs.io/v1/text-to-speech/${ELEVENLABS_VOICE_ID}/stream-input?model_id=${ELEVENLABS_MODEL_ID}&output_format=ulaw_8000`;
    
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(wsUrl, {
        headers: {
          'xi-api-key': ELEVENLABS_API_KEY
        }
      });
      
      // Store WebSocket reference for interruption handling
      const callState = calls[callSid];
      if (callState) {
        callState.currentElevenLabsWebSocket = ws;
        console.log(`[ElevenLabs-Stream] Stored WebSocket reference for interruption handling on ${callSid}`);
      }
      
      const audioChunks: Buffer[] = [];
      let isComplete = false;
      
      ws.on('open', () => {
        console.log(`[ElevenLabs-Stream] WebSocket connected for ${callSid}`);
        
        // Send the text to be synthesized
        ws.send(JSON.stringify({
          text: text,
          try_trigger_generation: true
        }));
        
        // Send end of stream signal
        ws.send(JSON.stringify({
          text: ""
        }));
      });
      
      ws.on('message', (data: Buffer) => {
        try {
          const response = JSON.parse(data.toString());
          
          if (response.audio) {
            // Decode base64 audio and stream directly to Twilio
            const audioBuffer = Buffer.from(response.audio, 'base64');
            audioChunks.push(audioBuffer); // Keep for backup/recording
            
            // Stream directly to Twilio WebSocket - this is the key improvement!
            streamAudioDirectlyToTwilio(callSid, audioBuffer);
            
            console.log(`[ElevenLabs-Stream] Streamed ${audioBuffer.length} bytes directly to Twilio for ${callSid}`);
          }
          
          if (response.isFinal) {
            console.log(`[ElevenLabs-Stream] Audio generation complete for ${callSid}`);
            isComplete = true;
            ws.close();
          }
        } catch (error) {
          console.error(`[ElevenLabs-Stream] Error parsing WebSocket message:`, error);
        }
      });
      
      ws.on('close', () => {
        console.log(`[ElevenLabs-Stream] WebSocket closed for ${callSid}`);
        
        // Clear WebSocket reference
        const callState = calls[callSid];
        if (callState && callState.currentElevenLabsWebSocket === ws) {
          callState.currentElevenLabsWebSocket = undefined;
          console.log(`[ElevenLabs-Stream] Cleared WebSocket reference for ${callSid}`);
        }
        
        // Record synthesis completion
        latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TTS_SYNTHESIS_COMPLETED);
        
        if (audioChunks.length > 0) {
          // Optionally save combined audio for recording purposes
          const finalAudio = Buffer.concat(audioChunks);
          fs.writeFileSync(audioPath, finalAudio);
          
          console.log(`[ElevenLabs-Stream] Direct streaming complete! Audio also saved to: ${audioPath} (${finalAudio.length} bytes)`);
          // Return null to indicate no file processing needed - audio was streamed directly
          resolve(null);
        } else {
          console.error(`[ElevenLabs-Stream] No audio data received for ${callSid}`);
          resolve(null); // Don't reject - the audio was already streamed
        }
      });
      
      ws.on('error', (error) => {
        console.error(`[ElevenLabs-Stream] WebSocket error for ${callSid}:`, error);
        
        // Clear WebSocket reference on error
        const callState = calls[callSid];
        if (callState && callState.currentElevenLabsWebSocket === ws) {
          callState.currentElevenLabsWebSocket = undefined;
          console.log(`[ElevenLabs-Stream] Cleared WebSocket reference on error for ${callSid}`);
        }
        
        reject(error);
      });
      
      // Intelligent timeout based on actual text length
      const intelligentTimeout = calculateTTSTimeout(text);
      setTimeout(() => {
        if (!isComplete) {
          console.error(`[ElevenLabs-Stream] Timeout waiting for audio generation for ${callSid} after ${intelligentTimeout/1000}s`);
          ws.close();
          reject(new Error('ElevenLabs streaming timeout'));
        }
      }, intelligentTimeout);
    });
    
  } catch (error) {
    console.error(`[ElevenLabs-Stream] Error in streaming TTS for ${callSid}:`, error);
    return null;
  }
}

/**
 * ElevenLabs TTS implementation
 */
async function generateAudioResponseElevenLabs(text: string, callSid: string, turnIndex: number): Promise<string | null> {
  try {
    // Ensure latency tracking session exists
    const existingSession = latencyTracker.getCurrentSession(callSid, turnIndex);
    if (!existingSession) {
      console.log(`[LATENCY-INIT] Starting latency tracking for ${callSid} turn ${turnIndex} at ElevenLabs TTS generation`);
      latencyTracker.startTracking(callSid, turnIndex);
      latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TURN_FINALIZED, {
        note: 'Started at TTS - missed silence detection'
      });
    }
    
    // Record TTS generation start
    const hasGenerationStarted = existingSession?.steps.some(step => step.name === LatencySteps.TTS_GENERATION_STARTED);
    if (!hasGenerationStarted) {
      latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TTS_GENERATION_STARTED, {
        textLength: text.length,
        text: text.substring(0, 100) + '...',
        provider: 'elevenlabs'
      });
    }
    
    console.log(`[ElevenLabs TTS] Generating audio response for callSid=${callSid}, turn=${turnIndex}`);
    
    if (!elevenLabsClient) {
      console.error('[ElevenLabs TTS] ElevenLabs client not initialized');
      return null;
    }
    
    // Generate a unique filename
    const timestamp = Date.now();
    const audioFilename = `${callSid}_turn${turnIndex}_${timestamp}.mp3`;
    const audioPath = path.join(AUDIO_DIR, audioFilename);
    
    // Record synthesis start
    latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TTS_SYNTHESIS_STARTED);
    
    // Call ElevenLabs text-to-speech API
    const audioResponse = await elevenLabsClient.textToSpeech.convert(ELEVENLABS_VOICE_ID, {
      text: text,
      model_id: ELEVENLABS_MODEL_ID,
      output_format: "mp3_44100_128"
    } as any); // Use 'as any' to bypass incorrect TypeScript types
    
    // Record synthesis completion
    latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TTS_SYNTHESIS_COMPLETED);
    
    // Convert response stream to buffer and write to file
    const chunks: Uint8Array[] = [];
    for await (const chunk of audioResponse) {
      chunks.push(chunk);
    }
    const audioBuffer = Buffer.concat(chunks);
    fs.writeFileSync(audioPath, audioBuffer);
    
    console.log(`[ElevenLabs TTS] Audio response saved to ${audioPath}`);
    
    // Record file written
    latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TTS_FILE_WRITTEN, {
      audioPath: audioFilename,
      fileSize: audioBuffer.length,
      provider: 'elevenlabs'
    });
    
    return audioPath;
    
  } catch (err) {
    console.error(`[ElevenLabs TTS] Error in audio generation for ${callSid}:`, err);
    // Fallback to Azure on error
    console.log(`[ElevenLabs TTS] Falling back to Azure TTS for ${callSid}`);
    return generateAudioResponseAzure(text, callSid, turnIndex);
  }
}

/**
 * ENHANCED generateAudioResponse with provider selection and optional streaming
 * 
 * This function routes to different TTS providers based on TTS_PROVIDER:
 * - azure: Uses Azure Speech Services TTS
 * - elevenlabs: Uses ElevenLabs TTS API
 * - Supports streaming mode for faster response times
 * - Always produces the expected single file per turn for recording systems
 */
export async function generateAudioResponse(text: string, callSid: string, turnIndex: number): Promise<string | null> {
  const callState = calls[callSid];
  if (!callState) {
    console.error(`[TTS-RACE-FIX] No call state found for ${callSid} - cannot generate TTS`);
    return null;
  }

  // Reset inactivity timer when AI starts generating speech (system activity)
  resetInactivityTimer(callSid);

  // CRITICAL FIX: Cancel active streaming TTS that causes double playback
  if (callState.isTtsGenerating) {
    console.log(`[TTS-STREAM-CANCEL] TTS generation already in progress for ${callSid} - cancelling previous and starting new`);
    
    // CRITICAL: Cancel active ElevenLabs WebSocket streaming
    if (callState.currentElevenLabsWebSocket) {
      try {
        console.log(`[TTS-STREAM-CANCEL] Closing active ElevenLabs WebSocket for ${callSid}`);
        callState.currentElevenLabsWebSocket.close();
        callState.currentElevenLabsWebSocket = undefined;
      } catch (error) {
        console.error(`[TTS-STREAM-CANCEL] Error closing ElevenLabs WebSocket:`, error);
      }
    }
    
    // CRITICAL: Cancel active Deepgram direct streaming
    if (callState.activeDeepgramStream) {
      try {
        console.log(`[TTS-STREAM-CANCEL] Closing active Deepgram stream for ${callSid}`);
        callState.activeDeepgramStream.close();
        callState.activeDeepgramStream = undefined;
      } catch (error) {
        console.error(`[TTS-STREAM-CANCEL] Error closing Deepgram stream:`, error);
      }
    }
    
    // CRITICAL: Cancel Azure streaming TTS processes
    if (callState.activeSynthesizers && callState.activeSynthesizers.length > 0) {
      console.log(`[TTS-STREAM-CANCEL] Cancelling ${callState.activeSynthesizers.length} active Azure synthesizers for ${callSid}`);
      for (const synth of callState.activeSynthesizers) {
        try {
          synth.synthesizer.close();
          console.log(`[TTS-STREAM-CANCEL] Closed Azure synthesizer for sentence ${synth.sentenceIndex}`);
        } catch (error) {
          console.error(`[TTS-STREAM-CANCEL] Error closing synthesizer ${synth.sentenceIndex}:`, error);
        }
      }
      callState.activeSynthesizers = [];
    }
    
    // Cancel active sentence streaming processes
    if (callState.activeSentenceStreams && callState.activeSentenceStreams.length > 0) {
      console.log(`[TTS-STREAM-CANCEL] Killing ${callState.activeSentenceStreams.length} active sentence streaming processes for ${callSid}`);
      for (const stream of callState.activeSentenceStreams) {
        try {
          stream.process.kill('SIGKILL');
          console.log(`[TTS-STREAM-CANCEL] Killed sentence streaming process for sentence ${stream.sentenceIndex}`);
        } catch (error) {
          console.error(`[TTS-STREAM-CANCEL] Error killing sentence stream ${stream.sentenceIndex}:`, error);
        }
      }
      callState.activeSentenceStreams = [];
    }

    // Cancel existing main FFmpeg-based TTS
    if (callState.currentFfmpegProcess) {
      try {
        console.log(`[TTS-STREAM-CANCEL] Killing existing main FFmpeg process for ${callSid}`);
        callState.currentFfmpegProcess.kill('SIGKILL');
        callState.currentFfmpegProcess = undefined;
      } catch (error) {
        console.error(`[TTS-STREAM-CANCEL] Error killing existing main FFmpeg process:`, error);
      }
    }

    // CRITICAL: Cancel active LiveKit TTS operations (same pattern as above)
    if (callState.livekitRTCHandler && callState.isModelSpeaking) {
      try {
        console.log(`[TTS-STREAM-CANCEL] Stopping active LiveKit audio playback for ${callSid} (STREAM CANCELLATION TRIGGER)`);
        await callState.livekitRTCHandler.stopAudio();
        console.log(`[TTS-STREAM-CANCEL] stopAudio() completed from stream cancellation for ${callSid}`);
        
        // CRITICAL FIX: Reset LiveKit handler for new turn before starting new TTS
        callState.livekitRTCHandler.resetForNewTurn();
        console.log(`🔄 [TTS-STREAM-CANCEL] Reset LiveKit handler for new turn before new TTS`);
      } catch (error) {
        console.error(`[TTS-STREAM-CANCEL] Error stopping LiveKit audio:`, error);
      }
    }
    
    // Clear speaking timeout
    if (callState.currentSpeakingTimeout) {
      clearTimeout(callState.currentSpeakingTimeout);
      callState.currentSpeakingTimeout = undefined;
    }
    
    // Reset speaking state
    callState.isModelSpeaking = false;
    
    console.log(`[TTS-STREAM-CANCEL] Successfully cancelled all active TTS for ${callSid}`);
  }
  
  // Set TTS generation flag to prevent race conditions
  callState.isTtsGenerating = true;
  callState.currentTtsText = text;
  callState.currentTtsTurnIndex = turnIndex;
  
  console.log(`[TTS-RACE-FIX] Starting TTS generation for ${callSid}, turn ${turnIndex}: "${text.substring(0, 50)}..."`);

  try {
    let audioPath: string | null = null;

    // Route to appropriate TTS provider
    if (TTS_PROVIDER === 'elevenlabs') {
      audioPath = await generateAudioResponseElevenLabs(text, callSid, turnIndex);
    } else if (TTS_PROVIDER === 'elevenlabs_stream') {
      // Check if this is a dynamic LLM response (longer text) or static message (shorter)
      // Static messages like "Welcome!" go through regular TTS for reliability
      // Dynamic responses go through streaming for ultra-low latency
      if (text.length < 100) {
        console.log(`[TTS-ROUTING] Using regular ElevenLabs for static text: "${text.substring(0, 50)}..."`);
        audioPath = await generateAudioResponseElevenLabs(text, callSid, turnIndex);
      } else {
        console.log(`[TTS-ROUTING] Using ElevenLabs streaming for dynamic response: "${text.substring(0, 50)}..."`);
        audioPath = await generateAudioResponseElevenLabsStream(text, callSid, turnIndex);
      }
    } else if (TTS_PROVIDER === 'deepgram') {
      audioPath = await generateAudioResponseDeepgram(text, callSid, turnIndex);
    } else {
      // Continue with Azure TTS logic below
      audioPath = await generateAudioResponseAzure(text, callSid, turnIndex);
    }
    
    // CRITICAL: Only clear TTS generation flag if this is still the current TTS request
    if (callState.currentTtsText === text && callState.currentTtsTurnIndex === turnIndex) {
      callState.isTtsGenerating = false;
      callState.currentTtsText = undefined;
      callState.currentTtsTurnIndex = undefined;
      console.log(`[TTS-RACE-FIX] TTS generation completed successfully for ${callSid}`);
      return audioPath;
    } else {
      console.log(`[TTS-RACE-FIX] TTS generation completed but was superseded by newer request for ${callSid} - discarding audio`);
      return null; // Don't process superseded TTS
    }
    
  } catch (error) {
    console.error(`[TTS-RACE-FIX] Error during TTS generation for ${callSid}:`, error);
    
    // Always clear the generation flag on error
    callState.isTtsGenerating = false;
    callState.currentTtsText = undefined;
    callState.currentTtsTurnIndex = undefined;
    
    return null;
  }
}

/**
 * PHASE 2 BACKWARD COMPATIBILITY WRAPPER
 * Azure TTS generation - now delegates to AudioProviderManager
 */
export async function generateAudioResponseAzure(text: string, callSid: string, turnIndex: number): Promise<string | null> {
  console.log(`[Azure TTS Wrapper] Generating via AudioProviderManager for call ${callSid}, turn ${turnIndex}`);
  
  // Import and use the centralized provider manager
  const { audioProviderManager } = await import('./services/AudioProviderManager');
  
  // Determine connection type from call state
  const callState = calls[callSid];
  const connectionType: ConnectionType = callState?.connectionType === 'livekit' ? ConnectionType.LIVEKIT :
                                        callState?.connectionType === 'direct' ? ConnectionType.DIRECT :
                                        ConnectionType.TWILIO;
  
  // Delegate to the provider manager while maintaining the same interface
  return await audioProviderManager.generateAndRouteTTS(callSid, text, turnIndex, connectionType);
}

// PHASE 2: generateAudioResponseOriginal function has been moved to AzureTTSProvider.generateAudioOriginal()
// All Azure TTS access now goes through AudioProviderManager for consistency

/**
 * Deepgram TTS implementation using Aura-2 
 */
async function generateAudioResponseDeepgram(text: string, callSid: string, turnIndex: number): Promise<string | null> {
  try {
    if (!deepgramClient) {
      throw new Error("Deepgram client not initialized!");
    }

    // Get call state to determine connection type
    const callState = calls[callSid];
    if (!callState) {
      throw new Error(`Call state not found for ${callSid} during Deepgram TTS generation`);
    }

    // Cancel any existing active stream for this call to prevent overlapping audio
    if (callState.activeDeepgramStream) {
      console.log(`[DEEPGRAM-DIRECT-STREAM] Cancelling existing stream for ${callSid}`);
      try {
        callState.activeDeepgramStream.close();
      } catch (error) {
        console.warn(`[DEEPGRAM-DIRECT-STREAM] Error closing existing stream:`, error);
      }
      callState.activeDeepgramStream = undefined;
    }

    // Determine optimal sample rate based on connection type  
    const connectionType = callState.connectionType || 'twilio';
    let sampleRate: number;
    switch (connectionType) {
      case 'livekit':
      case 'direct':
        sampleRate = 16000; // 16kHz for LiveKit/Direct connections
        break;
      case 'twilio':
      default:
        sampleRate = 8000;  // 8kHz for Twilio connections - but we'll convert to ulaw
        break;
    }

    console.log(`[DEEPGRAM-DIRECT-STREAM] Starting direct stream for ${callSid}, turn ${turnIndex} (${sampleRate}Hz)`);

    // Ensure latency tracking session exists
    const existingSession = latencyTracker.getCurrentSession(callSid, turnIndex);
    if (!existingSession) {
      console.log(`[LATENCY-INIT] Starting latency tracking for ${callSid} turn ${turnIndex} at Deepgram TTS generation`);
      latencyTracker.startTracking(callSid, turnIndex);
      latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TURN_FINALIZED, {
        note: 'Started at TTS - missed silence detection'
      });
    }

    // Record TTS generation start
    const hasGenerationStarted = existingSession?.steps.some(step => step.name === LatencySteps.TTS_GENERATION_STARTED);
    if (!hasGenerationStarted) {
      latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TTS_GENERATION_STARTED, {
        textLength: text.length,
        text: text.substring(0, 100) + '...',
        mode: 'direct_streaming'
      });
    }

    latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TTS_SYNTHESIS_STARTED);

    // Create single Deepgram TTS WebSocket connection for entire text
    const ttsConnection = deepgramClient.speak.live({
      model: DEEPGRAM_TTS_MODEL,
      encoding: 'linear16',
      sample_rate: sampleRate
    });

    // Store active connection for interruption handling
    callState.activeDeepgramStream = ttsConnection;

    return new Promise((resolve, reject) => {
      let firstChunkReceived = false;
      let audioChunks: Buffer[] = [];
      let isStreamComplete = false;

      const completeStream = () => {
        if (isStreamComplete) return;
        isStreamComplete = true;

        // Clear active stream reference
        if (callState.activeDeepgramStream === ttsConnection) {
          callState.activeDeepgramStream = undefined;
        }

        latencyTracker.recordStep(callSid, turnIndex, LatencySteps.TTS_SYNTHESIS_COMPLETED);

        // CRITICAL FIX: Send TTS_FINISHED event to XState machine like Azure TTS does
        const controller = callControllers[callSid];
        if (controller?.machine) {
          try {
            const eventId = Math.random().toString(36).substring(2, 9);
            console.log(`[XState:${callSid}] Sending TTS_FINISHED event (id: ${eventId}) from DeepGram completion to machine ID: ${controller.machine.id}`);
            
            // Mark model as no longer speaking
            if (calls[callSid]) {
              calls[callSid].isModelSpeaking = false;
            }
            
            // Send the TTS_FINISHED event to transition the machine state
            controller.machine.send({ 
              type: 'TTS_FINISHED', 
              callSid: callSid
            });
            
            console.log(`[XState:${callSid}] TTS_FINISHED event sent successfully for DeepGram completion`);
          } catch (err) {
            console.error(`[XState:${callSid}] Error sending TTS_FINISHED event from DeepGram:`, err);
          }
        } else {
          console.warn(`[XState:${callSid}] TTS finished but machine controller not found for DeepGram completion`);
        }

        // Optional: Save concatenated audio for recording (no delay since it's background)
        if (audioChunks.length > 0) {
          const finalAudio = Buffer.concat(audioChunks);
          const timestamp = Date.now();
          const audioPath = path.join(AUDIO_DIR, `${callSid}_turn${turnIndex}_${timestamp}.wav`);
          
          // Create WAV file in background (don't await)
          setImmediate(() => {
            try {
              const wavBuffer = createWavFile(finalAudio, sampleRate);
              fs.writeFileSync(audioPath, wavBuffer);
              console.log(`[DEEPGRAM-DIRECT-STREAM] Saved recording: ${audioPath}`);
            } catch (error) {
              console.warn(`[DEEPGRAM-DIRECT-STREAM] Failed to save recording:`, error);
            }
          });
          
          console.log(`[DEEPGRAM-DIRECT-STREAM] Direct streaming complete for ${callSid} turn ${turnIndex}`);
          resolve('DIRECT_STREAM_SUCCESS'); // Return success indicator for direct streaming
        } else {
          console.error(`[DEEPGRAM-DIRECT-STREAM] No audio data received for ${callSid}`);
          resolve(null);
        }
      };

      // Handle connection open
      ttsConnection.on(LiveTTSEvents.Open, () => {
        console.log(`[DEEPGRAM-DIRECT-STREAM] Connection opened for ${callSid}`);
        
        // Send the entire text for synthesis
        ttsConnection.sendText(text);
        ttsConnection.flush();
      });

      // Handle audio data - stream directly to Twilio
      ttsConnection.on(LiveTTSEvents.Audio, (data: any) => {
        if (isStreamComplete) return; // Ignore chunks after completion

        const audioBuffer = Buffer.from(data);
        audioChunks.push(audioBuffer); // Keep for recording

        // Convert PCM to µ-law for Twilio and stream immediately
        const ulawBuffer = pcmToUlaw(audioBuffer);
        streamAudioDirectlyToTwilio(callSid, ulawBuffer);

        // Record latency for first chunk
        if (!firstChunkReceived) {
          firstChunkReceived = true;
          latencyTracker.recordStep(callSid, turnIndex, LatencySteps.FIRST_AUDIO_CHUNK_SENT);
          latencyTracker.recordStep(callSid, turnIndex, LatencySteps.AUDIO_PLAYBACK_STARTED);
          console.log(`[DEEPGRAM-DIRECT-STREAM] ✅ First audio chunk streamed to ${callSid}`);
        }
      });

      // Handle connection close
      ttsConnection.on(LiveTTSEvents.Close, () => {
        console.log(`[DEEPGRAM-DIRECT-STREAM] Connection closed for ${callSid}`);
        completeStream();
      });

      // Handle errors
      ttsConnection.on(LiveTTSEvents.Error, (error: any) => {
        console.error(`[DEEPGRAM-DIRECT-STREAM] Error for ${callSid}:`, error);
        completeStream();
      });

      // Timeout fallback in case connection hangs
      setTimeout(() => {
        if (!isStreamComplete) {
          console.warn(`[DEEPGRAM-DIRECT-STREAM] Timeout for ${callSid}, completing stream`);
          completeStream();
        }
      }, 10000); // 10 second timeout
    });

  } catch (err) {
    console.error(`[DEEPGRAM-DIRECT-STREAM] Error in audio generation for ${callSid}:`, err);
    return null;
  }
}

/**
 * Synthesize a single sentence using Deepgram WebSocket streaming
 * Matches Azure's synthesizeSentence function behavior
 */
async function synthesizeDeepgramSentence(
  sentence: string, 
  callSid: string, 
  turnIndex: number, 
  sentenceIndex: number,
  sampleRate: number
): Promise<string | null> {
  try {
    const timestamp = Date.now();
    const audioFilename = `${callSid}_turn${turnIndex}_sentence${sentenceIndex}_${timestamp}.wav`;
    const audioPath = path.join(AUDIO_DIR, audioFilename);
    
    console.log(`[DEEPGRAM-SENTENCE] Synthesizing sentence ${sentenceIndex}: "${sentence.substring(0, 50)}..."`);
    
    // Create Deepgram TTS WebSocket connection for this sentence
    const ttsConnection = deepgramClient.speak.live({
      model: DEEPGRAM_TTS_MODEL,
      encoding: 'linear16',
      sample_rate: sampleRate
      // No container for streaming
    });
    
    // CRITICAL: Track connection for cancellation (same as Azure)
    const callState = calls[callSid];
    if (callState) {
      if (!callState.activeDeepgramConnections) {
        callState.activeDeepgramConnections = [];
      }
      callState.activeDeepgramConnections.push({ connection: ttsConnection, sentenceIndex });
      console.log(`[DEEPGRAM-SYNTHESIS] Tracking connection for sentence ${sentenceIndex}, total active: ${callState.activeDeepgramConnections.length}`);
    }
    
    return new Promise((resolve, reject) => {
      const chunks: Buffer[] = [];
      let isComplete = false;
      let audioTimeout: NodeJS.Timeout | null = null;

      const completeAndResolve = () => {
        if (isComplete) return;
        isComplete = true;
        
        // CRITICAL: Remove connection from tracking on completion (same as Azure)
        if (callState && callState.activeDeepgramConnections) {
          callState.activeDeepgramConnections = callState.activeDeepgramConnections.filter(c => c.connection !== ttsConnection);
          console.log(`[DEEPGRAM-SYNTHESIS] Removed connection for sentence ${sentenceIndex}, remaining active: ${callState.activeDeepgramConnections.length}`);
        }
        
        try {
          if (chunks.length === 0) {
            console.error(`[DEEPGRAM-SENTENCE] No audio data received for sentence ${sentenceIndex}`);
            resolve(null);
            return;
          }
          
          // Combine all audio chunks
          const audioBuffer = Buffer.concat(chunks);
          console.log(`[DEEPGRAM-SENTENCE] Sentence ${sentenceIndex} audio: ${audioBuffer.length} bytes`);

          // Create proper WAV file with header (since Deepgram returns raw PCM)
          const wavBuffer = createWavFile(audioBuffer, sampleRate);
          fs.writeFileSync(audioPath, wavBuffer);
          
          console.log(`[DEEPGRAM-SENTENCE] Sentence ${sentenceIndex} saved to ${audioPath}`);
          resolve(audioPath);
        } catch (error) {
          console.error(`[DEEPGRAM-SENTENCE] Error saving sentence ${sentenceIndex}:`, error);
          resolve(null);
        }
      };

      const resetAudioTimeout = () => {
        if (audioTimeout) clearTimeout(audioTimeout);
        audioTimeout = setTimeout(() => {
          console.log(`[DEEPGRAM-SENTENCE] Audio timeout for sentence ${sentenceIndex}, completing...`);
          completeAndResolve();
        }, 1000); // Wait 1 second after last chunk
      };

      // Handle connection open
      ttsConnection.on(LiveTTSEvents.Open, () => {
        console.log(`[DEEPGRAM-SENTENCE] Connection opened for sentence ${sentenceIndex}`);
        
        // Send the sentence text for synthesis
        ttsConnection.sendText(sentence);
        
        // Send flush to indicate end of text
        ttsConnection.flush();
        
        // Start timeout after flush
        resetAudioTimeout();
      });

      // Handle audio data
      ttsConnection.on(LiveTTSEvents.Audio, (data: any) => {
        // Reduced logging - only log occasionally to avoid spam
        if (chunks.length === 0 || chunks.length % 10 === 0) {
          console.log(`[DEEPGRAM-SENTENCE] Received ${chunks.length + 1} chunks for sentence ${sentenceIndex} (latest: ${data.byteLength} bytes)`);
        }
        chunks.push(Buffer.from(data));
        resetAudioTimeout(); // Reset timeout each time we get audio
      });

      // Handle completion
      ttsConnection.on(LiveTTSEvents.Close, () => {
        console.log(`[DEEPGRAM-SENTENCE] Connection closed for sentence ${sentenceIndex}`);
        completeAndResolve();
      });

      // Handle errors
      ttsConnection.on(LiveTTSEvents.Error, (error: any) => {
        console.error(`[DEEPGRAM-SENTENCE] Error for sentence ${sentenceIndex}:`, error);
        if (!isComplete) {
          isComplete = true;
          // Remove from tracking on error
          if (callState && callState.activeDeepgramConnections) {
            callState.activeDeepgramConnections = callState.activeDeepgramConnections.filter(c => c.connection !== ttsConnection);
          }
          resolve(null);
        }
      });

      // Overall timeout protection (same as Azure)
      setTimeout(() => {
        if (!isComplete) {
          console.error(`[DEEPGRAM-SENTENCE] Timeout for sentence ${sentenceIndex} after 15 seconds`);
          isComplete = true;
          if (audioTimeout) clearTimeout(audioTimeout);
          // Remove from tracking on timeout
          if (callState && callState.activeDeepgramConnections) {
            callState.activeDeepgramConnections = callState.activeDeepgramConnections.filter(c => c.connection !== ttsConnection);
          }
          resolve(null);
        }
      }, 15000);
    });

  } catch (err) {
    console.error(`[DEEPGRAM-SENTENCE] Error synthesizing sentence ${sentenceIndex}:`, err);
    return null;
  }
}

// =============================================================================
// iOS COMPATIBILITY ENDPOINTS
// =============================================================================

/**
 * GET /api/ios/lobby/status/:userId - Get lobby connection status for iOS debugging
 */
app.get('/api/ios/lobby/status/:userId', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token from Authorization header
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required. Include Authorization: Bearer <token> header.'
        });
      }

      // Authenticate user using the same system as WebSocket connections
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication failed. Invalid or expired token.'
        });
      }

      const userId = req.params.userId;
      
      if (!userId) {
        return res.status(400).json({ error: 'userId parameter required' });
      }

      // Ensure user can only check their own lobby status
      if (userId !== user.id) {
        return res.status(403).json({
          success: false,
          error: 'Cannot check lobby status for different user.'
        });
      }
      
      const hasLobby = livekitLobbyManager.hasLobbyConnection(userId);
      const lobbyStats = livekitLobbyManager.getLobbyStats();
      const userLobby = lobbyStats.lobbies.find(lobby => lobby.userId === userId);
      
      res.json({
        success: true,
        userId,
        hasLobbyConnection: hasLobby,
        lobbyDetails: userLobby || null,
        totalLobbies: lobbyStats.totalLobbies,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('[iOS Lobby Status] Error:', error);
      res.status(500).json({ 
        success: false,
        error: 'Failed to get lobby status',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  })().catch(err => {
    console.error('[iOS Lobby Status] Unhandled error:', err);
    res.status(500).json({ success: false, error: 'Internal server error' });
  });
});

/**
 * POST /api/ios/lobby/create - Pre-create lobby connection for iOS (alternative to automatic creation)
 */
app.post('/api/ios/lobby/create', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token from Authorization header
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required. Include Authorization: Bearer <token> header.'
        });
      }

      // Authenticate user using the same system as WebSocket connections
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication failed. Invalid or expired token.'
        });
      }

      const userId = user.id;
      
      console.log(`[iOS Lobby Create] Creating lobby for user ${userId}`);
      
      const success = await livekitLobbyManager.createLobbyConnection(userId);
      
      if (success) {
        res.json({
          success: true,
          message: 'Lobby connection created successfully',
          userId,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to create lobby connection',
          userId
        });
      }
      
    } catch (error) {
      console.error('[iOS Lobby Create] Error:', error);
      res.status(500).json({ 
        success: false,
        error: 'Failed to create lobby connection',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  })().catch(err => {
    console.error('[iOS Lobby Create] Unhandled error:', err);
    res.status(500).json({ success: false, error: 'Internal server error' });
  });
});

/**
 * DELETE /api/ios/lobby/:userId - Clean up lobby connection for iOS
 */
app.delete('/api/ios/lobby/:userId', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token from Authorization header
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required. Include Authorization: Bearer <token> header.'
        });
      }

      // Authenticate user using the same system as WebSocket connections
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication failed. Invalid or expired token.'
        });
      }

      const userId = req.params.userId;
      
      if (!userId) {
        return res.status(400).json({ error: 'userId parameter required' });
      }

      // Ensure user can only cleanup their own lobby
      if (userId !== user.id) {
        return res.status(403).json({
          success: false,
          error: 'Cannot cleanup lobby for different user.'
        });
      }
      
      console.log(`[iOS Lobby Cleanup] Cleaning up lobby for user ${userId}`);
      
      await livekitLobbyManager.cleanupLobbyConnection(userId);
      
      res.json({
        success: true,
        message: 'Lobby connection cleaned up successfully',
        userId,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('[iOS Lobby Cleanup] Error:', error);
      res.status(500).json({ 
        success: false,
        error: 'Failed to cleanup lobby connection',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  })().catch(err => {
    console.error('[iOS Lobby Cleanup] Unhandled error:', err);
    res.status(500).json({ success: false, error: 'Internal server error' });
  });
});

/**
 * GET /api/ios/health - Comprehensive health check for iOS apps
 */
app.get('/api/ios/health', (req: Request, res: Response) => {
  try {
    const webSocketConnections = wss ? wss.clients.size : 0;
    const activeCalls = Object.keys(calls).length;
    const lobbyStats = livekitLobbyManager.getLobbyStats();
    
    res.json({
      success: true,
      server: {
        status: 'healthy',
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || 'unknown'
      },
      connections: {
        webSocket: webSocketConnections,
        activeCalls: activeCalls,
        totalLobbies: lobbyStats.totalLobbies
      },
      services: {
        livekit: 'available',
        tts: TTS_PROVIDER,
        stt: STT_PROVIDER
      }
    });
    
  } catch (error) {
    console.error('[iOS Health Check] Error:', error);
    res.status(500).json({ 
      success: false,
      error: 'Health check failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/ios/status - Detailed server status for iOS debugging
 */
app.get('/api/ios/status', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token from Authorization header
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required. Include Authorization: Bearer <token> header.'
        });
      }

      // Authenticate user using the same system as WebSocket connections
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication failed. Invalid or expired token.'
        });
      }

      const userId = user.id;
      
      // Get user-specific status
      const userCall = Object.entries(calls).find(([_, call]) => 
        call.phoneNumberForSession?.includes(userId) || call.sessionId?.includes(userId)
      );
      
      const hasLobby = livekitLobbyManager.hasLobbyConnection(userId);
      const lobbyStats = livekitLobbyManager.getLobbyStats();
      
      res.json({
        success: true,
        user: {
          id: userId,
          hasActiveCall: !!userCall,
          callId: userCall?.[0] || null,
          hasLobbyConnection: hasLobby
        },
        server: {
          totalCalls: Object.keys(calls).length,
          totalLobbies: lobbyStats.totalLobbies,
          webSocketConnections: wss ? wss.clients.size : 0
        },
        configuration: {
          ttsProvider: TTS_PROVIDER,
          sttProvider: STT_PROVIDER,
          livekitServer: process.env.LIVEKIT_SERVER_URL || 'default'
        },
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('[iOS Status] Error:', error);
      res.status(500).json({ 
        success: false,
        error: 'Failed to get status',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  })().catch(err => {
    console.error('[iOS Status] Unhandled error:', err);
    res.status(500).json({ success: false, error: 'Internal server error' });
  });
});

/**
 * POST /api/ios/debug/test-connection - Test iOS app connectivity
 */
app.post('/api/ios/debug/test-connection', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token from Authorization header
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required. Include Authorization: Bearer <token> header.'
        });
      }

      // Authenticate user using the same system as WebSocket connections
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication failed. Invalid or expired token.'
        });
      }

      const { deviceInfo, appVersion } = req.body;
      
      console.log(`[iOS Debug] Connection test from user ${user.id}:`, { deviceInfo, appVersion });
      
      res.json({
        success: true,
        message: 'Connection test successful',
        server: {
          timestamp: new Date().toISOString(),
          receivedFrom: user.id,
          deviceInfo: deviceInfo || null,
          appVersion: appVersion || null
        },
        recommendations: {
          useWebSocket: true,
          prefetchLobby: true,
          enableLogging: true
        }
      });
      
    } catch (error) {
      console.error('[iOS Debug] Error:', error);
      res.status(500).json({ 
        success: false,
        error: 'Connection test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  })().catch(err => {
    console.error('[iOS Debug] Unhandled error:', err);
    res.status(500).json({ success: false, error: 'Internal server error' });
  });
});

//-----------------------------------------------
// DeviceCheck API Endpoints for CliniSim
//-----------------------------------------------

// Import DeviceCheck service
import { DeviceCheckService } from './services/devicecheck/devicecheck-service';
import { DeviceCheckServiceError } from './services/devicecheck/devicecheck-types';

// Initialize DeviceCheck service (will be created when first used)
let deviceCheckService: DeviceCheckService | null = null;

function getDeviceCheckService(): DeviceCheckService {
  if (!deviceCheckService) {
    deviceCheckService = new DeviceCheckService();
  }
  return deviceCheckService;
}

/**
 * POST /api/devicecheck/query
 * Query device account count using Apple DeviceCheck
 * REQUIRES: Authorization header with Supabase JWT token
 */
app.post('/api/devicecheck/query', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token from Authorization header
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        console.warn('[DeviceCheck] No authentication token provided for query');
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication required. Include Authorization: Bearer <token> header.',
          timestamp: new Date().toISOString()
        });
      }

      // Authenticate user using the same system as other API endpoints
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        console.warn('[DeviceCheck] Authentication failed for query - invalid or expired token');
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication failed. Invalid or expired token.',
          timestamp: new Date().toISOString()
        });
      }

      console.log(`[DeviceCheck] Query request from authenticated user: ${user.id}`);

      // Validate request body
      const { device_token } = req.body;
      if (!device_token) {
        return res.status(400).json({
          success: false,
          error: 'DEVICE_TOKEN_INVALID',
          message: 'device_token is required in request body',
          timestamp: new Date().toISOString()
        });
      }

      // Process DeviceCheck query
      const service = getDeviceCheckService();
      const result = await service.queryDeviceAccountCount({ device_token });

      // Log successful query
      console.log(`[DeviceCheck] Query successful for user ${user.id}: ${result.account_count} accounts, can_create: ${result.can_create_account}`);

      res.json(result);

    } catch (error) {
      console.error('[DeviceCheck] Query error:', error);
      
      if (error instanceof Error && 'code' in error && 'statusCode' in error) {
        const serviceError = error as DeviceCheckServiceError;
        return res.status(serviceError.statusCode).json({
          success: false,
          error: serviceError.code,
          message: serviceError.message,
          timestamp: new Date().toISOString(),
          ...(serviceError.details && { details: serviceError.details })
        });
      }

      // Unknown error
      res.status(500).json({
        success: false,
        error: 'SERVER_ERROR',
        message: 'Internal server error during DeviceCheck query',
        timestamp: new Date().toISOString()
      });
    }
  })().catch(err => {
    console.error('[DeviceCheck] Unhandled query error:', err);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  });
});

/**
 * POST /api/devicecheck/update
 * Update device account count using Apple DeviceCheck
 * REQUIRES: Authorization header with Supabase JWT token
 */
app.post('/api/devicecheck/update', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token from Authorization header
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        console.warn('[DeviceCheck] No authentication token provided for update');
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication required. Include Authorization: Bearer <token> header.',
          timestamp: new Date().toISOString()
        });
      }

      // Authenticate user
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        console.warn('[DeviceCheck] Authentication failed for update - invalid or expired token');
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication failed. Invalid or expired token.',
          timestamp: new Date().toISOString()
        });
      }

      console.log(`[DeviceCheck] Update request from authenticated user: ${user.id}`);

      // Validate request body
      const { device_token, account_count, timestamp } = req.body;
      
      if (!device_token || account_count === undefined || !timestamp) {
        return res.status(400).json({
          success: false,
          error: 'DEVICE_TOKEN_INVALID',
          message: 'device_token, account_count, and timestamp are required',
          timestamp: new Date().toISOString()
        });
      }

      // Process DeviceCheck update
      const service = getDeviceCheckService();
      const result = await service.updateDeviceAccountCount({
        device_token,
        account_count,
        timestamp
      });

      // Log successful update
      console.log(`[DeviceCheck] Update successful for user ${user.id}: account_count=${result.account_count}, limit_reached=${result.device_limit_reached}`);

      res.json(result);

    } catch (error) {
      console.error('[DeviceCheck] Update error:', error);
      
      if (error instanceof Error && 'code' in error && 'statusCode' in error) {
        const serviceError = error as DeviceCheckServiceError;
        return res.status(serviceError.statusCode).json({
          success: false,
          error: serviceError.code,
          message: serviceError.message,
          timestamp: new Date().toISOString(),
          ...(serviceError.details && { details: serviceError.details })
        });
      }

      // Unknown error
      res.status(500).json({
        success: false,
        error: 'SERVER_ERROR',
        message: 'Internal server error during DeviceCheck update',
        timestamp: new Date().toISOString()
      });
    }
  })().catch(err => {
    console.error('[DeviceCheck] Unhandled update error:', err);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  });
});

/**
 * GET /api/devicecheck/health
 * Health check for DeviceCheck service configuration
 * REQUIRES: Authorization header with Supabase JWT token
 */
app.get('/api/devicecheck/health', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token from Authorization header
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication required for health check',
          timestamp: new Date().toISOString()
        });
      }

      // Authenticate user
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication failed. Invalid or expired token.',
          timestamp: new Date().toISOString()
        });
      }

      // Test DeviceCheck service configuration
      const service = getDeviceCheckService();
      const configTest = await service.testConfiguration();

      res.json({
        success: true,
        status: configTest ? 'healthy' : 'configuration_error',
        configuration: {
          apple_team_id: !!process.env.APPLE_TEAM_ID,
          apple_key_id: !!process.env.APPLE_KEY_ID,
          apple_private_key_path: !!process.env.APPLE_PRIVATE_KEY_PATH,
          supabase_url: !!process.env.SUPABASE_URL,
          supabase_key: !!(process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY)
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('[DeviceCheck] Health check error:', error);
      res.status(500).json({
        success: false,
        error: 'SERVER_ERROR',
        message: 'Health check failed',
        timestamp: new Date().toISOString()
      });
    }
  })().catch(err => {
    console.error('[DeviceCheck] Unhandled health check error:', err);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  });
});

//-----------------------------------------------
// Apple In-App Purchase API Endpoints for CliniSim
//-----------------------------------------------

// Import IAP service
import { IAPService } from './services/iap-service';

// Initialize IAP service (will be created when first used)
let iapService: IAPService | null = null;

function getIAPService(): IAPService {
  if (!iapService) {
    iapService = new IAPService();
  }
  return iapService;
}

/**
 * POST /api/purchases/validate-subscription
 * Validate subscription purchase with Apple App Store Server API
 * REQUIRES: Authorization header with Supabase JWT token
 */
app.post('/api/purchases/validate-subscription', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication required. Include Authorization: Bearer <token> header.',
          timestamp: new Date().toISOString()
        });
      }

      // Authenticate user
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication failed. Invalid or expired token.',
          timestamp: new Date().toISOString()
        });
      }

      console.log(`[IAP] Subscription validation request from user: ${user.id}`);

      // Validate request body
      const { receipt_data, transaction_id, product_id } = req.body;
      if (!receipt_data || !transaction_id || !product_id) {
        return res.status(400).json({
          success: false,
          error: 'INVALID_REQUEST',
          message: 'receipt_data, transaction_id, and product_id are required',
          timestamp: new Date().toISOString()
        });
      }

      // Process subscription validation
      const service = getIAPService();
      const result = await service.validateSubscription(user.id, { receipt_data, transaction_id, product_id });

      console.log(`[IAP] Subscription validation ${result.success ? 'successful' : 'failed'} for user ${user.id}`);
      res.json(result);

    } catch (error) {
      console.error('[IAP] Subscription validation error:', error);
      res.status(500).json({
        success: false,
        error: 'SERVER_ERROR',
        message: 'Internal server error during subscription validation',
        timestamp: new Date().toISOString()
      });
    }
  })().catch(err => {
    console.error('[IAP] Unhandled subscription validation error:', err);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  });
});

/**
 * POST /api/purchases/validate-credits
 * Validate credit purchase with Apple App Store Server API
 * REQUIRES: Authorization header with Supabase JWT token
 */
app.post('/api/purchases/validate-credits', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication required. Include Authorization: Bearer <token> header.',
          timestamp: new Date().toISOString()
        });
      }

      // Authenticate user
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication failed. Invalid or expired token.',
          timestamp: new Date().toISOString()
        });
      }

      console.log(`[IAP] Credit validation request from user: ${user.id}`);

      // Validate request body
      const { receipt_data, transaction_id, product_id } = req.body;
      if (!receipt_data || !transaction_id || !product_id) {
        return res.status(400).json({
          success: false,
          error: 'INVALID_REQUEST',
          message: 'receipt_data, transaction_id, and product_id are required',
          timestamp: new Date().toISOString()
        });
      }

      // Process credit validation
      const service = getIAPService();
      const result = await service.validateCredits(user.id, { receipt_data, transaction_id, product_id });

      console.log(`[IAP] Credit validation ${result.success ? 'successful' : 'failed'} for user ${user.id}`);
      res.json(result);

    } catch (error) {
      console.error('[IAP] Credit validation error:', error);
      res.status(500).json({
        success: false,
        error: 'SERVER_ERROR',
        message: 'Internal server error during credit validation',
        timestamp: new Date().toISOString()
      });
    }
  })().catch(err => {
    console.error('[IAP] Unhandled credit validation error:', err);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  });
});

/**
 * GET /api/purchases/status
 * Get user subscription and credit status
 * REQUIRES: Authorization header with Supabase JWT token
 */
app.get('/api/purchases/status', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication required. Include Authorization: Bearer <token> header.',
          timestamp: new Date().toISOString()
        });
      }

      // Authenticate user
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication failed. Invalid or expired token.',
          timestamp: new Date().toISOString()
        });
      }

      console.log(`[IAP] Status request from user: ${user.id}`);

      // Get user status
      const service = getIAPService();
      const result = await service.getUserStatus(user.id);

      res.json(result);

    } catch (error) {
      console.error('[IAP] Status request error:', error);
      res.status(500).json({
        success: false,
        error: 'SERVER_ERROR',
        message: 'Internal server error during status request',
        timestamp: new Date().toISOString()
      });
    }
  })().catch(err => {
    console.error('[IAP] Unhandled status request error:', err);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  });
});

/**
 * GET /api/purchases/health
 * Health check for IAP service
 */
app.get('/api/purchases/health', function(req, res) {
  (async () => {
    try {
      const service = getIAPService();
      const isHealthy = await service.testConfiguration();

      if (isHealthy) {
        res.json({
          success: true,
          status: 'healthy',
          message: 'IAP service is operational',
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(503).json({
          success: false,
          status: 'unhealthy',
          message: 'IAP service configuration issue',
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      console.error('[IAP] Health check error:', error);
      res.status(500).json({
        success: false,
        error: 'SERVER_ERROR',
        message: 'Health check failed',
        timestamp: new Date().toISOString()
      });
    }
  })().catch(err => {
    console.error('[IAP] Unhandled health check error:', err);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  });
});

//-----------------------------------------------
// XP System API Endpoints for CliniSim
//-----------------------------------------------

// Import XP service
import { XPService } from './services/xp-service';
import { XPServiceError } from './services/xp-types';

// Initialize XP service (will be created when first used)
let xpService: XPService | null = null;

function getXPService(): XPService {
  if (!xpService) {
    xpService = new XPService();
  }
  return xpService;
}

/**
 * GET /api/xp/progress
 * Get user XP progress and stats
 * REQUIRES: Authorization header with Supabase JWT token
 */
app.get('/api/xp/progress', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication required. Include Authorization: Bearer <token> header.',
          timestamp: new Date().toISOString()
        });
      }

      // Authenticate user
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication failed. Invalid or expired token.',
          timestamp: new Date().toISOString()
        });
      }

      console.log(`[XP] Progress request from user: ${user.id}`);

      // Get user progress
      const service = getXPService();
      const result = await service.getUserProgress(user.id);

      res.json(result);

    } catch (error) {
      console.error('[XP] Progress request error:', error);
      
      if (error instanceof Error && 'code' in error && 'statusCode' in error) {
        const serviceError = error as XPServiceError;
        return res.status(serviceError.statusCode).json({
          success: false,
          error: serviceError.code,
          message: serviceError.message,
          timestamp: new Date().toISOString(),
          ...(serviceError.details && { details: serviceError.details })
        });
      }

      res.status(500).json({
        success: false,
        error: 'SERVER_ERROR',
        message: 'Internal server error during progress request',
        timestamp: new Date().toISOString()
      });
    }
  })().catch(err => {
    console.error('[XP] Unhandled progress request error:', err);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  });
});

/**
 * POST /api/xp/award
 * Award XP to user and update progression
 * REQUIRES: Authorization header with Supabase JWT token
 */
app.post('/api/xp/award', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication required. Include Authorization: Bearer <token> header.',
          timestamp: new Date().toISOString()
        });
      }

      // Authenticate user
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication failed. Invalid or expired token.',
          timestamp: new Date().toISOString()
        });
      }

      console.log(`[XP] Award XP request from user: ${user.id}`);

      // Validate request body
      const { amount, source, description, call_id, session_id } = req.body;
      if (!amount || !source) {
        return res.status(400).json({
          success: false,
          error: 'INVALID_REQUEST',
          message: 'amount and source are required',
          timestamp: new Date().toISOString()
        });
      }

      // Award XP
      const service = getXPService();
      const result = await service.awardXP({
        user_id: user.id,
        amount,
        source,
        description,
        call_id,
        session_id
      });

      console.log(`[XP] Awarded ${amount} XP to user ${user.id} from ${source}`);

      res.json(result);

    } catch (error) {
      console.error('[XP] Award XP error:', error);
      
      if (error instanceof Error && 'code' in error && 'statusCode' in error) {
        const serviceError = error as XPServiceError;
        return res.status(serviceError.statusCode).json({
          success: false,
          error: serviceError.code,
          message: serviceError.message,
          timestamp: new Date().toISOString(),
          ...(serviceError.details && { details: serviceError.details })
        });
      }

      res.status(500).json({
        success: false,
        error: 'SERVER_ERROR',
        message: 'Internal server error during XP award',
        timestamp: new Date().toISOString()
      });
    }
  })().catch(err => {
    console.error('[XP] Unhandled award XP error:', err);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  });
});

/**
 * GET /api/xp/transactions
 * Get user XP transaction history
 * REQUIRES: Authorization header with Supabase JWT token
 */
app.get('/api/xp/transactions', function(req, res) {
  (async () => {
    try {
      // Extract and validate JWT token
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
      
      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication required. Include Authorization: Bearer <token> header.',
          timestamp: new Date().toISOString()
        });
      }

      // Authenticate user
      const user = await webSocketAuthService.authenticate(token);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Authentication failed. Invalid or expired token.',
          timestamp: new Date().toISOString()
        });
      }

      console.log(`[XP] Transaction history request from user: ${user.id}`);

      // Parse query parameters
      const limit = Math.min(parseInt(req.query.limit as string) || 50, 100);
      const offset = parseInt(req.query.offset as string) || 0;

      // Get transaction history
      const service = getXPService();
      const result = await service.getXPTransactionHistory(user.id, limit, offset);

      res.json(result);

    } catch (error) {
      console.error('[XP] Transaction history error:', error);
      
      if (error instanceof Error && 'code' in error && 'statusCode' in error) {
        const serviceError = error as XPServiceError;
        return res.status(serviceError.statusCode).json({
          success: false,
          error: serviceError.code,
          message: serviceError.message,
          timestamp: new Date().toISOString(),
          ...(serviceError.details && { details: serviceError.details })
        });
      }

      res.status(500).json({
        success: false,
        error: 'SERVER_ERROR',
        message: 'Internal server error during transaction history request',
        timestamp: new Date().toISOString()
      });
    }
  })().catch(err => {
    console.error('[XP] Unhandled transaction history error:', err);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  });
});




/**
 * GET /api/xp/health
 * Health check for XP service
 */
app.get('/api/xp/health', function(req, res) {
  (async () => {
    try {
      const service = getXPService();
      const isHealthy = await service.testConfiguration();

      if (isHealthy) {
        res.json({
          success: true,
          status: 'healthy',
          message: 'XP service is operational',
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(503).json({
          success: false,
          status: 'unhealthy',
          message: 'XP service configuration issue',
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      console.error('[XP] Health check error:', error);
      res.status(500).json({
        success: false,
        error: 'SERVER_ERROR',
        message: 'Health check failed',
        timestamp: new Date().toISOString()
      });
    }
  })().catch(err => {
    console.error('[XP] Unhandled health check error:', err);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  });
});

// Clean up on server shutdown
process.on('SIGINT', async () => {
  console.log('Received SIGINT, shutting down gracefully...');
  
  if (wss) {
    wss.close();
  }
  
  // Clean up lobby manager
  await livekitLobbyManager.shutdown();
  
  // Clean up any recording streams
  for (const callId in calls) {
    const callState = calls[callId];
    if (callState.recordingWriter) {
      callState.recordingWriter.end();
    }
  }
  
  // Clean up Neo4j connections
  try {
    const { sessionManager } = await import('./tutor/session/session-manager');
    await sessionManager.cleanup();
    console.log('✅ Neo4j connections closed');
  } catch (error) {
    console.error('❌ Error closing Neo4j connections:', error);
  }
  
  process.exit(0);
});
