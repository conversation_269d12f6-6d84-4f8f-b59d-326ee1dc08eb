/**
 * Text-to-Speech Abstraction Layer (Complete Azure Logic)
 * 
 * Centralizes ALL TTS business logic extracted from Azure TTS Provider.
 * Providers are thin wrappers that only handle API-specific communication.
 * Preserves all interruption, cancellation, and state management functionality.
 */

import { ConnectionType } from '../config/audio-constants';
import { calls } from '../server';
import { latencyTracker, LatencySteps } from '../latencyTracker';
import { getTurnAuthorityService } from './turn-authority/XStateTurnAuthorityService';
import { ENABLE_WEBSOCKET_STREAMING, ENABLE_MEMORY_TTS } from '../constants';
import * as fs from 'fs';
import * as path from 'path';
import { spawn } from 'child_process';
import { WebSocket } from 'ws';
import { conversationTimingService, ConversationPhase } from './ConversationTimingService';

// Import for sending XState events
import { callControllers } from '../xstateIntegration';

// Unified task-based interface - completely provider-agnostic
export interface TTSTask {
  id: string;
  callId: string;
  text: string;
  sentenceIndex?: number;
  status: 'active' | 'completed' | 'cancelled' | 'error';
  startTime: number;
  endTime?: number;
}

export interface TTSProvider {
  // Existing file-based methods
  generateAudio(text: string, taskId: string): Promise<string | null>;
  generateSentenceAudio(sentence: string, taskId: string, sentenceIndex: number): Promise<string | null>;
  cancelTask(taskId: string): Promise<void>;
  cleanup(): Promise<void>;
  supportsConnectionType(connectionType: ConnectionType): boolean;
  
  // NEW: WebSocket streaming methods (optional for backward compatibility)
  generateStreamingAudio?(
    text: string, 
    taskId: string, 
    chunkCallback: (chunk: Buffer, metadata?: StreamingMetadata) => Promise<void>,
    errorCallback?: (error: Error) => void
  ): Promise<void>;
  
  supportsRealTimeStreaming?(): boolean;
  cancelStreamingTask?(taskId: string): Promise<void>;
}

// WebSocket streaming result indicator
export const WEBSOCKET_STREAM_SUCCESS = 'WEBSOCKET_STREAM_SUCCESS';

// Streaming metadata interface
export interface StreamingMetadata {
  chunkIndex: number;
  totalExpectedChunks?: number;
  isFirstChunk: boolean;
  isLastChunk: boolean;
  timestamp: number;
  audioFormat?: string;
}

export interface TTSProviderConfig {
  callId: string;
  connectionType: ConnectionType;
  turnIndex: number;
  speechKey: string;
  speechRegion: string;
  speechEndpoint?: string | null;
  speechVoice: string;
  enableStreamingTts: boolean;
  enableWebSocketStreaming: boolean;
  enableMemoryTts: boolean;
  audioDir: string;
}

// Task-based callback interface - no provider-specific objects
export interface TTSTaskCallbacks {
  onTaskStarted(task: TTSTask): void;
  onTaskCompleted(task: TTSTask, audioPath: string): void;
  onTaskError(task: TTSTask, error: any): void;
}

// Main abstraction layer that handles all business logic from Azure TTS Provider
export class TextToSpeechManagerComplete {
  private static instance: TextToSpeechManagerComplete;
  private activeProviders: Map<string, { provider: TTSProvider; type: string; createdAt: number }> = new Map();
  private activeTasks: Map<string, TTSTask> = new Map(); // Unified task tracking
  private memoryUsageTracker: Map<string, { bufferSize: number; timestamp: number }> = new Map(); // Memory leak prevention
  private cleanupIntervalId: NodeJS.Timeout | null = null;
  private readonly MAX_MEMORY_USAGE = 200 * 1024 * 1024; // 200MB total memory limit
  private readonly CLEANUP_INTERVAL_MS = 30000; // Clean up every 30 seconds
  private readonly MAX_TASK_AGE_MS = 300000; // 5 minutes max task age

  private constructor() {
    this.startMemoryCleanupInterval();
  }

  public static getInstance(): TextToSpeechManagerComplete {
    if (!TextToSpeechManagerComplete.instance) {
      TextToSpeechManagerComplete.instance = new TextToSpeechManagerComplete();
    }
    return TextToSpeechManagerComplete.instance;
  }

  /**
   * Initialize TTS with a specific provider (hot-swappable)
   * Contains ALL business logic from AzureTTSProvider.generateAudio()
   */
  public async initializeTTS(
    providerType: string,
    callId: string,
    connectionType: ConnectionType,
    text: string,
    turnIndex: number
  ): Promise<string | null> {
    console.log(`[TextToSpeechManager] Initializing TTS with provider: ${providerType} for call: ${callId}`);

    // Start TTS generation timing
    const ttsTimingId = conversationTimingService.startPhase(
      ConversationPhase.TTS_GENERATION,
      callId,
      turnIndex,
      {
        providerType,
        textLength: text.length,
        connectionType,
        textPreview: text.substring(0, 50)
      }
    );

    // Start pre-validation sub-phase
    conversationTimingService.startSubPhase(ttsTimingId, 'pre_validation', {
      turnIndex,
      textLength: text.length
    });

    // Pre-validation: Check if TTS should be blocked due to cleanup state
    const isTtsBlocked = await this.checkTtsBlocked(callId);
    if (isTtsBlocked) {
      console.log(`[TextToSpeechManager] TTS generation prevented due to cleanup state for call: ${callId}`);
      
      // End timing for interrupted TTS
      conversationTimingService.endSubPhase(ttsTimingId, 'pre_validation');
      conversationTimingService.endPhase(ttsTimingId, {
        interrupted: true,
        reason: 'xstate_interruption'
      });
      
      return null;
    }
    
    // TURN CANCELLATION FLAG SYSTEM: Ensure turn is registered in TurnCancellationRegistry
    try {
      const { turnCancellationRegistry } = require('./TurnCancellationRegistry');
      
      // Register turn in the cancellation registry (creates if doesn't exist)
      const turnSignal = turnCancellationRegistry.getTurnSignal(callId, turnIndex);
      console.log(`[TURN-REGISTRATION] ${callId}: Registered turn ${turnIndex} in cancellation registry`);
      
      // GATE #3: Audio generation gate - Check if specific turn is cancelled before starting TTS
      const isTurnCancelled = turnCancellationRegistry.isTurnCancelled(callId, turnIndex);
      
      if (isTurnCancelled) {
        console.log(`[TTS-GATE-3] ${callId}: BLOCKED TTS generation for cancelled turn ${turnIndex}: "${text.substring(0, 30)}..."`);
        
        // End timing for cancelled turn TTS
        conversationTimingService.endSubPhase(ttsTimingId, 'pre_validation');
        conversationTimingService.endPhase(ttsTimingId, {
          interrupted: true,
          reason: 'turn_cancelled',
          turnIndex: turnIndex
        });
        
        return null;
      }
    } catch (error) {
      console.error(`[TTS-GATE-3] ${callId}: Error with turn cancellation registry for turn ${turnIndex}:`, error);
      // Continue with TTS generation if registry is unavailable (fail-open for stability)
    }
    
    // End pre-validation sub-phase
    conversationTimingService.endSubPhase(ttsTimingId, 'pre_validation');

    // Create provider instance
    const provider = this.createProvider(providerType);
    
    if (!provider.supportsConnectionType(connectionType)) {
      throw new Error(`TTS provider '${providerType}' does not support connection type '${connectionType}'`);
    }

    // Get configuration for provider
    const config = this.getProviderConfig(callId, connectionType, turnIndex);

    // Store provider with explicit type tracking for cleanup
    this.activeProviders.set(callId, { 
      provider: provider, 
      type: providerType, 
      createdAt: Date.now() 
    });

    // === UNIFIED TASK-BASED TTS LOGIC ===
    
    // Start provider initialization sub-phase
    conversationTimingService.startSubPhase(ttsTimingId, 'provider_initialization', {
      providerType,
      connectionType,
      streamingEnabled: config.enableStreamingTts
    });
    
    conversationTimingService.endSubPhase(ttsTimingId, 'provider_initialization');
    
    try {
      let result: string | null;
      
      // Enhanced streaming decision with WebSocket priority
      console.log(`[TextToSpeechManager] DECISION DEBUG for ${callId}:`);
      console.log(`[TextToSpeechManager]   - enableWebSocketStreaming: ${config.enableWebSocketStreaming}`);
      console.log(`[TextToSpeechManager]   - provider.supportsRealTimeStreaming(): ${provider.supportsRealTimeStreaming?.()}`);
      console.log(`[TextToSpeechManager]   - enableStreamingTts: ${config.enableStreamingTts}`);
      console.log(`[TextToSpeechManager]   - enableMemoryTts: ${config.enableMemoryTts}`);
      console.log(`[TextToSpeechManager]   - provider type: ${provider.constructor.name}`);
      
      if (config.enableWebSocketStreaming && provider.supportsRealTimeStreaming?.()) {
        console.log(`[TextToSpeechManager] Using WebSocket streaming for ${callId}`);
        result = await this.generateWebSocketStreamingAudio(text, callId, turnIndex, provider, config, ttsTimingId);
      } else if (config.enableStreamingTts) {
        console.log(`[TextToSpeechManager] Using sentence streaming for ${callId}`);
        result = await this.generateStreamingAudio(text, callId, turnIndex, provider, config, ttsTimingId);
      } else {
        // REST API single generation pathway - memory vs disk I/O decision
        if (config.enableMemoryTts) {
          console.log(`[TextToSpeechManager] Using memory-based single generation for ${callId}`);
          result = await this.generateMemoryAudio(text, callId, turnIndex, provider, config, ttsTimingId);
        } else {
          console.log(`[TextToSpeechManager] Using disk-based single generation for ${callId}`);
          result = await this.generateAudioOriginal(text, callId, turnIndex, provider, config, ttsTimingId);
        }
      }
      
      // End TTS generation timing with success
      conversationTimingService.endPhase(ttsTimingId, {
        success: true,
        audioFilePath: result,
        streamingMode: config.enableStreamingTts,
        textLength: text.length,
        providerType
      });
      
      // TURN CANCELLATION FLAG SYSTEM: Mark turn as completed in TurnCancellationRegistry
      try {
        const { turnCancellationRegistry } = require('./TurnCancellationRegistry');
        const completionSuccess = turnCancellationRegistry.completeTurn(callId, turnIndex);
        
        if (completionSuccess) {
          console.log(`[TURN-COMPLETION] ${callId}: Marked turn ${turnIndex} as completed`);
        } else {
          console.warn(`[TURN-COMPLETION] ${callId}: Failed to mark turn ${turnIndex} as completed - may not exist in registry`);
        }
      } catch (error) {
        console.error(`[TURN-COMPLETION] ${callId}: Error marking turn ${turnIndex} as completed:`, error);
      }
      
      return result;
      
    } catch (error) {
      // End TTS generation timing with error
      conversationTimingService.endPhase(ttsTimingId, {
        success: false,
        error: error.message,
        providerType
      });
      throw error;
    }
  }

  /**
   * Streaming audio generation (extracted from AzureTTSProvider)
   */
  private async generateStreamingAudio(
    text: string, 
    callId: string, 
    turnIndex: number, 
    provider: TTSProvider,
    config: TTSProviderConfig,
    ttsTimingId: string
  ): Promise<string | null> {
    // Start streaming audio generation sub-phase
    conversationTimingService.startSubPhase(ttsTimingId, 'streaming_audio_generation', {
      textLength: text.length,
      turnIndex
    });
    try {
      // Ensure latency tracking session exists
      const existingSession = latencyTracker.getCurrentSession(callId, turnIndex);
      if (!existingSession) {
        console.log(`[LATENCY-INIT] Starting latency tracking for ${callId} turn ${turnIndex} at TTS generation`);
        latencyTracker.startTracking(callId, turnIndex);
        latencyTracker.recordStep(callId, turnIndex, LatencySteps.TURN_FINALIZED, {
          note: 'Started at TTS - missed silence detection'
        });
      }
      
      // Record TTS generation start
      const hasGenerationStarted = existingSession?.steps.some(step => step.name === LatencySteps.TTS_GENERATION_STARTED);
      if (!hasGenerationStarted) {
        latencyTracker.recordStep(callId, turnIndex, LatencySteps.TTS_GENERATION_STARTED, {
          textLength: text.length,
          text: text.substring(0, 100) + '...',
          mode: 'streaming'
        });
      } else {
        console.log(`[LATENCY-DEBUG] TTS_GENERATION_STARTED already recorded for ${callId} turn ${turnIndex} - skipping duplicate`);
      }
      
      console.log(`[STREAMING-TTS] Generating streaming audio response for callSid=${callId}, turn=${turnIndex}`);
      
      // Split text into sentences
      const sentences = this.splitIntoSentences(text);
      console.log(`[STREAMING-TTS] Split into ${sentences.length} sentences`);
      
      // Generate unique filename for the final concatenated file
      const timestamp = Date.now();
      const finalAudioFilename = `${callId}_turn${turnIndex}_${timestamp}.mp3`;
      const finalAudioPath = path.join(config.audioDir, finalAudioFilename);
      
      // Track sentence audio files for cleanup and concatenation
      const sentenceAudioPaths: string[] = [];
      let firstSentenceStreamed = false;
      
      latencyTracker.recordStep(callId, turnIndex, LatencySteps.TTS_SYNTHESIS_STARTED);
      
      // Process sentences in parallel for faster synthesis using unified task system
      const synthesisPromises = sentences.map(async (sentence, index) => {
        // Create unique task ID for this sentence
        const taskId = `${callId}_turn${turnIndex}_sentence${index}_${Date.now()}`;
        
        // Create task object for tracking
        const task: TTSTask = {
          id: taskId,
          callId,
          text: sentence,
          sentenceIndex: index,
          status: 'active',
          startTime: Date.now()
        };
        
        // Track task
        this.activeTasks.set(taskId, task);
        console.log(`[TTS-TASK] Started sentence task ${taskId} for sentence ${index}`);
        
        const audioPath = await provider.generateSentenceAudio(sentence, taskId, index);
        
        if (audioPath) {
          sentenceAudioPaths[index] = audioPath; // Maintain order
          
          // Mark task as completed
          task.status = 'completed';
          task.endTime = Date.now();
          this.activeTasks.delete(taskId);
          console.log(`[TTS-TASK] Completed sentence task ${taskId} for sentence ${index}`);
          
          // Stream the first sentence immediately for fastest response
          if (index === 0 && !firstSentenceStreamed) {
            firstSentenceStreamed = true;
            
            // Record first sentence ready (time to first audio)
            latencyTracker.recordStep(callId, turnIndex, 'FIRST_SENTENCE_READY', {
              sentenceIndex: index,
              sentenceText: sentence.substring(0, 50) + '...'
            });
            
            // Stream immediately
            await this.streamSentenceToCall(audioPath, callId, index, turnIndex);
            
            latencyTracker.recordStep(callId, turnIndex, LatencySteps.FIRST_AUDIO_CHUNK_SENT);
            latencyTracker.recordStep(callId, turnIndex, LatencySteps.AUDIO_PLAYBACK_STARTED);
          }
        } else {
          // Mark task as error
          task.status = 'error';
          task.endTime = Date.now();
          this.activeTasks.delete(taskId);
          console.log(`[TTS-TASK] Failed sentence task ${taskId} for sentence ${index}`);
        }
        
        return audioPath;
      });
      
      // Wait for all synthesis to complete
      const results = await Promise.all(synthesisPromises);
      
      // Filter out failed synthesis and maintain order
      const validAudioPaths = sentenceAudioPaths.filter(path => path && fs.existsSync(path));
      
      if (validAudioPaths.length === 0) {
        console.error(`[STREAMING-TTS] No valid audio files generated for ${callId} turn ${turnIndex}`);
        return null;
      }
      
      latencyTracker.recordStep(callId, turnIndex, LatencySteps.TTS_SYNTHESIS_COMPLETED);
      
      // Stream remaining sentences sequentially to maintain order
      for (let i = 1; i < validAudioPaths.length; i++) {
        if (validAudioPaths[i]) {
          await this.streamSentenceToCall(validAudioPaths[i], callId, i);
        }
      }
      
      // Concatenate all sentence files into the expected single file for recording
      console.log(`[STREAMING-TTS] Concatenating ${validAudioPaths.length} sentence files`);
      const concatSuccess = await this.concatenateAudioFiles(validAudioPaths, finalAudioPath);
      
      if (!concatSuccess) {
        console.error(`[STREAMING-TTS] Failed to concatenate audio files for ${callId} turn ${turnIndex}`);
        return null;
      }
      
      latencyTracker.recordStep(callId, turnIndex, LatencySteps.TTS_FILE_WRITTEN, {
        audioPath: finalAudioFilename,
        fileSize: fs.existsSync(finalAudioPath) ? fs.statSync(finalAudioPath).size : 0,
        sentenceCount: validAudioPaths.length
      });
      
      // Clean up individual sentence files
      validAudioPaths.forEach(sentencePath => {
        try {
          fs.unlinkSync(sentencePath);
        } catch (err) {
          console.warn(`[STREAMING-TTS] Failed to cleanup sentence file ${sentencePath}: ${err}`);
        }
      });
      
      console.log(`[STREAMING-TTS] Final concatenated audio saved to ${finalAudioPath}`);
      console.log(`[STREAMING-TTS] ✅ Streaming TTS completed with ${validAudioPaths.length} sentences`);
      
      // Route audio and send TTS_FINISHED event
      await this.routeAudio(callId, finalAudioPath, turnIndex, config.connectionType);
      await this.sendTtsFinishedEvent(callId);
      
      // End streaming audio generation sub-phase
      conversationTimingService.endSubPhase(ttsTimingId, 'streaming_audio_generation');
      
      return finalAudioPath;
      
    } catch (err) {
      console.error(`[STREAMING-TTS] Error in streaming audio generation for ${callId}:`, err);
      
      // End streaming audio generation sub-phase with error
      conversationTimingService.endSubPhase(ttsTimingId, 'streaming_audio_generation');
      
      // Fallback to original implementation on error
      return this.generateAudioOriginal(text, callId, turnIndex, provider, config, ttsTimingId);
    }
  }

  /**
   * Original generateAudioResponse implementation (extracted from AzureTTSProvider)
   */
  private async generateAudioOriginal(
    text: string, 
    callId: string, 
    turnIndex: number, 
    provider: TTSProvider,
    config: TTSProviderConfig,
    ttsTimingId: string
  ): Promise<string | null> {
    // Start original audio generation sub-phase
    conversationTimingService.startSubPhase(ttsTimingId, 'original_audio_generation', {
      textLength: text.length,
      turnIndex
    });
    try {
      // Ensure latency tracking session exists (start if needed)
      const existingSession = latencyTracker.getCurrentSession(callId, turnIndex);
      if (!existingSession) {
        console.log(`[LATENCY-INIT] Starting latency tracking for ${callId} turn ${turnIndex} at TTS generation`);
        latencyTracker.startTracking(callId, turnIndex);
        // Also record turn finalized since we missed the silence detection
        latencyTracker.recordStep(callId, turnIndex, LatencySteps.TURN_FINALIZED, {
          note: 'Started at TTS - missed silence detection'
        });
      }

      // Record TTS generation start
      const ttsStartTime = Date.now();
      console.log(`[STARTUP-TIMING] ${callId}: TTS generation started at ${ttsStartTime}`);
      const hasGenerationStarted = existingSession?.steps.some(step => step.name === LatencySteps.TTS_GENERATION_STARTED);
      if (!hasGenerationStarted) {
        latencyTracker.recordStep(callId, turnIndex, LatencySteps.TTS_GENERATION_STARTED, {
          textLength: text.length,
          text: text.substring(0, 100) + '...',
          mode: 'original'
        });
      } else {
        console.log(`[LATENCY-DEBUG] TTS_GENERATION_STARTED already recorded for ${callId} turn ${turnIndex} - skipping duplicate`);
      }

      console.log(`[TTS] Generating Azure TTS audio for call ${callId}, turn ${turnIndex}: "${text.substring(0, 50)}..."`);

      // Check if TTS is already generating for this call
      if (calls[callId] && calls[callId].isTtsGenerating) {
        console.log(`[TTS] TTS already generating for call ${callId}, skipping...`);
        return null;
      }

      // Initialize call state if it doesn't exist
      if (!calls[callId]) {
        calls[callId] = {} as any; // Type assertion for backward compatibility
      }
      calls[callId].isTtsGenerating = true;
      calls[callId].currentTtsText = text;
      calls[callId].currentTtsTurnIndex = turnIndex;

      // Record synthesis start
      latencyTracker.recordStep(callId, turnIndex, LatencySteps.TTS_SYNTHESIS_STARTED);

      // Create unified task for tracking
      const taskId = `${callId}_turn${turnIndex}_${Date.now()}`;
      const task: TTSTask = {
        id: taskId,
        callId,
        text,
        status: 'active',
        startTime: Date.now()
      };
      
      this.activeTasks.set(taskId, task);
      console.log(`[TTS-TASK] Started single audio task ${taskId}`);

      // Generate audio through provider
      const audioPath = await provider.generateAudio(text, taskId);
      
      // Update task status
      if (audioPath) {
        task.status = 'completed';
        task.endTime = Date.now();
        this.activeTasks.delete(taskId);
        console.log(`[TTS-TASK] Completed single audio task ${taskId}`);
      } else {
        task.status = 'error';
        task.endTime = Date.now();
        this.activeTasks.delete(taskId);
        console.log(`[TTS-TASK] Failed single audio task ${taskId}`);
      }

      // Mark TTS as no longer generating
      if (calls[callId]) {
        calls[callId].isTtsGenerating = false;
      }

      if (audioPath) {
        console.log(`[TTS] Speech synthesized for text: "${text.substring(0, 50)}..."`);

        // Record synthesis completion and file writing
        latencyTracker.recordStep(callId, turnIndex, LatencySteps.TTS_SYNTHESIS_COMPLETED);
        latencyTracker.recordStep(callId, turnIndex, LatencySteps.TTS_FILE_WRITTEN, {
          audioPath: path.basename(audioPath),
          fileSize: fs.existsSync(audioPath) ? fs.statSync(audioPath).size : 0
        });

        // Route audio and send TTS_FINISHED event
        await this.routeAudio(callId, audioPath, turnIndex, config.connectionType);
        await this.sendTtsFinishedEvent(callId);

        // End original audio generation sub-phase
        conversationTimingService.endSubPhase(ttsTimingId, 'original_audio_generation');

        return audioPath;
      } else {
        console.error(`Speech synthesis failed`);
        
        // End original audio generation sub-phase with failure
        conversationTimingService.endSubPhase(ttsTimingId, 'original_audio_generation');
        
        return null;
      }

    } catch (err) {
      console.error(`Error in generateAudioResponse: ${err}`);
      
      // End original audio generation sub-phase with error
      conversationTimingService.endSubPhase(ttsTimingId, 'original_audio_generation');
      
      // Mark TTS as no longer generating on error
      if (calls[callId]) {
        calls[callId].isTtsGenerating = false;
      }
      return null;
    }
  }

  /**
   * Memory-based single audio generation - processes audio in memory without file I/O
   */
  private async generateMemoryAudio(
    text: string, 
    callId: string, 
    turnIndex: number, 
    provider: TTSProvider,
    config: TTSProviderConfig,
    ttsTimingId: string
  ): Promise<string | null> {
    // Start memory audio generation sub-phase
    conversationTimingService.startSubPhase(ttsTimingId, 'memory_audio_generation', {
      textLength: text.length,
      turnIndex
    });
    
    try {
      // Ensure latency tracking session exists (start if needed)
      const existingSession = latencyTracker.getCurrentSession(callId, turnIndex);
      if (!existingSession) {
        console.log(`[LATENCY-INIT] Starting latency tracking for ${callId} turn ${turnIndex} at TTS generation`);
        latencyTracker.startTracking(callId, turnIndex);
        // Also record turn finalized since we missed the silence detection
        latencyTracker.recordStep(callId, turnIndex, LatencySteps.TURN_FINALIZED, {
          note: 'Started at TTS - missed silence detection'
        });
      }

      // Record TTS generation start
      const ttsStartTime = Date.now();
      console.log(`[STARTUP-TIMING] ${callId}: Memory-based TTS generation started at ${ttsStartTime}`);
      const hasGenerationStarted = existingSession?.steps.some(step => step.name === LatencySteps.TTS_GENERATION_STARTED);
      if (!hasGenerationStarted) {
        latencyTracker.recordStep(callId, turnIndex, LatencySteps.TTS_GENERATION_STARTED, {
          textLength: text.length,
          text: text.substring(0, 100) + '...',
          mode: 'memory'
        });
      } else {
        console.log(`[LATENCY-DEBUG] TTS_GENERATION_STARTED already recorded for ${callId} turn ${turnIndex} - skipping duplicate`);
      }

      console.log(`[MEMORY-TTS] Generating memory-based TTS audio for call ${callId}, turn ${turnIndex}: "${text.substring(0, 50)}..."`);

      // Check if TTS is already generating for this call
      if (calls[callId] && calls[callId].isTtsGenerating) {
        console.log(`[MEMORY-TTS] TTS already generating for call ${callId}, skipping`);
        conversationTimingService.endSubPhase(ttsTimingId, 'memory_audio_generation');
        return null;
      }

      // Mark TTS as generating
      if (calls[callId]) {
        calls[callId].isTtsGenerating = true;
        calls[callId].currentTtsText = text;
        calls[callId].currentTtsTurnIndex = turnIndex;
      }

      // Record synthesis start
      latencyTracker.recordStep(callId, turnIndex, LatencySteps.TTS_SYNTHESIS_STARTED);

      // Create unified task for tracking
      const taskId = `${callId}_turn${turnIndex}_${Date.now()}`;
      const task: TTSTask = {
        id: taskId,
        callId,
        text,
        status: 'active',
        startTime: Date.now()
      };
      
      this.activeTasks.set(taskId, task);
      console.log(`[TTS-TASK] Started memory-based audio task ${taskId}`);

      // Generate audio through provider (this will return audio buffer instead of file path)
      // For now, we'll use the existing generateAudio method and then process the file in memory
      // TODO: In future iterations, providers could directly return buffers
      const audioPath = await provider.generateAudio(text, taskId);
      
      // Memory leak prevention: Initialize buffer tracking
      let audioBuffer: Buffer | null = null;
      let tempFilePath = audioPath;
      let finalResult = audioPath;
      
      // Update task status
      if (audioPath) {
        try {
          // Memory leak prevention: Check file size before loading into memory
          const stats = await fs.promises.stat(audioPath);
          const MAX_BUFFER_SIZE = 50 * 1024 * 1024; // 50MB limit
          
          if (stats.size > MAX_BUFFER_SIZE) {
            throw new Error(`Audio file too large for memory processing: ${stats.size} bytes (max: ${MAX_BUFFER_SIZE})`);
          }
          
          // Check if memory usage would exceed limits
          if (!this.checkMemoryUsage(taskId, stats.size)) {
            throw new Error('Memory usage would exceed limit for concurrent operations');
          }
          
          console.log(`[MEMORY-TTS] Reading ${stats.size} bytes into memory: ${audioPath}`);
          
          // Track memory usage for this task
          this.trackMemoryUsage(taskId, stats.size);
          
          // Read file into buffer with explicit memory tracking
          audioBuffer = await fs.promises.readFile(audioPath);
          
          // Verify buffer allocation succeeded
          if (!audioBuffer || audioBuffer.length === 0) {
            throw new Error('Failed to read audio file into memory buffer');
          }
          
          console.log(`[MEMORY-TTS] Successfully loaded ${audioBuffer.length} bytes into memory`);
          
          // Memory leak prevention: Delete temp file immediately after reading
          try {
            await fs.promises.unlink(audioPath);
            console.log(`[MEMORY-TTS] Deleted temporary file: ${audioPath}`);
            tempFilePath = null; // Mark as cleaned up
          } catch (unlinkError) {
            console.warn(`[MEMORY-TTS] Failed to delete temp file ${audioPath}: ${unlinkError.message}`);
          }
          
          // Process audio buffer through memory-based operations
          // For now, we'll simulate processing and create final output
          console.log(`[MEMORY-TTS] Processing ${audioBuffer.length} bytes of audio data in memory`);
          
          // Memory leak prevention: Create final file path with unique name to avoid conflicts
          const timestamp = Date.now();
          const finalAudioFilename = `${callId}_turn${turnIndex}_memory_${timestamp}.mp3`;
          const finalAudioPath = path.join(config.audioDir, finalAudioFilename);
          
          // Write processed buffer to final location
          await fs.promises.writeFile(finalAudioPath, audioBuffer);
          console.log(`[MEMORY-TTS] Memory-processed audio written to: ${finalAudioPath}`);
          
          // Memory leak prevention: Explicitly release buffer reference and memory tracking
          audioBuffer = null;
          this.releaseMemoryTracking(taskId);
          
          // Update task with final path
          task.status = 'completed';
          task.endTime = Date.now();
          this.activeTasks.delete(taskId);
          console.log(`[TTS-TASK] Completed memory-based audio task ${taskId}`);
          
          // Update result path for downstream processing
          finalResult = finalAudioPath;
          
        } catch (memoryError) {
          console.error(`[MEMORY-TTS] Error in memory processing: ${memoryError.message}`);
          
          // Memory leak prevention: Clean up resources on error
          if (audioBuffer) {
            audioBuffer = null; // Release buffer reference
          }
          this.releaseMemoryTracking(taskId); // Clean up memory tracking
          
          // Clean up temp file if it still exists
          if (tempFilePath) {
            try {
              await fs.promises.unlink(tempFilePath);
              console.log(`[MEMORY-TTS] Cleaned up temp file after error: ${tempFilePath}`);
            } catch (cleanupError) {
              console.warn(`[MEMORY-TTS] Failed to cleanup temp file: ${cleanupError.message}`);
            }
          }
          
          task.status = 'error';
          task.endTime = Date.now();
          this.activeTasks.delete(taskId);
          
          // Mark TTS as no longer generating
          if (calls[callId]) {
            calls[callId].isTtsGenerating = false;
          }
          
          conversationTimingService.endSubPhase(ttsTimingId, 'memory_audio_generation');
          return null;
        }
      } else {
        task.status = 'error';
        task.endTime = Date.now();
        this.activeTasks.delete(taskId);
        console.log(`[TTS-TASK] Failed memory-based audio task ${taskId} - no audio path returned`);
      }

      // Mark TTS as no longer generating
      if (calls[callId]) {
        calls[callId].isTtsGenerating = false;
      }

      if (finalResult) {
        console.log(`[MEMORY-TTS] Memory-based speech synthesis completed for text: "${text.substring(0, 50)}..."`);

        // Record synthesis completion and processing
        latencyTracker.recordStep(callId, turnIndex, LatencySteps.TTS_SYNTHESIS_COMPLETED);
        latencyTracker.recordStep(callId, turnIndex, LatencySteps.TTS_FILE_WRITTEN, {
          audioPath: path.basename(finalResult),
          fileSize: fs.existsSync(finalResult) ? fs.statSync(finalResult).size : 0,
          processingMode: 'memory'
        });

        // Route audio and send TTS_FINISHED event
        await this.routeAudio(callId, finalResult, turnIndex, config.connectionType);
        await this.sendTtsFinishedEvent(callId);

        // End memory audio generation sub-phase
        conversationTimingService.endSubPhase(ttsTimingId, 'memory_audio_generation');

        return finalResult;
      } else {
        console.error(`[MEMORY-TTS] Memory-based speech synthesis failed`);
        
        // End memory audio generation sub-phase with failure
        conversationTimingService.endSubPhase(ttsTimingId, 'memory_audio_generation');
        
        return null;
      }

    } catch (err) {
      console.error(`[MEMORY-TTS] Error in memory-based audio generation: ${err}`);
      
      // End memory audio generation sub-phase with error
      conversationTimingService.endSubPhase(ttsTimingId, 'memory_audio_generation');
      
      // Mark TTS as no longer generating on error
      if (calls[callId]) {
        calls[callId].isTtsGenerating = false;
      }
      return null;
    }
  }

  /**
   * WebSocket streaming audio generation - bypasses file system entirely
   */
  private async generateWebSocketStreamingAudio(
    text: string, 
    callId: string, 
    turnIndex: number, 
    provider: TTSProvider,
    config: TTSProviderConfig,
    ttsTimingId: string
  ): Promise<string | null> {
    console.log(`[WEBSOCKET-TTS] Starting WebSocket streaming for ${callId}, text: "${text.substring(0, 50)}..."`);
    
    // Pre-flight validation: Ensure provider supports WebSocket streaming
    if (!provider.supportsRealTimeStreaming?.()) {
      console.warn(`[WEBSOCKET-TTS] Provider ${provider.constructor.name} doesn't support WebSocket streaming, falling back`);
      if (config.enableStreamingTts) {
        return this.generateStreamingAudio(text, callId, turnIndex, provider, config, ttsTimingId);
      } else {
        return this.generateAudioOriginal(text, callId, turnIndex, provider, config, ttsTimingId);
      }
    }

    // Pre-flight validation: Ensure provider has streaming method
    if (!provider.generateStreamingAudio) {
      console.warn(`[WEBSOCKET-TTS] Provider ${provider.constructor.name} missing generateStreamingAudio method, falling back`);
      if (config.enableStreamingTts) {
        return this.generateStreamingAudio(text, callId, turnIndex, provider, config, ttsTimingId);
      } else {
        return this.generateAudioOriginal(text, callId, turnIndex, provider, config, ttsTimingId);
      }
    }
    
    // Start WebSocket streaming sub-phase
    conversationTimingService.startSubPhase(ttsTimingId, 'websocket_streaming_generation', {
      textLength: text.length,
      connectionType: config.connectionType,
      provider: provider.constructor.name
    });
    
    try {
      let chunkCount = 0;
      let firstChunkReceived = false;
      let streamingStartTime: number;
      
      // WebSocket chunk callback with interruption checking and routing
      const chunkCallback = async (chunk: Buffer, metadata?: StreamingMetadata) => {
        // Critical: Check if TTS should be blocked before processing each chunk
        const isTtsBlocked = await this.checkTtsBlocked(callId);
        if (isTtsBlocked) {
          console.log(`[WEBSOCKET-TTS] Streaming blocked for ${callId} at chunk ${chunkCount} due to cleanup`);
          throw new Error('TTS_STREAMING_INTERRUPTED');
        }
        
        chunkCount++;
        
        // Record first chunk timing and send AUDIO_PLAYBACK_STARTED event
        if (!firstChunkReceived) {
          firstChunkReceived = true;
          streamingStartTime = performance.now();
          
          // Send AUDIO_PLAYBACK_STARTED event when first audio chunk is received
          const controller = callControllers[callId];
          if (controller?.machine) {
            controller.machine.send({
              type: 'AUDIO_PLAYBACK_STARTED',
              callSid: callId,
              turnIndex: turnIndex,
              timestamp: Date.now()
            });
            console.log(`[WEBSOCKET-TTS] Sent AUDIO_PLAYBACK_STARTED event for ${callId} turn ${turnIndex} on first chunk`);
          }
          
          conversationTimingService.startSubPhase(ttsTimingId, 'first_chunk_latency');
          console.log(`[WEBSOCKET-TTS] First chunk received for ${callId} (${chunk.length} bytes)`);
        }
        
        // Route audio chunk directly to connection platform
        await this.routeAudioChunk(chunk, callId, turnIndex, config.connectionType, metadata);
      };
      
      // Error callback for WebSocket failures
      const errorCallback = (error: Error) => {
        console.error(`[WEBSOCKET-TTS] Streaming error for ${callId}:`, error);
        conversationTimingService.endSubPhase(ttsTimingId, 'websocket_streaming_generation');
        throw error;
      };
      
      // Generate unique task ID for this streaming session
      const taskId = `${callId}_turn${turnIndex}_ws_${Date.now()}`;
      
      // Start WebSocket streaming with provider
      await provider.generateStreamingAudio!(text, taskId, chunkCallback, errorCallback);
      
      // Record completion timing
      if (firstChunkReceived) {
        conversationTimingService.endSubPhase(ttsTimingId, 'first_chunk_latency');
      }
      
      console.log(`[WEBSOCKET-TTS] ✅ WebSocket streaming completed for ${callId}, processed ${chunkCount} chunks`);
      
      // CRITICAL FIX: Signal TTS completion like other pathways
      await this.sendTtsFinishedEvent(callId);
      console.log(`[WEBSOCKET-TTS] ✅ Sent TTS finished event for ${callId}`);
      
      // End WebSocket streaming sub-phase
      conversationTimingService.endSubPhase(ttsTimingId, 'websocket_streaming_generation');
      
      // Trigger WebSocket streaming analysis for performance insights
      setTimeout(() => {
        conversationTimingService.analyzeWebSocketStreaming(ttsTimingId);
      }, 100); // Small delay to ensure all sub-phases are completed
      
      return WEBSOCKET_STREAM_SUCCESS;
      
    } catch (error: any) {
      const errorMessage = error?.message || 'Unknown error';
      console.error(`[WEBSOCKET-TTS] Error in WebSocket streaming for ${callId}:`, error);
      
      // End WebSocket streaming sub-phase with error
      conversationTimingService.endSubPhase(ttsTimingId, 'websocket_streaming_generation');
      
      // Enhanced error categorization for better fallback decisions
      const errorType = this.categorizeWebSocketError(error);
      
      // Don't fallback if explicitly interrupted
      if (errorMessage === 'TTS_STREAMING_INTERRUPTED') {
        console.log(`[WEBSOCKET-TTS] Streaming interrupted for ${callId}, not attempting fallback`);
        return null;
      }
      
      // Connection errors - attempt fallback if configuration allows
      if (errorType === 'CONNECTION_ERROR' && config.enableStreamingTts) {
        console.log(`[WEBSOCKET-TTS] WebSocket connection failed for ${callId}, falling back to file-based streaming`);
        try {
          return await this.generateStreamingAudio(text, callId, turnIndex, provider, config, ttsTimingId);
        } catch (fallbackError) {
          console.error(`[WEBSOCKET-TTS] File-based streaming fallback also failed for ${callId}:`, fallbackError);
          // Final fallback to original generation
          return await this.generateAudioOriginal(text, callId, turnIndex, provider, config, ttsTimingId);
        }
      }
      
      // Provider errors - try alternative streaming method
      if (errorType === 'PROVIDER_ERROR' && config.enableStreamingTts) {
        console.log(`[WEBSOCKET-TTS] Provider error for ${callId}, attempting file-based streaming`);
        try {
          return await this.generateStreamingAudio(text, callId, turnIndex, provider, config, ttsTimingId);
        } catch (fallbackError) {
          console.error(`[WEBSOCKET-TTS] File-based streaming fallback also failed for ${callId}:`, fallbackError);
          return await this.generateAudioOriginal(text, callId, turnIndex, provider, config, ttsTimingId);
        }
      }
      
      // Audio routing errors - try different routing or fallback
      if (errorType === 'ROUTING_ERROR') {
        console.log(`[WEBSOCKET-TTS] Audio routing failed for ${callId}, falling back to file-based approach`);
        if (config.enableStreamingTts) {
          try {
            return await this.generateStreamingAudio(text, callId, turnIndex, provider, config, ttsTimingId);
          } catch (fallbackError) {
            console.error(`[WEBSOCKET-TTS] File-based streaming fallback failed for ${callId}:`, fallbackError);
            return await this.generateAudioOriginal(text, callId, turnIndex, provider, config, ttsTimingId);
          }
        } else {
          return await this.generateAudioOriginal(text, callId, turnIndex, provider, config, ttsTimingId);
        }
      }
      
      // Default fallback strategy for unknown errors
      console.log(`[WEBSOCKET-TTS] Unknown error (${errorType}) for ${callId}, using default fallback strategy`);
      try {
        if (config.enableStreamingTts) {
          console.log(`[WEBSOCKET-TTS] Attempting file-based streaming fallback for ${callId}`);
          return await this.generateStreamingAudio(text, callId, turnIndex, provider, config, ttsTimingId);
        } else {
          console.log(`[WEBSOCKET-TTS] Using original file generation fallback for ${callId}`);
          return await this.generateAudioOriginal(text, callId, turnIndex, provider, config, ttsTimingId);
        }
      } catch (fallbackError) {
        console.error(`[WEBSOCKET-TTS] All fallback attempts failed for ${callId}:`, fallbackError);
        // As last resort, try original generation even if other methods failed
        try {
          return await this.generateAudioOriginal(text, callId, turnIndex, provider, config, ttsTimingId);
        } catch (finalError) {
          console.error(`[WEBSOCKET-TTS] Final fallback also failed for ${callId}:`, finalError);
          return null;
        }
      }
    }
  }

  /**
   * Categorize WebSocket errors for intelligent fallback decisions
   */
  private categorizeWebSocketError(error: any): string {
    const errorMessage = error?.message?.toLowerCase() || '';
    const errorCode = error?.code;
    
    // Connection-related errors
    if (errorMessage.includes('websocket') || 
        errorMessage.includes('connection') ||
        errorMessage.includes('timeout') ||
        errorMessage.includes('network') ||
        errorCode === 'ECONNREFUSED' ||
        errorCode === 'ENOTFOUND' ||
        errorCode === 'ETIMEDOUT') {
      return 'CONNECTION_ERROR';
    }
    
    // Provider/API errors
    if (errorMessage.includes('unauthorized') ||
        errorMessage.includes('forbidden') ||
        errorMessage.includes('api key') ||
        errorMessage.includes('quota') ||
        errorMessage.includes('rate limit') ||
        errorMessage.includes('deepgram') ||
        errorCode === 401 ||
        errorCode === 403 ||
        errorCode === 429) {
      return 'PROVIDER_ERROR';
    }
    
    // Audio routing errors
    if (errorMessage.includes('routing') ||
        errorMessage.includes('livekit') ||
        errorMessage.includes('twilio') ||
        errorMessage.includes('publish') ||
        errorMessage.includes('audio chunk')) {
      return 'ROUTING_ERROR';
    }
    
    // Interruption (not really an error, but categorized for clarity)
    if (errorMessage.includes('interrupted') ||
        errorMessage.includes('cancelled')) {
      return 'INTERRUPTION';
    }
    
    // Format/parsing errors
    if (errorMessage.includes('invalid') ||
        errorMessage.includes('malformed') ||
        errorMessage.includes('parse') ||
        errorMessage.includes('format')) {
      return 'FORMAT_ERROR';
    }
    
    return 'UNKNOWN_ERROR';
  }

  /**
   * Route audio chunks directly to connection platforms (bypasses file system)
   */
  private async routeAudioChunk(
    chunk: Buffer, 
    callId: string, 
    turnIndex: number, 
    connectionType: ConnectionType,
    metadata?: StreamingMetadata
  ): Promise<void> {
    try {
      // Validate chunk before routing
      if (!chunk || chunk.length === 0) {
        // Empty chunks are valid (end-of-stream indicators)
        if (metadata?.isLastChunk) {
          console.log(`[WEBSOCKET-TTS] Received end-of-stream chunk for ${callId}`);
        }
        return;
      }
      
      // Import routing functions dynamically
      const { streamAudioChunkToLiveKit, streamAudioChunkToDirect, streamAudioChunkToTwilio } = await import('../server');
      
      const chunkInfo = metadata ? 
        `chunk ${metadata.chunkIndex} (${chunk.length} bytes)` : 
        `chunk (${chunk.length} bytes)`;
      
      // Route based on connection type with enhanced error context
      try {
        if (connectionType === ConnectionType.LIVEKIT) {
          console.log(`[WEBSOCKET-TTS] Routing ${chunkInfo} to LiveKit for ${callId}`);
          await streamAudioChunkToLiveKit(callId, chunk, turnIndex, metadata);
        } else if (connectionType === ConnectionType.DIRECT) {
          console.log(`[WEBSOCKET-TTS] Routing ${chunkInfo} to Direct WebSocket for ${callId}`);
          await streamAudioChunkToDirect(callId, chunk, turnIndex, metadata);
        } else {
          console.log(`[WEBSOCKET-TTS] Routing ${chunkInfo} to Twilio for ${callId}`);
          await streamAudioChunkToTwilio(callId, chunk, turnIndex, metadata);
        }
      } catch (routingError: any) {
        const errorMessage = routingError?.message || 'Unknown routing error';
        console.error(`[WEBSOCKET-TTS] Failed to route ${chunkInfo} to ${connectionType} for ${callId}:`, routingError);
        
        // Enhance error context for better debugging
        const enhancedError = new Error(`Audio chunk routing failed: ${errorMessage} (connectionType: ${connectionType}, chunkSize: ${chunk.length})`);
        (enhancedError as any).cause = routingError;
        throw enhancedError;
      }
      
    } catch (error: any) {
      const errorMessage = error?.message || 'Unknown error';
      console.error(`[WEBSOCKET-TTS] Error in routeAudioChunk for ${callId}:`, error);
      
      // Add more context to routing errors for better error handling upstream
      const contextualError = new Error(`Audio chunk routing error for ${callId} (${connectionType}): ${errorMessage}`);
      (contextualError as any).cause = error;
      throw contextualError;
    }
  }

  /**
   * Split text into sentences (extracted from AzureTTSProvider)
   */
  private splitIntoSentences(text: string): string[] {
    // Handle common sentence endings, including edge cases
    const sentences = text
      .split(/([.!?]+\s+)/) // Split on sentence endings with space
      .filter(s => s.trim().length > 0)
      .reduce((acc: string[], curr, index, arr) => {
        if (index % 2 === 0) {
          // This is text content
          const nextPunctuation = arr[index + 1] || '';
          acc.push((curr + nextPunctuation).trim());
        }
        return acc;
      }, [])
      .filter(s => s.length > 0);

    // Fallback: if no sentences detected, return the whole text
    return sentences.length > 0 ? sentences : [text];
  }

  /**
   * Stream audio file to call (extracted from AzureTTSProvider)
   */
  private async streamSentenceToCall(audioPath: string, callId: string, sentenceIndex: number, turnIndex?: number): Promise<boolean> {
    // Access global state (preserving existing pattern)
    const { wss } = await import('../server');
    const state = calls[callId];
    
    if (!state || !state.streamSid) {
      console.error(`No call state or streamSid for ${callId}`);
      return false;
    }

    try {
      const ffmpegArgs = [
        '-re',
        '-i', audioPath,
        '-acodec', 'pcm_mulaw',
        '-ar', '8000',
        '-ac', '1',
        '-f', 'mulaw',
        '-'
      ];

      const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);
      
      // Track sentence streaming process for cancellation
      if (!state.activeSentenceStreams) {
        state.activeSentenceStreams = [];
      }
      state.activeSentenceStreams.push({ process: ffmpegProcess, sentenceIndex });
      console.log(`[AZURE-SENTENCE-STREAM] Tracking sentence ${sentenceIndex} streaming process, total active: ${state.activeSentenceStreams.length}`);
      
      let audioPlaybackStartEventSent = false;
      
      ffmpegProcess.stdout.on('data', async (chunk) => {
        // Send AUDIO_PLAYBACK_STARTED event on first chunk (actual audio streaming begins)
        if (!audioPlaybackStartEventSent) {
          audioPlaybackStartEventSent = true;
          const controller = callControllers[callId];
          if (controller?.machine) {
            // Use consistent turn index: parameter > Turn Authority > state > fallback
            let consistentTurnIndex = turnIndex;
            
            if (consistentTurnIndex === undefined) {
              try {
                const turnAuthorityService = getTurnAuthorityService();
                const turnData = await turnAuthorityService.getCurrentTurnData(callId);
                if (turnData) {
                  consistentTurnIndex = turnData.turnIndex;
                  console.log(`[STREAMING-TTS] Using Turn Authority turn index: ${consistentTurnIndex} for ${callId}`);
                } else {
                  consistentTurnIndex = state.currentTurnIndex || 0;
                  console.log(`[STREAMING-TTS] Using legacy turn index fallback: ${consistentTurnIndex} for ${callId}`);
                }
              } catch (error) {
                console.error(`[STREAMING-TTS] Error getting turn index from Turn Authority, using fallback:`, error);
                consistentTurnIndex = state.currentTurnIndex || 0;
              }
            }
            
            controller.machine.send({
              type: 'AUDIO_PLAYBACK_STARTED',
              callSid: callId,
              turnIndex: consistentTurnIndex,
              timestamp: Date.now()
            });
            console.log(`[STREAMING-TTS] Sent AUDIO_PLAYBACK_STARTED event for sentence ${sentenceIndex} in ${callId}, turn ${consistentTurnIndex}`);
          }
        }
        
        const audioEvent = {
          event: 'media',
          streamSid: state.streamSid,
          media: {
            payload: chunk.toString('base64')
          }
        };
        
        // Send to Twilio
        wss.clients.forEach((client) => {
          if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify(audioEvent));
          }
        });
      });

      return new Promise((resolve) => {
        ffmpegProcess.on('close', (code) => {
          // Remove process from tracking on completion
          if (state.activeSentenceStreams) {
            state.activeSentenceStreams = state.activeSentenceStreams.filter(s => s.process !== ffmpegProcess);
            console.log(`[AZURE-SENTENCE-STREAM] Removed sentence ${sentenceIndex} streaming process on completion, remaining active: ${state.activeSentenceStreams.length}`);
          }
          
          console.log(`[STREAMING-TTS] Sentence ${sentenceIndex} streaming completed (code: ${code})`);
          resolve(code === 0);
        });
        
        ffmpegProcess.on('error', (err) => {
          // Remove process from tracking on error
          if (state.activeSentenceStreams) {
            state.activeSentenceStreams = state.activeSentenceStreams.filter(s => s.process !== ffmpegProcess);
            console.log(`[AZURE-SENTENCE-STREAM] Removed sentence ${sentenceIndex} streaming process on error, remaining active: ${state.activeSentenceStreams.length}`);
          }
          
          console.error(`[STREAMING-TTS] Error streaming sentence ${sentenceIndex}: ${err}`);
          resolve(false);
        });
      });
    } catch (err) {
      console.error(`[STREAMING-TTS] Failed to stream sentence ${sentenceIndex}: ${err}`);
      return false;
    }
  }

  /**
   * Concatenate multiple audio files (extracted from AzureTTSProvider)
   */
  private async concatenateAudioFiles(inputPaths: string[], outputPath: string): Promise<boolean> {
    if (inputPaths.length === 0) return false;
    if (inputPaths.length === 1) {
      // Just copy the single file
      try {
        await fs.promises.copyFile(inputPaths[0], outputPath);
        return true;
      } catch (err) {
        console.error(`Failed to copy single audio file: ${err}`);
        return false;
      }
    }

    return new Promise((resolve) => {
      // Create a temporary file list for FFmpeg concat
      const fileListPath = path.join(path.dirname(outputPath), `concat_list_${Date.now()}.txt`);
      const fileListContent = inputPaths.map(p => `file '${path.basename(p)}'`).join('\n');
      fs.writeFileSync(fileListPath, fileListContent);

      const ffmpegArgs = [
        '-f', 'concat',
        '-safe', '0',
        '-i', fileListPath,
        '-c', 'copy',
        outputPath,
        '-y' // Overwrite output file
      ];

      const ffmpegProcess = spawn('ffmpeg', ffmpegArgs, { cwd: path.dirname(outputPath) });

      ffmpegProcess.on('close', (code) => {
        // Clean up temp file
        try {
          fs.unlinkSync(fileListPath);
        } catch (err) {
          console.warn(`Failed to cleanup temp file ${fileListPath}: ${err}`);
        }

        resolve(code === 0);
      });

      ffmpegProcess.on('error', (err) => {
        console.error(`FFmpeg concatenation error: ${err}`);
        try {
          fs.unlinkSync(fileListPath);
        } catch (cleanupErr) {}
        resolve(false);
      });
    });
  }

  /**
   * Create provider instance based on type - Unified constructor pattern
   */
  private createProvider(providerType: string): TTSProvider {
    switch (providerType.toLowerCase()) {
      case 'azure':
        const { AzureTTSProviderThin } = require('./audio-plugins/providers/AzureTTSProviderThin');
        return new AzureTTSProviderThin();
      case 'deepgram':
        const { DeepgramTTSProviderThin } = require('./audio-plugins/providers/DeepgramTTSProviderThin');
        return new DeepgramTTSProviderThin();
      case 'elevenlabs':
        const { ElevenLabsTTSProviderThin } = require('./audio-plugins/providers/ElevenLabsTTSProviderThin');
        return new ElevenLabsTTSProviderThin();
      // Add other providers here
      default:
        throw new Error(`Unknown TTS provider: ${providerType}`);
    }
  }

  /**
   * Get provider configuration from environment and call context
   */
  private getProviderConfig(callId: string, connectionType: ConnectionType, turnIndex: number): TTSProviderConfig {
    return {
      callId,
      connectionType,
      turnIndex,
      speechKey: process.env.AZURE_SPEECH_KEY || '********************************',
      speechRegion: process.env.AZURE_SPEECH_REGION || 'swedencentral',
      speechEndpoint: process.env.AZURE_SPEECH_ENDPOINT || null,
      speechVoice: 'en-US-AvaMultilingualNeural',
      enableStreamingTts: process.env.ENABLE_STREAMING_TTS === 'true',
      enableWebSocketStreaming: ENABLE_WEBSOCKET_STREAMING,
      enableMemoryTts: ENABLE_MEMORY_TTS,
      audioDir: path.join(__dirname, '../../../audio_responses')
    };
  }

  /**
   * Get active tasks for a call (for cancellation and debugging)
   */
  public getActiveTasks(callId: string): TTSTask[] {
    return Array.from(this.activeTasks.values()).filter(task => task.callId === callId);
  }

  /**
   * Get active tasks for a call from the currently active provider only
   * This prevents cross-provider contamination during cleanup
   */
  public getActiveTasksForCurrentProvider(callId: string): TTSTask[] {
    const activeProviderInfo = this.activeProviders.get(callId);
    if (!activeProviderInfo) {
      console.log(`[TTS-PROVIDER-ISOLATION] No active provider for ${callId} - no tasks to check`);
      return [];
    }

    const providerType = activeProviderInfo.type;
    const allTasks = Array.from(this.activeTasks.values()).filter(task => task.callId === callId);
    
    // Since we have explicit provider type, we can filter tasks more robustly
    // For now, return all tasks for the call since we have proper provider isolation
    // In the future, tasks should include provider type metadata
    const providerTasks = allTasks; // All tasks for this call should be from current provider

    console.log(`[TTS-PROVIDER-ISOLATION] ${callId}: Found ${allTasks.length} tasks for provider '${providerType}' (created at ${new Date(activeProviderInfo.createdAt).toISOString()})`);

    return providerTasks;
  }

  /**
   * Get the provider type for a call (single source of truth)
   */
  public getProviderTypeForCall(callId: string): string {
    const activeProviderInfo = this.activeProviders.get(callId);
    return activeProviderInfo?.type || 'unknown';
  }

  /**
   * Route audio based on connection type (preserving existing logic)
   */
  private async routeAudio(callId: string, audioPath: string, turnIndex: number, connectionType: ConnectionType): Promise<void> {
    console.log(`[TextToSpeechManager] Routing audio for ${callId}, path: ${audioPath}, connection: ${connectionType}`);
    
    // Start audio streaming timing
    const streamingTimingId = conversationTimingService.startPhase(
      ConversationPhase.AUDIO_STREAMING,
      callId,
      turnIndex,
      {
        connectionType,
        audioFilePath: audioPath,
        audioFileSize: require('fs').existsSync(audioPath) ? require('fs').statSync(audioPath).size : 0
      }
    );
    
    // Start audio routing sub-phase
    conversationTimingService.startSubPhase(streamingTimingId, 'audio_routing', {
      connectionType,
      targetPlatform: connectionType === ConnectionType.LIVEKIT ? 'livekit' : 
                     connectionType === ConnectionType.DIRECT ? 'direct_websocket' : 'twilio'
    });
    
    try {
      // Update call state with audio path
      if (calls[callId]) {
        calls[callId].audioResponsePath = audioPath;
      }
      
      // Import routing functions dynamically to avoid circular dependencies
      const { sendTTSToLiveKit, streamAudioToCall } = await import('../server');
      
      // Send AUDIO_PLAYBACK_STARTED event before actual audio streaming begins
      const controller = callControllers[callId];
      if (controller?.machine) {
        controller.machine.send({
          type: 'AUDIO_PLAYBACK_STARTED',
          callSid: callId,
          turnIndex: turnIndex,
          timestamp: Date.now()
        });
        console.log(`[FILE-TTS] Sent AUDIO_PLAYBACK_STARTED event for ${callId} turn ${turnIndex} before ${connectionType} streaming`);
      }
      
      // Route based on connection type
      if (connectionType === ConnectionType.LIVEKIT) {
        console.log(`[TextToSpeechManager] Routing to LiveKit for ${callId}`);
        conversationTimingService.startSubPhase(streamingTimingId, 'livekit_streaming');
        await sendTTSToLiveKit(callId, audioPath, turnIndex);
        conversationTimingService.endSubPhase(streamingTimingId, 'livekit_streaming');
      } else if (connectionType === ConnectionType.DIRECT) {
        console.log(`[TextToSpeechManager] Routing to Direct WebSocket for ${callId}`);
        conversationTimingService.startSubPhase(streamingTimingId, 'direct_websocket_streaming');
        await streamAudioToCall(audioPath, callId, turnIndex);
        conversationTimingService.endSubPhase(streamingTimingId, 'direct_websocket_streaming');
      } else {
        console.log(`[TextToSpeechManager] Routing to Twilio for ${callId}`);
        conversationTimingService.startSubPhase(streamingTimingId, 'twilio_streaming');
        await streamAudioToCall(audioPath, callId, turnIndex);
        conversationTimingService.endSubPhase(streamingTimingId, 'twilio_streaming');
      }
      
      // End audio routing sub-phase
      conversationTimingService.endSubPhase(streamingTimingId, 'audio_routing');
      
      // Note: Audio streaming timing will be completed when actual audio delivery finishes
      // For now, we complete it here since we don't have direct feedback from the streaming endpoints
      conversationTimingService.endPhase(streamingTimingId, {
        success: true,
        connectionType,
        audioFilePath: audioPath
      });
      
      console.log(`[TextToSpeechManager] ✅ Audio routing completed for ${callId} via ${connectionType}`);
      
    } catch (error) {
      // End sub-phases and main phase on error
      conversationTimingService.endSubPhase(streamingTimingId, 'audio_routing');
      conversationTimingService.endPhase(streamingTimingId, {
        success: false,
        error: error.message,
        connectionType
      });
      
      console.error(`[TextToSpeechManager] ❌ Audio routing failed for ${callId}:`, error);
      throw error;
    }
  }

  /**
   * Send TTS_FINISHED event to XState (preserving existing integration)
   */
  private async sendTtsFinishedEvent(callId: string): Promise<void> {
    try {
      const { sendTtsFinishedEvent } = await import('../xstateIntegration');
      sendTtsFinishedEvent(callId);
      console.log(`[TextToSpeechManager] ✅ TTS_FINISHED event sent for ${callId}`);
    } catch (error) {
      console.warn(`[TextToSpeechManager] ⚠️ Could not send TTS_FINISHED event for ${callId}:`, error);
    }
  }

  /**
   * Cleanup TTS provider for a call - Unified task-based cleanup
   */
  public async cleanup(callId: string): Promise<void> {
    console.log(`[TextToSpeechManager] Starting unified TTS cleanup for ${callId}`);

    try {
      const callState = calls[callId];
      
      // Cancel all active tasks for this call
      const activeTasks = this.getActiveTasks(callId);
      if (activeTasks.length > 0) {
        console.log(`[TextToSpeechManager] Cancelling ${activeTasks.length} active tasks for ${callId}`);
        
        const providerInfo = this.activeProviders.get(callId);
        if (providerInfo) {
          for (const task of activeTasks) {
            try {
              await providerInfo.provider.cancelTask(task.id);
              task.status = 'cancelled';
              task.endTime = Date.now();
              this.activeTasks.delete(task.id);
              console.log(`[TextToSpeechManager] Cancelled task ${task.id}`);
            } catch (error) {
              console.error(`[TextToSpeechManager] Error cancelling task ${task.id}:`, error);
            }
          }
        }
      }
      
      // Cancel any active sentence streams (still needed for streaming audio)
      if (callState?.activeSentenceStreams && callState.activeSentenceStreams.length > 0) {
        console.log(`[TextToSpeechManager] Killing ${callState.activeSentenceStreams.length} active sentence streams for ${callId}`);
        for (const stream of callState.activeSentenceStreams) {
          try {
            stream.process.kill();
            console.log(`[TextToSpeechManager] Killed sentence stream for sentence ${stream.sentenceIndex}`);
          } catch (error) {
            console.error(`[TextToSpeechManager] Error killing stream ${stream.sentenceIndex}:`, error);
          }
        }
        callState.activeSentenceStreams = [];
      }

      // Clean up provider instance
      const providerInfo = this.activeProviders.get(callId);
      if (providerInfo) {
        try {
          await providerInfo.provider.cleanup();
          this.activeProviders.delete(callId);
          console.log(`[TextToSpeechManager] ✅ Provider cleanup completed for call: ${callId}`);
        } catch (error) {
          console.error(`[TextToSpeechManager] Error during provider cleanup for call ${callId}:`, error);
        }
      }
      
      console.log(`[TextToSpeechManager] ✅ Unified TTS cleanup completed for ${callId}`);
      
    } catch (error) {
      console.error(`[TextToSpeechManager] ⚠️ TTS cleanup error for ${callId}:`, error);
      // Don't throw on cleanup errors - log and continue
    }
  }

  /**
   * Cancel active TTS for a call (preserving existing functionality)
   */
  public async cancelTTS(callId: string): Promise<void> {
    console.log(`[TextToSpeechManager] Cancelling TTS for call: ${callId}`);
    await this.cleanup(callId);
  }

  /**
   * Check if TTS should be blocked due to cleanup state
   * NOTE: Interruption handling is now done by XState events, not persistent timestamps
   */
  private async checkTtsBlocked(callId: string): Promise<boolean> {
    try {
      // Only check TTS cleanup coordination flag - no more interruption timestamp checking
      const callState = calls[callId];
      if (callState?.ttsCleanupInProgress) {
        console.log(`[TTS-COORDINATION] 🛑 TTS generation blocked - cleanup in progress for ${callId}`);
        return true; // Block TTS operations during cleanup
      }

      return false; // TTS is allowed - interruptions are handled by XState events
      
    } catch (error) {
      console.error(`[TextToSpeechManager] Error checking TTS cleanup state:`, error);
      return false; // Default to allowing TTS on error
    }
  }

  /**
   * Get available providers
   */
  public getAvailableProviders(): string[] {
    return ['azure', 'deepgram', 'elevenlabs']; // Add more as they're implemented
  }

  /**
   * PHASE 1: Resource Ownership Coordination Methods
   * 
   * These methods enable coordinated cleanup between TTS and LiveKit systems
   * to prevent FFI panics from simultaneous resource access
   */

  /**
   * Prepare TTS system for cleanup - Cancel active tasks and signal cleanup intention
   */
  public async prepareForCleanup(callId: string): Promise<void> {
    console.log(`[TTS-COORDINATION] 🚦 Preparing TTS system for cleanup: ${callId}`);
    
    try {
      // 1. Signal cleanup intention to prevent new TTS operations
      const callState = calls[callId];
      if (callState) {
        callState.ttsCleanupInProgress = true;
        console.log(`[TTS-COORDINATION] 🚦 Set ttsCleanupInProgress=true for ${callId}`);
      }

      // 2. Cancel all active tasks immediately (non-blocking)
      const activeTasks = this.getActiveTasks(callId);
      if (activeTasks.length > 0) {
        console.log(`[TTS-COORDINATION] 🚦 Initiating cancellation of ${activeTasks.length} active tasks`);
        
        const providerInfo = this.activeProviders.get(callId);
        if (providerInfo) {
          // Start cancellation for all tasks (don't wait for completion)
          const cancellationPromises = activeTasks.map(async (task) => {
            try {
              await providerInfo.provider.cancelTask(task.id);
              task.status = 'cancelled';
              task.endTime = Date.now();
              this.activeTasks.delete(task.id);
              console.log(`[TTS-COORDINATION] ✅ Cancelled task ${task.id}`);
            } catch (error) {
              console.error(`[TTS-COORDINATION] ⚠️ Error cancelling task ${task.id}:`, error);
              // Mark as cancelled even if error - don't block cleanup
              task.status = 'cancelled';
              task.endTime = Date.now();
              this.activeTasks.delete(task.id);
            }
          });
          
          // Don't wait for all cancellations - proceed with cleanup preparation
          Promise.all(cancellationPromises).catch(error => {
            console.warn(`[TTS-COORDINATION] Some task cancellations failed, but cleanup can proceed:`, error);
          });
        }
      }

      // 3. Cancel active sentence streams immediately
      if (callState?.activeSentenceStreams && callState.activeSentenceStreams.length > 0) {
        console.log(`[TTS-COORDINATION] 🚦 Killing ${callState.activeSentenceStreams.length} active sentence streams`);
        for (const stream of callState.activeSentenceStreams) {
          try {
            stream.process.kill('SIGKILL'); // Force kill for immediate effect
            console.log(`[TTS-COORDINATION] ✅ Killed sentence stream ${stream.sentenceIndex}`);
          } catch (error) {
            console.error(`[TTS-COORDINATION] ⚠️ Error killing stream ${stream.sentenceIndex}:`, error);
          }
        }
        callState.activeSentenceStreams = [];
      }

      console.log(`[TTS-COORDINATION] ✅ TTS cleanup preparation completed for ${callId}`);
      
    } catch (error) {
      console.error(`[TTS-COORDINATION] ❌ Error preparing TTS cleanup for ${callId}:`, error);
      // Don't throw - cleanup must proceed even if preparation fails
    }
  }

  /**
   * Wait for TTS resource release confirmation before LiveKit FFI operations
   */
  public async waitForResourceRelease(callId: string, maxWaitMs: number = 5000): Promise<boolean> {
    console.log(`[TTS-COORDINATION] ⏳ Waiting for TTS resource release: ${callId} (max ${maxWaitMs}ms)`);
    
    const startTime = Date.now();
    const checkInterval = 100; // Check every 100ms
    
    while (Date.now() - startTime < maxWaitMs) {
      try {
        // 1. Check if any active tasks remain for this call FROM CURRENT PROVIDER ONLY
        const activeTasks = this.getActiveTasksForCurrentProvider(callId);
        if (activeTasks.length > 0) {
          console.log(`[TTS-COORDINATION] ⏳ Still waiting - ${activeTasks.length} active tasks remaining (current provider only)`, {
            taskIds: activeTasks.map(task => task.id),
            taskStates: activeTasks.map(task => task.status || 'unknown')
          });
          await new Promise(resolve => setTimeout(resolve, checkInterval));
          continue;
        }

        // 2. Check if LiveKit resources are being used by TTS
        const callState = calls[callId];
        if (callState?.livekitRTCHandler) {
          // Check if TTS is actively using LiveKit resources
          const livekitResourcesInUse = await this.checkLiveKitResourceUsage(callId);
          if (livekitResourcesInUse) {
            console.log(`[TTS-COORDINATION] ⏳ Still waiting - LiveKit resources in use by TTS`, {
              isTtsGenerating: callState?.isTtsGenerating,
              activeTasks: activeTasks.length,
              sentenceStreams: callState?.activeSentenceStreams?.length || 0
            });
            await new Promise(resolve => setTimeout(resolve, checkInterval));
            continue;
          }
        }

        // 3. Check if any sentence streams are still active
        if (callState?.activeSentenceStreams && callState.activeSentenceStreams.length > 0) {
          console.log(`[TTS-COORDINATION] ⏳ Still waiting - ${callState.activeSentenceStreams.length} sentence streams active`, {
            streamProcesses: callState.activeSentenceStreams.map(s => ({ 
              sentenceIndex: s.sentenceIndex, 
              processKilled: s.process.killed || false 
            }))
          });
          await new Promise(resolve => setTimeout(resolve, checkInterval));
          continue;
        }

        // All resources released
        console.log(`[TTS-COORDINATION] ✅ TTS resource release confirmed for ${callId} after ${Date.now() - startTime}ms`);
        return true;
        
      } catch (error) {
        console.error(`[TTS-COORDINATION] ⚠️ Error checking resource release for ${callId}:`, error);
        await new Promise(resolve => setTimeout(resolve, checkInterval));
      }
    }
    
    // Timeout reached
    console.warn(`[TTS-COORDINATION] ⚠️ Timeout waiting for TTS resource release: ${callId} after ${maxWaitMs}ms`);
    return false;
  }

  /**
   * Check if LiveKit resources are currently being used by TTS
   */
  private async checkLiveKitResourceUsage(callId: string): Promise<boolean> {
    try {
      const callState = calls[callId];
      if (!callState?.livekitRTCHandler) {
        return false; // No LiveKit handler means no resource usage
      }

      // Check if TTS is actively streaming
      if (callState.isTtsGenerating) {
        console.log(`[TTS-COORDINATION] 🔍 LiveKit resources in use - TTS generation active`);
        return true;
      }

      // Check if any active tasks are using LiveKit (for streaming TTS) - CURRENT PROVIDER ONLY
      const activeTasks = this.getActiveTasksForCurrentProvider(callId);
      if (activeTasks.length > 0) {
        console.log(`[TTS-COORDINATION] 🔍 LiveKit resources potentially in use - ${activeTasks.length} active tasks (current provider only)`);
        return true;
      }

      // Check if sentence streams are active (they use LiveKit for output)
      if (callState.activeSentenceStreams && callState.activeSentenceStreams.length > 0) {
        console.log(`[TTS-COORDINATION] 🔍 LiveKit resources in use - ${callState.activeSentenceStreams.length} sentence streams`);
        return true;
      }

      return false;
      
    } catch (error) {
      console.error(`[TTS-COORDINATION] Error checking LiveKit resource usage for ${callId}:`, error);
      return false; // Default to no usage on error
    }
  }

  /**
   * Enhanced cleanup method with resource release coordination
   */
  public async cleanupWithCoordination(callId: string): Promise<void> {
    console.log(`[TTS-COORDINATION] 🏁 Starting coordinated TTS cleanup for ${callId}`);
    
    try {
      // 1. Prepare for cleanup (cancel tasks, streams)
      await this.prepareForCleanup(callId);
      
      // 2. Wait for resource release
      const resourcesReleased = await this.waitForResourceRelease(callId);
      if (!resourcesReleased) {
        console.warn(`[TTS-COORDINATION] ⚠️ Proceeding with cleanup despite resource release timeout`);
      }
      
      // 3. Perform standard cleanup
      await this.cleanup(callId);
      
      // 4. Clear coordination flags
      const callState = calls[callId];
      if (callState) {
        callState.ttsCleanupInProgress = false;
        console.log(`[TTS-COORDINATION] ✅ Cleared ttsCleanupInProgress flag for ${callId}`);
      }
      
      console.log(`[TTS-COORDINATION] ✅ Coordinated TTS cleanup completed for ${callId}`);
      
    } catch (error) {
      console.error(`[TTS-COORDINATION] ❌ Error in coordinated TTS cleanup for ${callId}:`, error);
      
      // Clear coordination flags even on error
      const callState = calls[callId];
      if (callState) {
        callState.ttsCleanupInProgress = false;
      }
      
      // Don't throw - cleanup must not fail
    }
  }

  /**
   * Start memory cleanup interval to prevent memory leaks
   */
  private startMemoryCleanupInterval(): void {
    if (this.cleanupIntervalId) {
      clearInterval(this.cleanupIntervalId);
    }
    
    this.cleanupIntervalId = setInterval(() => {
      this.performMemoryCleanup();
    }, this.CLEANUP_INTERVAL_MS);
    
    console.log(`[MEMORY-CLEANUP] Started memory cleanup interval every ${this.CLEANUP_INTERVAL_MS}ms`);
  }

  /**
   * Stop memory cleanup interval
   */
  private stopMemoryCleanupInterval(): void {
    if (this.cleanupIntervalId) {
      clearInterval(this.cleanupIntervalId);
      this.cleanupIntervalId = null;
      console.log(`[MEMORY-CLEANUP] Stopped memory cleanup interval`);
    }
  }

  /**
   * Perform memory cleanup to prevent leaks
   */
  private performMemoryCleanup(): void {
    try {
      const now = Date.now();
      let totalMemoryUsage = 0;
      let cleanedTasks = 0;
      
      // Calculate total memory usage and clean up old tasks
      for (const [taskId, memoryInfo] of this.memoryUsageTracker.entries()) {
        totalMemoryUsage += memoryInfo.bufferSize;
        
        // Clean up tasks older than MAX_TASK_AGE_MS
        if (now - memoryInfo.timestamp > this.MAX_TASK_AGE_MS) {
          this.memoryUsageTracker.delete(taskId);
          cleanedTasks++;
          console.log(`[MEMORY-CLEANUP] Cleaned up abandoned memory task: ${taskId}`);
        }
      }
      
      // Log memory usage status
      if (totalMemoryUsage > 0) {
        const memoryUsageMB = (totalMemoryUsage / (1024 * 1024)).toFixed(2);
        const maxMemoryMB = (this.MAX_MEMORY_USAGE / (1024 * 1024)).toFixed(0);
        console.log(`[MEMORY-CLEANUP] Current memory usage: ${memoryUsageMB}MB / ${maxMemoryMB}MB (${this.memoryUsageTracker.size} active tasks)`);
        
        if (cleanedTasks > 0) {
          console.log(`[MEMORY-CLEANUP] Cleaned up ${cleanedTasks} abandoned memory tracking entries`);
        }
        
        // Warn if approaching memory limit
        if (totalMemoryUsage > this.MAX_MEMORY_USAGE * 0.8) {
          console.warn(`[MEMORY-CLEANUP] High memory usage detected: ${memoryUsageMB}MB (${(totalMemoryUsage / this.MAX_MEMORY_USAGE * 100).toFixed(1)}% of limit)`);
        }
      }
      
    } catch (error) {
      console.error(`[MEMORY-CLEANUP] Error during memory cleanup:`, error);
    }
  }

  /**
   * Check if memory usage is within limits
   */
  private checkMemoryUsage(taskId: string, bufferSize: number): boolean {
    const currentTotal = Array.from(this.memoryUsageTracker.values())
      .reduce((sum, info) => sum + info.bufferSize, 0);
    
    if (currentTotal + bufferSize > this.MAX_MEMORY_USAGE) {
      console.warn(`[MEMORY-LIMIT] Task ${taskId} would exceed memory limit: ${((currentTotal + bufferSize) / (1024 * 1024)).toFixed(2)}MB > ${(this.MAX_MEMORY_USAGE / (1024 * 1024)).toFixed(0)}MB`);
      return false;
    }
    
    return true;
  }

  /**
   * Track memory usage for a task
   */
  private trackMemoryUsage(taskId: string, bufferSize: number): void {
    this.memoryUsageTracker.set(taskId, {
      bufferSize,
      timestamp: Date.now()
    });
  }

  /**
   * Release memory tracking for a task
   */
  private releaseMemoryTracking(taskId: string): void {
    const memoryInfo = this.memoryUsageTracker.get(taskId);
    if (memoryInfo) {
      this.memoryUsageTracker.delete(taskId);
      console.log(`[MEMORY-CLEANUP] Released memory tracking for task ${taskId}: ${(memoryInfo.bufferSize / (1024 * 1024)).toFixed(2)}MB`);
    }
  }

  /**
   * Enhanced cleanup that includes memory cleanup interval
   */
  public async shutdown(): Promise<void> {
    console.log(`[TextToSpeechManager] Starting shutdown process`);
    
    // Stop memory cleanup interval
    this.stopMemoryCleanupInterval();
    
    // Clean up all active tasks
    for (const [callId] of this.activeTasks) {
      await this.cleanup(callId);
    }
    
    // Clear all memory tracking
    this.memoryUsageTracker.clear();
    
    console.log(`[TextToSpeechManager] Shutdown completed`);
  }
}

// Export singleton instance
export const textToSpeechManagerComplete = TextToSpeechManagerComplete.getInstance();