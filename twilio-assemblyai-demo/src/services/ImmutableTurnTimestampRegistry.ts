/**
 * FAANG-Level Immutable Turn Timestamp Registry
 * 
 * Eliminates timestamp contamination by architectural design:
 * - Single write per turn (immutable after creation)
 * - Type-safe turn access (wrong turn = compile error)
 * - Event-sourced timeline (build up, never overwrite)
 * - Atomic turn creation (all timestamps or none)
 * 
 * ARCHITECTURAL GUARANTEES:
 * - Cannot overwrite existing turn timestamps
 * - Cannot access non-existent turns
 * - Cannot corrupt timing data across turns
 * - Cannot have undefined timing states
 */

export interface FrozenTurnTimestamps {
  readonly turnIndex: number;
  readonly userStartedAt: number;      // When user FIRST started speaking (interruption)
  readonly userStoppedAt: number;      // When user finished speaking
  readonly turnFinalizedAt: number;    // When VAD+STT completed processing
  readonly utteranceText: string;      // GRACE PERIOD FIX: Store actual utterance text for reliable cross-turn access
  readonly ttsStartedAt?: number;      // When TTS began for this turn (undefined = not started)
  readonly ttsCompletedAt?: number;    // When TTS finished for this turn
  readonly audioStartedAt?: number;    // When audio playback began
  readonly audioEndedAt?: number;      // When audio playback ended
  readonly interruptionTimestamp?: number; // When this turn was interrupted
  readonly audioGenerationStartAt?: number; // When TTS generation began
  readonly audioGenerationEndAt?: number;   // When TTS generation completed
  readonly audioDurationMs?: number;   // Total audio duration
  readonly createdAt: number;          // Registry creation timestamp
}

export interface TurnTimestampCreationRequest {
  readonly turnIndex: number;
  readonly userStartedAt: number;
  readonly userStoppedAt: number;
  readonly turnFinalizedAt: number;
  readonly utteranceText: string;      // GRACE PERIOD FIX: Required utterance text for cross-turn access
  readonly ttsStartedAt?: number;
  readonly ttsCompletedAt?: number;
  readonly audioStartedAt?: number;
  readonly audioEndedAt?: number;
  readonly interruptionTimestamp?: number;
  readonly audioGenerationStartAt?: number;
  readonly audioGenerationEndAt?: number;
  readonly audioDurationMs?: number;
}

/**
 * Grace period chain state for registry management
 */
export interface RegistryGraceChain {
  readonly isActive: boolean;
  readonly accumulatedText: string;
  readonly turnCount: number;
  readonly chainStartedAt: number;
  readonly mostRecentTurnIndex: number;
  readonly turnIndices: readonly number[];
}

/**
 * Context bridge data for migration compatibility
 */
export interface ContextBridgeData {
  readonly userStoppedAt?: number;
  readonly turnFinalizedAt?: number;
  readonly previousUserStoppedAt?: number;
  readonly previousTurnFinalizedAt?: number;
  readonly interruptionTimestamp?: number;
  readonly audioPlaybackStartTime?: number;
  readonly turnTimings?: readonly any[];
  readonly graceChain?: {
    readonly isActive: boolean;
    readonly accumulatedText: string;
    readonly turnCount: number;
    readonly mostRecentTurnContext?: {
      readonly turnFinalizedAt?: number;
      readonly ttsStreamingStartTime?: number;
      readonly userStoppedAt?: number;
      readonly utteranceText?: string;
    };
  };
}

/**
 * FAANG-Level Immutable Turn Timestamp Registry
 * 
 * Core Architecture:
 * - Each turn gets exactly ONE immutable timestamp record
 * - Registry enforces single-write semantics (cannot overwrite)
 * - Type-safe access prevents wrong-turn contamination
 * - Event-sourced design builds timeline incrementally
 */
export class ImmutableTurnTimestampRegistry {
  private readonly turnRecords = new Map<number, FrozenTurnTimestamps>();
  private readonly callId: string;
  
  constructor(callId: string) {
    this.callId = callId;
  }
  
  /**
   * Create immutable timestamp record for a turn
   * ARCHITECTURAL GUARANTEE: Can only be called once per turn index
   */
  createTurnRecord(request: TurnTimestampCreationRequest): FrozenTurnTimestamps {
    const { turnIndex } = request;
    
    // IMMUTABILITY ENFORCEMENT: Prevent overwriting existing turns
    if (this.turnRecords.has(turnIndex)) {
      throw new Error(`[TIMESTAMP-REGISTRY] Turn ${turnIndex} already exists for call ${this.callId} - immutable violation detected`);
    }
    
    // VALIDATION: Ensure required timestamps are present and logical
    if (!Number.isInteger(turnIndex) || turnIndex < 0) {
      throw new Error(`[TIMESTAMP-REGISTRY] Invalid turnIndex: ${turnIndex} (must be non-negative integer)`);
    }
    
    if (request.userStartedAt <= 0 || request.userStoppedAt <= 0 || request.turnFinalizedAt <= 0) {
      throw new Error(`[TIMESTAMP-REGISTRY] Invalid timestamps: userStartedAt=${request.userStartedAt}, userStoppedAt=${request.userStoppedAt}, turnFinalizedAt=${request.turnFinalizedAt}`);
    }
    
    if (request.userStartedAt > request.userStoppedAt) {
      throw new Error(`[TIMESTAMP-REGISTRY] Logical error: userStartedAt (${request.userStartedAt}) cannot be after userStoppedAt (${request.userStoppedAt})`);
    }
    
    if (request.userStoppedAt > request.turnFinalizedAt) {
      throw new Error(`[TIMESTAMP-REGISTRY] Logical error: userStoppedAt (${request.userStoppedAt}) cannot be after turnFinalizedAt (${request.turnFinalizedAt})`);
    }
    
    // CREATE IMMUTABLE RECORD: All timestamps and utterance text frozen at creation
    const frozenRecord: FrozenTurnTimestamps = Object.freeze({
      turnIndex,
      userStartedAt: request.userStartedAt,
      userStoppedAt: request.userStoppedAt,
      turnFinalizedAt: request.turnFinalizedAt,
      utteranceText: request.utteranceText, // GRACE PERIOD FIX: Store utterance text immutably
      ttsStartedAt: request.ttsStartedAt,
      ttsCompletedAt: request.ttsCompletedAt,
      audioStartedAt: request.audioStartedAt,
      interruptionTimestamp: request.interruptionTimestamp,
      audioGenerationStartAt: request.audioGenerationStartAt,
      audioGenerationEndAt: request.audioGenerationEndAt,
      audioDurationMs: request.audioDurationMs,
      createdAt: Date.now()
    });
    
    // ATOMIC WRITE: Single operation to store immutable record
    this.turnRecords.set(turnIndex, frozenRecord);
    
    console.log(`[TIMESTAMP-REGISTRY] Created immutable record for turn ${turnIndex} (call ${this.callId})`);
    return frozenRecord;
  }
  
  /**
   * Get immutable timestamp record for a turn
   * ARCHITECTURAL GUARANTEE: Type-safe access to frozen data
   */
  getTurnTimestamps(turnIndex: number): FrozenTurnTimestamps {
    const record = this.turnRecords.get(turnIndex);
    if (!record) {
      throw new Error(`[TIMESTAMP-REGISTRY] Turn ${turnIndex} not found in registry for call ${this.callId}. Available turns: [${Array.from(this.turnRecords.keys()).join(', ')}]`);
    }
    return record; // Already frozen, cannot be modified
  }
  
  /**
   * Check if turn exists without throwing
   */
  hasTurn(turnIndex: number): boolean {
    return this.turnRecords.has(turnIndex);
  }
  
  /**
   * Get previous turn timestamps (safe access)
   */
  getPreviousTurn(currentTurnIndex: number): FrozenTurnTimestamps | null {
    const previousTurnIndex = currentTurnIndex - 1;
    if (previousTurnIndex < 0 || !this.turnRecords.has(previousTurnIndex)) {
      return null;
    }
    return this.turnRecords.get(previousTurnIndex)!;
  }

  /**
   * GRACE PERIOD FIX: Get previous turn's utterance text for reliable cross-turn combination
   * ARCHITECTURAL GUARANTEE: Cannot access contaminated data - previous turn is immutably frozen
   */
  getPreviousUtteranceText(currentTurnIndex: number): string | null {
    const previousTurn = this.getPreviousTurn(currentTurnIndex);
    if (!previousTurn) {
      return null;
    }
    
    console.log(`[REGISTRY-GRACE-PERIOD] Retrieved previous turn ${previousTurn.turnIndex} utterance: "${previousTurn.utteranceText}"`);
    return previousTurn.utteranceText;
  }

  /**
   * Get the last (most recent) turn record
   */
  getLastTurnRecord(): FrozenTurnTimestamps | null {
    if (this.turnRecords.size === 0) {
      return null;
    }
    
    const turnIndices = Array.from(this.turnRecords.keys()).sort((a, b) => b - a);
    const lastTurnIndex = turnIndices[0];
    return this.turnRecords.get(lastTurnIndex)!;
  }
  
  /**
   * Calculate clean gap between current interruption and previous turn completion
   * ARCHITECTURAL GUARANTEE: Uses only correct timestamps from correct turns
   */
  calculateGracePeriodGap(currentTurnIndex: number, currentInterruptionTimestamp: number): {
    gap: number;
    previousTurn: FrozenTurnTimestamps | null;
    calculation: string;
  } {
    const previousTurn = this.getPreviousTurn(currentTurnIndex);
    
    if (!previousTurn) {
      return {
        gap: -1,
        previousTurn: null,
        calculation: 'No previous turn exists'
      };
    }
    
    const gap = currentInterruptionTimestamp - previousTurn.turnFinalizedAt;
    const calculation = `currentInterruption(${currentInterruptionTimestamp}) - previousTurnEnd(${previousTurn.turnFinalizedAt}) = ${gap}ms`;
    
    return { gap, previousTurn, calculation };
  }
  
  /**
   * Get all turn indices for debugging
   */
  getAllTurnIndices(): number[] {
    return Array.from(this.turnRecords.keys()).sort((a, b) => a - b);
  }
  
  /**
   * Get the most recent interruption timestamp within the specified time window
   * REGISTRY-FIRST: Direct access to interruption data without context contamination
   */
  getRecentInterruption(maxAgeMs: number): { timestamp: number; turnIndex: number } | null {
    const now = Date.now();
    const allTurns = Array.from(this.turnRecords.entries()).sort((a, b) => b[0] - a[0]); // Sort by turn index desc
    
    for (const [turnIndex, turnRecord] of allTurns) {
      if (turnRecord.interruptionTimestamp) {
        const age = now - turnRecord.interruptionTimestamp;
        if (age <= maxAgeMs) {
          return {
            timestamp: turnRecord.interruptionTimestamp,
            turnIndex: turnIndex
          };
        }
      }
    }
    
    return null;
  }
  
  /**
   * Update TTS started timestamp for an existing turn
   * ARCHITECTURAL GUARANTEE: Can only update undefined ttsStartedAt (single write)
   */
  updateTurnTtsStarted(turnIndex: number, ttsStartedAt: number): FrozenTurnTimestamps {
    const existingRecord = this.turnRecords.get(turnIndex);
    if (!existingRecord) {
      throw new Error(`[TIMESTAMP-REGISTRY] Cannot update TTS for non-existent turn ${turnIndex} in call ${this.callId}`);
    }
    
    // IMMUTABILITY ENFORCEMENT: Can only set TTS timestamp once
    if (existingRecord.ttsStartedAt !== undefined) {
      throw new Error(`[TIMESTAMP-REGISTRY] TTS timestamp already set for turn ${turnIndex} (${existingRecord.ttsStartedAt}) - immutable violation`);
    }
    
    // VALIDATION: Ensure TTS timestamp is logical
    if (ttsStartedAt <= existingRecord.turnFinalizedAt) {
      throw new Error(`[TIMESTAMP-REGISTRY] TTS cannot start (${ttsStartedAt}) before turn finalized (${existingRecord.turnFinalizedAt})`);
    }
    
    // CREATE NEW IMMUTABLE RECORD: Update with TTS timestamp
    const updatedRecord: FrozenTurnTimestamps = Object.freeze({
      ...existingRecord,
      ttsStartedAt,
      audioStartedAt: ttsStartedAt // Set audio start to same time by default
    });
    
    // ATOMIC UPDATE: Replace record with updated version
    this.turnRecords.set(turnIndex, updatedRecord);
    
    console.log(`[TIMESTAMP-REGISTRY] Updated TTS timestamp for turn ${turnIndex} (call ${this.callId}): ${ttsStartedAt}`);
    return updatedRecord;
  }
  
  /**
   * Update TTS completed timestamp for an existing turn  
   * RACE CONDITION FIX: Enables atomic turn cancellation and proper grace period calculation
   */
  updateTurnTtsCompleted(turnIndex: number, ttsCompletedAt: number): FrozenTurnTimestamps {
    const existingRecord = this.turnRecords.get(turnIndex);
    if (!existingRecord) {
      throw new Error(`[TIMESTAMP-REGISTRY] Cannot update TTS completion for non-existent turn ${turnIndex} in call ${this.callId}`);
    }
    
    // VALIDATION: Ensure TTS completion timestamp is logical
    if (existingRecord.ttsStartedAt && ttsCompletedAt < existingRecord.ttsStartedAt) {
      throw new Error(`[TIMESTAMP-REGISTRY] TTS cannot complete (${ttsCompletedAt}) before it started (${existingRecord.ttsStartedAt})`);
    }
    
    // CREATE NEW IMMUTABLE RECORD: Update with TTS completion timestamp
    const updatedRecord: FrozenTurnTimestamps = Object.freeze({
      ...existingRecord,
      ttsCompletedAt,
      audioEndedAt: ttsCompletedAt, // Set audio end time to same as completion by default
      audioDurationMs: existingRecord.ttsStartedAt ? ttsCompletedAt - existingRecord.ttsStartedAt : undefined
    });
    
    // ATOMIC UPDATE: Replace record with updated version
    this.turnRecords.set(turnIndex, updatedRecord);
    
    console.log(`[TIMESTAMP-REGISTRY] Updated TTS completion for turn ${turnIndex} (call ${this.callId}): ${ttsCompletedAt} (duration: ${updatedRecord.audioDurationMs}ms)`);
    return updatedRecord;
  }

  /**
   * Update turn utterance text for grace period chain accumulation
   * ARCHITECTURAL GUARANTEE: Enables proper chain accumulation beyond 2 segments
   */
  updateTurnUtteranceText(turnIndex: number, accumulatedText: string): FrozenTurnTimestamps {
    const existingRecord = this.turnRecords.get(turnIndex);
    if (!existingRecord) {
      throw new Error(`[TIMESTAMP-REGISTRY] Cannot update utterance for non-existent turn ${turnIndex} in call ${this.callId}`);
    }
    
    // CREATE NEW IMMUTABLE RECORD: Update with accumulated utterance text
    const updatedRecord: FrozenTurnTimestamps = Object.freeze({
      ...existingRecord,
      utteranceText: accumulatedText
    });
    
    // ATOMIC UPDATE: Replace record with updated version
    this.turnRecords.set(turnIndex, updatedRecord);
    
    console.log(`[TIMESTAMP-REGISTRY] Updated utterance text for turn ${turnIndex} (call ${this.callId}): "${accumulatedText}"`);
    return updatedRecord;
  }
  
  // ===================================================================
  // GRACE PERIOD CHAIN MANAGEMENT
  // ===================================================================
  
  private graceChain: RegistryGraceChain = {
    isActive: false,
    accumulatedText: '',
    turnCount: 0,
    chainStartedAt: 0,
    mostRecentTurnIndex: -1,
    turnIndices: []
  };
  
  /**
   * Add turn to grace period chain
   * ARCHITECTURAL GUARANTEE: Chain state is immutable and consistent
   */
  addToGraceChain(turnIndex: number, utteranceText: string): RegistryGraceChain {
    const turnRecord = this.getTurnTimestamps(turnIndex);
    
    if (!this.graceChain.isActive) {
      // Start new chain
      this.graceChain = Object.freeze({
        isActive: true,
        accumulatedText: utteranceText,
        turnCount: 1,
        chainStartedAt: turnRecord.turnFinalizedAt,
        mostRecentTurnIndex: turnIndex,
        turnIndices: [turnIndex]
      });
    } else {
      // Add to existing chain
      this.graceChain = Object.freeze({
        ...this.graceChain,
        accumulatedText: this.graceChain.accumulatedText + ' ' + utteranceText,
        turnCount: this.graceChain.turnCount + 1,
        mostRecentTurnIndex: turnIndex,
        turnIndices: [...this.graceChain.turnIndices, turnIndex]
      });
    }
    
    console.log(`[TIMESTAMP-REGISTRY] Added turn ${turnIndex} to grace chain (${this.graceChain.turnCount} turns, ${this.graceChain.accumulatedText.length} chars)`);
    return this.graceChain;
  }
  
  /**
   * Get current grace chain state
   */
  getActiveGraceChain(): RegistryGraceChain {
    return this.graceChain;
  }
  
  /**
   * Finalize and close current grace chain
   */
  finalizeGraceChain(): { finalText: string; turnIndices: readonly number[] } {
    const result = {
      finalText: this.graceChain.accumulatedText,
      turnIndices: this.graceChain.turnIndices
    };
    
    // Reset chain
    this.graceChain = Object.freeze({
      isActive: false,
      accumulatedText: '',
      turnCount: 0,
      chainStartedAt: 0,
      mostRecentTurnIndex: -1,
      turnIndices: []
    });
    
    console.log(`[TIMESTAMP-REGISTRY] Finalized grace chain: "${result.finalText}" (${result.turnIndices.length} turns)`);
    return result;
  }
  
  // ===================================================================
  // ADVANCED TIMING METHODS
  // ===================================================================
  
  /**
   * Update audio streaming timestamps for existing turn
   */
  updateAudioStreaming(turnIndex: number, audioStartedAt?: number, audioEndedAt?: number, audioDurationMs?: number): FrozenTurnTimestamps {
    const existingRecord = this.turnRecords.get(turnIndex);
    if (!existingRecord) {
      throw new Error(`[TIMESTAMP-REGISTRY] Cannot update audio streaming for non-existent turn ${turnIndex} in call ${this.callId}`);
    }
    
    const updatedRecord: FrozenTurnTimestamps = Object.freeze({
      ...existingRecord,
      audioStartedAt: audioStartedAt ?? existingRecord.audioStartedAt,
      audioEndedAt: audioEndedAt ?? existingRecord.audioEndedAt,
      audioDurationMs: audioDurationMs ?? existingRecord.audioDurationMs
    });
    
    this.turnRecords.set(turnIndex, updatedRecord);
    console.log(`[TIMESTAMP-REGISTRY] Updated audio streaming for turn ${turnIndex}`);
    return updatedRecord;
  }
  
  /**
   * Update interruption timestamp for existing turn
   */  
  updateInterruption(turnIndex: number, interruptionTimestamp: number): FrozenTurnTimestamps {
    const existingRecord = this.turnRecords.get(turnIndex);
    if (!existingRecord) {
      throw new Error(`[TIMESTAMP-REGISTRY] Cannot update interruption for non-existent turn ${turnIndex} in call ${this.callId}`);
    }
    
    const updatedRecord: FrozenTurnTimestamps = Object.freeze({
      ...existingRecord,
      interruptionTimestamp
    });
    
    this.turnRecords.set(turnIndex, updatedRecord);
    console.log(`[TIMESTAMP-REGISTRY] Updated interruption timestamp for turn ${turnIndex}: ${interruptionTimestamp}`);
    return updatedRecord;
  }
  
  /**
   * Update audio generation timestamps for existing turn
   */
  updateAudioGeneration(turnIndex: number, audioGenerationStartAt?: number, audioGenerationEndAt?: number): FrozenTurnTimestamps {
    const existingRecord = this.turnRecords.get(turnIndex);
    if (!existingRecord) {
      throw new Error(`[TIMESTAMP-REGISTRY] Cannot update audio generation for non-existent turn ${turnIndex} in call ${this.callId}`);
    }
    
    const updatedRecord: FrozenTurnTimestamps = Object.freeze({
      ...existingRecord,
      audioGenerationStartAt: audioGenerationStartAt ?? existingRecord.audioGenerationStartAt,
      audioGenerationEndAt: audioGenerationEndAt ?? existingRecord.audioGenerationEndAt
    });
    
    this.turnRecords.set(turnIndex, updatedRecord);
    console.log(`[TIMESTAMP-REGISTRY] Updated audio generation for turn ${turnIndex}`);
    return updatedRecord;
  }
  
  // ===================================================================
  // CONTEXT BRIDGE API (MIGRATION COMPATIBILITY)
  // ===================================================================
  
  /**
   * Import timing data from XState context for migration
   */
  syncFromContext(contextData: ContextBridgeData, currentTurnIndex: number): void {
    console.log(`[TIMESTAMP-REGISTRY] Syncing context data for turn ${currentTurnIndex}`);
    
    // Import current turn data if not already exists
    if (!this.turnRecords.has(currentTurnIndex) && contextData.userStoppedAt && contextData.turnFinalizedAt) {
      const request: TurnTimestampCreationRequest = {
        turnIndex: currentTurnIndex,
        userStartedAt: contextData.interruptionTimestamp || contextData.userStoppedAt - 1000,
        userStoppedAt: contextData.userStoppedAt,
        turnFinalizedAt: contextData.turnFinalizedAt,
        utteranceText: contextData.graceChain?.mostRecentTurnContext?.utteranceText || '', // GRACE PERIOD FIX: Import utterance text
        ttsStartedAt: contextData.audioPlaybackStartTime,
        interruptionTimestamp: contextData.interruptionTimestamp
      };
      
      try {
        this.createTurnRecord(request);
        console.log(`[TIMESTAMP-REGISTRY] Created turn ${currentTurnIndex} from context data`);
      } catch (error) {
        console.warn(`[TIMESTAMP-REGISTRY] Failed to create turn from context: ${error.message}`);
      }
    }
    
    // Import grace chain state
    if (contextData.graceChain?.isActive) {
      this.graceChain = Object.freeze({
        isActive: contextData.graceChain.isActive,
        accumulatedText: contextData.graceChain.accumulatedText,
        turnCount: contextData.graceChain.turnCount,
        chainStartedAt: contextData.graceChain.mostRecentTurnContext?.turnFinalizedAt || Date.now(),
        mostRecentTurnIndex: currentTurnIndex,
        turnIndices: [currentTurnIndex] // Simplified for migration
      });
    }
  }
  
  /**
   * Export timing data to XState context format for backward compatibility
   */
  exportToContext(currentTurnIndex: number): ContextBridgeData {
    const currentTurn = this.turnRecords.get(currentTurnIndex);
    const previousTurn = this.getPreviousTurn(currentTurnIndex);
    
    return {
      userStoppedAt: currentTurn?.userStoppedAt,
      turnFinalizedAt: currentTurn?.turnFinalizedAt,
      previousUserStoppedAt: previousTurn?.userStoppedAt,
      previousTurnFinalizedAt: previousTurn?.turnFinalizedAt,
      interruptionTimestamp: currentTurn?.interruptionTimestamp,
      audioPlaybackStartTime: currentTurn?.ttsStartedAt,
      graceChain: {
        isActive: this.graceChain.isActive,
        accumulatedText: this.graceChain.accumulatedText,
        turnCount: this.graceChain.turnCount,
        mostRecentTurnContext: currentTurn ? {
          turnFinalizedAt: currentTurn.turnFinalizedAt,
          ttsStreamingStartTime: currentTurn.ttsStartedAt,
          userStoppedAt: currentTurn.userStoppedAt,
          utteranceText: currentTurn.utteranceText // GRACE PERIOD FIX: Now stored in registry
        } : undefined
      }
    };
  }
  
  /**
   * Validate registry data matches context data
   */
  validateConsistency(contextData: ContextBridgeData, currentTurnIndex: number): {
    consistent: boolean;
    discrepancies: string[];
  } {
    const discrepancies: string[] = [];
    const currentTurn = this.turnRecords.get(currentTurnIndex);
    
    if (currentTurn && contextData.userStoppedAt && currentTurn.userStoppedAt !== contextData.userStoppedAt) {
      discrepancies.push(`userStoppedAt: registry=${currentTurn.userStoppedAt}, context=${contextData.userStoppedAt}`);
    }
    
    if (currentTurn && contextData.turnFinalizedAt && currentTurn.turnFinalizedAt !== contextData.turnFinalizedAt) {
      discrepancies.push(`turnFinalizedAt: registry=${currentTurn.turnFinalizedAt}, context=${contextData.turnFinalizedAt}`);
    }
    
    if (this.graceChain.isActive !== (contextData.graceChain?.isActive || false)) {
      discrepancies.push(`graceChain.isActive: registry=${this.graceChain.isActive}, context=${contextData.graceChain?.isActive}`);
    }
    
    return {
      consistent: discrepancies.length === 0,
      discrepancies
    };
  }
  
  /**
   * Get formatted timing data for logging/debugging
   */
  getTimingForDisplay(turnIndex: number): string {
    const turn = this.turnRecords.get(turnIndex);
    if (!turn) return `Turn ${turnIndex}: NOT FOUND`;
    
    const formatTime = (ts?: number) => ts ? new Date(ts).toISOString().split('T')[1].replace('Z', '') : 'N/A';
    
    return `Turn ${turnIndex}: started=${formatTime(turn.userStartedAt)}, stopped=${formatTime(turn.userStoppedAt)}, finalized=${formatTime(turn.turnFinalizedAt)}, tts=${formatTime(turn.ttsStartedAt)}`;
  }
  
  /**
   * Get registry statistics
   */
  getStats(): {
    turnCount: number;
    oldestTurn: number | null;
    newestTurn: number | null;
    callId: string;
  } {
    const indices = this.getAllTurnIndices();
    return {
      turnCount: indices.length,
      oldestTurn: indices.length > 0 ? indices[0] : null,
      newestTurn: indices.length > 0 ? indices[indices.length - 1] : null,
      callId: this.callId
    };
  }
}

/**
 * Registry manager for per-call timestamp registries
 */
export class TurnTimestampRegistryManager {
  private static registries = new Map<string, ImmutableTurnTimestampRegistry>();
  
  static getOrCreateRegistry(callId: string): ImmutableTurnTimestampRegistry {
    if (!this.registries.has(callId)) {
      this.registries.set(callId, new ImmutableTurnTimestampRegistry(callId));
      console.log(`[TIMESTAMP-REGISTRY-MANAGER] Created new registry for call ${callId}`);
    }
    return this.registries.get(callId)!;
  }
  
  static cleanup(callId: string): void {
    if (this.registries.has(callId)) {
      this.registries.delete(callId);
      console.log(`[TIMESTAMP-REGISTRY-MANAGER] Cleaned up registry for call ${callId}`);
    }
  }
  
  static updateTurnTtsStarted(callId: string, turnIndex: number, ttsStartedAt: number): void {
    const registry = this.registries.get(callId);
    if (!registry) {
      console.warn(`[TIMESTAMP-REGISTRY-MANAGER] No registry found for call ${callId} - cannot update TTS timestamp`);
      return;
    }
    
    try {
      registry.updateTurnTtsStarted(turnIndex, ttsStartedAt);
    } catch (error) {
      console.error(`[TIMESTAMP-REGISTRY-MANAGER] Failed to update TTS timestamp for call ${callId}, turn ${turnIndex}:`, error.message);
    }
  }
  
  static updateTurnTtsCompleted(callId: string, turnIndex: number, ttsCompletedAt: number): void {
    const registry = this.registries.get(callId);
    if (!registry) {
      console.warn(`[TIMESTAMP-REGISTRY-MANAGER] No registry found for call ${callId} - cannot update TTS completion`);
      return;
    }
    
    try {
      registry.updateTurnTtsCompleted(turnIndex, ttsCompletedAt);
    } catch (error) {
      console.error(`[TIMESTAMP-REGISTRY-MANAGER] Failed to update TTS completion for call ${callId}, turn ${turnIndex}:`, error.message);
    }
  }

  static updateTurnUtteranceText(callId: string, turnIndex: number, accumulatedText: string): void {
    const registry = this.registries.get(callId);
    if (!registry) {
      console.warn(`[TIMESTAMP-REGISTRY-MANAGER] No registry found for call ${callId} - cannot update utterance text`);
      return;
    }
    
    try {
      registry.updateTurnUtteranceText(turnIndex, accumulatedText);
    } catch (error) {
      console.error(`[TIMESTAMP-REGISTRY-MANAGER] Failed to update utterance text for call ${callId}, turn ${turnIndex}:`, error.message);
    }
  }
  
  static getAllCallIds(): string[] {
    return Array.from(this.registries.keys());
  }
}