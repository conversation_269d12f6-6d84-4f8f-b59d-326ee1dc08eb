// types.ts - Shared type definitions for the application

import { GracePeriodResult } from './services/GracePeriodArchitecture';

/**
 * Represents the structure of a question object as stored in the database
 */
export interface CaseQuestionFromDB {
  text: string;
  expected_answers: string[];
  follow_up?: string;
}

/**
 * Case data interface that defines the structure of case information
 */
export interface CaseData {
  id: string;
  title?: string;
  specialty?: string;
  category?: string;
  difficulty?: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert';

  initial_question?: string; // From dbRecord.initial_question, typically for the first TTS prompt
  questions: string[];       // User-facing questions, from dbRecord.questions.map(q => q.text)

  patient_agent_data?: any;   // From dbRecord.case_template.patient_agent
  examiner_agent_data?: any;  // From dbRecord.case_template.examiner_agent
  
  case_template?: any;        // The raw case_template from DB, carried over for flexibility

  // To carry over any other top-level fields from the DB record
  [key: string]: any;
}

/**
 * Defines the possible outcomes of the case selection process,
 * providing detailed status and necessary data for the state machine.
 */
export type CaseSelectionResult =
  | { status: 'NEW_CASE_SELECTED'; caseData: CaseData }
  | { status: 'NO_CASES_MATCHING_PREFERENCES' } // No active cases match criteria AT ALL
  | { status: 'ALL_CASES_COMPLETED'; availableCasesToRepeat: CaseData[] } // Active cases match, but ALL have been played
  | { status: 'ERROR'; error?: any }; // Optional error details

/**
 * Response type for case selection operations
 */
export interface FetchAndSelectCaseResponse {
  caseData: CaseData | null;
  selectedCaseId: string | null;
  responseText?: string;
  error?: string;
}

/**
 * Conversation turn between user and assistant
 */
export interface ConversationTurn {
  speaker: 'caller' | 'assistant';
  text: string;
  
  // Interruption metadata for AI responses
  interrupted?: boolean;           // Was this AI response interrupted before completion?
  completionPercent?: number;      // Estimated % of audio that played before interruption (0-100)
  interruptedAt?: number;          // Timestamp when interruption occurred
  audioStartedAt?: number;         // Timestamp when audio playback started
  audioFinishedAt?: number;        // Timestamp when audio completed (undefined if interrupted)
  turnIndex?: number;              // Turn index for tracking and debugging
  
  // XState context information
  xstate_state?: string;           // The XState machine state when this turn was created
  timestamp?: string;              // ISO timestamp when turn was created
}

/**
 * Structure for details stored temporarily when a call starts,
 * before the state machine is fully initialized.
 */
export interface PendingCallDetailsEntry {
  from: string | null;
  to: string | null;
  userId: string | null;
}

/**
 * User preferences for case selection
 */
export interface UserPreferences {
  user_id: string;
  preferred_categories: string[]; // Mapped from DB 'selected_categories'. Stores 'Specialty:SubName'.
  category_mode: 'all' | 'selected'; // Derived: 'selected' if filter_type and preferred_categories are set, else 'all'.
  filter_type?: 'conditions' | 'presentations'; // Mapped from DB 'preferred_category_mode'.
  created_at?: string; // Made optional - Assuming string from DB timestamp, not present in DB
  updated_at: string; // Assuming string from DB timestamp
}

/**
 * Clinical scenario as stored in the database
 */
export interface ClinicalScenarioFromDB {
  id: string;
  title: string;
  specialty: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  scenario: string;
  symptoms: string;
  history: string;
  initial_question: string;
  questions: Array<{
    text: string;
    expected_answers: string[];
    follow_up?: string;
  }>;
  created_at?: string;
  updated_at?: string;
}

// Define types for DTMF input events
export interface DtmfInputEvent {
  type: 'DTMF_INPUT';
  digit: string;
  callSid: string;
}

// Define union type for all possible events
export type OsceEvent = 
  | { type: 'START'; callSid: string }
  | { type: 'TTS_FINISHED'; id?: string; callSid?: string } 
  | { type: 'TRANSCRIPTION'; text: string; callSid?: string } // Raw transcription
  | { type: 'USER_UTTERANCE'; transcription: string; callSid?: string; isFinal: boolean; timestamp?: number } // Processed user input
  | { type: 'LLM_REQUEST'; input: string; currentDialogTurn: number; callSid: string } // Request to LLM
  | { type: 'LLM_RESPONSE_RECEIVED'; response: string; callSid?: string } // Response from LLM
  | { type: 'SPEAK_MESSAGE'; message: string; callSid: string } // Instruction to speak
  | { type: 'INTERNAL_SET_CASE'; data: CaseData; callSid?: string } 
  | { type: 'INTERNAL_UPDATE_RESPONSE'; response: string; callSid: string } // Internal action to update response text
  | { type: 'DTMF_INPUT'; digit: string; callSid: string } // Explicit DTMF type
  | { type: 'INTERNAL_NEXT_STATE'; data: any } // Generic internal transition trigger
  | { type: 'FORCE_RESPONSE'; text: string } // Add missing event type used in osceEvents.ts
  | { type: 'INTERNAL_TRANSITION_ERROR'; data: any } // Error during transition
  | { type: 'SPEECH_DETECTED'; callSid?: string } // VAD event
  | { type: 'CALL_ENDED'; callSid?: string }
  | { type: 'REQUEST_TRANSITION'; targetState: string; reason: string }
  | { type: 'ACCEPT_TRANSITION' }
  | { type: 'REJECT_TRANSITION' }
  | { type: 'TRANSITION_TO_EXAMINATION'; callSid: string }
  | { type: 'TRANSITION_TO_MARKING'; callSid: string }
  | { type: 'TRANSITION_TO_FEEDBACK'; callSid: string }
  | { type: 'TRANSITION_TO_CLOSING'; callSid: string }
  | { type: 'SELECT_CASE'; data: CaseData }
  | { type: 'SET_CONVERSATION'; data: Array<{ speaker: string; text: string }> }
  | { type: 'SPEECH_FRAME' }
  | { type: 'SILENCE_FRAME'; accumulatedText?: string }
  | { type: 'SILENCE_FRAME_SHORT'; accumulatedText?: string; turnFinalizedAt?: number }
  | { type: 'SILENCE_FRAME_LONG'; accumulatedText?: string }
  | { type: 'FORCE_SET_UTTERANCE'; utteranceText: string }
  | { type: 'INTERRUPT'; transcriptText?: string; timestamp?: number }
  | { type: 'USER_INTERRUPTION_DETECTED'; callSid: string; timestamp: number; vadProbability: number; currentTurnIndex?: number }
  | { type: 'EXTERNAL_FINALIZATION_DONE' }
  | { type: 'AUDIO_PLAYBACK_STARTED'; callSid?: string; turnIndex?: number; timestamp?: number }
  | { type: 'CLEAR_STREAMING_FLAG' }
  // 🏗️ PHASE 2: Monitoring Events (SAFE - READ-ONLY)
  | { type: 'OBSERVE_TRANSCRIPT'; text: string; isFinal: boolean; timestamp: number }
  | { type: 'OBSERVE_PROCESS_SPAWN'; pid: number; processType: string; timestamp: number }
  | { type: 'OBSERVE_PROCESS_EXIT'; pid: number; processType: string; timestamp: number }
  | { type: 'OBSERVE_VAD_FRAME'; isVoice: boolean; frameCount: number; timestamp: number }
  | { type: 'OBSERVE_CONNECTION_EVENT'; eventType: 'connect' | 'disconnect' | 'message' | 'error'; timestamp: number }
  | { type: 'OBSERVE_TIMING_EVENT'; phase: string; eventType: 'start' | 'end'; latency?: number; timestamp: number }
  // 🏗️ PHASE 4: Infrastructure Cutover Events
  | { type: 'UPDATE_TRANSCRIPT'; text: string; isFinal: boolean; timestamp: number }
  | { type: 'UPDATE_VAD_STATE'; isVoice: boolean; consecutiveFrames: number; timestamp: number }
  | { type: 'FINALIZE_TURN'; reason: string; transcript: string; timestamp: number }
  | { type: 'RESET_TURN_STATE'; timestamp: number }
  | { type: 'INITIALIZE_INFRASTRUCTURE'; timestamp: number }
  | { type: 'UPDATE_CONNECTION_TYPE'; connectionType: 'twilio' | 'livekit' | 'webrtc' }
  | { type: 'DIRECT_CALL_SESSION_INITIATE'; callId: string; userId: string; audioFormat: string; sampleRate: number; record: boolean }
  // 🧠 ANALYSIS SYSTEM EVENTS
  | { type: 'TRIGGER_ANALYSIS'; trigger: string; priority: 'immediate' | 'high' | 'normal'; sessionId: string; reason?: string }
  | { type: 'ANALYSIS_COMPLETED'; sessionId: string; analysisId: string; insights: any; timestamp: Date }
  | { type: 'ANALYSIS_FAILED'; sessionId: string; analysisId: string; error: string; timestamp: Date }
  | { type: 'AREA_COMPLETION_DETECTED'; areaType: string; areaId: string; sessionId: string; confidence: number }
  | { type: 'CONFUSION_DETECTED'; level: 'mild' | 'moderate' | 'severe'; sessionId: string; userMessage: string }
  | { type: 'MISCONCEPTION_DETECTED'; misconceptions: string[]; sessionId: string; severity: 'minor' | 'moderate' | 'major' }
  | { type: 'ENGAGEMENT_DROP_DETECTED'; level: number; sessionId: string; reason: string }
  | { type: 'REASONING_BREAKDOWN_DETECTED'; pattern: string; sessionId: string; evidence: string[] }
  // 🎯 CENTRALIZATION EVENTS - Move execution thread to XState machine
  | { type: 'LIVEKIT_VAD_DATA'; speech: boolean; probability: number; callSid: string; timestamp?: number }
  | { type: 'LIVEKIT_INIT_STT'; callSid: string }
  // 🚀 PHASE 2: Unified TTS generation event for all connection types
  | { type: 'GENERATE_TTS'; text: string; callSid: string; turnIndex: number; connectionType: string; source?: TTSSource }
  // 🚀 PHASE 2: Internal TTS processing control events
  | { type: 'INTERNAL_START_TTS_PROCESSING' }
  | { type: 'INTERNAL_TTS_PROCESSING_SUCCESS' }
  | { type: 'INTERNAL_TTS_PROCESSING_ERROR' }
  | { type: 'TTS_ERROR'; error?: string }
  // 🚀 PHASE 2: Turn Coordination Events
  | { type: 'INTERNAL_START_TURN_COORDINATION'; originalEvent: any; callSid?: string }
  | { type: 'INTERNAL_TURN_COORDINATION_SUCCESS'; result: any; callSid?: string }
  | { type: 'INTERNAL_TURN_COORDINATION_ERROR'; error: string; callSid?: string }
  | { type: 'INTERNAL_TURN_COORDINATION_RECOVERED'; callSid?: string }
  | { type: 'INTERNAL_USE_LEGACY_COORDINATION'; originalEvent: any; callSid?: string }
  | { type: 'INTERNAL_EXECUTE_COORDINATION'; originalEvent: any; callSid?: string }
  | { type: 'COORDINATED_TURN_COMPLETE'; transcriptText: string; userStoppedAt: number; interruptionTimestamp?: number; gracePeriodApplied: boolean; turnLifecycleContext?: any; coordinationMetadata?: any; callSid?: string }
  | { type: 'SYNC_GRACE_PERIOD_CONTEXT'; lastUserUtterance: string; userStoppedAt: number; gracePeriodApplied: boolean; combinedText: string; callSid?: string }
  // 🚀 PHASE 3: LiveKit Connection State Events
  | { type: 'LIVEKIT_ROOM_CONNECTED'; roomName: string; participantSid: string; participantIdentity: string; callSid?: string }
  | { type: 'LIVEKIT_ROOM_DISCONNECTED'; reason?: string; callSid?: string }
  | { type: 'LIVEKIT_PARTICIPANT_CONNECTED'; participantSid: string; participantIdentity: string; callSid?: string }
  | { type: 'LIVEKIT_PARTICIPANT_DISCONNECTED'; participantSid: string; reason?: string; callSid?: string }
  | { type: 'LIVEKIT_TRACK_PUBLISHED'; trackSid: string; trackType: 'audio' | 'video'; callSid?: string }
  | { type: 'LIVEKIT_TRACK_UNPUBLISHED'; trackSid: string; trackType: 'audio' | 'video'; callSid?: string }
  | { type: 'LIVEKIT_TRACK_SUBSCRIBED'; trackSid: string; participantSid: string; callSid?: string }
  | { type: 'LIVEKIT_TRACK_UNSUBSCRIBED'; trackSid: string; participantSid: string; callSid?: string }
  | { type: 'LIVEKIT_CONNECTION_QUALITY_CHANGED'; quality: 'excellent' | 'good' | 'poor'; participantSid: string; callSid?: string }
  | { type: 'LIVEKIT_RTC_HANDLER_READY'; callSid?: string }
  | { type: 'LIVEKIT_RTC_HANDLER_ERROR'; error: string; callSid?: string }
  | { type: 'LIVEKIT_AUDIO_STREAMING_STARTED'; callSid?: string }
  | { type: 'LIVEKIT_AUDIO_STREAMING_STOPPED'; reason?: string; callSid?: string }
  | { type: 'LIVEKIT_AUDIO_STREAMING_CANCELLED'; callSid?: string }
  | { type: 'UPDATE_TURN_LIFECYCLE_CONTEXT'; contextUpdate: any; stage: TurnLifecycleStage; timestamp: number }
  // 🚀 INACTIVITY & DISCONNECT MANAGEMENT EVENTS
  // Activity tracking events
  | { type: 'ACTIVITY_DETECTED'; source: 'speech' | 'transcription'; callSid?: string; timestamp: number }
  | { type: 'INACTIVITY_WARNING'; warningNumber: 1 | 2; callSid?: string; timestamp: number }
  | { type: 'INACTIVITY_TIMEOUT'; reason: 'no_activity' | 'max_duration'; callSid?: string }
  // Disconnect classification events
  | { type: 'PARTICIPANT_DISCONNECTED'; source: 'websocket' | 'livekit'; callSid?: string; disconnectData: { code?: number; reason?: string; identity?: string; userInitiated?: boolean } }
  | { type: 'PARTICIPANT_RECONNECTED'; gracePeriodUsed: number; source: 'websocket' | 'livekit'; callSid?: string }
  | { type: 'START_GRACE_PERIOD'; duration: number; reason: string; callSid?: string }
  | { type: 'CANCEL_GRACE_PERIOD'; reason: 'reconnected' | 'expired'; callSid?: string }
  // WebSocket-specific connection events for directWebSocketHandler integration
  | { type: 'WEBSOCKET_DISCONNECTED'; callSid: string; code: number; reason: string; userInitiated?: boolean }
  | { type: 'WEBSOCKET_RECONNECTED'; callSid: string; gracePeriodUsed: number }
  | { type: 'GRACE_PERIOD_EXPIRED'; callSid: string }
  // 🚀 CENTRALIZED CLEANUP EVENTS - XState-driven cleanup system
  | { type: 'END_CALL'; callSid: string; source?: string; reason?: string }
  | { type: 'FORCE_CLEANUP'; callSid: string; reason?: string }
  | { type: 'CLEANUP_LIVEKIT'; callSid: string }
  | { type: 'CLEANUP_STT'; callSid: string }
  | { type: 'CLEANUP_WEBSOCKET'; callSid: string }
  | { type: 'CLEANUP_COMPLETE'; callSid: string }
  // 🚀 MISSING EVENTS - Add events that exist in osceMachine.ts but not in types
  | { type: 'INTERNAL_CANCEL_TTS_QUEUE'; callSid?: string; turnIndex?: number; reason?: string; stage?: string }
  | { type: 'RECOGNIZER_CONNECTED'; callSid?: string }
  | { type: 'TRANSITION_TO_DIAGNOSIS'; callSid?: string }
  | { type: 'TRANSITION_TO_NEXT_QUESTION'; callSid?: string }
  | { type: 'UPDATE_TRANSCRIPT_RECEIVED'; callSid?: string }
  | { type: 'UPDATE_AUDIO_STREAMING_START'; callSid?: string }
  | { type: 'UPDATE_AUDIO_STREAMING_END'; callSid?: string }
  | { type: 'UPDATE_INTERRUPTION_TIMESTAMP'; callSid?: string }
  | { type: 'UPDATE_SILENCE_DETECTED'; callSid?: string };

export type OsceEvents = OsceEvent;

export enum TransitionReasons {
  HISTORY = 'history',
  EXAMINATION = 'examination',
  DIAGNOSIS = 'diagnosis',
  MARKING = 'marking',
  FEEDBACK = 'feedback',
  CLOSING = 'closing',
  USER_REQUEST = 'user_request',
  USER_READY = 'USER_READY'
}

/**
 * Turn Lifecycle Stages for turn-based grace period logic
 */
export enum TurnLifecycleStage {
  STARTED = 'started',                    // STT transcription begins
  STT_FINALIZED = 'stt_finalized',       // STT final + VAD silence detected → sent to LLM
  SENT_TO_LLM = 'sent_to_llm',           // LLM processing started
  TTS_STARTED = 'tts_started',           // Audio playback begins for this turn
  COMPLETED = 'completed',               // Turn finished (TTS complete or interrupted)
  INTERRUPTED = 'interrupted'            // Turn was interrupted during processing
}

/**
 * Grace Period Types for turn-based logic
 */
export enum GracePeriodType {
  PRE_TTS = 'pre_tts',                   // Between STT finalized and TTS started
  TTS_ACTIVE = 'tts_active',             // Within first 3 seconds of TTS playback
  TTS_INTERRUPTED = 'tts_interrupted',   // Within 5 seconds of TTS interruption
  NONE = 'none'                          // Outside any grace period window
}

/**
 * TTS Source Categories for preventing infinite inactivity loops
 */
export enum TTSSource {
  NORMAL = 'normal',                     // Standard AI responses
  INACTIVITY_WARNING = 'inactivity_warning' // "Are you still there?" warnings
}

/**
 * State Type Classification for activity timer management
 */
export enum StateType {
  USER_ACTIVE = 'user_active',           // User is actively participating (run timers)
  SYSTEM_PROCESSING = 'system_processing', // System is processing (don't run timers)
  AUDIO_PLAYING = 'audio_playing',       // Audio is playing (don't run timers)
  TRANSITION = 'transition'              // State transition in progress (don't run timers)
}

/**
 * Interface for the state machine context
 * This is used by both osceMachine.ts and xstateIntegration.ts
 */
// Interface for detailed timing information for each turn
export interface TurnTiming {
  turnIndex?: number;
  
  // CORE TIMING: User speech timing (for grace period calculations)
  userStartedAt?: number;              // When user FIRST started speaking (interruption timestamp)
  userStoppedAt?: number;              // When user finished speaking
  
  // Original timing fields (maintained for backward compatibility)
  silenceDetectedAt?: number;
  transcriptReceivedAt?: number;
  ragRequestStartAt?: number;
  ragResponseReceivedAt?: number;
  audioGenerationStartAt?: number;
  audioGenerationEndAt?: number;
  ttsFilename?: string;
  audioStreamingStartAt?: number;
  audioStreamingEndAt?: number;
  audioDurationMs?: number;
  interruptionTimestamp?: number;
  
  // Turn lifecycle timing fields for turn-based grace period logic
  turnStartedAt?: number;              // When STT transcription begins
  sttFinalizedAt?: number;             // When STT is finalized and sent to LLM
  sentToLlmAt?: number;                // When LLM processing starts
  ttsStartedAt?: number;               // When TTS audio playback begins
  turnCompletedAt?: number;            // When turn finishes (TTS complete or interrupted)
  
  // Turn lifecycle state tracking
  lifecycleStage?: TurnLifecycleStage; // Current stage of this turn
  gracePeriodType?: GracePeriodType;   // What type of grace period applies to this turn
  
  // Grace period accumulation tracking
  accumulatedFromTurnIndex?: number;   // If this turn was accumulated from another turn
  isAccumulated?: boolean;             // Whether this turn contains accumulated text
  originalText?: string;               // Original transcript text before accumulation
  accumulatedText?: string;            // Final accumulated text if applicable
}

// ===================================================================
// FAANG-LEVEL XSTATE-CENTRIC TIMING ENTITIES
// ===================================================================

/**
 * Individual utterance timing entity for XState context
 * Tracks timing for individual utterances like "alcohol", "smoking", "drugs"
 */
export interface IndividualUtteranceEntity {
  turnIndex: number;
  text: string;
  userStartedAt: number;        // When user FIRST started speaking this utterance (interruption)
  userStoppedAt: number;        // When user stopped speaking for this utterance
  turnFinalizedAt: number;      // When VAD+STT completed for this utterance
  ttsStartedAt?: number;        // When TTS started for this utterance (undefined = not started)
  isPartOfCombined: boolean;    // Has this been promoted to a combined response?
}

/**
 * Combined response timing entity for XState context
 * Tracks timing for combined responses like "alcohol smoking", "alcohol smoking drugs"
 */

/**
 * Grace period evaluation context built from XState context
 * Used for bulletproof evaluation against the correct timing reference
 */

/**
 * Timing reference for XState context evaluation
 * Points to the current entity that subsequent utterances should evaluate against
 */

export interface OsceContext {
  // Call and user identification
  callSid: string;
  userId: string | null;
  sessionId?: string | null; // Session ID for transcript storage across calls
  sessionType?: 'simulation' | 'tutoring'; // PHASE 1: Session type for unified state machine

  // Case data and selection
  caseData: CaseData | null;
  selectedCaseId: string | null;
  caseLoadingError: string | null;
  availableCasesToRepeat?: CaseData[]; // For storing cases when ALL_CASES_COMPLETED
  // History of conversation
  conversation: ConversationTurn[];

  // Current state tracking
  currentState: string;

  // Phase timing for transcript storage
  phaseStartTimes?: {
    historyTaking?: Date;
    examination?: Date;
    marking?: Date;
    feedback?: Date;
  };

  // History-taking state
  currentQuestionIdx: number;
  // Text waiting to be converted to speech
  responseText?: string;
  // Flag indicating if the current response was generated via streaming
  wasStreamed?: boolean;
  // Timer handles (cleared on exit)
  timerId?: NodeJS.Timeout;

  // Voice Activity Detection related state
  consecutiveSilenceFrames: number;
  speechFramesInCurrentTurn: number;
  hasSpeechDetected: boolean;
  vadWaitLogged?: boolean; // Flag to prevent VAD wait message spam
  lastActivityLogTime?: number; // Timestamp to rate limit activity timer reset logging
  needsFinalization?: boolean; // Flag to signal that the turn needs to be finalized by server.ts

  // Performance timing data
  turnTimings: TurnTiming[];
  currentTurnStartTime?: number;
  currentlyPlayingTurnIndex?: number; // Tracks which turn's TTS is playing
  currentTtsFilename?: string;      // Filename of the TTS audio for the current AI turn
  
  // Transition state - CRITICAL for state machine transitions
  fromState?: string;
  toState?: string;
  transitionReason?: TransitionReasons | string;
  rejectionReason?: string;

  // TTS duration for the previous turn
  ttsLastTurnDuration: number; 
  turnNumber: number; 
  currentTurnIndex?: number; // Current turn index for transcript tracking
  lastDtmfDigit?: string;
  readyToTransition?: boolean; 
  waitingForUserInput?: boolean;
  lastUserUtterance?: string;
  // GRACE PERIOD FIX: Removed previousLastUserUtterance - now using ImmutableTurnTimestampRegistry for reliable cross-turn access
  llmInvokeStartTime?: number;
  ttsStartTime?: number;
  inTransition?: boolean;
  speechDetected?: boolean;
  ttsFinished?: boolean;
  llmResponseReceived?: boolean;
  turnFinalized?: boolean;
  turnFinalizedAt?: number; // Timestamp when turn was finalized for grace period
  userStoppedAt?: number; // Timestamp when user stops talking (start of grace period)
  previousUserStoppedAt?: number; // FANG-FIX: When previous user utterance ended (for grace period calculation)
  previousTurnFinalizedAt?: number; // TURN-FINALIZATION-TIMING: When previous turn was finalized (for accurate grace period calculation)
  interruptionTimestamp?: number; // INTERRUPTION TIMING FIX: When user interrupted active TTS (for grace period evaluation)
  recognizerReady?: boolean; // Added to track recognizer status
  
  // CANONICAL TURN MANAGEMENT - XState as Single Source of Truth
  // Replace multiple competing systems with unified state
  turnBarrier: {
    hasFinal: boolean;
    hasSilence: boolean;
    transcript?: string;
    userStoppedAt?: number;
  };
  
  playback: {
    isPlaying: boolean;
    ttsStartedAt?: number;
    ttsStoppedAt?: number;
  };
  
  interruption: {
    interruptedThisTurn: boolean;
    lastInterruptionAt?: number;
  };
  
  // Single registry instance per call - XState owned
  registry: import('./services/ImmutableTurnTimestampRegistry').ImmutableTurnTimestampRegistry;
  
  // Optional grace period decision (only set when interruption occurred)
  graceDecision?: {
    action: 'cutPreviousAndRespondNow' | 'combineWithCurrent' | 'queueResponse' | 'ignore';
    rationale: string;
    combinedText?: string;
  };

  // DEPRECATED: Will be removed in favor of canonical fields above
  gracePeriodChain?: import('./services/GracePeriodArchitecture').GracePeriodChain;

  // Fields for the transition orchestrator
  orchestratorTask?: string; // e.g., 'ASK_INITIAL_READINESS', 'PROCESS_INITIAL_READINESS_UTTERANCE', etc.
  readinessDecision?: 'PROCEED' | 'WAIT' | 'CLARIFY' | undefined;
  previousStateForOrchestrator?: string; // e.g., 'greeting', 'history_taking'
  intendedNextPhaseTarget?: string; // e.g., TransitionReasons.HISTORY, TransitionReasons.EXAMINATION
  ttsAgenda?: string; // e.g., 'speak', 'listen', 'none_or_custom'
  
  // Temporary storage for accumulated text during transitions
  tempAccumulatedText?: string;
  
  // 🚀 TURN-BASED GRACE PERIOD: Turn Lifecycle Management
  // Turn lifecycle context for turn-based grace period logic
  currentTurnLifecycle?: {
    turnIndex: number;                      // Current turn index
    stage: TurnLifecycleStage;             // Current lifecycle stage
    gracePeriodType: GracePeriodType;      // Active grace period type
    startedAt: number;                     // When turn started
    sttFinalizedAt?: number;               // When STT was finalized
    sentToLlmAt?: number;                  // When sent to LLM
    ttsStartedAt?: number;                 // When TTS started
    completedAt?: number;                  // When turn completed
    interruptedAt?: number;                // When turn was interrupted
  };
  
  // Turn accumulation state for grace period text combining
  turnAccumulation?: {
    isAccumulating: boolean;               // Whether we're currently accumulating
    accumulatedText: string;               // Combined text from multiple utterances
    originalTurnIndex: number;             // Turn index where accumulation started
    lastAccumulationAt: number;            // When last text was accumulated
    accumulationChain: Array<{             // Chain of accumulated utterances
      turnIndex: number;
      text: string;
      timestamp: number;
      gracePeriodType: GracePeriodType;
    }>;
  };
  
  // Reference to the last completed turn for grace period evaluation
  lastCompletedTurn?: {
    turnIndex: number;
    stage: TurnLifecycleStage;
    completedAt: number;
    ttsStartedAt?: number;
    sttFinalizedAt?: number;
  };
  
  // Track additional input during processing to enable transcript combination
  hasAdditionalInput?: boolean;
  
  // Track current LLM operation to prevent stale responses from playing (now string-based for uniqueness)
  currentLlmOperationId?: string;
  
  // Track whether audio playback has started (for pre/post-playback combination logic)
  audioPlaybackStarted?: boolean;
  audioPlaybackStartTime?: number;
  
  // FANG-Level Grace Period Chaining: Single Source of Truth
  // XState-driven grace period management eliminates complex timing calculations
  graceChain?: {
    isActive: boolean;          // Is grace period chaining active?
    accumulatedText: string;    // Building combined text across chain
    turnCount: number;          // Number of turns in current chain
    // ARCHITECTURAL FIX: Track most recent turn context for proper grace period evaluation
    mostRecentTurnContext?: {
      turnFinalizedAt: number;      // When the most recent turn was finalized
      ttsStreamingStartTime?: number; // When TTS started for most recent turn
      userStoppedAt: number;        // When user stopped speaking for most recent turn
      utteranceText: string;        // Text of the most recent utterance in chain
    };
  };
  
  // 🚀 PHASE 1: XState TTS Queue Management (Migration from Bootstrap)
  // This replaces the bootstrap TTS callback system with XState-managed queue
  ttsQueue?: {
    items: Array<{
      id: string;
      text: string;
      timestamp: number;
      turnIndex: number;
      connectionType: string;
      priority: 'normal' | 'high';
      llmOperationId?: string; // Link to originating LLM operation for atomic cancellation
      source?: TTSSource; // Track TTS source for inactivity loop prevention
    }>;
    isProcessing: boolean;
    currentOperation?: {
      id: string;
      text: string;
      startTime: number;
      turnIndex: number;
      connectionType: string;
      llmOperationId?: string; // Link to originating LLM operation for atomic cancellation
      source?: TTSSource; // Track TTS source for inactivity loop prevention
    };
    lastProcessedTime: number;
    totalProcessed: number;
    failureCount: number;
  };

  // Examination state properties
  examinationQuestions?: string[];
  currentQuestionIndex?: number;
  answerStartTime?: number;
  silenceCounter?: number;
  lastActivityTime?: number;
  hasAcknowledgedContinuation?: boolean; // Flag to prevent repeating "I see you're continuing..." message
  userIsCurrentlySpeaking?: boolean; // Flag to prevent auto-prompt timer when user is actively speaking
  
  // History taking timing
  historyTakingStartTime?: number;

  // 🏗️ PHASE 1: Infrastructure State Extension (SAFE - ADDITIVE ONLY)
  // This is optional and initially unused - maintains 100% backward compatibility
  infrastructure?: {
    transcript: {
      accumulation: {
        currentText: string;
        lastProcessedTime: number;
        isAccumulating: boolean;
        duplicateCount: number;
        totalProcessed: number;
      };
    };
    audio: {
      processTracking: {
        activePids: number[];
        lastSpawnTime: number;
        processCount: number;
        failureCount: number;
      };
    };
    vad: {
      metrics: {
        speechFrameCount: number;
        silenceFrameCount: number;
        lastActivityTime: number;
        consecutiveSpeechFrames: number;
        consecutiveSilenceFrames: number;
      };
    };
    connection: {
      status: 'connected' | 'disconnected' | 'connecting';
      type: 'twilio' | 'livekit' | 'webrtc';
      lastActivity: number;
      reconnectAttempts: number;
      messagesSent: number;
      errorsCount: number;
      
      // 🚀 PHASE 3: LiveKit-specific connection state
      livekit?: {
        roomName?: string;
        participantSid?: string;
        participantIdentity?: string;
        connectionQuality: 'excellent' | 'good' | 'poor' | 'unknown';
        isRoomConnected: boolean;
        isParticipantConnected: boolean;
        isAudioPublishing: boolean;
        isAudioSubscribing: boolean;
        lastRoomEvent?: string;
        lastEventTimestamp?: number;
        reconnectAttempts: number;
        rtcHandlerReady: boolean;
        currentTrackSid?: string;
        audioSourceActive: boolean;
        streamingActive: boolean;
        streamingCancelled: boolean;
      };
    };
    timing: {
      phaseStartTime: number;
      lastTransitionTime: number;
      processingLatencies: number[];
      averageLatency: number;
    };
  };

  // 🚀 PHASE 2: Turn Coordination State (ADDITIVE - BACKWARD COMPATIBLE)
  // Centralized coordination state for eliminating race conditions
  coordination?: {
    lastCompletionTime?: number;
    lastCoordinationTime?: number;
    gracePeriodApplied?: boolean;
    coordinationAttempts?: number;
    isCoordinating?: boolean;
  };

  // ============================================================================
  // TUTORING SYSTEM FIELDS
  // These fields are only used when sessionType === 'tutoring'
  // ============================================================================
  
  // Tutoring session management
  tutoringMode?: boolean;
  tutoringSessionId?: string;
  tutoringSession?: any;
  mentalModelReady?: boolean;
  
  // Session timing
  sessionStartTime?: number;
  
  // Current tutoring state
  tutoringSessionPhase?: string;
  tutoringProgress?: number;
  teachingStrategy?: string;
  conceptsIntroduced?: string[];
  assessmentRequired?: boolean;
  shouldEndSession?: boolean;
  assessmentResponse?: string;
  
  // Error handling
  errorMessage?: string;
  
  // Gamification
  experienceGained?: number;
  prestigeLevelUp?: boolean;
  achievementsUnlocked?: any[];
  
  // User interaction
  userUtterance?: string; // Different from lastUserUtterance for tutoring context
  
  // ============================================================================
  // CONVERSATION ANALYSIS SYSTEM FIELDS
  // Integration with the intelligent conversation analysis system
  // ============================================================================
  
  // Analysis state tracking
  analysisQueue?: any[]; // Queue of pending analysis requests
  lastDeepAnalysis?: Date; // When the last deep analysis was performed
  turnsSinceLastAnalysis?: number; // Counter for scheduled analysis triggering
  currentLearningArea?: string; // Current knowledge area being studied
  
  // Analysis insights cache (for immediate access)
  latestAnalysisInsights?: {
    confidenceLevel: number;
    understandingLevel: number;
    engagementLevel: number;
    medicalReasoningQuality: string;
    knowledgeGapsDetected: string[];
    misconceptionsDetected: string[];
    readyForNextArea: boolean;
    recommendedActions: string[];
  };
  
  // Analysis trigger state
  analysisTriggersActive?: {
    confusionDetected: boolean;
    topicTransition: boolean;
    areaCompletion: boolean;
    misconceptionDetected: boolean;
    engagementDrop: boolean;
    reasoningBreakdown: boolean;
  };
  
  // Analysis performance metrics
  analysisMetrics?: {
    totalAnalysesCompleted: number;
    averageAnalysisTime: number;
    analysisSuccessRate: number;
    queueLength: number;
    costIncurred: number;
  };
  
  // 🚀 INACTIVITY & DISCONNECT MANAGEMENT STATE
  // Activity tracking for inactivity timeouts
  activityTracking?: {
    lastActivity: number;           // timestamp of last speech/STT activity
    warningCount: number;          // 0, 1, or 2 warnings sent
    absoluteCallStart: number;     // call start timestamp for 1-hour limit
    timers: {
      warning1?: NodeJS.Timeout;   // 30s warning timer
      warning2?: NodeJS.Timeout;   // 60s final warning timer  
      maxDuration?: NodeJS.Timeout; // 1-hour absolute duration timer
    };
    // XState actor references for new activity timer system
    warningTimerRef?: any;         // Reference to activityWarningTimer actor
    timeoutTimerRef?: any;         // Reference to activityTimeoutTimer actor
  };

  // Grace period state for reconnection handling
  gracePeriodState?: {
    active: boolean;               // whether grace period is currently active
    startTime?: number;            // when grace period started
    timeoutId?: NodeJS.Timeout;    // grace period timeout handle
    source: 'websocket' | 'livekit'; // what triggered the grace period
    disconnectData?: {             // data about the disconnect event
      code?: number;
      reason?: string;
      identity?: string;
      userInitiated?: boolean;
    };
  };

  // Conversation timing tracking
  xstateTimingId?: string;        // Timing ID for XState processing phase
  llmTimingId?: string;           // Timing ID for LLM processing phase
  
  // TTS RACE CONDITION FIX: Atomic turn number for immediate TTS operations
  _atomicTurnNumber?: number;     // Atomic turn number assignment to prevent race conditions
  
  // Enhanced grace period evaluation result stored in context
  lastGracePeriodEvaluation?: GracePeriodResult;
  // ===================================================================
  // FAANG-LEVEL TIMING ENTITY ARCHITECTURE
  // ===================================================================
  timingEntities?: TimingEntityContext;

}

/**
 * Schema for the OsceMachine - Using XState's recommended pattern
 * This allows proper TypeScript integration with the machine
 */
export interface OsceMachineSchema {
  context: OsceContext;
  events: any; // Specific event types defined in osceEvents.ts
}

// Global type definitions for context recovery
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace NodeJS {
    interface Global {
      caseDataCache?: Record<string, any>;
    }
  }
}

// Helper interfaces for state management
export interface StateSnapshot {
  value: any;
  context: OsceContext;
  inTransition?: boolean;
  state?: string;
}

// ===================================================================
// FAANG-LEVEL GRACE PERIOD TIMING ARCHITECTURE
// ===================================================================

export interface IndividualUtteranceEntity {
  turnIndex: number;
  text: string;
  userStartedAt: number; // When user started speaking
  userStoppedAt: number; // When user stopped speaking (VAD silence)
  turnFinalizedAt: number; // When this utterance was finalized (gated STT+VAD)
  ttsStartedAt?: number; // When TTS for THIS utterance started playback
  isPartOfCombined: boolean; // Is this part of a combined response?
}

export interface CombinedResponseEntity {
  turnIndex: number; // The turn index where this combination was created
  combinedText: string;
  utteranceTurnIndices: number[]; // Which turns are included
  turnFinalizedAt: number; // Finalization of the last utterance in chain
  ttsStartedAt?: number; // When TTS for the COMBINED response started
}

export type TimingReference = {
  type: 'individual' | 'combined';
  entityId: number; // Corresponds to turnIndex
  referenceText: string; // For debugging
}

export interface TimingEntityContext {
  individualUtterances: Map<number, IndividualUtteranceEntity>;
  combinedResponses: Map<number, CombinedResponseEntity>;
  activeTimingReference?: TimingReference; // Which entity to evaluate against
}

export interface GraceEvaluationContext {
  referenceTurnFinalizedAt: number;
  referenceTtsStartedAt?: number;
  currentUtteranceUserStartedAt: number;
}
