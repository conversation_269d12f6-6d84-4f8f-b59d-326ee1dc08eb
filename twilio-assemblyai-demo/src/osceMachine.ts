import { setup, assign, raise, sendParent, from<PERSON>rom<PERSON>, from<PERSON>allback, type ErrorActorEvent } from 'xstate';
import { OsceContext, CaseData, OsceEvent, TransitionReasons, ConversationTurn, FetchAndSelectCaseResponse, CaseSelectionResult, TurnTiming, TurnLifecycleStage, GracePeriodType, StateType, TTSSource, IndividualUtteranceEntity, CombinedResponseEntity, TimingReference, GraceEvaluationContext } from './types';
import axios from 'axios';
import { preserveContext, restoreContext } from './contextUtils';
import { selectCaseForUser } from './xstateIntegration';
import { SILENCE_FRAMES_THRESHOLD, MIN_SPEECH_FRAMES, getMinSpeechFrames, TTS_PROVIDER } from './constants';
import { latencyTracker, LatencySteps } from './latencyTracker';
import { generateUniqueOperationId } from './operationIdGenerator';
import { makeCancellableRequest } from './requestCancellation';
// import { checkForDuplicate, recordTTSResponse } from './responseDeduplication';
import { conversationTimingService, ConversationPhase } from './services/ConversationTimingService';
import { 
  routeReadinessDecision, 
  routePatientLLM, 
  routeExaminerLLM, 
  routeMarkLLM, 
  routeExaminationIntentCheck, 
  routeSystemTransitionCheck 
} from './llm-mode-router';
import { 
  turnAuthorityGracePeriodHandler,
  shouldUseTurnAuthority,
  shouldCentralizeGracePeriod,
  getXStateTurnAuthorityIntegration 
} from './services/turn-authority/XStateIntegration';
import { 
  ImmutableTurnTimingContext,
  GracePeriodChain,
  evaluateGracePeriod as bulletproofEvaluateGracePeriod,
  formatTimestamp,
  formatDuration,
  createDebugSummary,
  DEFAULT_GRACE_PERIOD_CONFIG
} from './services/GracePeriodArchitecture';
import { 
  ImmutableTurnTimestampRegistry, 
  TurnTimestampRegistryManager,
  FrozenTurnTimestamps,
  TurnTimestampCreationRequest,
  ContextBridgeData
} from './services/ImmutableTurnTimestampRegistry';
import { getCallLogger, logGracePeriodFlow } from './utils';
// Removed external turnManager - now using same simple turn system as Twilio
// import { getCurrentTurn, startLlmProcessing, startTtsGeneration } from './turnManager';

/**
 * Helper function to extract main state from micro-states
 * Returns the part before the first dot (e.g. "history_taking_main.processing" -> "history_taking_main")
 */
function getMainState(fullState: string): string {
  return fullState.split('.')[0];
}

/**
 * Helper function to check if a turn was interrupted based on turn timings
 */
function isInterrupted(turnIndex: number, turnTimings: TurnTiming[]): boolean {
  const timing = turnTimings.find(t => t.turnIndex === turnIndex);
  return timing ? !!timing.interruptionTimestamp : false;
}

/**
 * Helper function to safely get a valid turn index from context
 * Ensures we never have undefined turn indices causing "Turn unknown" logs
 */
function getSafeValidTurnIndex(context: OsceContext, eventTurnIndex?: number): number {
  // Priority 1: Use event turnIndex if provided and valid
  if (eventTurnIndex !== undefined && eventTurnIndex !== null && Number.isInteger(eventTurnIndex) && eventTurnIndex >= 0) {
    return eventTurnIndex;
  }
  
  // Priority 2: Use context turnNumber if valid
  if (context.turnNumber !== undefined && context.turnNumber !== null && Number.isInteger(context.turnNumber) && context.turnNumber >= 0) {
    return context.turnNumber;
  }
  
  // Priority 3: Derive from turnTimings array length if available
  if (context.turnTimings && Array.isArray(context.turnTimings) && context.turnTimings.length > 0) {
    // Find the highest turn index and add 1, or use length as fallback
    const maxTurnIndex = Math.max(...context.turnTimings.map(t => t.turnIndex || 0));
    return Math.max(maxTurnIndex, context.turnTimings.length - 1);
  }
  
  // Priority 4: Derive from conversation array if available
  if (context.conversation && Array.isArray(context.conversation)) {
    return Math.max(0, Math.floor(context.conversation.length / 2));
  }
  
  // Final fallback: start at 0
  console.warn(`[TURN-INDEX-VALIDATION] No valid turn index found in context for ${context.callSid}, using fallback value 0`);
  return 0;
}

/**
 * CONSOLIDATED TTS CANCELLATION: Shared helper for all interrupt handlers
 * Prevents code duplication and ensures consistent cancellation behavior
 */
function cancelAllPreviousTurnsHelper(context: OsceContext, reason: string, sourceText = ''): void {
  try {
    const { handleCascadingCancellation } = require('./xstateIntegration');
    const currentTurn = context.turnNumber || 0;
    
    console.log(`[TTS-CANCELLATION-${reason}] Cancelling ALL previous TTS requests (turns 0 to ${currentTurn - 1})`);
    
    let cancelledCount = 0;
    for (let turnIndex = 0; turnIndex < currentTurn; turnIndex++) {
      try {
        handleCascadingCancellation(context.callSid, 'responding', sourceText, turnIndex);
        cancelledCount++;
      } catch (turnError) {
        console.error(`[TTS-CANCELLATION-${reason}] Error cancelling turn ${turnIndex}:`, turnError);
      }
    }
    
    console.log(`[TTS-CANCELLATION-${reason}] Successfully cancelled ${cancelledCount} previous TTS requests for ${context.callSid} (current turn: ${currentTurn})`);
  } catch (error) {
    console.error(`[TTS-CANCELLATION-${reason}] Error in shared cancellation helper:`, error);
  }
}

/**
 * Helper function to get or create a TurnTiming object for the current turn
 * Ensures we have exactly one timing object per turn and provides consistent logging
 */
function getOrCreateTurnTiming(context: OsceContext, turnIndex: number, newTurnTimings: TurnTiming[]): { timing: TurnTiming; isNew: boolean } {
  // CRITICAL FIX: Strict validation to prevent undefined turnIndex values
  if (turnIndex === undefined || turnIndex === null || !Number.isInteger(turnIndex) || turnIndex < 0) {
    const safeTurnIndex = getSafeValidTurnIndex(context);
    console.error(`[TURN-INDEX-VALIDATION] Invalid turnIndex (${turnIndex}) passed to getOrCreateTurnTiming for ${context.callSid}. Using safe fallback: ${safeTurnIndex}`);
    console.error(`[TURN-INDEX-VALIDATION] Call stack:`, new Error().stack);
    turnIndex = safeTurnIndex;
  }
  
  let currentTiming = newTurnTimings.find(t => t.turnIndex === turnIndex);
  const isNew = !currentTiming;
  
  if (isNew) {
    console.log(`[TurnTiming] Creating new timing for turn ${turnIndex}`);
    currentTiming = {
      turnIndex,
      // Initialize all fields to undefined
      silenceDetectedAt: undefined,
      transcriptReceivedAt: undefined,
      ragRequestStartAt: undefined,
      ragResponseReceivedAt: undefined,
      audioGenerationStartAt: undefined,
      audioGenerationEndAt: undefined,
      audioStreamingStartAt: undefined,
      audioStreamingEndAt: undefined,
      audioDurationMs: undefined,
      interruptionTimestamp: undefined,
      ttsFilename: undefined
    };
    newTurnTimings.push(currentTiming);
  } else {
    console.log(`[TurnTiming] Found existing timing for turn ${turnIndex}`);
  }
  
  return { timing: currentTiming, isNew };
}

/**
 * Helper function to log the state of all turn timings for debugging
 */
function logTurnTimings(turnTimings: TurnTiming[], currentTurnIndex?: number) {
  console.log(`[TurnTiming] Current turn timings (${turnTimings.length}):`);
  turnTimings.forEach(t => {
    const isCurrent = t.turnIndex === currentTurnIndex;
    console.log(`  Turn ${t.turnIndex}${isCurrent ? ' (current)' : ''}:`);
    console.log(`    Start: ${t.audioStreamingStartAt || 'not set'}`);
    console.log(`    End: ${t.audioStreamingEndAt || 'not set'}`);
    if (t.audioStreamingStartAt && t.audioStreamingEndAt) {
      console.log(`    Duration: ${t.audioStreamingEndAt - t.audioStreamingStartAt}ms`);
    }
    if (t.interruptionTimestamp) {
      console.log(`    Interrupted at: ${t.interruptionTimestamp}`);
    }
  });
}

/**
 * Helper function to update turn timing fields in a type-safe way
 */
function updateTurnTiming(
  turnTimings: TurnTiming[],
  turnIndex: number,
  updates: Partial<Omit<TurnTiming, 'turnIndex'>>
): TurnTiming[] {
  const newTurnTimings = [...(turnTimings || [])];
  let timing = newTurnTimings.find(t => t.turnIndex === turnIndex);
  
  if (!timing) {
    console.log(`[TurnTiming] Creating new timing for turn ${turnIndex}`);
    timing = { turnIndex };
    newTurnTimings.push(timing);
  }
  
  // Update all provided fields
  Object.assign(timing, updates);
  
  // Log the update for debugging
  const updatedFields = Object.keys(updates).join(', ');
  console.log(`[TurnTiming] Updated turn ${turnIndex}: ${updatedFields}`);
  
  return newTurnTimings;
}



import {
  getUserPreferences,
  getClinicalScenarios,
  getUserSessionHistory,
  recordCompletedSession
} from './database';
import { infrastructureActors } from './infrastructureActors';
import { transcriptActions } from './transcriptActions';

// Constants for Voice Activity Detection (VAD) - now imported from shared constants
// const MIN_SPEECH_FRAMES = 5; // Removed - now imported
// const SILENCE_FRAMES_THRESHOLD = 35; // Removed - now imported

// Cache for cross-call context data
const contextBackup = {
  lastCallSid: undefined,
  contexts: {} as Record<string, any>
};

// Call context backups to prevent the model losing context
export function backupContext(callSid: string, context: OsceContext) {
  console.log(`[XState] Backing up context for callSid ${callSid}`);
  contextBackup.lastCallSid = callSid;
  contextBackup.contexts[callSid] = context;
}

export function getBackupContext(callSid: string): OsceContext | undefined {
  return contextBackup.contexts[callSid];
}

// Initialize context for new calls (using the comprehensive implementation)
function initializeContextForCall(callSid: string): OsceContext {
  console.log(`[XState] Initializing context for callSid ${callSid}`);
  return createInitialContext({ callSid });
}

/** Creates a complete initial context with all required OsceContext fields */
export function createInitialContext(
  input: { callSid: string } & Partial<OsceContext>
): OsceContext {
  const { callSid, ...initialOverrides } = input;
  return {
    // Call and user identification
    callSid: callSid,
    userId: null,

    // Case data and selection
    caseData: null,
    selectedCaseId: null,
    caseLoadingError: null,
    availableCasesToRepeat: undefined,
    // History of conversation
    conversation: [],
    // Current state tracking
    currentState: 'idle',
    // History-taking state
    currentQuestionIdx: 0,
    // Voice Activity Detection related state
    consecutiveSilenceFrames: 0,
    speechFramesInCurrentTurn: 0,
    hasSpeechDetected: false,
    turnFinalized: false, // Initialize turnFinalized
    turnFinalizedAt: undefined, // Initialize turnFinalizedAt for grace period logic
    userStoppedAt: undefined, // Timestamp when user stops talking (start of grace period)
    previousUserStoppedAt: undefined, // FANG-FIX: When previous user utterance ended (for grace period calculation)
    // FANG-Level Grace Period Chaining: Single Source of Truth
    graceChain: {
      isActive: false,          // Is grace period chaining active?
      accumulatedText: '',      // Building combined text across chain
      turnCount: 0,             // Number of turns in current chain
      mostRecentTurnContext: undefined // ARCHITECTURAL FIX: Track most recent turn context
    },
    // Performance timing data
    turnTimings: [],
    // TTS duration for the previous turn
    ttsLastTurnDuration: 0,
    // Sequential turn number for the current state
    turnNumber: 0,
    // Text waiting to be converted to speech
    responseText: 'Welcome! Are you ready to begin the case?',
    // Last DTMF digit received, if any
    lastDtmfDigit: undefined,
    // Transition reason
    transitionReason: undefined,
    // Flag to indicate if waiting for user input
    waitingForUserInput: true,
    // Last user utterance
    lastUserUtterance: undefined,
    // GRACE PERIOD FIX: Removed previousLastUserUtterance - now using ImmutableTurnTimestampRegistry for cross-turn access
    // Additional runtime flags not in the interface but used
    readyToTransition: false,
    ttsFinished: false,
    llmResponseReceived: false,
    needsFinalization: false, // Initialize the new flag
    hasAcknowledgedContinuation: false, // Initialize acknowledgment flag for examination
    userIsCurrentlySpeaking: false, // Initialize speaking flag
    
    // FAANG-LEVEL TIMING ENTITY ARCHITECTURE
    timingEntities: {
      individualUtterances: new Map(),
      combinedResponses: new Map(),
      activeTimingReference: undefined
    },

    
    // 🚀 PHASE 1: Initialize XState TTS Queue Management (Migration from Bootstrap)
    ttsQueue: {
      items: [],
      isProcessing: false,
      currentOperation: undefined,
      lastProcessedTime: 0,
      totalProcessed: 0,
      failureCount: 0
    },
    
    // 🏗️ PHASE 1: Initialize Infrastructure State (SAFE - UNUSED BY EXISTING LOGIC)
    infrastructure: {
      transcript: {
        accumulation: {
          currentText: '',
          lastProcessedTime: 0,
          isAccumulating: false,
          duplicateCount: 0,
          totalProcessed: 0
        }
      },
      audio: {
        processTracking: {
          activePids: [],
          lastSpawnTime: 0,
          processCount: 0,
          failureCount: 0
        }
      },
      vad: {
        metrics: {
          speechFrameCount: 0,
          silenceFrameCount: 0,
          lastActivityTime: Date.now(),
          consecutiveSpeechFrames: 0,
          consecutiveSilenceFrames: 0
        }
      },
      connection: {
        status: 'disconnected' as const,
        type: 'twilio' as const,
        lastActivity: Date.now(),
        reconnectAttempts: 0,
        messagesSent: 0,
        errorsCount: 0
      },
      timing: {
        phaseStartTime: Date.now(),
        lastTransitionTime: Date.now(),
        processingLatencies: [],
        averageLatency: 0
      }
    },
    
    // CANONICAL TURN MANAGEMENT - XState as Single Source of Truth
    // Initialize unified turn state to replace competing systems
    turnBarrier: {
      hasFinal: false,
      hasSilence: false,
      transcript: undefined,
      userStoppedAt: undefined
    },
    
    playback: {
      isPlaying: false,
      ttsStartedAt: undefined,
      ttsStoppedAt: undefined
    },
    
    interruption: {
      interruptedThisTurn: false,
      lastInterruptionAt: undefined
    },
    
    // Single registry instance - XState owned, no more global registries
    registry: new ImmutableTurnTimestampRegistry(callSid),
    
    // Grace decision only set when interruption actually occurs
    graceDecision: undefined,
    
    // DEPRECATED: Will be removed after migration
    gracePeriodChain: new GracePeriodChain(DEFAULT_GRACE_PERIOD_CONFIG),
    
    // Merge any overrides
    ...initialOverrides
  };
}

/**
 * UNIFIED TWO-PHASE GRACE PERIOD EVALUATION - GRACE PERIOD TIMING FUNDAMENTAL FIX
 * 
 * Implements the correct grace period logic across all states:
 * Phase 1: From USER turn finalization until TTS starts playing (always combine)
 * Phase 2: Within 3 seconds after TTS starts playing (combine if within window)
 * 
 * FIXED: Now correctly uses turnFinalizedAt (when USER turn finishes via gated STT_FINALIZED + VAD)
 * as the authoritative anchor point, eliminating all conflicting timing implementations.
 */
// GRACE PERIOD TIMING FUNDAMENTAL FIX: This function now works correctly with fixed turnFinalizedAt timing
// turnFinalizedAt is now correctly set when USER turn finalizes (gated STT_FINALIZED + VAD), not when AI completes
// Implements the correct two-phase grace period logic as requested by user
function evaluateUnifiedGracePeriod(context: OsceContext, now: number): { 
  withinGracePeriod: boolean; 
  gracePeriodType: string; 
  phase: 'pre-tts' | 'post-tts' | 'expired';
  debugInfo: any;
} {
  const debugInfo = {
    now,
    turnFinalizedAt: context.turnFinalizedAt, // Now correctly represents when USER turn finalized
    audioPlaybackStartTime: context.audioPlaybackStartTime,
    timeSinceFinalization: context.turnFinalizedAt ? now - context.turnFinalizedAt : null,
    timeSinceAudioStart: context.audioPlaybackStartTime ? now - context.audioPlaybackStartTime : null
  };

  // Guard: No grace period evaluation without turn finalization
  if (!context.turnFinalizedAt) {
    return {
      withinGracePeriod: false,
      gracePeriodType: 'no-anchor-timestamp',
      phase: 'expired',
      debugInfo
    };
  }

  // PHASE 1: Pre-TTS Grace Period (from turn finalization until TTS starts)
  if (!context.audioPlaybackStartTime || now < context.audioPlaybackStartTime) {
    return {
      withinGracePeriod: true,
      gracePeriodType: 'pre-tts-phase',
      phase: 'pre-tts',
      debugInfo
    };
  }

  // PHASE 2: Post-TTS Grace Period (within 3 seconds after TTS starts)
  const timeSinceAudioStart = now - context.audioPlaybackStartTime;
  const POST_TTS_GRACE_WINDOW_MS = 3000; // Unified 3-second window
  
  if (timeSinceAudioStart <= POST_TTS_GRACE_WINDOW_MS) {
    return {
      withinGracePeriod: true,
      gracePeriodType: 'post-tts-phase',
      phase: 'post-tts',
      debugInfo
    };
  }

  // EXPIRED: Outside both grace period phases
  return {
    withinGracePeriod: false,
    gracePeriodType: 'expired',
    phase: 'expired',
    debugInfo
  };
}

// ===================================================================
// FAANG-LEVEL XSTATE-CENTRIC TIMING CONTEXT HELPERS
// ===================================================================

/**
 * Get current timing entity from XState context
 * Returns the entity that subsequent utterances should evaluate against
 */
function getCurrentTimingEntity(context: OsceContext): IndividualUtteranceEntity | CombinedResponseEntity | null {
  if (!context.timingEntities?.activeTimingReference) {
    return null;
  }
  
  const ref = context.timingEntities.activeTimingReference;
  
  if (ref.type === 'combined') {
    return context.timingEntities.combinedResponses.get(ref.entityId) || null;
  } else {
    return context.timingEntities.individualUtterances.get(ref.entityId) || null;
  }
}

/**
 * Build evaluation context from XState context for bulletproof grace period evaluation
 * This is the core function that selects the appropriate timing reference
 */
function buildEvaluationContext(
  context: OsceContext, 
  currentUtterance: { text: string; userStartedAt: number; userStoppedAt: number }
): GraceEvaluationContext | null {
  
  if (!context.timingEntities?.activeTimingReference) {
    console.log(`[XSTATE-GRACE] No active timing reference available`);
    return null;
  }
  
  const timingRef = context.timingEntities.activeTimingReference;
  
  if (timingRef.type === 'combined') {
    const combinedEntity = context.timingEntities.combinedResponses.get(timingRef.entityId);
    if (!combinedEntity) {
      console.error(`[XSTATE-GRACE] Combined entity ${timingRef.entityId} not found in context`);
      return null;
    }
    
    // CRITICAL FIX: Fallback to registry if timing entity missing TTS data
    let referenceTtsStartedAt = combinedEntity.ttsStartedAt;
    if (!referenceTtsStartedAt && context.registry) {
      try {
        const lastTurnRecord = context.registry.getLastTurnRecord();
        if (lastTurnRecord?.ttsStartedAt) {
          referenceTtsStartedAt = lastTurnRecord.ttsStartedAt;
          console.log(`[XSTATE-GRACE] Using registry fallback for TTS timestamp: ${referenceTtsStartedAt}`);
        }
      } catch (error) {
        console.warn(`[XSTATE-GRACE] Registry fallback failed:`, error.message);
      }
    }
    
    return {
      referenceTurnFinalizedAt: combinedEntity.turnFinalizedAt,
      referenceTtsStartedAt: referenceTtsStartedAt,
      currentUtteranceUserStartedAt: currentUtterance.userStartedAt
    };
  } else {
    const individualEntity = context.timingEntities.individualUtterances.get(timingRef.entityId);
    if (!individualEntity) {
      console.error(`[XSTATE-GRACE] Individual entity ${timingRef.entityId} not found in context`);
      return null;
    }
    
    // CRITICAL FIX: Fallback to registry if timing entity missing TTS data
    let referenceTtsStartedAt = individualEntity.ttsStartedAt;
    if (!referenceTtsStartedAt && context.registry) {
      try {
        const lastTurnRecord = context.registry.getLastTurnRecord();
        if (lastTurnRecord?.ttsStartedAt) {
          referenceTtsStartedAt = lastTurnRecord.ttsStartedAt;
          console.log(`[XSTATE-GRACE] Using registry fallback for TTS timestamp: ${referenceTtsStartedAt}`);
        }
      } catch (error) {
        console.warn(`[XSTATE-GRACE] Registry fallback failed:`, error.message);
      }
    }
    
    return {
      referenceTurnFinalizedAt: individualEntity.turnFinalizedAt,
      referenceTtsStartedAt: referenceTtsStartedAt,
      currentUtteranceUserStartedAt: currentUtterance.userStartedAt
    };
  }
}

/**
 * Initialize timing entities structure in XState context
 * Called when context needs timing entities for the first time
 */
function initializeTimingEntities(context: OsceContext): OsceContext['timingEntities'] {
  const turnIndex = getSafeValidTurnIndex(context);
  return {
    individualUtterances: new Map<number, IndividualUtteranceEntity>(),
    combinedResponses: new Map<number, CombinedResponseEntity>(),
    activeTimingReference: {
      type: 'individual',
      entityId: turnIndex,
      referenceText: 'none'
    }
  };
}

/**
 * Creates an OSCE (Objective Structured Clinical Examination) state machine
 * using XState's setup pattern for better type safety
 */
export function createOsceMachine(sessionType: 'simulation' | 'tutoring' = 'simulation') {
  // V5: Define setup for actors, actions, guards, delays
  const machine = setup({
    types: {
      context: {} as OsceContext,
      events: {} as OsceEvent,
      input: {} as OsceContext // Machine expects a full OsceContext as input
    }, // Uses the schema defined in types.ts
    actors: {
      // Actor for fetching and selecting a case for a user
      fetchCaseForUserActor: fromPromise<
        CaseSelectionResult,
        { userId: string | null; sessionType?: 'simulation' | 'tutoring' }
      >(async ({ input }) => {
        const { userId, sessionType = 'simulation' } = input;
        
        if (!userId) {
          console.error('[XState] fetchCaseForUserActor: No userId provided for case selection');
          return { status: 'ERROR', error: 'No userId provided for case selection' };
        }

        try {
          console.log(`[XState] fetchCaseForUserActor: Fetching case for user ${userId}`);
          // Call the new selectCaseForUser function that handles all the database interactions
          const result = await selectCaseForUser(userId, sessionType);
          // Log only essential case information instead of the entire result object
          if (result.status === 'NEW_CASE_SELECTED') {
            console.log('[XState] fetchCaseForUserActor: Case selection result:', {
              status: result.status,
              caseId: result.caseData.id,
              title: result.caseData.title,
              specialty: result.caseData.specialty,
              condition: result.caseData.condition,
              presentation: result.caseData.presentation
            });
          } else {
            console.log('[XState] fetchCaseForUserActor: Case selection result:', result);
          }
          return result;
        } catch (error) {
          console.error('[XState] fetchCaseForUserActor: Error selecting case:', error);
          return { 
            status: 'ERROR', 
            error: error instanceof Error ? error.message : 'Failed to select case'
          };
        }
      }),
      // Placeholder for the LLM actor that processes user utterances during history taking
      historyTakingLlmActor: fromPromise<
        { responseText: string; wasStreamed: boolean; operationId?: string }, 
        { userUtterance: string | undefined; caseData: CaseData | null; conversationHistory: ConversationTurn[]; callSid: string; turnNumber?: number; operationId?: string }
      >(
        async ({ input: actorInput }) => {
          const { userUtterance, caseData, conversationHistory, callSid, operationId } = actorInput;

          // Construct the request payload for the LLM server
          const llmRequestPayload = {
            callSid: callSid,
            userMessage: userUtterance,
            conversationHistory: conversationHistory,
            caseData: caseData,
          };

          console.log('Sending request to /rag/patient', { callSid, payload: { ...llmRequestPayload, caseData: '...hidden...' } });

          // Use centralized turn manager for consistent turn numbering
          const authoritativeTurnNumber = 0; // Using simple turn system like Twilio
          // startLlmProcessing(callSid); // Using simple turn system like Twilio // Update phase to LLM processing
          
          latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_REQUEST_STARTED, {
            endpoint: '/rag/patient',
            messageLength: userUtterance?.length || 0,
            conversationTurns: conversationHistory.length
          });

          try {
            // Check if we should use streaming mode
            if (TTS_PROVIDER === 'elevenlabs_stream') {
              console.log(`[STREAMING-MODE] Using streaming response for patient endpoint, callSid: ${callSid}`);
              
              // Import the streaming function from server
              const { generateStreamingResponse } = await import('./server');
              
              latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_REQUEST_SENT, {
                endpoint: '/rag/patient/stream',
                streaming: true
              });

              // Call the streaming response function which handles:
              // 1. Streaming LLM request to Python server
              // 2. Real-time streaming to ElevenLabs WebSocket  
              // 3. Direct audio streaming to Twilio WebSocket
              const result = await generateStreamingResponse(
                userUtterance || '',
                callSid,
                authoritativeTurnNumber,
                conversationHistory,
                caseData
              );

              if (result !== null) {
                // Streaming completed successfully - audio was sent directly to Twilio
                const audioPath = typeof result === 'string' ? result : result.audioPath;
                const responseText = typeof result === 'string' ? null : result.responseText;
                
                console.log(`[STREAMING-MODE] Streaming completed successfully for ${callSid}, audio file: ${audioPath}`);
                console.log(`[STREAMING-MODE] Captured response text: "${responseText}"`);
                
                // Record completion steps
                latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_RESPONSE_RECEIVED, {
                  endpoint: '/rag/patient/stream',
                  streaming: true,
                  responseLength: responseText?.length || 0
                });
                
                latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_RESPONSE_PROCESSED, {
                  endpoint: '/rag/patient/stream',
                  streaming: true
                });
                
                // Update phase to TTS generation (even though it's already done)
                // startTtsGeneration(callSid); // Using simple turn system like Twilio
                latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.TTS_GENERATION_STARTED, {
                  textLength: responseText?.length || 0,
                  text: responseText || 'streaming_completed_no_tts_needed',
                  source: 'streaming_completion',
                  streaming: true
                });

                // Return the actual response text for conversation history
                return { responseText, wasStreamed: true, operationId };
              } else {
                // Fallback: streaming failed, use the fallback text
                console.error(`[STREAMING-MODE] Streaming failed for ${callSid}, using fallback response`);
                const assistantReply = 'I encountered an issue. Could you please repeat that?';
                
                latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_RESPONSE_RECEIVED, {
                  endpoint: '/rag/patient/stream',
                  streaming: false,
                  fallback: true,
                  responseLength: assistantReply.length
                });
                
                latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_RESPONSE_PROCESSED, {
                  endpoint: '/rag/patient/stream',
                  fallback: true
                });
                
                // startTtsGeneration(callSid); // Using simple turn system like Twilio
                latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.TTS_GENERATION_STARTED, {
                  textLength: assistantReply.length,
                  text: assistantReply.substring(0, 100) + '...',
                  source: 'streaming_fallback'
                });

                return { responseText: assistantReply, wasStreamed: false, operationId };
              }
            } else {
              // Regular non-streaming mode
              console.log(`[REGULAR-MODE] Using regular response for patient endpoint, callSid: ${callSid}`);
              
              // Record when request is actually sent
              latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_REQUEST_SENT, {
                endpoint: '/rag/patient'
              });

              // Use the LLM router for patient requests
              const response = await routePatientLLM(
                callSid,
                actorInput.userUtterance,
                conversationHistory,
                caseData,
                operationId?.toString()
              );
              const assistantReply = response.assistantReply;

              // Record when response is received
              latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_RESPONSE_RECEIVED, {
                endpoint: '/rag/patient',
                responseLength: assistantReply?.length || 0
              });

              if (!assistantReply) {
                console.error('LLM server response missing assistantReply', { callSid, responseData: response });
                throw new Error('Invalid response from LLM server');
              }

              console.log('Received response from /rag/patient', { callSid, replyStart: assistantReply.substring(0, 50) + '...' });

              // Record when response is processed
              latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_RESPONSE_PROCESSED, {
                endpoint: '/rag/patient'
              });

              // Update phase to TTS generation and record step
              // startTtsGeneration(callSid); // Using simple turn system like Twilio
              latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.TTS_GENERATION_STARTED, {
                textLength: assistantReply.length,
                text: assistantReply.substring(0, 100) + '...',
                source: 'LLM_actor_completion'
              });

              // Return the LLM's response
              return { responseText: assistantReply, wasStreamed: false, operationId };
            } // Close the else block
          } catch (error: any) {
            console.error('Error calling /rag/patient LLM endpoint', {
              callSid,
              errorMessage: error.message,
              errorDetails: error.response?.data, // Log response data if available
              requestPayload: { ...llmRequestPayload, caseData: '...hidden...' }, // Log request payload (hide sensitive data)
            });
            // Fallback response
            return { responseText: 'I encountered an issue processing that. Could you please repeat it?', wasStreamed: false, operationId };
          }
        }
      ),
      
      // Examination LLM actor for examination state
      examinationLlmActor: fromPromise<
        { responseText: string; wasStreamed: boolean }, 
        { userUtterance: string | undefined; caseData: CaseData | null; conversationHistory: ConversationTurn[]; callSid: string; turnNumber?: number; operationId?: string }
      >(
        async ({ input: actorInput }) => {
          const { userUtterance, caseData, conversationHistory, callSid, operationId } = actorInput;

          // Construct the request payload for the LLM server
          const llmRequestPayload = {
            callSid: callSid,
            userMessage: userUtterance,
            conversationHistory: conversationHistory,
            caseData: caseData,
          };

          console.log('Sending request to /rag/examiner', { callSid, payload: { ...llmRequestPayload, caseData: '...hidden...' } });

          // Use centralized turn manager for consistent turn numbering
          const authoritativeTurnNumber = 0; // Using simple turn system like Twilio
          // startLlmProcessing(callSid); // Using simple turn system like Twilio // Update phase to LLM processing
          
          latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_REQUEST_STARTED, {
            endpoint: '/rag/examiner',
            messageLength: userUtterance?.length || 0,
            conversationTurns: conversationHistory.length
          });

          try {
            // Check if we should use streaming mode
            if (TTS_PROVIDER === 'elevenlabs_stream') {
              console.log(`[STREAMING-MODE] Using streaming response for examiner endpoint, callSid: ${callSid}`);
              
              // Import the streaming function from server
              const { generateStreamingResponse } = await import('./server');
              
              latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_REQUEST_SENT, {
                endpoint: '/rag/examiner/stream',
                streaming: true
              });

              const result = await generateStreamingResponse(
                userUtterance || '',
                callSid,
                authoritativeTurnNumber,
                conversationHistory,
                caseData
              );

              if (result !== null) {
                const audioPath = typeof result === 'string' ? result : result.audioPath;
                const responseText = typeof result === 'string' ? null : result.responseText;
                
                console.log(`[STREAMING-MODE] Streaming completed successfully for examiner ${callSid}, audio file: ${audioPath}`);
                console.log(`[STREAMING-MODE] Captured response text: "${responseText}"`);
                
                latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_RESPONSE_RECEIVED, {
                  endpoint: '/rag/examiner/stream',
                  streaming: true,
                  responseLength: responseText?.length || 0
                });
                
                latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_RESPONSE_PROCESSED, {
                  endpoint: '/rag/examiner/stream',
                  streaming: true
                });
                
                // startTtsGeneration(callSid); // Using simple turn system like Twilio
                latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.TTS_GENERATION_STARTED, {
                  textLength: responseText?.length || 0,
                  text: responseText || 'streaming_completed_no_tts_needed',
                  source: 'streaming_completion',
                  streaming: true
                });

                return { responseText, wasStreamed: true };
              } else {
                console.error(`[STREAMING-MODE] Streaming failed for examiner ${callSid}, using fallback response`);
                const assistantReply = 'I encountered an issue processing that examination request. Could you please repeat it?';
                
                latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_RESPONSE_RECEIVED, {
                  endpoint: '/rag/examiner/stream',
                  streaming: false,
                  fallback: true,
                  responseLength: assistantReply.length
                });
                
                latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_RESPONSE_PROCESSED, {
                  endpoint: '/rag/examiner/stream',
                  fallback: true
                });
                
                // startTtsGeneration(callSid); // Using simple turn system like Twilio
                latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.TTS_GENERATION_STARTED, {
                  textLength: assistantReply.length,
                  text: assistantReply.substring(0, 100) + '...',
                  source: 'streaming_fallback'
                });

                return { responseText: assistantReply, wasStreamed: false };
              }
            } else {
              // Regular non-streaming mode
              console.log(`[REGULAR-MODE] Using regular response for examiner endpoint, callSid: ${callSid}`);
              
              // Record when request is actually sent
              latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_REQUEST_SENT, {
                endpoint: '/rag/examiner'
              });

              // Use the LLM router for examiner requests
              const response = await routeExaminerLLM(
                callSid,
                actorInput.userUtterance,
                conversationHistory,
                caseData,
                operationId?.toString()
              );
              const assistantReply = response.assistantReply;

            // Record when response is received
            latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_RESPONSE_RECEIVED, {
              endpoint: '/rag/examiner',
              responseLength: assistantReply?.length || 0
            });

            if (!assistantReply) {
              console.error('LLM server response missing assistantReply', { callSid, responseData: response });
              throw new Error('Invalid response from LLM server');
            }

            console.log('Received response from /rag/examiner', { callSid, replyStart: assistantReply.substring(0, 50) + '...' });

            // Record when response is processed
            latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_RESPONSE_PROCESSED, {
              endpoint: '/rag/examiner'
            });

            // Update phase to TTS generation and record step
            // startTtsGeneration(callSid); // Using simple turn system like Twilio
            latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.TTS_GENERATION_STARTED, {
              textLength: assistantReply.length,
              text: assistantReply.substring(0, 100) + '...',
              source: 'LLM_actor_completion'
            });

              // Return the LLM's response
              return { responseText: assistantReply, wasStreamed: false };
            } // Close the else block
          } catch (error: any) {
            console.error('Error calling /rag/examiner LLM endpoint', {
              callSid,
              errorMessage: error.message,
              errorDetails: error.response?.data,
              requestPayload: { ...llmRequestPayload, caseData: '...hidden...' },
            });
            // Fallback response
            return { responseText: 'I encountered an issue processing that examination request. Could you please repeat it?', wasStreamed: false };
          }
        }
      ),
      
      // Diagnosis LLM actor for diagnosis state
      diagnosisLlmActor: fromPromise<
        { responseText: string; wasStreamed: boolean }, 
        { userUtterance: string | undefined; caseData: CaseData | null; conversationHistory: ConversationTurn[]; callSid: string; turnNumber?: number; operationId?: string }
      >(
        async ({ input: actorInput }) => {
          const { userUtterance, caseData, conversationHistory, callSid, operationId } = actorInput;

          // Construct the request payload for the LLM server
          const llmRequestPayload = {
            callSid: callSid,
            userMessage: userUtterance,
            conversationHistory: conversationHistory,
            caseData: caseData,
          };

          console.log('Sending request to /rag/mark', { callSid, payload: { ...llmRequestPayload, caseData: '...hidden...' } });

          // Use centralized turn manager for consistent turn numbering
          const authoritativeTurnNumber = 0; // Using simple turn system like Twilio
          // startLlmProcessing(callSid); // Using simple turn system like Twilio // Update phase to LLM processing
          
          latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_REQUEST_STARTED, {
            endpoint: '/rag/mark',
            messageLength: userUtterance?.length || 0,
            conversationTurns: conversationHistory.length
          });

          try {
            // Record when request is actually sent
            latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_REQUEST_SENT, {
              endpoint: '/rag/mark'
            });

            // Use the LLM router for mark requests
            const response = await routeMarkLLM(
              callSid,
              actorInput.userUtterance,
              conversationHistory,
              caseData,
              operationId?.toString()
            );
            const assistantReply = response.assistantReply;

            // Record when response is received
            latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_RESPONSE_RECEIVED, {
              endpoint: '/rag/mark',
              responseLength: assistantReply?.length || 0
            });

            if (!assistantReply) {
              console.error('LLM server response missing assistantReply', { callSid, responseData: response });
              throw new Error('Invalid response from LLM server');
            }

            console.log('Received response from /rag/mark', { callSid, replyStart: assistantReply.substring(0, 50) + '...' });

            // Record when response is processed
            latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_RESPONSE_PROCESSED, {
              endpoint: '/rag/mark'
            });

            // Update phase to TTS generation and record step
            // startTtsGeneration(callSid); // Using simple turn system like Twilio
            latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.TTS_GENERATION_STARTED, {
              textLength: assistantReply.length,
              text: assistantReply.substring(0, 100) + '...',
              source: 'LLM_actor_completion'
            });

            // Return the LLM's response
            return { responseText: assistantReply, wasStreamed: false };
          } catch (error: any) {
            console.error('Error calling /rag/mark LLM endpoint', {
              callSid,
              errorMessage: error.message,
              errorDetails: error.response?.data,
              requestPayload: { ...llmRequestPayload, caseData: '...hidden...' },
            });
            // Fallback response
            return { responseText: 'I encountered an issue processing that diagnosis request. Could you please repeat it?', wasStreamed: false };
          }
        }
      ),

      // Streaming LLM actor for ultra-low latency streaming responses
      streamingLlmActor: fromPromise<
        { responseText: string; wasStreamed: boolean }, 
        { userUtterance: string | undefined; caseData: CaseData | null; conversationHistory: ConversationTurn[]; callSid: string; role: 'patient' | 'examiner' | 'mark'; turnNumber?: number }
      >(
        async ({ input: actorInput }) => {
          const { userUtterance, caseData, conversationHistory, callSid, role } = actorInput;

          console.log(`[streamingLlmActor] Starting streaming response for /${role} endpoint, callSid: ${callSid}`);

          // Import the streaming function from server
          const { generateStreamingResponse } = await import('./server');
          
          try {
            // Use centralized turn manager for consistent turn numbering
            const authoritativeTurnNumber = 0; // Using simple turn system like Twilio
            // startLlmProcessing(callSid); // Using simple turn system like Twilio // Update phase to LLM processing
            
            latencyTracker.recordStep(callSid, authoritativeTurnNumber, LatencySteps.LLM_REQUEST_STARTED, {
              endpoint: `/rag/${role}/stream`,
              messageLength: userUtterance?.length || 0,
              conversationTurns: conversationHistory.length,
              streaming: true
            });

            // Call the streaming response function which handles:
            // 1. Streaming LLM request to Python server
            // 2. Real-time streaming to ElevenLabs WebSocket  
            // 3. Direct audio streaming to Twilio WebSocket
            const result = await generateStreamingResponse(
              userUtterance || '',
              callSid,
              authoritativeTurnNumber,
              conversationHistory,
              caseData
            );

            if (result !== null) {
              // Streaming completed successfully - audio was sent directly to Twilio
              const audioPath = typeof result === 'string' ? result : result.audioPath;
              const responseText = typeof result === 'string' ? null : result.responseText;
              
              console.log(`[streamingLlmActor] Streaming completed successfully for ${callSid}, audio file: ${audioPath}`);
              console.log(`[streamingLlmActor] Captured response text: "${responseText}"`);
              
              // Return the actual response text for conversation history
              return { responseText, wasStreamed: true };
            } else {
              // Fallback: streaming failed, result is null
              console.error(`[streamingLlmActor] Streaming failed for ${callSid}, falling back to regular response`);
              return { responseText: 'I encountered an issue. Could you please repeat that?', wasStreamed: false };
            }
          } catch (error: any) {
            console.error(`[streamingLlmActor] Error in streaming LLM for ${callSid}:`, error);
            // Fallback response
            return { responseText: 'I encountered an issue processing that. Could you please repeat it?', wasStreamed: false };
          }
        }
      ),
      
      // Intent checker for examination state - determines what user wants to do
      examinationIntentChecker: fromPromise<
        { intent: 'repeat_question' | 'completion_check' | 'continue_answering' }, 
        { userUtterance: string | undefined; currentQuestion: string; callSid: string }
      >(
        async ({ input }) => {
          const { userUtterance, currentQuestion, callSid } = input;
          
          if (!userUtterance) {
            return { intent: 'continue_answering' };
          }

          try {
            // Use the LLM router for examination intent check
            const response = await routeExaminationIntentCheck(
              callSid,
              userUtterance || '',
              currentQuestion || ''
            );

            console.log(`[examinationIntentChecker] User said: "${userUtterance}"`);
            console.log(`[examinationIntentChecker] LLM intent decision:`, response);
            return { intent: response.intent || 'continue_answering' };
          } catch (error) {
            console.error('[examinationIntentChecker] Error:', error);
            return { intent: 'continue_answering' };
          }
        }
      ),
      

      
      // 🏗️ PHASE 2: Infrastructure Monitoring Actors (SAFE - READ-ONLY)
      transcriptMonitor: infrastructureActors.transcriptMonitorActor,
      processMonitor: infrastructureActors.processMonitorActor, 
      vadMonitor: infrastructureActors.vadMonitorActor,
      connectionMonitor: infrastructureActors.connectionMonitorActor,
      timingMonitor: infrastructureActors.timingMonitorActor,
      
      llmActor: fromPromise< // A more generic LLM actor, ensure its types match its usage
        { responseText: string; nextState?: string }, 
        { utterance: string | undefined; caseData: CaseData | null; currentState: string | undefined, conversationHistory: ConversationTurn[]; callSid: string }
      > (async ({ input: actorInput }) => {
        console.log('[llmActor] Generic actor received input:', actorInput);
        await new Promise(resolve => setTimeout(resolve, 300));
        return { responseText: `Generic LLM response to "${actorInput.utterance}"`, wasStreamed: false }
      }),
      
      systemTransitionCheckActor: fromPromise<
        { assistantReply: string }, 
        { callSid: string; userMessage: string; conversationHistory: ConversationTurn[]; caseData: CaseData | null }
      > (async ({ input: actorInput }) => {
        console.log('[systemTransitionCheckActor] Received input:', actorInput);
        
        try {
          // Use the LLM router for system transition check
          const response = await routeSystemTransitionCheck(
            actorInput.callSid,
            actorInput.userMessage
          );
          
          console.log('[systemTransitionCheckActor] Response:', response);
          return { assistantReply: response.decision };
        } catch (error) {
          console.error('[systemTransitionCheckActor] Error:', error);
          return { assistantReply: 'CONTINUE_HISTORY' }; // Default to continuing
        }
      }),
      transitionOrchestratorLlmActor: fromPromise< // For orchestrator decisions
        { responseText?: string; readinessDecision?: 'PROCEED' | 'WAIT' | 'CLARIFY'; orchestratorTask?: string }, 
        { userUtterance: string | undefined; orchestratorTask?: string; currentState: string | undefined; conversationHistory?: any[]; callSid?: string; turnNumber?: number }
      > (async ({ input: actorInput }) => {
        console.log('[transitionOrchestratorLlmActor] Received input:', actorInput);
        
        // If this is a readiness check, call the LLM server's /decide_readiness endpoint
        if (actorInput.orchestratorTask === 'AWAIT_USER_READINESS_RESPONSE') {
          try {
            console.log('[transitionOrchestratorLlmActor] Calling /decide_readiness endpoint');
            
            // Record latency for readiness decision endpoint
            const currentTurnIndex = actorInput.turnNumber || 0;
            const callSid = actorInput.callSid || 'unknown';
            latencyTracker.recordStep(callSid, currentTurnIndex, LatencySteps.LLM_REQUEST_STARTED, {
              endpoint: '/decide_readiness',
              messageLength: actorInput.userUtterance?.length || 0,
              conversationTurns: actorInput.conversationHistory?.length || 0
            });
            
            latencyTracker.recordStep(callSid, currentTurnIndex, LatencySteps.LLM_REQUEST_SENT, {
              endpoint: '/decide_readiness'
            });
            
            // Use the LLM router for readiness decision
            const response = await routeReadinessDecision(
              actorInput.orchestratorTask,
              actorInput.userUtterance,
              'case_selection'
            );
            
            latencyTracker.recordStep(callSid, currentTurnIndex, LatencySteps.LLM_RESPONSE_RECEIVED, {
              endpoint: '/decide_readiness',
              responseLength: response.responseText?.length || 0
            });
            
            console.log('[transitionOrchestratorLlmActor] LLM readiness response:', response);
            
            latencyTracker.recordStep(callSid, currentTurnIndex, LatencySteps.LLM_RESPONSE_PROCESSED, {
              endpoint: '/decide_readiness'
            });

            // Record that TTS generation is starting (LLM processing is complete)
            latencyTracker.recordStep(callSid, currentTurnIndex, LatencySteps.TTS_GENERATION_STARTED, {
              textLength: response.responseText?.length || 0,
              text: response.responseText?.substring(0, 100) + '...' || 'No response text',
              source: 'orchestrator_actor_completion'
            });
            
            return {
              responseText: response.responseText,
              readinessDecision: response.readinessDecision,
              orchestratorTask: actorInput.orchestratorTask
            };
                    } catch (error) {
            console.error('[transitionOrchestratorLlmActor] Error calling /decide_readiness:', error);
            
            // Fallback response
            return {
              responseText: "I'm sorry, I had trouble processing that. Are you ready to start the scenario?",
              readinessDecision: 'CLARIFY' as 'PROCEED' | 'WAIT' | 'CLARIFY',
              orchestratorTask: actorInput.orchestratorTask
            };
          }
        }
        
        // Fallback for other orchestrator tasks (keep existing logic)
        await new Promise(resolve => setTimeout(resolve, 300));
        return { responseText: "Orchestrator LLM default response.", wasStreamed: false };
      }),

      // ============================================================================
      // TUTORING SYSTEM ACTORS
      // ============================================================================
      
      tutoringSessionActor: fromPromise<
        { sessionId: string; session: any; mentalModelReady: boolean; hasExistingProgress: boolean; progressData: any; initialResponse?: string },
        { userId: string; sessionType: string }
      >(async ({ input }) => {
        const { userId, sessionType } = input;
        console.log(`[TutoringActor] Initializing tutoring session for user ${userId}`);
        
        try {
          // Import tutoring functions dynamically to avoid circular imports
          const { startTutoringSession } = await import('./tutor');
          
          const result = await startTutoringSession({
            userId,
            specialtyId: 1, // Default to general medicine - could be made configurable
            difficultyLevel: 'intermediate',
            targetDurationMinutes: 30
          });
          
          console.log(`[TutoringActor] Session initialized: ${result.sessionId}`);
          return result;
        } catch (error) {
          console.error('[TutoringActor] Failed to initialize session:', error);
          throw error;
        }
      }),

      tutoringConversationActor: fromPromise<
        { 
          tutorMessage: string; 
          sessionPhase: string; 
          progressUpdate: number; 
          teachingStrategy: string; 
          conceptsIntroduced: string[]; 
          assessmentRequired: boolean; 
          sessionShouldEnd: boolean; 
        },
        { sessionId: string; userMessage: string; responseTime: number; metadata: any }
      >(async ({ input }) => {
        const { sessionId, userMessage, responseTime, metadata } = input;
        console.log(`[TutoringConversationActor] Processing conversation for session ${sessionId}`);
        
        try {
          // Import tutoring functions dynamically
          const { handleConversationTurn } = await import('./tutor');
          
          const result = await handleConversationTurn({
            sessionId,
            userMessage,
            responseTime,
            timestamp: new Date(),
            metadata
          });
          
          console.log(`[TutoringConversationActor] Conversation processed, strategy: ${result.teachingStrategy}`);
          return result;
        } catch (error) {
          console.error('[TutoringConversationActor] Failed to process conversation:', error);
          throw error;
        }
      }),

      tutoringCompletionActor: fromPromise<
        { 
          experienceGained: number; 
          prestigeLevelUp: boolean; 
          newPrestigeLevel: number; 
          achievements: any[]; 
          badge: string; 
        },
        { sessionId: string; conceptsLearned: string[]; objectivesAchieved: string[]; finalScore: number; timeSpent: number }
      >(async ({ input }) => {
        const { sessionId, conceptsLearned, objectivesAchieved, finalScore, timeSpent } = input;
        console.log(`[TutoringCompletionActor] Completing session ${sessionId}`);
        
        try {
          // Import tutoring functions dynamically
          const { finalizeTutoringSession } = await import('./tutor');
          
          const result = await finalizeTutoringSession(sessionId, {
            conceptsLearned,
            objectivesAchieved,
            finalScore,
            timeSpent
          });
          
          console.log(`[TutoringCompletionActor] Session completed, XP: ${result.experienceGained}`);
          
          // Import prestige functions to get additional data
          const { getUserPrestige, getPrestigeBadge } = await import('./tutor');
          
          // Get current prestige information
          const currentPrestige = await getUserPrestige(result.session.userId, result.session.specialtyId);
          
          // Return the expected fields for the actor
          return {
            experienceGained: result.experienceGained,
            prestigeLevelUp: false, // We'd need to track level before/after to detect this
            newPrestigeLevel: currentPrestige?.prestigeLevel || 0,
            achievements: result.achievementsUnlocked || [],
            badge: currentPrestige ? getPrestigeBadge(currentPrestige.prestigeLevel) : '○'
          };
        } catch (error) {
          console.error('[TutoringCompletionActor] Failed to complete session:', error);
          throw error;
        }
      }),
      
      // ============================================================================
      // CENTRALIZED CLEANUP SERVICE
      // ============================================================================
      cleanupCallResources: fromPromise<
        { success: boolean; message: string },
        { callSid: string; userId?: string }
      >(async ({ input }) => {
        const { callSid, userId } = input;
        
        console.log(`[ENTERPRISE-CLEANUP] Starting FANG-level cleanup for call ${callSid}`);
        
        try {
          // Import the new enterprise cleanup service
          const { executeCompatibilityCleanup } = await import('./services/cleanup/CleanupServiceFactory');
          
          // Execute enterprise cleanup with full dependency ordering, 
          // resource ownership tracking, and verification
          const result = await executeCompatibilityCleanup(callSid);
          
          console.log(`[ENTERPRISE-CLEANUP] Cleanup completed for ${callSid}: ${result.success ? 'SUCCESS' : 'FAILURE'}`);
          console.log(`[ENTERPRISE-CLEANUP] Result: ${result.message}`);
          
          return result;
          
        } catch (error) {
          console.error(`[ENTERPRISE-CLEANUP] ❌ Enterprise cleanup failed for ${callSid}:`, error);
          
          // Emergency fallback - try to at least clean up critical resources
          console.warn(`[ENTERPRISE-CLEANUP] 🚨 Attempting emergency fallback cleanup for ${callSid}`);
          
          try {
            const { calls } = require('./server');
            const callState = calls[callSid];
            
            // Emergency cleanup of most critical resources
            if (callState?.livekitRTCHandler) {
              try {
                await callState.livekitRTCHandler.disconnect();
                delete callState.livekitRTCHandler;
                console.log(`[ENTERPRISE-CLEANUP] 🚨 Emergency: LiveKit RTC handler cleaned up`);
              } catch (rtcError) {
                console.error(`[ENTERPRISE-CLEANUP] 🚨 Emergency: LiveKit cleanup failed:`, rtcError);
                delete callState.livekitRTCHandler; // Force delete to prevent phantom calls
              }
            }
            
            // Remove call state to prevent phantom calls
            delete calls[callSid];
            console.log(`[ENTERPRISE-CLEANUP] 🚨 Emergency: Call state removed`);
            
            // Clean up XState controller
            const { callControllers } = require('./xstateIntegration');
            if (callControllers[callSid]) {
              delete callControllers[callSid];
              console.log(`[ENTERPRISE-CLEANUP] 🚨 Emergency: XState controller removed`);
            }
            
            return { 
              success: false, 
              message: `Emergency cleanup completed after enterprise cleanup failure: ${error.message}` 
            };
            
          } catch (emergencyError) {
            console.error(`[ENTERPRISE-CLEANUP] 🔥 Emergency cleanup also failed for ${callSid}:`, emergencyError);
            return { 
              success: false, 
              message: `Both enterprise and emergency cleanup failed: ${error.message}` 
            };
          }
        }
      }),
      
      // ===================================================================
      // ACTIVITY TIMER ACTORS - MISSING COMPONENT FOR INACTIVITY DETECTION
      // ===================================================================
      
      /**
       * Activity Warning Timer - Sends INACTIVITY_WARNING after 30 seconds
       * This is invoked when startActivityTimers action is called
       */
      activityWarningTimer: fromCallback(({ input, sendBack }) => {
        const { callSid } = input as { callSid: string };
        
        // THROTTLED LOGGING: Only log occasionally to prevent spam
        if (!global.activityLogThrottle) global.activityLogThrottle = {};
        const now = Date.now();
        const lastLog = global.activityLogThrottle[`warning_${callSid}`] || 0;
        if (now - lastLog > 10000) { // Only log every 10 seconds
          console.log(`[ACTIVITY-WARNING-TIMER] Starting 30-second inactivity warning timer for ${callSid}`);
          global.activityLogThrottle[`warning_${callSid}`] = now;
        }
        
        const timeoutId = setTimeout(() => {
          console.log(`[ACTIVITY-WARNING-TIMER] 30 seconds of inactivity reached for ${callSid} - sending warning`);
          sendBack({ 
            type: 'INACTIVITY_WARNING',
            callSid,
            warningNumber: 1,
            timestamp: Date.now()
          });
        }, 30000); // 30 seconds
        
        // Return cleanup function with throttled logging
        return () => {
          const now = Date.now();
          const lastCancelLog = global.activityLogThrottle[`warning_cancel_${callSid}`] || 0;
          if (now - lastCancelLog > 10000) { // Only log every 10 seconds
            console.log(`[ACTIVITY-WARNING-TIMER] Cancelled 30-second timer for ${callSid}`);
            global.activityLogThrottle[`warning_cancel_${callSid}`] = now;
          }
          clearTimeout(timeoutId);
        };
      }),
      
      /**
       * Activity Timeout Timer - Sends INACTIVITY_TIMEOUT after 60 seconds  
       * This is invoked when startActivityTimers action is called
       */
      activityTimeoutTimer: fromCallback(({ input, sendBack }) => {
        const { callSid } = input as { callSid: string };
        
        // THROTTLED LOGGING: Only log occasionally to prevent spam
        if (!global.activityLogThrottle) global.activityLogThrottle = {};
        const now = Date.now();
        const lastLog = global.activityLogThrottle[`timeout_${callSid}`] || 0;
        if (now - lastLog > 10000) { // Only log every 10 seconds
          console.log(`[ACTIVITY-TIMEOUT-TIMER] Starting 60-second inactivity timeout timer for ${callSid}`);
          global.activityLogThrottle[`timeout_${callSid}`] = now;
        }
        
        const timeoutId = setTimeout(() => {
          console.log(`[ACTIVITY-TIMEOUT-TIMER] 60 seconds of inactivity reached for ${callSid} - terminating call`);
          sendBack({ 
            type: 'INACTIVITY_TIMEOUT',
            callSid,
            reason: 'Session ended due to inactivity',
            timestamp: Date.now()
          });
        }, 60000); // 60 seconds
        
        // Return cleanup function with throttled logging
        return () => {
          const now = Date.now();
          const lastCancelLog = global.activityLogThrottle[`timeout_cancel_${callSid}`] || 0;
          if (now - lastCancelLog > 10000) { // Only log every 10 seconds
            console.log(`[ACTIVITY-TIMEOUT-TIMER] Cancelled 60-second timer for ${callSid}`);
            global.activityLogThrottle[`timeout_cancel_${callSid}`] = now;
          }
          clearTimeout(timeoutId);
        };
      })
    },
    actions: { // Define actions within setup
      logStateEntry: ({ context }) => {
        console.log(`[XState:${context.callSid}] Entered state: ${context.currentState}`);
      },
      logDtmfEvent: ({ context, event }) => {
        // Ensure event is of type DtmfInputEvent before accessing digit
        if (event.type === 'DTMF_INPUT') {
          console.log(`[XState] DTMF event received: ${event.digit} in state: ${context.currentState}`);
        }
      },
      logUserUtterance: ({ context, event }) => {
         if (event.type === 'USER_UTTERANCE') {
             console.log(`[XState:${context.callSid}] User utterance: ${event.transcription} (Final: ${event.isFinal})`);
         }
      },
      setLlmStartTime: assign(({ context }) => {
        const now = Date.now();
        console.log(`[XState] LLM invoke start time: ${now}`);
        
        // End XState processing and start LLM processing timing
        if (context.xstateTimingId) {
          conversationTimingService.endPhase(context.xstateTimingId, {
            llmInvokeStartTime: now,
            xstateProcessingCompleted: true
          });
        }
        
        // Start LLM processing timing
        const llmTimingId = conversationTimingService.startPhase(
          ConversationPhase.LLM_PROCESSING,
          context.callSid,
          context.turnNumber || 0,
          {
            llmProvider: 'openai', // Could be dynamic based on configuration
            transcriptLength: context.lastUserUtterance?.length || 0
          }
        );
        
        return {
          llmInvokeStartTime: now,
          currentTurnStartTime: now,
          llmTimingId: llmTimingId
        };
      }),
      setTtsStartTime: assign(({ context }) => {
        const now = Date.now();
        const currentAiTurnIndex = getSafeValidTurnIndex(context);
        console.log(`[setTtsStartTime] Starting TTS generation for turn ${currentAiTurnIndex} at ${now}`);
        
        // Update turnTimings with TTS generation start time (NOT audio playback start)
        const newTurnTimings = [...(context.turnTimings || [])];
        const { timing } = getOrCreateTurnTiming(context, currentAiTurnIndex, newTurnTimings);
        
        // Set TTS generation start time (audioStreamingStartAt will be set by AUDIO_PLAYBACK_STARTED events)
        timing.audioGenerationStartAt = now;
        console.log(`[setTtsStartTime] Set audioGenerationStartAt for turn ${currentAiTurnIndex} to ${now} (audioStreamingStartAt will be set by AUDIO_PLAYBACK_STARTED event)`);
        
        logTurnTimings(newTurnTimings, currentAiTurnIndex);
        
        return {
          ttsStartTime: now,
          currentTurnStartTime: now,
          turnTimings: newTurnTimings
        };
      }),
      setAudioPlaybackStartTime: assign(({ context, event }) => {
        const now = event.type === 'AUDIO_PLAYBACK_STARTED' && event.timestamp ? event.timestamp : Date.now();
        
        // CRITICAL FIX: Use safe turn index validation for consistent synchronization
        const eventTurnIndex = event.type === 'AUDIO_PLAYBACK_STARTED' ? (event as any).turnIndex : undefined;
        const currentlyPlayingTurnIndex = getSafeValidTurnIndex(context, eventTurnIndex);
        
        console.log(`[setAudioPlaybackStartTime] Audio playback started at ${now}, turn ${currentlyPlayingTurnIndex} (from ${eventTurnIndex !== undefined ? 'event' : 'context'})`);
        
        return {
          audioPlaybackStartTime: now,
          audioPlaybackStarted: true,
          currentlyPlayingTurnIndex: currentlyPlayingTurnIndex // CRITICAL FIX: Sync with XState context
        };
      }),
      
      // PHASE 3-1: Mark TTS started in grace period chain for post-TTS window evaluation
      markTtsStartedInGracePeriod: ({ context, event }) => {
        try {
          const now = event.type === 'AUDIO_PLAYBACK_STARTED' && event.timestamp ? event.timestamp : Date.now();
          // Get turn number from event or context
          const eventTurnIndex = event.type === 'AUDIO_PLAYBACK_STARTED' ? (event as any).turnIndex : undefined;
          const currentTurnNumber = eventTurnIndex !== undefined 
            ? eventTurnIndex 
            : context.turnNumber || 0;
          
          console.log(`[GRACE-PERIOD-TTS] Marking TTS started for turn ${currentTurnNumber} at ${now} (source: ${eventTurnIndex !== undefined ? 'event.turnIndex' : 'context.turnNumber'})`);
          
          // CRITICAL FIX: Update registry with TTS started timestamp for grace period calculations
          // The grace period evaluation in osceMachine.ts line 3075 uses previousTurn.ttsStartedAt from registry
          try {
            context.registry.updateTurnTtsStarted(currentTurnNumber, now);
            console.log(`[GRACE-PERIOD-TTS] ✅ Registry updated: Turn ${currentTurnNumber} ttsStartedAt=${now}`);
          } catch (registryError) {
            console.error(`[GRACE-PERIOD-TTS] ❌ Failed to update registry TTS timing for turn ${currentTurnNumber}:`, registryError);
          }
          
          console.log(`[GRACE-PERIOD-TTS] Post-TTS 3-second window now active for future utterances`);
        } catch (error) {
          console.error(`[GRACE-PERIOD-TTS] Error marking TTS started:`, error);
        }
      },

      // PHASE 3-2: Clean up grace period chain during call cleanup
      cleanupGracePeriodChain: ({ context }) => {
        try {
          if (context.gracePeriodChain) {
            console.log(`[GRACE-PERIOD-CLEANUP] Resetting grace period chain for ${context.callSid}`);
            
            // CRITICAL FIX: Only reset chain state, don't destroy the instance
            // This prevents the "GracePeriodChain not initialized" error
            context.gracePeriodChain.reset();
            
            console.log(`[GRACE-PERIOD-CLEANUP] Grace period chain reset completed for ${context.callSid}`);
          } else {
            console.error(`[GRACE-PERIOD-CLEANUP] FATAL: Grace period chain is undefined for ${context.callSid}! This should never happen with proper context preservation.`);
            throw new Error(`GracePeriodChain is undefined for ${context.callSid}. This indicates context corruption.`);
          }
        } catch (error) {
          console.error(`[GRACE-PERIOD-CLEANUP] FATAL: Error cleaning up grace period chain for ${context.callSid}:`, error);
          // Root cause elimination: Grace Period Chain should never fail with proper lifecycle management
          // Removing fallback recreation that could cause architecture violations
          throw error; // Let the error bubble up to identify any remaining root causes
        }
      },
      
      // PRODUCTION-GRADE: Action to send response text to TTS with deduplication
      sendToTts: ({ context, self }) => {
        console.log(`[TTS-DEBUG] sendToTts called for ${context.callSid}, responseText: "${context.responseText}"`);
        if (context.responseText) {
          // Check for duplicate responses
          const operationId = context.currentLlmOperationId?.toString() || 'unknown';
          const turnNumber = context.turnNumber || 0;
          
          // const duplicateCheck = checkForDuplicate(context.callSid, context.responseText, operationId, turnNumber);
          
          // if (duplicateCheck.isDuplicate) {
          //   console.warn(`[TTS-DEDUP-BLOCK] ${context.callSid}: Blocking duplicate TTS - ${duplicateCheck.reason}`);
          //   console.warn(`[TTS-DEDUP-BLOCK] Blocked content: "${context.responseText.substring(0, 100)}..."`);
          //   return; // Don't send to TTS
          // }
          
          // Record this response
          // recordTTSResponse(context.callSid, context.responseText, operationId, turnNumber);
          
          console.log(`[XState:${context.callSid}] Sending to TTS: "${context.responseText}"`); 
          
          // 🔍 DEBUG: Log connection type detection
          const connectionType = context.infrastructure?.connection?.type;
          console.log(`🔍 [CONNECTION-DEBUG] ${context.callSid}: XState context connection type: ${connectionType}`);
          console.log(`🔍 [CONNECTION-DEBUG] ${context.callSid}: Full infrastructure: ${JSON.stringify(context.infrastructure?.connection || {})}`);
          
          // Route based on connection type detection
          // 🚀 PHASE 2: All connection types now use unified plugin-based TTS
          console.log(`[XState:${context.callSid}] 🚀 Routing ALL TTS through plugin system for ${connectionType || 'twilio'} connection`);
          
          // TURN-BASED GRACE F9: Add turn lifecycle integration - TTS started
          try {
            const { turnCoordinationService } = require('./services/TurnCoordinationService');
            turnCoordinationService.updateTurnState(context.callSid, {
              stage: TurnLifecycleStage.TTS_STARTED,
              timestamp: Date.now(),
              assistantText: context.responseText
            });
          } catch (error) {
            console.error(`[TURN-LIFECYCLE] ${context.callSid}: Error updating turn state after TTS start:`, error);
          }
          
          self.send({ 
            type: 'GENERATE_TTS', 
            text: context.responseText, 
            callSid: context.callSid, 
            turnIndex: context.turnNumber || 0,
            connectionType: connectionType || 'twilio'
          });
        } else {
          console.warn(`[XState:${context.callSid}] TTS requested but responseText is empty`);
        }
      },
      
      // Conditional TTS action for LiveKit connections - wait for iOS audio ready signal
      sendToTtsIfNotLiveKit: ({ context, self }) => {
        const connectionType = context.infrastructure?.connection?.type;
        
        if (connectionType === 'livekit') {
          console.log(`[TTS-CONDITIONAL] ${context.callSid}: LiveKit connection detected - waiting for iOS audio ready signal before TTS`);
          console.log(`[TTS-CONDITIONAL] ${context.callSid}: Welcome message prepared: "${context.responseText}"`);
          // Don't send TTS yet - wait for iOS audio ready signal
          return;
        }
        
        console.log(`[TTS-CONDITIONAL] ${context.callSid}: Non-LiveKit connection (${connectionType || 'twilio'}) - sending TTS immediately`);
        
        // For non-LiveKit connections, send TTS immediately (existing behavior)
        if (context.responseText) {
          // Check for duplicate responses
          const operationId = context.currentLlmOperationId?.toString() || 'unknown';
          const turnNumber = context.turnNumber || 0;
          
          // const duplicateCheck = checkForDuplicate(context.callSid, context.responseText, operationId, turnNumber);
          
          // if (duplicateCheck.isDuplicate) {
          //   console.warn(`[TTS-DEDUP-BLOCK] ${context.callSid}: Blocking duplicate TTS - ${duplicateCheck.reason}`);
          //   console.warn(`[TTS-DEDUP-BLOCK] Blocked content: "${context.responseText.substring(0, 100)}..."`);
          //   return; // Don't send to TTS
          // }
          
          // Record this response
          // recordTTSResponse(context.callSid, context.responseText, operationId, turnNumber);
          
          console.log(`[XState:${context.callSid}] Sending to TTS: "${context.responseText}"`); 
          
          // Route based on connection type detection
          console.log(`[XState:${context.callSid}] 🚀 Routing TTS through plugin system for ${connectionType || 'twilio'} connection`);
          
          // TURN-BASED GRACE F9: Add turn lifecycle integration - TTS started
          try {
            const { turnCoordinationService } = require('./services/TurnCoordinationService');
            turnCoordinationService.updateTurnState(context.callSid, {
              stage: TurnLifecycleStage.TTS_STARTED,
              timestamp: Date.now(),
              assistantText: context.responseText
            });
          } catch (error) {
            console.error(`[TURN-LIFECYCLE] ${context.callSid}: Error updating turn state after TTS start:`, error);
          }
          
          self.send({ 
            type: 'GENERATE_TTS', 
            text: context.responseText, 
            callSid: context.callSid, 
            turnIndex: context.turnNumber || 0,
            connectionType: connectionType || 'twilio'
          });
        } else {
          console.warn(`[XState:${context.callSid}] TTS requested but responseText is empty`);
        }
      },
      
      // 🚀 PHASE 1: XState TTS Queue Management Actions (Migration from Bootstrap)
      // These replace the bootstrap TTS callback system with XState-managed queue processing
      
      enqueueTtsRequest: assign(({ context, event }) => {
        const ttsEvent = event as any;
        const requestId = `tts-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        // RACE CONDITION FIX: Use atomic turn number if available (from grace period combination)
        // This prevents the race condition where grace period TTS gets the old turn number
        const effectiveTurnIndex = ttsEvent.turnIndex || context._atomicTurnNumber || context.turnNumber || 0;
        console.log(`[RACE-CONDITION-FIX] TTS enqueue using turn index: ${effectiveTurnIndex} (atomic: ${context._atomicTurnNumber}, context: ${context.turnNumber}, event: ${ttsEvent.turnIndex})`);
        
        const ttsRequest = {
          id: requestId,
          text: ttsEvent.text,
          timestamp: Date.now(),
          turnIndex: effectiveTurnIndex, // RACE CONDITION FIX: Use atomic turn number
          connectionType: ttsEvent.connectionType || 'twilio',
          priority: ttsEvent.priority || 'normal' as const,
          llmOperationId: context.currentLlmOperationId || undefined, // Link to originating LLM operation
          source: ttsEvent.source || TTSSource.NORMAL // Track TTS source for inactivity loop prevention
        };
        
        const currentQueue = context.ttsQueue || {
          items: [],
          isProcessing: false,
          currentOperation: undefined,
          lastProcessedTime: 0,
          totalProcessed: 0,
          failureCount: 0
        };
        
        // CRITICAL FIX: Check for duplicate TTS requests in the queue
        const isDuplicateInQueue = currentQueue.items.some(existingItem => {
          const textMatch = existingItem.text === ttsRequest.text;
          const timeWindow = Math.abs(existingItem.timestamp - ttsRequest.timestamp) < 2000; // 2 second window
          return textMatch && timeWindow;
        });
        
        // Also check if currently processing operation is the same
        const isDuplicateOfCurrent = currentQueue.currentOperation && 
          currentQueue.currentOperation.text === ttsRequest.text;
        
        if (isDuplicateInQueue || isDuplicateOfCurrent) {
          console.warn(`[XState-TTS-QUEUE-DEDUP] ${context.callSid}: Blocking duplicate TTS request: "${ttsRequest.text.substring(0, 30)}..."`);
          console.warn(`[XState-TTS-QUEUE-DEDUP] Duplicate source: ${isDuplicateInQueue ? 'queue' : 'current operation'}`);
          return {}; // Don't modify the queue
        }
        
        console.log(`[XState-TTS-QUEUE] ${context.callSid}: Enqueueing TTS request: "${ttsRequest.text.substring(0, 30)}..." (ID: ${requestId})`);
        
        return {
          ttsQueue: {
            ...currentQueue,
            items: [...currentQueue.items, ttsRequest]
          }
        };
      }),
      
      startTtsProcessing: assign(({ context }) => {
        const currentQueue = context.ttsQueue;
        if (!currentQueue || currentQueue.items.length === 0) {
          console.warn(`[XState-TTS-QUEUE] ${context.callSid}: No TTS requests to process`);
          return {};
        }
        
        // GATE #1: XState TTS Queue processing gate - Filter out cancelled turns
        let nextRequest = currentQueue.items[0];
        let filteredItems = [...currentQueue.items];
        let skippedCount = 0;
        
        // TURN INDEX VALIDATION: Check for turn index consistency and correct mismatches
        const expectedTurnIndex = context._atomicTurnNumber || context.turnNumber || 0;
        filteredItems.forEach((item, index) => {
          const turnIndexMismatch = Math.abs(item.turnIndex - expectedTurnIndex) > 2; // Allow reasonable variance
          if (turnIndexMismatch) {
            console.warn(`[TURN-INDEX-VALIDATION] ${context.callSid}: Potential turn index mismatch - item ${index} has turnIndex ${item.turnIndex}, expected ~${expectedTurnIndex}`);
            console.warn(`[TURN-INDEX-VALIDATION] Item: "${item.text.substring(0, 30)}..." (ID: ${item.id})`);
          }
        });
        
        // Check if the next request's turn is cancelled using synchronous registry check
        try {
          const { turnCancellationRegistry } = require('./services/TurnCancellationRegistry');
          
          // Skip cancelled turns at the front of the queue
          while (filteredItems.length > 0) {
            const candidateRequest = filteredItems[0];
            const isCancelled = turnCancellationRegistry.isTurnCancelled(context.callSid, candidateRequest.turnIndex);
            
            if (isCancelled) {
              console.log(`[TTS-GATE-1] ${context.callSid}: BLOCKED and REMOVED cancelled turn ${candidateRequest.turnIndex}: "${candidateRequest.text.substring(0, 30)}..."`);
              filteredItems = filteredItems.slice(1); // Remove cancelled item
              skippedCount++;
            } else {
              nextRequest = candidateRequest;
              
              // TURN INDEX VALIDATION: Final validation before processing
              const validationTurnIndex = context._atomicTurnNumber || context.turnNumber || 0;
              if (Math.abs(nextRequest.turnIndex - validationTurnIndex) > 1) {
                console.warn(`[TURN-INDEX-VALIDATION] ${context.callSid}: Processing TTS with potentially stale turn index ${nextRequest.turnIndex} (context: ${validationTurnIndex})`);
                console.warn(`[TURN-INDEX-VALIDATION] Text: "${nextRequest.text.substring(0, 30)}..." - continuing with original index for safety`);
              }
              break; // Found a non-cancelled request
            }
          }
          
          if (skippedCount > 0) {
            console.log(`[TTS-GATE-1] ${context.callSid}: Skipped ${skippedCount} cancelled TTS requests`);
          }
          
          // If all requests were cancelled, return updated queue without starting processing
          if (filteredItems.length === 0) {
            console.log(`[TTS-GATE-1] ${context.callSid}: All TTS requests were cancelled - queue empty`);
            return {
              ttsQueue: {
                ...currentQueue,
                items: [], // Clear all cancelled items
                isProcessing: false
              }
            };
          }
          
        } catch (error) {
          console.error(`[TTS-GATE-1] ${context.callSid}: Error checking turn cancellation status:`, error);
          // Continue with original processing if registry is unavailable (fail-open for stability)
        }
        
        console.log(`[XState-TTS-QUEUE] ${context.callSid}: Starting TTS processing for: "${nextRequest.text.substring(0, 30)}..." (ID: ${nextRequest.id}, turn: ${nextRequest.turnIndex})`);
        
        return {
          ttsQueue: {
            ...currentQueue,
            isProcessing: true,
            currentOperation: {
              id: nextRequest.id,
              text: nextRequest.text,
              startTime: Date.now(),
              turnIndex: nextRequest.turnIndex,
              connectionType: nextRequest.connectionType,
              llmOperationId: nextRequest.llmOperationId,
              source: nextRequest.source
            },
            items: filteredItems.slice(1) // Remove the item being processed and any cancelled items
          }
        };
      }),
      
      completeTtsProcessing: assign(({ context, event }) => {
        const currentQueue = context.ttsQueue;
        if (!currentQueue || !currentQueue.currentOperation) {
          console.warn(`[XState-TTS-QUEUE] ${context.callSid}: No active TTS operation to complete`);
          return {};
        }
        
        const processingTime = Date.now() - currentQueue.currentOperation.startTime;
        console.log(`[XState-TTS-QUEUE] ${context.callSid}: Completed TTS processing for: "${currentQueue.currentOperation.text.substring(0, 30)}..." (${processingTime}ms)`);
        
        const isError = event.type === 'TTS_ERROR';
        
        return {
          ttsQueue: {
            ...currentQueue,
            isProcessing: false,
            currentOperation: undefined,
            lastProcessedTime: Date.now(),
            totalProcessed: currentQueue.totalProcessed + 1,
            failureCount: isError ? currentQueue.failureCount + 1 : currentQueue.failureCount
          }
        };
      }),
      
      // RECONNECT H3: Cancel TTS Queue Action - Enhanced with TurnCancellationRegistry
      cancelTtsQueue: assign(({ context, event }) => {
        const typedEvent = event as any;
        const reason = typedEvent.reason || 'unknown';
        const stage = typedEvent.stage || 'unknown';
        const targetTurnIndex = typedEvent.turnIndex;
        
        console.log(`[XState-TTS-CANCEL] ${context.callSid}: Cancelling TTS queue - reason: ${reason}, stage: ${stage}, targetTurn: ${targetTurnIndex}`);
        
        // 🚀 PHASE 3.2: Integrate with TurnCancellationRegistry for coordinated cancellation
        (async () => {
          try {
            const { turnCancellationRegistry } = await import('./services/TurnCancellationRegistry');
            
            if (targetTurnIndex !== undefined) {
              // Cancel specific turn in the registry
              turnCancellationRegistry.cancelTurn(context.callSid, targetTurnIndex, reason);
            } else {
              // Cancel all turns for this call (nuclear option)
              turnCancellationRegistry.cancelAllTurnsForCall(context.callSid, reason);
            }
          } catch (error) {
            console.error(`[XState-TTS-CANCEL] ${context.callSid}: Error accessing TurnCancellationRegistry:`, error);
          }
        })();
        
        const currentQueue = context.ttsQueue;
        if (!currentQueue) {
          console.log(`[XState-TTS-CANCEL] ${context.callSid}: No TTS queue to cancel`);
          return {};
        }
        
        const queueSize = currentQueue.items.length;
        const hasCurrentOperation = !!currentQueue.currentOperation;
        
        // If no turn index specified, fall back to nuclear clear (legacy behavior)
        if (targetTurnIndex === undefined) {
          console.log(`[XState-TTS-CANCEL] ${context.callSid}: No turn specified - clearing entire queue (${queueSize} items, current operation: ${hasCurrentOperation})`);
          
          return {
            ttsQueue: {
              items: [], // Clear all pending requests
              isProcessing: false, // Stop processing
              currentOperation: undefined, // Cancel current operation
              lastProcessedTime: Date.now(),
              totalProcessed: currentQueue.totalProcessed,
              failureCount: currentQueue.failureCount
            }
          };
        }
        
        // GRACE PERIOD TTS PROTECTION: Protect TTS operations generated during grace period combination
        // Check if we're in a grace period and if the target TTS is for a combined utterance
        const isGracePeriodActive = context.graceChain?.isActive;
        const gracePeriodProtectedTurns = new Set();
        
        if (isGracePeriodActive && context._atomicTurnNumber) {
          gracePeriodProtectedTurns.add(context._atomicTurnNumber);
          console.log(`[GRACE-PERIOD-PROTECTION] Protecting TTS for atomic turn: ${context._atomicTurnNumber} (grace period active)`);
        }
        
        // TURN-SPECIFIC CANCELLATION: Only cancel items for the specified turn (with grace period protection)
        const filteredItems = currentQueue.items.filter(item => {
          const shouldKeep = item.turnIndex !== targetTurnIndex || gracePeriodProtectedTurns.has(item.turnIndex);
          if (item.turnIndex === targetTurnIndex && gracePeriodProtectedTurns.has(item.turnIndex)) {
            console.log(`[GRACE-PERIOD-PROTECTION] Protecting TTS item for turn ${item.turnIndex}: "${item.text.substring(0, 30)}..."`);
          }
          return shouldKeep;
        });
        const removedItems = currentQueue.items.filter(item => 
          item.turnIndex === targetTurnIndex && !gracePeriodProtectedTurns.has(item.turnIndex)
        );
        
        // Check if current operation belongs to the target turn (with grace period protection)
        const shouldCancelCurrentOperation = currentQueue.currentOperation?.turnIndex === targetTurnIndex && 
                                           !gracePeriodProtectedTurns.has(currentQueue.currentOperation.turnIndex);
        
        if (currentQueue.currentOperation?.turnIndex === targetTurnIndex && gracePeriodProtectedTurns.has(currentQueue.currentOperation.turnIndex)) {
          console.log(`[GRACE-PERIOD-PROTECTION] Protecting current TTS operation for turn ${currentQueue.currentOperation.turnIndex}: "${currentQueue.currentOperation.text.substring(0, 30)}..."`);
        }
        
        console.log(`[XState-TTS-CANCEL] ${context.callSid}: Turn-specific cancellation for turn ${targetTurnIndex}:`);
        console.log(`[XState-TTS-CANCEL] ${context.callSid}: - Removed ${removedItems.length} items from queue`);
        console.log(`[XState-TTS-CANCEL] ${context.callSid}: - Kept ${filteredItems.length} items for other turns`);
        console.log(`[XState-TTS-CANCEL] ${context.callSid}: - Current operation belongs to target turn: ${shouldCancelCurrentOperation}`);
        
        if (shouldCancelCurrentOperation) {
          console.log(`[XState-TTS-CANCEL] ${context.callSid}: Cancelling current operation for turn ${targetTurnIndex}: "${currentQueue.currentOperation?.text.substring(0, 30)}..."`);
        }
        
        return {
          ttsQueue: {
            items: filteredItems, // Keep items from other turns
            isProcessing: filteredItems.length > 0 && !shouldCancelCurrentOperation, // Continue processing if other items exist
            currentOperation: shouldCancelCurrentOperation ? undefined : currentQueue.currentOperation, // Cancel only if it belongs to target turn
            lastProcessedTime: Date.now(),
            totalProcessed: currentQueue.totalProcessed,
            failureCount: currentQueue.failureCount
          }
        };
      }),
      
      // Send TTS_FINISHED event to trigger state machine transition back to listening
      sendTtsFinishedEvent: ({ context, self }) => {
        console.log(`[XState-TTS-QUEUE] ${context.callSid}: Sending TTS_FINISHED event to transition back to listening`);
        self.send({ type: 'TTS_FINISHED' });
      },

      // CRITICAL FIX: Shared action for COORDINATED_TURN_COMPLETE event handling
      // Ensures consistent userStoppedAt timestamp tracking across all handlers
      handleCoordinatedTurnComplete: assign(({ context, event }) => {
        const coordEvent = event as any;
        const turnNumber = (context.turnNumber || 0) + 1;
        
        // 🔍 INVESTIGATION: Turn lifecycle logging
        console.log(`🔍 [TURN-LIFECYCLE] handleCoordinatedTurnComplete: Assigning turn number ${turnNumber} for callSid=${context.callSid}`);
        console.log(`🔍 [TURN-LIFECYCLE] Previous context.turnNumber: ${context.turnNumber}`);
        console.log(`🔍 [TURN-LIFECYCLE] Event transcriptText: "${coordEvent.transcriptText?.substring(0, 50)}..."`);
        
        // CRITICAL FIX: Pass correct turn number to grace period handler
        // Store calculated turn number for grace period logic to use
        coordEvent._calculatedTurnNumber = turnNumber;
        
        console.log(`[COORDINATED-TURN-COMPLETE] ${context.callSid}: Updating context with userStoppedAt=${coordEvent.userStoppedAt}`);
        
        // ===================================================================
        // PHASE 2: REGISTRY-FIRST ARCHITECTURE (SINGLE SOURCE OF TRUTH)
        // ===================================================================
        // ===================================================================
        // GRACE PERIOD FIX: Create missing previous turn before grace evaluation
        // ===================================================================
        
        try {
          const registry = TurnTimestampRegistryManager.getOrCreateRegistry(context.callSid || "unknown");
          const previousTurnNumber = (context.turnNumber || 0);
          
          // Check if previous turn exists - if not, create it for grace period logic
          if (previousTurnNumber > 0) {
            const previousTurnExists = registry.hasTurn(previousTurnNumber);
            if (!previousTurnExists) {
              console.log(`[REGISTRY-GRACE-FIX] Creating missing turn ${previousTurnNumber} before grace evaluation`);
              
              // Create previous turn record using context data
              const previousTurnRequest: TurnTimestampCreationRequest = {
                turnIndex: previousTurnNumber,
                userStartedAt: context.previousUserStoppedAt || (Date.now() - 5000),
                userStoppedAt: context.previousUserStoppedAt || (Date.now() - 4000),
                turnFinalizedAt: context.turnFinalizedAt || (Date.now() - 4000),
                utteranceText: "", // Will be filled when TTS processes
                ttsStartedAt: undefined,
                interruptionTimestamp: context.interruption?.lastInterruptionAt
              };
              registry.createTurnRecord(previousTurnRequest);
            }
          }
        } catch (registryError) {
          console.error(`[REGISTRY-GRACE-FIX] Failed to create missing previous turn:`, registryError.message);
        }

        
        try {
          // Get or create registry for this call
          const registry = TurnTimestampRegistryManager.getOrCreateRegistry(context.callSid || 'unknown');
          
          // Create immutable turn record in registry (SINGLE SOURCE OF TRUTH)
          const currentInterruptionTimestamp = coordEvent.interruptionTimestamp || coordEvent.userStoppedAt;
          const turnRequest: TurnTimestampCreationRequest = {
            turnIndex: turnNumber,
            userStartedAt: currentInterruptionTimestamp,
            userStoppedAt: coordEvent.userStoppedAt,
            turnFinalizedAt: coordEvent.userStoppedAt,
            utteranceText: coordEvent.transcriptText || coordEvent.transcription || '', // GRACE PERIOD FIX: Store utterance text
            ttsStartedAt: undefined, // TTS hasn't started for this new turn yet
            interruptionTimestamp: coordEvent.interruptionTimestamp
          };
          
          const registryRecord = registry.createTurnRecord(turnRequest);
          console.log(`[REGISTRY-FIRST] Created immutable turn ${turnNumber} in registry: ${registry.getTimingForDisplay(turnNumber)}`);
          
          // MIGRATION VALIDATION: Check consistency between registry and context
          const contextData: ContextBridgeData = {
            userStoppedAt: coordEvent.userStoppedAt,
            turnFinalizedAt: coordEvent.userStoppedAt,
            previousUserStoppedAt: context.previousUserStoppedAt,
            previousTurnFinalizedAt: context.turnFinalizedAt,
            interruptionTimestamp: coordEvent.interruptionTimestamp,
            audioPlaybackStartTime: context.audioPlaybackStartTime
          };
          
          const consistency = registry.validateConsistency(contextData, turnNumber);
          if (!consistency.consistent) {
            console.warn(`[REGISTRY-MIGRATION] Timing discrepancies detected:`, consistency.discrepancies);
          }
          
        } catch (registryError) {
          console.error(`[REGISTRY-FIRST] Failed to create turn ${turnNumber} in registry:`, registryError.message);
          // Continue with context-only for backward compatibility
        }
        
        // Start XState processing timing
        const xstateTimingId = conversationTimingService.startPhase(
          ConversationPhase.XSTATE_PROCESSING,
          context.callSid,
          turnNumber,
          {
            transcriptLength: coordEvent.transcriptText?.length || 0,
            gracePeriodApplied: coordEvent.gracePeriodApplied,
            hasInterruption: !!coordEvent.interruptionTimestamp
          }
        );
        
        // Start context update sub-phase
        conversationTimingService.startSubPhase(xstateTimingId, 'context_update', {
          previousTurnNumber: context.turnNumber || 0,
          newTurnNumber: turnNumber
        });
        
        const currentTurnStartTime = Date.now();
        
        conversationTimingService.endSubPhase(xstateTimingId, 'context_update');
        
        console.log(`[GRACE-PERIOD-TIMING-DEFENSIVE] handleCoordinatedTurnComplete preserving previousUserStoppedAt=${context.previousUserStoppedAt}`);
        console.log(`[TURN-FINALIZATION-TIMING] handleCoordinatedTurnComplete preserving previousTurnFinalizedAt=${context.previousTurnFinalizedAt}`);
        console.log(`[GRACE-PERIOD-FUNDAMENTAL-FIX] Setting turnFinalizedAt=${coordEvent.userStoppedAt} (USER turn finalization from gated STT+VAD), previousTurnFinalizedAt=${context.turnFinalizedAt}`);
        
        // PARALLEL CONTEXT WRITES (FOR MIGRATION VALIDATION - WILL BE REMOVED IN PHASE 4)
        return {
          // GRACE PERIOD FIX: Remove XState context-based previousLastUserUtterance (now using registry)
          // Registry provides immutable, contamination-proof access to previous turn utterances
          lastUserUtterance: coordEvent.transcriptText,
          userStoppedAt: coordEvent.userStoppedAt, // CRITICAL: Preserve userStoppedAt timestamp
          previousUserStoppedAt: context.previousUserStoppedAt, // CRITICAL FIX: Preserve previousUserStoppedAt for grace period calculation
          // GRACE PERIOD TIMING FUNDAMENTAL FIX: Set turnFinalizedAt when USER turn finalizes (coordEvent.userStoppedAt from gated process)
          turnFinalizedAt: coordEvent.userStoppedAt, // CORRECT: When user's turn finalizes (STT_FINALIZED + VAD), not when AI completes
          previousTurnFinalizedAt: context.turnFinalizedAt, // TURN-FINALIZATION-TIMING: Preserve previous for grace period calculation
          // INTERRUPTION TIMING FIX: Store interruption timestamp for grace period evaluation during active TTS
          interruptionTimestamp: coordEvent.interruptionTimestamp, // When user interrupted (if applicable)
          // PHASE 3 FIX: Preserve critical timing data for grace period calculations
          audioPlaybackStartTime: context.audioPlaybackStartTime, // CRITICAL: Preserve TTS streaming start time
          waitingForUserInput: false,
          turnNumber: turnNumber,
          currentTurnStartTime: currentTurnStartTime,
          coordination: {
            lastCompletionTime: currentTurnStartTime,
            gracePeriodApplied: coordEvent.gracePeriodApplied
          },
          // Store timing ID for later phases
          xstateTimingId: xstateTimingId,
          // CRITICAL FIX: Preserve Grace Period Chain instance to prevent destruction
          gracePeriodChain: context.gracePeriodChain
        };
      }),

      // CRITICAL FIX: Centralized Turn Timing Actions
      // These replace scattered turnTimings updates throughout the codebase
      // to ensure XState context and global calls object stay synchronized
      updateAudioStreamingStart: assign(({ context, event }) => {
        const timestamp = (event as any).timestamp || Date.now();
        const turnIndex = (event as any).turnIndex || context.turnNumber || 0;
        
        console.log(`[TurnTiming-Action] updateAudioStreamingStart: turn ${turnIndex}, timestamp ${timestamp}`);
        
        // ===================================================================
        // PHASE 2: REGISTRY-FIRST AUDIO STREAMING (SINGLE SOURCE OF TRUTH)
        // ===================================================================
        
        try {
          const registry = TurnTimestampRegistryManager.getOrCreateRegistry(context.callSid || 'unknown');
          
          // Update registry with audio streaming start (AUTHORITATIVE)
          registry.updateAudioStreaming(turnIndex, timestamp);
          console.log(`[REGISTRY-FIRST] Updated audio streaming start for turn ${turnIndex}: ${timestamp}`);
          
          // Also update TTS timestamp if not already set
          if (!registry.hasTurn(turnIndex) || !registry.getTurnTimestamps(turnIndex).ttsStartedAt) {
            try {
              registry.updateTurnTtsStarted(turnIndex, timestamp);
              console.log(`[REGISTRY-FIRST] Also updated TTS start for turn ${turnIndex}: ${timestamp}`);
            } catch (ttsError) {
              // TTS already set or turn doesn't exist - this is fine
            }
          }
          
        } catch (registryError) {
          console.error(`[REGISTRY-FIRST] Failed to update audio streaming start for turn ${turnIndex}:`, registryError.message);
        }
        
        // PARALLEL CONTEXT WRITES (FOR MIGRATION VALIDATION - WILL BE REMOVED IN PHASE 4)
        const newTurnTimings = [...(context.turnTimings || [])];
        const { timing } = getOrCreateTurnTiming(context, turnIndex, newTurnTimings);
        timing.audioStreamingStartAt = timestamp;
        
        // 🔍 INVESTIGATION: TTS lifecycle logging
        console.log(`🔍 [TTS-LIFECYCLE] Recording audioStreamingStartAt=${timestamp} for turn ${turnIndex} (callSid=${context.callSid})`);
        console.log(`🔍 [TTS-LIFECYCLE] Turn timing state: audioStarted=${!!timing.audioStreamingStartAt}, interrupted=${!!timing.interruptionTimestamp}`);
        
        // Sync to global calls object to prevent coordinator retries
        try {
          const { calls } = require('./server');
          if (calls[context.callSid]) {
            calls[context.callSid].turnTimings = newTurnTimings;
            console.log(`[TurnTiming-Sync] Synced audioStreamingStart to global calls for ${context.callSid}`);
          }
        } catch (error) {
          console.warn(`[TurnTiming-Sync] Failed to sync audioStreamingStart to global calls:`, error);
        }
        
        return { turnTimings: newTurnTimings };
      }),

      updateAudioStreamingEnd: assign(({ context, event }) => {
        const timestamp = (event as any).timestamp || Date.now();
        const turnIndex = (event as any).turnIndex || context.turnNumber || 0;
        const audioDurationMs = (event as any).audioDurationMs;
        
        console.log(`[TurnTiming-Action] updateAudioStreamingEnd: turn ${turnIndex}, timestamp ${timestamp}, duration ${audioDurationMs}ms`);
        
        // ===================================================================
        // PHASE 2: REGISTRY-FIRST AUDIO STREAMING END (SINGLE SOURCE OF TRUTH)
        // ===================================================================
        
        try {
          const registry = TurnTimestampRegistryManager.getOrCreateRegistry(context.callSid || 'unknown');
          registry.updateAudioStreaming(turnIndex, undefined, timestamp, audioDurationMs);
          console.log(`[REGISTRY-FIRST] Updated audio streaming end for turn ${turnIndex}: ${timestamp}ms duration: ${audioDurationMs}ms`);
        } catch (registryError) {
          console.error(`[REGISTRY-FIRST] Failed to update audio streaming end for turn ${turnIndex}:`, registryError.message);
        }
        
        // PARALLEL CONTEXT WRITES (FOR MIGRATION VALIDATION)
        const newTurnTimings = [...(context.turnTimings || [])];
        const { timing } = getOrCreateTurnTiming(context, turnIndex, newTurnTimings);
        timing.audioStreamingEndAt = timestamp;
        if (audioDurationMs !== undefined) {
          timing.audioDurationMs = audioDurationMs;
        }
        
        // Sync to global calls object
        try {
          const { calls } = require('./server');
          if (calls[context.callSid]) {
            calls[context.callSid].turnTimings = newTurnTimings;
            console.log(`[TurnTiming-Sync] Synced audioStreamingEnd to global calls for ${context.callSid}`);
          }
        } catch (error) {
          console.warn(`[TurnTiming-Sync] Failed to sync audioStreamingEnd to global calls:`, error);
        }
        
        return { turnTimings: newTurnTimings };
      }),

      updateInterruptionTimestamp: assign(({ context, event }) => {
        const timestamp = (event as any).timestamp || Date.now();

        // CRITICAL FIX: Record interruption on the turn that WAS INTERRUPTED, not the current turn
        // Use currentTurnIndex from event (the turn being interrupted) or currentlyPlayingTurnIndex from context
        const interruptedTurnIndex = (event as any).currentTurnIndex || context.currentlyPlayingTurnIndex || (context.turnNumber || 0);

        console.log(`[INTERRUPTION-REGISTRY-FIX] Recording interruption on turn ${interruptedTurnIndex} at timestamp ${timestamp}`);
        console.log(`[INTERRUPTION-REGISTRY-FIX] Event currentTurnIndex: ${(event as any).currentTurnIndex}, context.currentlyPlayingTurnIndex: ${context.currentlyPlayingTurnIndex}, context.turnNumber: ${context.turnNumber}`);

        // ===================================================================
        // PHASE 2: REGISTRY-FIRST INTERRUPTION (SINGLE SOURCE OF TRUTH)
        // ===================================================================

        try {
          const registry = TurnTimestampRegistryManager.getOrCreateRegistry(context.callSid || 'unknown');

          // Check if turn exists, if not create it (this can happen for assistant turns)
          if (!registry.hasTurn(interruptedTurnIndex)) {
            console.log(`[INTERRUPTION-REGISTRY-FIX] Turn ${interruptedTurnIndex} doesn't exist in registry, creating it for interruption recording`);

            // Create a basic turn record for the interrupted turn (likely an assistant turn)
            const turnRequest: TurnTimestampCreationRequest = {
              turnIndex: interruptedTurnIndex,
              userStartedAt: timestamp - 1000, // Estimate start time
              userStoppedAt: timestamp - 500,  // Estimate stop time
              turnFinalizedAt: timestamp - 500, // Estimate finalization
              utteranceText: '', // Assistant turn text (will be filled later)
              ttsStartedAt: context.audioPlaybackStartTime, // Use actual TTS start time if available
              interruptionTimestamp: timestamp // Record the interruption
            };

            try {
              registry.createTurnRecord(turnRequest);
              console.log(`[INTERRUPTION-REGISTRY-FIX] ✅ Created turn ${interruptedTurnIndex} for interruption recording`);
            } catch (createError) {
              console.error(`[INTERRUPTION-REGISTRY-FIX] ❌ Failed to create turn ${interruptedTurnIndex}:`, createError.message);
            }
          } else {
            // Turn exists, just update the interruption timestamp
            registry.updateInterruption(interruptedTurnIndex, timestamp);
            console.log(`[REGISTRY-FIRST] ✅ Updated interruption timestamp for existing turn ${interruptedTurnIndex}: ${timestamp}`);
          }
        } catch (registryError) {
          console.error(`[REGISTRY-FIRST] ❌ Failed to handle interruption for turn ${interruptedTurnIndex}:`, registryError.message);
        }
        
        // PARALLEL CONTEXT WRITES (FOR MIGRATION VALIDATION)
        const newTurnTimings = [...(context.turnTimings || [])];
        const { timing } = getOrCreateTurnTiming(context, interruptedTurnIndex, newTurnTimings);
        timing.interruptionTimestamp = timestamp;
        
        // Sync to global calls object to prevent coordinator retries
        try {
          const { calls } = require('./server');
          if (calls[context.callSid]) {
            calls[context.callSid].turnTimings = newTurnTimings;
            console.log(`[TurnTiming-Sync] Synced interruptionTimestamp to global calls for ${context.callSid}`);
          }
        } catch (error) {
          console.warn(`[TurnTiming-Sync] Failed to sync interruptionTimestamp to global calls:`, error);
        }
        
        return { turnTimings: newTurnTimings };
      }),

      updateSilenceDetected: assign(({ context, event }) => {
        const timestamp = (event as any).timestamp || Date.now();
        const turnIndex = (event as any).turnIndex || (context.turnNumber || 0) + 1;
        
        console.log(`[TurnTiming-Action] updateSilenceDetected: turn ${turnIndex}, timestamp ${timestamp}`);
        
        // LATENCY FIX: Initialize latency tracking for this turn if not already started
        try {
          const { latencyTracker, LatencySteps } = require('./latencyTracker');
          const existingSession = latencyTracker.getCurrentSession(context.callSid, turnIndex);
          if (!existingSession) {
            console.log(`[LATENCY-INIT] Starting latency tracking for ${context.callSid} turn ${turnIndex} at silence detection`);
            latencyTracker.startTracking(context.callSid, turnIndex);
            latencyTracker.recordStep(context.callSid, turnIndex, LatencySteps.SILENCE_DETECTED, {
              timestamp: timestamp,
              vadThreshold: '660ms default',
              initiatedBy: 'updateSilenceDetected'
            });
          } else {
            console.log(`[LATENCY-EXISTING] Latency tracking already exists for ${context.callSid} turn ${turnIndex}`);
          }
        } catch (error) {
          console.warn(`[LATENCY-INIT] Failed to initialize latency tracking:`, error);
        }
        
        const newTurnTimings = [...(context.turnTimings || [])];
        const { timing } = getOrCreateTurnTiming(context, turnIndex, newTurnTimings);
        timing.silenceDetectedAt = timestamp;
        
        // Sync to global calls object
        try {
          const { calls } = require('./server');
          if (calls[context.callSid]) {
            calls[context.callSid].turnTimings = newTurnTimings;
            console.log(`[TurnTiming-Sync] Synced silenceDetected to global calls for ${context.callSid}`);
          }
        } catch (error) {
          console.warn(`[TurnTiming-Sync] Failed to sync silenceDetected to global calls:`, error);
        }
        
        return { turnTimings: newTurnTimings };
      }),

      updateTranscriptReceived: assign(({ context, event }) => {
        const timestamp = (event as any).timestamp || Date.now();
        const turnIndex = (event as any).turnIndex || context.turnNumber || 0;
        
        console.log(`[TurnTiming-Action] updateTranscriptReceived: turn ${turnIndex}, timestamp ${timestamp}`);
        
        const newTurnTimings = [...(context.turnTimings || [])];
        const { timing } = getOrCreateTurnTiming(context, turnIndex, newTurnTimings);
        timing.transcriptReceivedAt = timestamp;
        
        // Sync to global calls object
        try {
          const { calls } = require('./server');
          if (calls[context.callSid]) {
            calls[context.callSid].turnTimings = newTurnTimings;
            console.log(`[TurnTiming-Sync] Synced transcriptReceived to global calls for ${context.callSid}`);
          }
        } catch (error) {
          console.warn(`[TurnTiming-Sync] Failed to sync transcriptReceived to global calls:`, error);
        }
        
        return { turnTimings: newTurnTimings };
      }),

      // 🚀 PHASE 2: Turn Coordination Actions
      // Centralized turn coordination to eliminate race conditions
      coordinateAtomicTurnCompletion: async ({ context, event, self }) => {
        try {
          const { ENABLE_CENTRALIZED_TURNS } = await import('./services/CallStateProxy');
          
          if (!ENABLE_CENTRALIZED_TURNS) {
            console.log(`[TURN-COORDINATION] ${context.callSid}: Centralized turns disabled, using legacy coordination`);
            return;
          }

          console.log(`[TURN-COORDINATION] ${context.callSid}: Starting centralized turn coordination`);
          
          const { turnCoordinationService } = await import('./services/TurnCoordinationService');
          const { ConnectionType } = await import('./config/audio-constants');
          
          // Extract coordination data from event
          const coordinatedEvent = event as any;
          const transcriptText = coordinatedEvent.transcriptText || '';
          const silenceTimestamp = coordinatedEvent.userStoppedAt || Date.now();
          const interruptionTimestamp = coordinatedEvent.interruptionTimestamp;
          
          // Map connection type
          let connectionType = ConnectionType.TWILIO;
          if (context.infrastructure?.connection?.type === 'livekit') {
            connectionType = ConnectionType.LIVEKIT;
          } else if (context.infrastructure?.connection?.type === 'webrtc') {
            connectionType = ConnectionType.DIRECT;
          }
          
          // Execute atomic turn coordination
          const result = await turnCoordinationService.coordinateAtomicTurnCompletion(
            context.callSid,
            silenceTimestamp,
            transcriptText,
            connectionType,
            interruptionTimestamp
          );
          
          if (result.success) {
            console.log(`[TURN-COORDINATION] ${context.callSid}: Coordination successful in ${result.coordinationTime}ms`);
            
            // Send internal success event to trigger processing
            self.send({
              type: 'INTERNAL_TURN_COORDINATION_SUCCESS',
              result
            });
          } else {
            console.error(`[TURN-COORDINATION] ${context.callSid}: Coordination failed:`, result.error);
            
            // Send internal error event for recovery
            self.send({
              type: 'INTERNAL_TURN_COORDINATION_ERROR', 
              error: result.error
            });
          }
          
        } catch (error) {
          console.error(`[TURN-COORDINATION] ${context.callSid}: Coordination action failed:`, error);
          
          // Send internal error event for recovery
          self.send({
            type: 'INTERNAL_TURN_COORDINATION_ERROR',
            error: error instanceof Error ? error.message : 'Unknown coordination error'
          });
        }
      },

      // Mark turn coordination as completed and update context
      completeTurnCoordination: assign(({ context, event }) => {
        const coordEvent = event as any;
        const result = coordEvent.result;
        
        if (!result) {
          return {};
        }
        
        console.log(`[TURN-COORDINATION] ${context.callSid}: Completing coordination for turn ${result.turnNumber}`);
        
        return {
          lastUserUtterance: result.finalText,
          turnNumber: result.turnNumber,
          // Update timing information
          coordination: {
            ...context.coordination,
            lastCompletionTime: Date.now(),
            lastCoordinationTime: result.coordinationTime,
            gracePeriodApplied: result.gracePeriodApplied
          }
        };
      }),

      // Recovery action for stuck turn coordination
      recoverTurnCoordination: async ({ context, self }) => {
        try {
          console.log(`[TURN-COORDINATION] ${context.callSid}: Attempting coordination recovery`);
          
          const { turnCoordinationService } = await import('./services/TurnCoordinationService');
          
          const recovered = await turnCoordinationService.recoverStuckCoordination(context.callSid);
          
          if (recovered) {
            console.log(`[TURN-COORDINATION] ${context.callSid}: Recovery successful`);
            
            // Reset coordination state and try again
            self.send({ type: 'INTERNAL_TURN_COORDINATION_RECOVERED' });
          } else {
            console.log(`[TURN-COORDINATION] ${context.callSid}: No recovery needed`);
          }
          
        } catch (error) {
          console.error(`[TURN-COORDINATION] ${context.callSid}: Recovery failed:`, error);
        }
      },

      // 🚀 PHASE 2: Intelligent Turn Coordination Router
      // Routes to centralized coordination when enabled, legacy when disabled
      routeTurnCoordination: async ({ context, event, self }) => {
        try {
          const { ENABLE_CENTRALIZED_TURNS } = await import('./services/CallStateProxy');
          
          if (ENABLE_CENTRALIZED_TURNS) {
            console.log(`[TURN-ROUTER] ${context.callSid}: Routing to centralized coordination`);
            
            // Use new centralized coordination
            await import('./services/TurnCoordinationService').then(() => {
              self.send({
                type: 'INTERNAL_START_TURN_COORDINATION',
                originalEvent: event
              });
            });
          } else {
            console.log(`[TURN-ROUTER] ${context.callSid}: Routing to legacy coordination`);
            
            // Use centralized grace period handler action (defined in this same file)
            // This is already an action, so we just trigger the existing COORDINATED_TURN_COMPLETE flow
            self.send({
              type: 'INTERNAL_USE_LEGACY_COORDINATION',
              originalEvent: event
            });
          }
          
        } catch (error) {
          console.error(`[TURN-ROUTER] ${context.callSid}: Routing failed, falling back to legacy:`, error);
          
          // Fallback to legacy coordination
          self.send({
            type: 'INTERNAL_USE_LEGACY_COORDINATION',
            originalEvent: event
          });
        }
      },
      
      processNextTtsRequest: ({ context, self }) => {
        const currentQueue = context.ttsQueue;
        if (!currentQueue || currentQueue.isProcessing || currentQueue.items.length === 0) {
          console.log(`[XState-TTS-QUEUE] ${context.callSid}: No next TTS request to process (processing: ${currentQueue?.isProcessing}, queue length: ${currentQueue?.items.length || 0})`);
          return;
        }
        
        console.log(`[XState-TTS-QUEUE] ${context.callSid}: Starting TTS processing (queue length: ${currentQueue.items.length})`);
        
        // Start processing the first item in the queue
        self.send({ type: 'INTERNAL_START_TTS_PROCESSING' });
      },
      
      // 🚀 PHASE 2: Actual TTS processing action using AudioProviderManager
      processTtsWithProvider: ({ context, self }) => {
        const currentQueue = context.ttsQueue;
        if (!currentQueue?.currentOperation) {
          console.error(`[XState-TTS-PROCESSING] ${context.callSid}: No current TTS operation to process`);
          return;
        }
        
        const operation = currentQueue.currentOperation;
        console.log(`[XState-TTS-PROCESSING] ${context.callSid}: Processing TTS with AudioProviderManager: "${operation.text.substring(0, 50)}..."`);
        
        // 🚀 PHASE 3.2: Pre-generation interruption check using TurnCancellationRegistry
        (async () => {
          try {
            const { turnCancellationRegistry } = await import('./services/TurnCancellationRegistry');
            
            // Check if this turn has been cancelled before starting TTS generation
            if (turnCancellationRegistry.isTurnCancelled(context.callSid, operation.turnIndex)) {
              console.log(`[XState-TTS-PROCESSING] ${context.callSid}: Turn ${operation.turnIndex} already cancelled - aborting TTS generation`);
              self.send({ type: 'INTERNAL_TTS_PROCESSING_ERROR' });
              return;
            }
            
            // Get abort signal for this turn to enable mid-generation cancellation
            const abortSignal = turnCancellationRegistry.getTurnSignal(context.callSid, operation.turnIndex);
            
            // Register this TTS operation for tracking
            turnCancellationRegistry.registerOperation(context.callSid, operation.turnIndex, `TTS-${operation.id}`);
            
            const { audioProviderManager } = await import('./services/AudioProviderManager');
            const { ConnectionType } = await import('./config/audio-constants');
            
            // Map connection type string to enum
            let connectionType = ConnectionType.TWILIO; // default
            const requestConnectionType = operation.connectionType || 'twilio';
            if (requestConnectionType === 'livekit') {
              connectionType = ConnectionType.LIVEKIT;
            } else if (requestConnectionType === 'direct') {
              connectionType = ConnectionType.DIRECT;
            }
            
            console.log(`[XState-TTS-PROCESSING] ${context.callSid}: Using AudioProviderManager for ${requestConnectionType}`);
            
            // 🚀 PHASE 3.2: Check for cancellation again right before expensive TTS generation
            if (abortSignal.aborted) {
              console.log(`[XState-TTS-PROCESSING] ${context.callSid}: Turn ${operation.turnIndex} cancelled during setup - aborting TTS generation`);
              self.send({ type: 'INTERNAL_TTS_PROCESSING_ERROR' });
              return;
            }
            
            // Note: Conversation is now maintained in XState context only - no server duplication
            
            // CRITICAL FIX: Check for cancellation with polling during TTS generation
            let audioPath: string | null = null;
            
            // Start TTS generation with promise race against cancellation polling
            const ttsPromise = audioProviderManager.generateAndRouteTTS(
              context.callSid,
              operation.text,
              operation.turnIndex,
              connectionType
            );
            
            // Poll for cancellation every 100ms during TTS generation
            const cancellationPoller = async (): Promise<never> => {
              while (!abortSignal.aborted) {
                await new Promise(resolve => setTimeout(resolve, 100));
              }
              throw new Error(`Turn ${operation.turnIndex} cancelled during TTS generation`);
            };
            
            try {
              // Race between TTS completion and cancellation polling
              audioPath = await Promise.race([ttsPromise, cancellationPoller()]);
            } catch (error: any) {
              if (error.message.includes('cancelled during TTS generation')) {
                console.log(`[XState-TTS-PROCESSING] ${context.callSid}: Turn ${operation.turnIndex} cancelled during TTS generation - aborting`);
                self.send({ type: 'INTERNAL_TTS_PROCESSING_ERROR' });
                return;
              }
              throw error; // Re-throw other errors
            }
            
            // 🚀 PHASE 3.2: Post-generation cancellation check
            if (abortSignal.aborted) {
              console.log(`[XState-TTS-PROCESSING] ${context.callSid}: Turn ${operation.turnIndex} cancelled after generation - discarding audio`);
              self.send({ type: 'INTERNAL_TTS_PROCESSING_ERROR' });
              return;
            }
            
            if (audioPath) {
              console.log(`[XState-TTS-PROCESSING] ${context.callSid}: ✅ TTS processing completed successfully`);
              self.send({ type: 'INTERNAL_TTS_PROCESSING_SUCCESS' });
            } else {
              console.error(`[XState-TTS-PROCESSING] ${context.callSid}: ❌ TTS processing failed`);
              self.send({ type: 'INTERNAL_TTS_PROCESSING_ERROR' });
            }
            
          } catch (error) {
            console.error(`[XState-TTS-PROCESSING] ${context.callSid}: ❌ Error in TTS processing:`, error);
            self.send({ type: 'INTERNAL_TTS_PROCESSING_ERROR' });
          }
        })();
      },
      
      selectCaseAutomatically: assign(({ context }) => { // Corrected context destructuring
        const availableCases = {
          case1: {
            scenario: 'Chest Pain',
            symptoms: 'Chest pain, difficulty breathing',
            history: 'Patient has a history of heart disease',
            initial_question: 'Okay, tell me about this chest pain.',
            questions: [] // Added to satisfy CaseData
          },
          case2: {
            scenario: 'Headache',
            symptoms: 'Headache, nausea',
            history: 'Patient has a history of migraines',
            initial_question: 'Got it. Can you describe the headache for me?',
            questions: [] // Added to satisfy CaseData
          }
        };
        const case_keys = Object.keys(availableCases);
        const random_index = Math.floor(Math.random() * case_keys.length);
        const selectedCaseKey = case_keys[random_index] as keyof typeof availableCases;

        const selectedCaseDetails: CaseData = {
          id: selectedCaseKey,
          scenario: availableCases[selectedCaseKey].scenario,
          symptoms: availableCases[selectedCaseKey].symptoms,
          history: availableCases[selectedCaseKey].history,
          initial_question: availableCases[selectedCaseKey].initial_question,
          questions: availableCases[selectedCaseKey].questions
        };
        
        console.log(`[XState:${context.callSid}] Running selectCaseAutomatically action`);
        
        // Set the case data and the agreed-upon response text
        return {
          caseData: selectedCaseDetails,
          responseText: 'The case is now loaded. Please begin when you are ready.', // Corrected message
          waitingForUserInput: false,
          ttsAgenda: 'speak' 
        };
      }),
      resetTurnSpecificContext: assign(({ context }) => {
        console.log(`[XState:${context.callSid}] RESET: VAD state before reset - hasSpeech=${context.hasSpeechDetected}, speechFrames=${context.speechFramesInCurrentTurn}, silenceFrames=${context.consecutiveSilenceFrames}`);
        
        // GRACE PERIOD AWARE RESET: Only clear lastUserUtterance if we're not in a potential grace period
        // WORKING COMMIT APPROACH: More carefully manage context between turns
        // Only preserve context when actually within a valid grace period window
        const now = Date.now();
        let shouldPreserveForGracePeriod = false;
        let immediateLastUtterance = '';
        
        // 🚀 UNIFIED GRACE PERIOD CHECK FOR CONTEXT PRESERVATION
        // Uses the same two-phase logic as the main grace period evaluation
        if (context.lastUserUtterance) {
          const graceEval = evaluateUnifiedGracePeriod(context, now);
          
          if (graceEval.withinGracePeriod) {
            shouldPreserveForGracePeriod = true;
            // Extract only the most recent utterance from the accumulated text
            const utteranceParts = context.lastUserUtterance.split(/[.!?]+\s+/);
            immediateLastUtterance = utteranceParts[utteranceParts.length - 1].trim();
            console.log(`[RESET-UNIFIED-GRACE] ${graceEval.phase} phase (${graceEval.gracePeriodType}) - preserving: "${immediateLastUtterance}"`);
            console.log(`[RESET-UNIFIED-GRACE] Debug:`, graceEval.debugInfo);
          } else {
            console.log(`[RESET-GRACE-EXPIRED] Grace period expired (${graceEval.gracePeriodType}) - not preserving`);
            console.log(`[RESET-GRACE-EXPIRED] Debug:`, graceEval.debugInfo);
          }
        } else {
          console.log(`[RESET-GRACE-NONE] No lastUserUtterance - not preserving`);
        }
        
        console.log(`[RESET-GRACE-AWARE] Grace period preservation: shouldPreserve=${shouldPreserveForGracePeriod}, immediate utterance: "${immediateLastUtterance}"`);
        console.log(`[RESET-DEBUG] Before reset - turnFinalizedAt: ${context.turnFinalizedAt}, audioPlaybackStartTime: ${context.audioPlaybackStartTime}`);
        
        // WORKING COMMIT APPROACH: Clear transcript accumulation to prevent cross-turn combinations
        // Don't preserve currentText - this was causing over-accumulation
        const resetInfrastructure = context.infrastructure ? {
          ...context.infrastructure,
          transcript: {
            ...context.infrastructure.transcript,
            accumulation: {
              ...context.infrastructure.transcript.accumulation,
              // CLEAR currentText to prevent over-accumulation (working commit approach)
              currentText: '', // Clear accumulated text between turns
              isAccumulating: false,
              lastProcessedTime: Date.now()
            }
          }
        } : context.infrastructure;
        console.log(`[RESET-TRANSCRIPT] Cleared transcript accumulation to prevent over-accumulation`);
        
        // CRITICAL: Reset speech detection in server state for clean turn boundaries
        // This prevents false turn triggers when user goes silent after TTS
        try {
          const { calls } = require('./server');
          const callState = calls[context.callSid];
          if (callState) {
            console.log(`[SPEECH-RESET] ${context.callSid}: Resetting hasSpeechDetected to false at clean turn boundary`);
            callState.hasSpeechDetected = false;
          }
        } catch (error) {
          console.error(`[SPEECH-RESET] ${context.callSid}: Error resetting speech detection state:`, error);
        }
        
        // CRITICAL FIX: Validate turnFinalizedAt before preserving
        // Preserve turnFinalizedAt as-is without age filtering
        let validTurnFinalizedAt = context.turnFinalizedAt;

        const result = {
          // WORKING COMMIT APPROACH: Preserve only immediate previous utterance during grace periods
          lastUserUtterance: shouldPreserveForGracePeriod ? immediateLastUtterance : '', // Only immediate utterance, not entire conversation
          tempAccumulatedText: '', // Clear accumulated text to prevent cross-turn combinations
          speechFramesInCurrentTurn: 0,
          consecutiveSilenceFrames: 0,
          hasSpeechDetected: false,
          llmResponseReceived: false, // Ensure this is reset too
          turnFinalized: false, // And this
          vadWaitLogged: false, // Reset VAD wait logging flag
          interruptionTimestamp: context.interruptionTimestamp, // Preserve interruption timestamp without clearing
          // responseText: undefined, // Optionally clear responseText if it shouldn't persist
          // currentTurnStartTime: undefined, // Reset if new turn starts strictly with user speech
          waitingForUserInput: true, // Explicitly set to true, though often set by target state's entry
          // CRITICAL: Preserve grace period context during VAD reset
          audioPlaybackStartTime: context.audioPlaybackStartTime,
          audioPlaybackStarted: context.audioPlaybackStarted,
          turnFinalizedAt: validTurnFinalizedAt, // CRITICAL FIX: Only preserve if recent and valid
          userStoppedAt: context.userStoppedAt, // CRITICAL: Preserve userStoppedAt for grace period logic!
          previousUserStoppedAt: context.previousUserStoppedAt, // CRITICAL FIX: MISSING - Preserve previousUserStoppedAt for grace period calculation!
          previousTurnFinalizedAt: context.previousTurnFinalizedAt, // TURN-FINALIZATION-TIMING: Preserve previousTurnFinalizedAt for accurate grace period calculation
          // CRITICAL FIX: Reset infrastructure transcript state
          infrastructure: resetInfrastructure
        };
        console.log(`[RESET-DEBUG] After reset - preserving turnFinalizedAt: ${result.turnFinalizedAt}, userStoppedAt: ${result.userStoppedAt}, audioPlaybackStartTime: ${result.audioPlaybackStartTime}`);
        console.log(`[GRACE-PERIOD-TIMING-DEFENSIVE] resetTurnSpecificContext preserving previousUserStoppedAt=${result.previousUserStoppedAt}`);
        console.log(`[TURN-FINALIZATION-TIMING] resetTurnSpecificContext preserving previousTurnFinalizedAt=${result.previousTurnFinalizedAt}`);
        return result;
      }),
      logGuardSuccess: ({ context, event }) => {
         // Access event properties safely based on type
        let detail = event.type;
        if (event.type === 'DTMF_INPUT') detail += ` '${event.digit}'`;
        console.log(`[XState Guard] SUCCESS: Guard passed for event '${detail}' in state '${context.currentState}'`);
      },
      logGuardFailure: ({ context, event }) => {
        let detail = event.type;
        if (event.type === 'DTMF_INPUT') detail += ` '${event.digit}'`;
        console.log(`[XState Guard] FAILURE: Guard failed for event '${detail}' in state '${context.currentState}'`);
      },
      assignWelcomeMessage: assign({ // Action to set the initial welcome message
        responseText: ({ context }) => {
          console.log(`[XState:${context.callSid}] Setting welcome message for sessionType: ${sessionType}`);
          
          if (sessionType === 'tutoring') {
            return 'Welcome to your personalized medical learning session! I\'m here to help you learn through interactive conversation and adaptive teaching. Let me prepare your learning environment...';
          } else {
            return 'Welcome! Are you ready to start a scenario?'; // COMBINED MESSAGE for simulation
          }
        }
      }),
      assignReadinessPrompt: assign({ // NEW ACTION
        responseText: ({ context }) => {
          console.log(`[XState:${context.callSid}] Setting readiness prompt`);
          return 'Are you ready to start a scenario?';
        }
      }),
      logWelcomeMessage: ({ context }) => { // Action to log the welcome message assignment
        console.log(`[XState:${context.callSid}] Welcome message set to: "${context.responseText}"`);
        // Removed global context cache update - handle context via state machine if possible
      },
      
      assignCaseDataAndInitialResponse: assign(({ context, event }) => { 
        // Ensure event is of type INTERNAL_SET_CASE and has data
        if (event.type === 'INTERNAL_SET_CASE') {
          // The primary condition for INTERNAL_SET_CASE
          // No longer use event.data.initial_question
          if (event.data) { // Check if data exists
            return {
              ...context,
              caseData: event.data,
              responseText: 'The case is loaded. Please begin when you are ready.', // MODIFIED
              waitingForUserInput: false, // System will speak next
            };
          }
        }
        // Fallback if event type is not INTERNAL_SET_CASE, or if data is missing.
        console.warn('[assignCaseDataAndInitialResponse] Event not INTERNAL_SET_CASE or data missing, using fallback responseText.', event);
        
        let updatedCaseData = context.caseData; // Default to current caseData
        // If the event was indeed INTERNAL_SET_CASE but missed the primary condition,
        // still try to update caseData if present on the event.
        if (event.type === 'INTERNAL_SET_CASE' && event.data) {
          updatedCaseData = event.data;
        }

        return {
          ...context,
          caseData: updatedCaseData,
          responseText: 'The case is loaded. Please begin when you are ready.', // MODIFIED
          waitingForUserInput: false,
        };
      }),
      assignUserUtterance: assign({ // Defined action
        lastUserUtterance: ({ event }) => event.type === 'USER_UTTERANCE' ? event.transcription : '',
        hasSpeechDetected: true, // Assume speech if this is called
        speechFramesInCurrentTurn: ({context}) => context.speechFramesInCurrentTurn +1 // crude, but indicates activity
      }),
      incrementConsecutiveSilenceFrames: assign({ // Defined action
        consecutiveSilenceFrames: ({ context }) => context.consecutiveSilenceFrames + 1
      }),
      // Transcript actions (only XState-compatible ones)
      startHistoryTakingTracking: transcriptActions.startHistoryTakingTracking,
      saveHistoryTakingTranscript: transcriptActions.saveHistoryTakingTranscript,
      startExaminationTracking: transcriptActions.startExaminationTracking,
      saveExaminationTranscript: transcriptActions.saveExaminationTranscript,
      startMarkingTracking: transcriptActions.startMarkingTracking,
      saveMarkingTranscript: transcriptActions.saveMarkingTranscript,
      startFeedbackTracking: transcriptActions.startFeedbackTracking,
      saveFeedbackTranscript: transcriptActions.saveFeedbackTranscript,
      completeSession: transcriptActions.completeSession,
      // PRODUCTION-GRADE: Action to track examination (TTS handled by sendToTts action)
      sendToTtsAndTrackExamination: ({ context, self }) => {
        console.log(`[XState:${context.callSid}] Tracking examination progress and sending TTS`);
        
        if (context.responseText) {
          // Track assistant response in examination phase
          if (context.currentState?.startsWith('examination')) {
            const { addExaminationResponse } = transcriptActions;
            addExaminationResponse(context, 'assistant', context.responseText);
            console.log(`[EXAMINATION-TRACKING] Added assistant response: "${context.responseText}"`);
          }
          
          // CRITICAL FIX: Send TTS event to trigger XState TTS processing
          console.log(`[XState:${context.callSid}] Sending examination TTS: "${context.responseText}"`);
          
          // Get connection type for TTS routing
          const connectionType = context.infrastructure?.connection?.type;
          
          // TURN-BASED GRACE F9: Add turn lifecycle integration - TTS started
          try {
            const { turnCoordinationService } = require('./services/TurnCoordinationService');
            turnCoordinationService.updateTurnState(context.callSid, {
              stage: TurnLifecycleStage.TTS_STARTED,
              timestamp: Date.now(),
              assistantText: context.responseText
            });
          } catch (error) {
            console.error(`[TURN-LIFECYCLE] ${context.callSid}: Error updating turn state after examination TTS start:`, error);
          }
          
          // Send GENERATE_TTS event to trigger XState TTS processing (same as sendToTts action)
          self.send({ 
            type: 'GENERATE_TTS', 
            text: context.responseText, 
            callSid: context.callSid, 
            turnIndex: context.turnNumber || 0,
            connectionType: connectionType || 'twilio'
          });
        } else {
          console.warn(`[XState:${context.callSid}] TTS requested but responseText is empty`);
        }
      },
      
      // SIMPLIFIED CASCADING INTERRUPTION (working commit style)
      handleCascadingInterruption: ({ context, event }) => {
        const typedEvent = event as OsceEvent & { type: 'USER_UTTERANCE', transcription: string };
        const newText = typedEvent.transcription || '';
        
        // SIMPLIFIED: Use direct context access instead of complex server state lookups
        const previousText = context.tempAccumulatedText || context.lastUserUtterance || '';
        const combinedText = previousText ? `${previousText} ${newText}`.trim() : newText;
        
        console.log(`[CASCADING-INTERRUPT-SIMPLE] Cancelling and combining: "${previousText}" + "${newText}" = "${combinedText}"`);
        
        // Use shared cancellation logic
        cancelAllPreviousTurnsHelper(context, 'CASCADING', combinedText);
      },
      
      applyCombinedUtterance: assign(({ context, event }) => {
        const typedEvent = event as OsceEvent & { type: 'USER_UTTERANCE', transcription: string };
        const newText = typedEvent.transcription || '';
        
        // SIMPLIFIED: Use direct context access instead of complex server state lookups
        const previousText = context.tempAccumulatedText || context.lastUserUtterance || '';
        const combinedText = previousText ? `${previousText} ${newText}`.trim() : newText;
        
        console.log(`[COMBINED-UTTERANCE-SIMPLE] Updating context with combined utterance: "${combinedText}"`);
        console.log(`[GRACE-PERIOD-TIMING-DEFENSIVE] applyCombinedUtterance preserving previousUserStoppedAt=${context.previousUserStoppedAt}`);
        console.log(`[TURN-FINALIZATION-TIMING] applyCombinedUtterance preserving previousTurnFinalizedAt=${context.previousTurnFinalizedAt}`);
        
        return {
          lastUserUtterance: combinedText,
          tempAccumulatedText: combinedText,
          turnNumber: (context.turnNumber || 0) + 1,
          waitingForUserInput: false,
          // Reset pipeline state for restart
          llmResponseReceived: false,
          responseText: undefined,
          currentLlmOperationId: generateUniqueOperationId(context.callSid), // New operation ID to invalidate old responses
          // Preserve grace period context
          turnFinalizedAt: context.turnFinalizedAt,
          audioPlaybackStartTime: context.audioPlaybackStartTime,
          previousUserStoppedAt: context.previousUserStoppedAt, // CRITICAL FIX: Preserve previousUserStoppedAt for grace period calculation
          previousTurnFinalizedAt: context.previousTurnFinalizedAt // TURN-FINALIZATION-TIMING: Preserve previousTurnFinalizedAt for accurate grace period calculation
        };
      }),

      // ============================================================================
      // TUTORING SYSTEM ACTIONS
      // ============================================================================
      
      logTutoringSessionComplete: ({ context }) => {
        console.log(`[Tutoring] Session completed for user ${context.userId}, session: ${context.tutoringSessionId}`);
        console.log(`[Tutoring] Experience gained: ${context.experienceGained}, Prestige level up: ${context.prestigeLevelUp}`);
      },

      logTutoringError: ({ context }) => {
        console.error(`[Tutoring] Error in session ${context.tutoringSessionId}: ${context.errorMessage}`);
      },

      // Removed assignTutoringResponse - event handling is done directly in state transitions

      initializeTutoringContext: assign({
        sessionStartTime: () => Date.now(),
        tutoringMode: () => true,
        currentState: () => 'tutoring_initialization',
        experienceGained: () => 0,
        prestigeLevelUp: () => false,
        achievementsUnlocked: () => [],
        conceptsIntroduced: () => [],
        teachingStrategy: () => 'socratic_questioning',
        assessmentRequired: () => false,
        shouldEndSession: () => false
      }),
      
      
      // CANONICAL TURN COMPLETION HANDLER - XState as Single Source of Truth
      // NO FALLBACKS - Clean architecture that just works
      canonicalTurnCompletionHandler: assign(({ context, event, self }) => {
        const coordEvent = event as any;
        const transcriptText = coordEvent.transcriptText || coordEvent.transcription || '';
        const userStoppedAt = coordEvent.userStoppedAt || Date.now();

        // GRACE PERIOD FIX: Use REGISTRY-BASED previous turn access
        const registry = TurnTimestampRegistryManager.getOrCreateRegistry(context.callSid || 'unknown');
        const previousUserUtterance = registry.getPreviousUtteranceText(context.turnNumber || 0);

        // CRITICAL FIX: Check if CURRENT turn has interruption timestamp (meaning it interrupted previous assistant response)
        let currentTurnRecord = null;
        try {
          currentTurnRecord = registry.getTurnTimestamps(context.turnNumber || 0);
        } catch (error) {
          // Turn doesn't exist in registry yet - this is normal for new turns
          currentTurnRecord = null;
        }
        const currentTurnIsInterruption = !!(currentTurnRecord && currentTurnRecord.interruptionTimestamp);

        console.log(`[GRACE-PERIOD-REGISTRY-FIX] Current transcript: "${transcriptText}"`);
        console.log(`[GRACE-PERIOD-REGISTRY-FIX] Previous utterance (from registry): "${previousUserUtterance}"`);
        console.log(`[GRACE-PERIOD-REGISTRY-FIX] Current turn is interruption: ${currentTurnIsInterruption}`);
        if (currentTurnRecord && currentTurnRecord.interruptionTimestamp) {
          console.log(`[GRACE-PERIOD-REGISTRY-FIX] Current turn interruption timestamp: ${currentTurnRecord.interruptionTimestamp}`);
        }

        console.log(`[CANONICAL-TURN] ${context.callSid}: Processing turn - transcript="${transcriptText}", currentTurnIsInterruption=${currentTurnIsInterruption}`);

        // Normal turn processing (current turn is NOT an interruption)
        if (!currentTurnIsInterruption) {
          const turnFinalizedAt = Date.now();
          
          // GRACE PERIOD FIX: Check if this turn should be combined with previous turn
          // Use existing grace period evaluation for consecutive utterances
          const graceResult = evaluateUnifiedGracePeriod(context, turnFinalizedAt);
          
          let finalText = transcriptText;
          let graceDecision = undefined;
          
          if (graceResult.withinGracePeriod && previousUserUtterance) {
            // Combine with previous utterance
            finalText = `${previousUserUtterance} ${transcriptText}`.trim();
            graceDecision = {
              action: 'combineWithPrevious',
              rationale: `${graceResult.gracePeriodType}: ${graceResult.phase}`,
              combinedText: finalText,
              gracePeriodType: graceResult.gracePeriodType,
              phase: graceResult.phase
            };
            console.log(`[CANONICAL-TURN] Grace period combination: "${previousUserUtterance}" + "${transcriptText}" = "${finalText}"`);
            console.log(`[CANONICAL-TURN] Grace period rationale: ${graceDecision.rationale}`);
          } else {
            console.log(`[CANONICAL-TURN] No grace period combination - processing as new turn: "${transcriptText}"`);
            if (!graceResult.withinGracePeriod) {
              console.log(`[CANONICAL-TURN] Grace period expired: ${graceResult.gracePeriodType} (${graceResult.phase})`);
            }
          }
          
          // GRACE PERIOD FIX: Turn already registered in handleCoordinatedTurnComplete with correct utterance
          // No duplicate registration needed here
          console.log(`[CANONICAL-TURN] Turn ${context.turnNumber} already in registry, proceeding to TTS`);
          
          console.log(`[CANONICAL-TURN-DEBUG] Setting lastUserUtterance="${finalText}", tempAccumulatedText="${finalText}"`);
          
          return {
            lastUserUtterance: finalText,
            tempAccumulatedText: finalText,
            userStoppedAt: userStoppedAt,
            waitingForUserInput: false,
            turnFinalizedAt: Date.now(),
            // ARCHITECTURE FIX: Keep same turn number - user utterance and AI response share same turn
            // Turn only increments when complete user-AI cycle finishes (TTS_FINISHED)
            turnNumber: context.turnNumber,
            turnBarrier: {
              hasFinal: false,
              hasSilence: false,
              transcript: undefined,
              userStoppedAt: undefined
            },
            playback: context.playback,
            interruption: {
              interruptedThisTurn: false,
              lastInterruptionAt: undefined
            },
            registry: context.registry,
            graceDecision: graceDecision
          };
        }
        
        // Current turn IS an interruption - process with grace period combination
        console.log(`[CANONICAL-TURN] Current turn is an interruption - evaluating accumulative grace chain`);
        
        const turnFinalizedAt = Date.now();
        
        // ARCHITECTURE FIX: Replace pairwise evaluation with accumulative registry chaining
        // This enables true three-part conversations (height + weight + age) instead of pairwise only
        
        // Import grace configuration for timing windows
        const { DEFAULT_GRACE_CONFIG } = require('./services/grace/evaluateGrace');
        
        // Get previous turn for timing analysis
        const previousTurn = context.registry.getPreviousTurn(context.turnNumber);
        
        // DEBUG: Check registry state
        console.log(`[GRACE-DEBUG] Looking for previous turn: current=${context.turnNumber}, previous=${context.turnNumber - 1}`);
        console.log(`[GRACE-DEBUG] Registry has previous turn: ${!!previousTurn}`);
        if (previousTurn) {
          console.log(`[GRACE-DEBUG] Previous turn data:`, { 
            turnIndex: previousTurn.turnIndex, 
            utteranceText: previousTurn.utteranceText?.substring(0, 50),
            ttsStartedAt: previousTurn.ttsStartedAt 
          });
        }
        
        // GRACE CHAIN LOGIC: Determine if we should continue/start accumulation
        let shouldAddToChain = false;
        let gracePeriodRationale = '';
        
        if (!previousTurn) {
          // First turn - no previous turn to chain with
          gracePeriodRationale = 'First turn - no previous turn available for chaining';
          console.log(`[GRACE-CHAIN] ${gracePeriodRationale}`);
        } else {
          // TIMING ANALYSIS: Check if we're within grace period windows
          const currentInterruptionTimestamp = currentTurnRecord?.interruptionTimestamp || Date.now();
          const timeSinceUserStopped = currentInterruptionTimestamp - previousTurn.userStoppedAt;
          const timeSinceTurnFinalized = currentInterruptionTimestamp - previousTurn.turnFinalizedAt;

          console.log(`[GRACE-CHAIN] TIMING ANALYSIS:`);
          console.log(`[GRACE-CHAIN] - previousTurn.userStoppedAt: ${previousTurn.userStoppedAt}`);
          console.log(`[GRACE-CHAIN] - previousTurn.turnFinalizedAt: ${previousTurn.turnFinalizedAt}`);
          console.log(`[GRACE-CHAIN] - previousTurn.ttsStartedAt: ${previousTurn.ttsStartedAt || 'N/A'}`);
          console.log(`[GRACE-CHAIN] - currentInterruptionTimestamp: ${currentInterruptionTimestamp}`);
          console.log(`[GRACE-CHAIN] - timeSinceUserStopped: ${timeSinceUserStopped}ms`);
          console.log(`[GRACE-CHAIN] - timeSinceTurnFinalized: ${timeSinceTurnFinalized}ms`);

          if (!previousTurn.ttsStartedAt) {
            // PRE-TTS PHASE: Previous turn hasn't started TTS yet - always combine
            shouldAddToChain = true;
            gracePeriodRationale = 'Pre-TTS interruption - within unlimited grace window';
            console.log(`[GRACE-CHAIN] ✅ PRE-TTS GRACE: ${gracePeriodRationale}`);
          } else {
            // POST-TTS PHASE: Check if within 3-second window after TTS started
            const timeSinceTtsStart = currentInterruptionTimestamp - previousTurn.ttsStartedAt;
            const withinPostTtsWindow = timeSinceTtsStart <= DEFAULT_GRACE_CONFIG.postTtsWindowMs;
            
            console.log(`[GRACE-CHAIN] - timeSinceTtsStart: ${timeSinceTtsStart}ms`);
            console.log(`[GRACE-CHAIN] - postTtsWindowMs: ${DEFAULT_GRACE_CONFIG.postTtsWindowMs}ms`);
            console.log(`[GRACE-CHAIN] - withinPostTtsWindow: ${withinPostTtsWindow}`);
            
            if (withinPostTtsWindow) {
              shouldAddToChain = true;
              gracePeriodRationale = `Post-TTS interruption within grace window (${timeSinceTtsStart}ms <= ${DEFAULT_GRACE_CONFIG.postTtsWindowMs}ms)`;
              console.log(`[GRACE-CHAIN] ✅ POST-TTS GRACE: ${gracePeriodRationale}`);
            } else {
              shouldAddToChain = false;
              gracePeriodRationale = `Post-TTS interruption outside grace window (${timeSinceTtsStart}ms > ${DEFAULT_GRACE_CONFIG.postTtsWindowMs}ms)`;
              console.log(`[GRACE-CHAIN] ❌ OUTSIDE GRACE WINDOW: ${gracePeriodRationale}`);
            }
          }
        }
        
        // ACCUMULATIVE CHAINING: Add to or finalize grace chain
        let finalText = transcriptText;
        let chainActive = false;
        
        if (shouldAddToChain) {
          try {
            // Add current turn to accumulative chain
            const chainState = context.registry.addToGraceChain(context.turnNumber, transcriptText);
            finalText = chainState.accumulatedText;
            chainActive = chainState.isActive;
            
            console.log(`[GRACE-CHAIN] ✅ ADDED TO CHAIN: Turn ${context.turnNumber} accumulated`);
            console.log(`[GRACE-CHAIN] ✅ Chain State: active=${chainActive}, turnCount=${chainState.turnCount}`);
            console.log(`[GRACE-CHAIN] ✅ Accumulated Text: "${chainState.accumulatedText}"`);
            console.log(`[GRACE-CHAIN] ✅ Turn Indices: [${chainState.turnIndices.join(', ')}]`);
          } catch (error) {
            console.error(`[GRACE-CHAIN] ❌ Error adding to chain: ${error.message}`);
            // Fallback to individual processing
            finalText = transcriptText;
            chainActive = false;
            gracePeriodRationale = `Chain error - processing individually: ${error.message}`;
          }
        } else {
          // CHAIN FINALIZATION: Grace period expired, finalize any existing chain
          try {
            const activeChain = context.registry.getActiveGraceChain();
            if (activeChain.isActive) {
              const finalizedChain = context.registry.finalizeGraceChain();
              console.log(`[GRACE-CHAIN] 🏁 FINALIZED CHAIN: "${finalizedChain.finalText}" (${finalizedChain.turnIndices.length} turns)`);
              
              // Process current turn individually since grace period expired
              finalText = transcriptText;
              chainActive = false;
              
              // Note: The finalized chain text should have already been sent to LLM in previous turn
              console.log(`[GRACE-CHAIN] Current turn processed individually due to grace period expiration`);
            } else {
              // No active chain - process individually
              finalText = transcriptText;
              chainActive = false;
              console.log(`[GRACE-CHAIN] No active chain - processing individually`);
            }
          } catch (error) {
            console.error(`[GRACE-CHAIN] ❌ Error finalizing chain: ${error.message}`);
            finalText = transcriptText;
            chainActive = false;
          }
        }
        
        console.log(`[CANONICAL-TURN] Interruption turn ${context.turnNumber} - Chain Active: ${chainActive}`);
        console.log(`[CANONICAL-TURN] Grace Chain Decision: ${gracePeriodRationale}`);
        
        console.log(`[CANONICAL-TURN-DEBUG] Setting lastUserUtterance="${finalText}", tempAccumulatedText="${finalText}"`);
        
        // REGISTRY-TO-CONTEXT SYNCHRONIZATION: Sync registry state to XState context
        const activeRegistryChain = context.registry.getActiveGraceChain();
        
        console.log(`[CONTEXT-SYNC] Syncing registry chain to context:`);
        console.log(`[CONTEXT-SYNC] - registryChain.isActive: ${activeRegistryChain.isActive}`);
        console.log(`[CONTEXT-SYNC] - registryChain.accumulatedText: "${activeRegistryChain.accumulatedText}"`);
        console.log(`[CONTEXT-SYNC] - registryChain.turnCount: ${activeRegistryChain.turnCount}`);
        console.log(`[CONTEXT-SYNC] - chainActive (local): ${chainActive}`);
        
        // Determine if we should wait for more utterances (active chain) or proceed to LLM
        const shouldWaitForMoreUtterances = chainActive && activeRegistryChain.isActive;
        
        // Determine if TTS should be cut (interruption during active TTS)
        const shouldCutTts = previousTurn?.ttsStartedAt && !previousTurn?.ttsCompletedAt;
        
        console.log(`[CANONICAL-TURN] Context Update Summary:`);
        console.log(`[CANONICAL-TURN] - shouldWaitForMoreUtterances: ${shouldWaitForMoreUtterances}`);
        console.log(`[CANONICAL-TURN] - shouldCutTts: ${shouldCutTts}`);
        console.log(`[CANONICAL-TURN] - finalText: "${finalText}"`);
        
        return {
          lastUserUtterance: finalText,
          tempAccumulatedText: finalText,
          userStoppedAt: userStoppedAt,
          waitingForUserInput: shouldWaitForMoreUtterances,
          turnFinalizedAt: Date.now(),
          // ARCHITECTURE FIX: Keep same turn number - user utterance and AI response share same turn
          // Turn only increments when complete user-AI cycle finishes (TTS_FINISHED)
          turnNumber: context.turnNumber,
          // REGISTRY-TO-CONTEXT SYNC: Update context graceChain with registry state
          graceChain: {
            isActive: activeRegistryChain.isActive,
            accumulatedText: activeRegistryChain.accumulatedText,
            turnCount: activeRegistryChain.turnCount,
            mostRecentTurnContext: activeRegistryChain.isActive ? {
              turnFinalizedAt: turnFinalizedAt,
              ttsStreamingStartTime: undefined, // TTS hasn't started for current turn yet
              userStoppedAt: userStoppedAt,
              utteranceText: transcriptText
            } : undefined
          },
          turnBarrier: {
            hasFinal: false,
            hasSilence: false,
            transcript: undefined,
            userStoppedAt: undefined
          },
          playback: shouldCutTts ? {
            isPlaying: false,
            ttsStartedAt: undefined,
            ttsStoppedAt: Date.now()
          } : context.playback,
          interruption: {
            interruptedThisTurn: true, // Mark interruption occurred
            lastInterruptionAt: currentTurnRecord?.interruptionTimestamp || Date.now()
          },
          registry: context.registry
        };
      }),

      // CANONICAL BARRIER MANAGEMENT - XState as Single Source of Truth
      // Handle FINAL_TRANSCRIPT events to update turn barrier
      handleFinalTranscript: assign(({ context, event }) => {
        const transcriptEvent = event as any;
        const transcript = transcriptEvent.transcript || transcriptEvent.text || transcriptEvent.transcriptText || '';
        const timestamp = transcriptEvent.timestamp || Date.now();
        
        console.log(`[BARRIER-FINAL] ${context.callSid}: Final transcript received - "${transcript}"`);
        
        return {
          turnBarrier: {
            ...context.turnBarrier,
            hasFinal: true,
            transcript: transcript,
          },
          // Check if both conditions met
          ...(context.turnBarrier.hasSilence && {
            // Both conditions met - ready for turn completion
            lastUserUtterance: transcript,
            waitingForUserInput: false
          })
        };
      }),
      
      // Handle SILENCE_DETECTED events to update turn barrier
      handleSilenceDetected: assign(({ context, event }) => {
        const silenceEvent = event as any;
        const speechEndTime = silenceEvent.speechEndTime || silenceEvent.timestamp || Date.now();
        
        console.log(`[BARRIER-SILENCE] ${context.callSid}: Silence detected - speechEndTime=${speechEndTime}`);
        
        return {
          turnBarrier: {
            ...context.turnBarrier,
            hasSilence: true,
            userStoppedAt: speechEndTime,
          },
          // Check if both conditions met
          ...(context.turnBarrier.hasFinal && {
            // Both conditions met - ready for turn completion
            userStoppedAt: speechEndTime,
            waitingForUserInput: false
          })
        };
      }),
      
      // Check barrier completion and emit COORDINATED_TURN_COMPLETE if ready
      checkBarrierCompletion: ({ context, self }) => {
        const { turnBarrier } = context;
        
        if (turnBarrier.hasFinal && turnBarrier.hasSilence) {
          console.log(`[BARRIER-COMPLETE] ${context.callSid}: Both conditions met - emitting COORDINATED_TURN_COMPLETE`);
          
          // Emit the coordinated turn complete event with canonical data
          self.send({
            type: 'COORDINATED_TURN_COMPLETE',
            callSid: context.callSid,
            transcriptText: turnBarrier.transcript || '',
            userStoppedAt: turnBarrier.userStoppedAt || Date.now(),
            interruptionTimestamp: context.interruption.lastInterruptionAt,
            gracePeriodApplied: false // Will be determined by canonicalTurnCompletionHandler
          });
        } else {
          console.log(`[BARRIER-WAITING] ${context.callSid}: Waiting for conditions - final:${turnBarrier.hasFinal}, silence:${turnBarrier.hasSilence}`);
        }
      },
      
      // CANONICAL PLAYBACK STATE MANAGEMENT - Track TTS lifecycle
      handleTtsStarted: assign(({ context, event }) => {
        const ttsEvent = event as any;
        const timestamp = ttsEvent.timestamp || Date.now();
        const providerId = ttsEvent.providerId || 'unknown';
        const turnIndex = ttsEvent.turnIndex || context.turnNumber;
        
        console.log(`[PLAYBACK-START] ${context.callSid}: TTS started - provider=${providerId}, timestamp=${timestamp}, turn=${turnIndex}`);
        
        // CRITICAL FIX: Update turn timestamp registry with TTS started time
        try {
          if (context.registry && context.registry.hasTurn(turnIndex)) {
            context.registry.updateTurnTtsStarted(turnIndex, timestamp);
            console.log(`[TTS-REGISTRY-UPDATE] ${context.callSid}: Updated TTS started timestamp for turn ${turnIndex}`);
          } else {
            console.warn(`[TTS-REGISTRY-UPDATE] ${context.callSid}: Turn ${turnIndex} not found in registry, cannot update TTS timestamp`);
          }
        } catch (error) {
          console.error(`[TTS-REGISTRY-UPDATE] ${context.callSid}: Error updating TTS timestamp for turn ${turnIndex}:`, error.message);
        }
        
        // CRITICAL FIX: Also mark TTS started in gracePeriodChain for compatibility
        try {
          if (context.gracePeriodChain) {
            context.gracePeriodChain.markTtsStarted(turnIndex, timestamp);
            console.log(`[GRACE-CHAIN-TTS] ${context.callSid}: Marked TTS started for turn ${turnIndex} in grace period chain`);
          }
        } catch (error) {
          console.error(`[GRACE-CHAIN-TTS] ${context.callSid}: Error marking TTS started in grace period chain:`, error.message);
        }
        
        return {
          playback: {
            isPlaying: true,
            ttsStartedAt: timestamp,
            ttsStoppedAt: undefined
          }
        };
      }),
      
      handleTtsStopped: assign(({ context, event }) => {
        const ttsEvent = event as any;
        const timestamp = ttsEvent.timestamp || Date.now();
        const providerId = ttsEvent.providerId || 'unknown';
        
        console.log(`[PLAYBACK-STOP] ${context.callSid}: TTS stopped - provider=${providerId}, timestamp=${timestamp}`);
        
        return {
          playback: {
            isPlaying: false,
            ttsStartedAt: context.playback.ttsStartedAt,
            ttsStoppedAt: timestamp
          }
        };
      }),
      
      // Handle speech start during TTS (interruption detection)
      handleUserSpeechStart: assign(({ context, event }) => {
        const speechEvent = event as any;
        const timestamp = speechEvent.timestamp || Date.now();
        
        console.log(`[INTERRUPTION-DETECT] ${context.callSid}: User started speaking - timestamp=${timestamp}, ttsPlaying=${context.playback.isPlaying}`);
        
        // Detect interruption if TTS is currently playing
        if (context.playback.isPlaying) {
          console.log(`[INTERRUPTION-CONFIRMED] ${context.callSid}: Interruption detected during TTS playback`);
          
          return {
            interruption: {
              interruptedThisTurn: true,
              lastInterruptionAt: timestamp
            }
          };
        }
        
        return {};
      }),

      // DEPRECATED: Old problematic handler that threw exceptions for every turn  
      turnBasedGracePeriodHandler: assign(({ context, event, self }) => {
        // PHASE 2B: FANG-LEVEL GRACE PERIOD EVALUATION USING GracePeriodChain
        // XState now handles ALL grace period logic using sophisticated timing evaluation
        try {
          const transcriptText = (event as any).transcriptText || (event as any).transcription || '';
          const userStoppedAt = (event as any).userStoppedAt || Date.now();
          const eventInterruptionTimestamp = (event as any).interruptionTimestamp;
          
          console.log(`[GRACE-PERIOD-CHAIN] Processing utterance: "${transcriptText}"`);
          
          // Ensure gracePeriodChain is initialized (should be from context initialization)
          if (!context.gracePeriodChain) {
            console.error(`[GRACE-PERIOD-CHAIN] FATAL: GracePeriodChain is undefined! This should never happen with proper context preservation.`);
            throw new Error(`GracePeriodChain is undefined for ${context.callSid}. This indicates handleCoordinatedTurnComplete did not preserve the gracePeriodChain in the returned context.`);
          }
          
          const previousTurnNumber = context.turnNumber || 0;
          const newTurnNumber = previousTurnNumber + 1;
          
          // CRITICAL FIX: Properly retrieve interruption timestamp from registry instead of corrupt fallback
          // The root cause of grace period failure was this line using userStoppedAt (speech END time) instead of interruption START time
          console.log(`[TIMESTAMP-FLOW] ════════════════════════════════════════════════════════════`);
          console.log(`[TIMESTAMP-FLOW] GRACE PERIOD TIMESTAMP ARCHITECTURE DEBUG`);
          console.log(`[TIMESTAMP-FLOW] Turn ${newTurnNumber} for callSid: ${context.callSid}`);
          console.log(`[TIMESTAMP-FLOW] Input data:`);
          console.log(`[TIMESTAMP-FLOW]   - userStoppedAt (speech END): ${userStoppedAt} (${formatTimestamp(userStoppedAt)})`);
          console.log(`[TIMESTAMP-FLOW]   - eventInterruptionTimestamp: ${eventInterruptionTimestamp || 'UNDEFINED'}`);
          console.log(`[TIMESTAMP-FLOW]   - currentlyPlayingTurnIndex: ${context.currentlyPlayingTurnIndex}`);
          
          let userStartedAt: number;
          
          try {
            // First try: Use interruption timestamp from event if available
            if (eventInterruptionTimestamp) {
              userStartedAt = eventInterruptionTimestamp;
              console.log(`[TIMESTAMP-FLOW] ✅ SUCCESS: Using event interruption timestamp`);
              console.log(`[TIMESTAMP-FLOW]   - Source: COORDINATED_TURN_COMPLETE event`);
              console.log(`[TIMESTAMP-FLOW]   - userStartedAt (speech START): ${userStartedAt} (${formatTimestamp(userStartedAt)})`);
            } else {
              // REGISTRY LOOKUP: Get actual interruption timestamp for current turn if it was an interruption
              const registry = TurnTimestampRegistryManager.getOrCreateRegistry(context.callSid || 'unknown');
              console.log(`[TIMESTAMP-FLOW] 🔍 REGISTRY LOOKUP: Event has no interruption timestamp, checking registry`);
              
              // For interruptions, get the interruption timestamp from the most recent turn
              // This is the actual timestamp when user STARTED speaking (VAD detection time)
              const currentTurnIndex = context.currentlyPlayingTurnIndex || newTurnNumber;
              console.log(`[TIMESTAMP-FLOW]   - Looking for interruption in turn: ${currentTurnIndex}`);
              
              let registryInterruptionTimestamp: number | undefined;
              try {
                const turnRecord = registry.getTurnTimestamps(currentTurnIndex);
                registryInterruptionTimestamp = turnRecord.interruptionTimestamp;
              } catch (registryGetError) {
                console.log(`[TIMESTAMP-FLOW]   - Turn ${currentTurnIndex} not found in registry (expected for non-interruptions)`);
                registryInterruptionTimestamp = undefined;
              }
              
              if (registryInterruptionTimestamp) {
                userStartedAt = registryInterruptionTimestamp;
                console.log(`[TIMESTAMP-FLOW] ✅ SUCCESS: Using registry interruption timestamp`);
                console.log(`[TIMESTAMP-FLOW]   - Source: TurnTimestampRegistry for turn ${currentTurnIndex}`);
                console.log(`[TIMESTAMP-FLOW]   - userStartedAt (VAD START): ${userStartedAt} (${formatTimestamp(userStartedAt)})`);
                console.log(`[TIMESTAMP-FLOW]   - Gap analysis: userStartedAt vs userStoppedAt = ${userStoppedAt - userStartedAt}ms`);
              } else {
                // NOT an interruption - estimate user start time (this should be rare)
                // For non-interruptions, estimate user started speaking slightly before they stopped
                userStartedAt = userStoppedAt - 1000; // Estimate 1 second of speech
                console.log(`[TIMESTAMP-FLOW] ⚠️  ESTIMATION: No interruption detected - not an interruption scenario`);
                console.log(`[TIMESTAMP-FLOW]   - Source: Estimated (userStoppedAt - 1000ms)`);
                console.log(`[TIMESTAMP-FLOW]   - userStartedAt (ESTIMATED): ${userStartedAt} (${formatTimestamp(userStartedAt)})`);
                console.log(`[TIMESTAMP-FLOW]   - Reason: Normal turn completion, not interrupting TTS`);
              }
            }
          } catch (registryError) {
            console.error(`[TIMESTAMP-FLOW] ❌ ERROR: Registry access failed, using fallback estimation:`, registryError);
            // Fallback: Estimate user start time without corrupting with speech end time
            userStartedAt = userStoppedAt - 1000;
            console.log(`[TIMESTAMP-FLOW]   - Source: ERROR FALLBACK (userStoppedAt - 1000ms)`);
            console.log(`[TIMESTAMP-FLOW]   - userStartedAt (FALLBACK): ${userStartedAt} (${formatTimestamp(userStartedAt)})`);
          }
          
          console.log(`[TIMESTAMP-FLOW] FINAL RESULT:`);
          console.log(`[TIMESTAMP-FLOW]   - userStartedAt: ${userStartedAt} (when user STARTED speaking)`);
          console.log(`[TIMESTAMP-FLOW]   - userStoppedAt: ${userStoppedAt} (when user STOPPED speaking)`);
          console.log(`[TIMESTAMP-FLOW]   - Speech duration: ${userStoppedAt - userStartedAt}ms`);
          console.log(`[TIMESTAMP-FLOW] ════════════════════════════════════════════════════════════`);
          const turnFinalizedAt = Date.now();
          
          // PHASE 2B: Register current turn in GracePeriodChain
          console.log(`[GRACE-PERIOD-CHAIN] Registering turn ${newTurnNumber} for grace period evaluation`);
          context.gracePeriodChain.registerTurn(
            newTurnNumber,
            turnFinalizedAt,    // When turn was finalized
            userStartedAt,      // When user started speaking (interruption time if available)
            userStoppedAt,      // When user stopped speaking
            1,                  // Chain position (will be updated by evaluation)
            false               // Is chain continuation (will be determined by evaluation)
          );
          
          // CRITICAL FIX: Evaluate grace period for PREVIOUS turn using CURRENT utterance start time
          // This fixes the core timing bug where grace period was calculating gaps within same turn
          console.log(`[GRACE-EVALUATION] ════════════════════════════════════════════════════════════`);
          console.log(`[GRACE-EVALUATION] GRACE PERIOD EVALUATION DEBUG`);
          console.log(`[GRACE-EVALUATION] Current turn: ${newTurnNumber}, Previous turn: ${previousTurnNumber}`);
          
          let gracePeriodResult;
          if (previousTurnNumber > 0) {
            console.log(`[GRACE-EVALUATION] 🔍 EVALUATING: Previous turn ${previousTurnNumber} for grace period compatibility`);
            console.log(`[GRACE-EVALUATION] Input to evaluateTurn():`);
            console.log(`[GRACE-EVALUATION]   - previousTurnIndex: ${previousTurnNumber}`);
            console.log(`[GRACE-EVALUATION]   - currentUtteranceStartedAt: ${userStartedAt} (${formatTimestamp(userStartedAt)})`);
            
            // Get previous turn timing info for logging
            const previousTurnTiming = context.gracePeriodChain.getTurnTiming(previousTurnNumber);
            if (previousTurnTiming) {
              console.log(`[GRACE-EVALUATION] Previous turn timing context:`);
              console.log(`[GRACE-EVALUATION]   - turnFinalizedAt: ${previousTurnTiming.turnFinalizedAt} (${formatTimestamp(previousTurnTiming.turnFinalizedAt)})`);
              console.log(`[GRACE-EVALUATION]   - ttsStartedAt: ${previousTurnTiming.ttsStartedAt || 'UNDEFINED'} ${previousTurnTiming.ttsStartedAt ? `(${formatTimestamp(previousTurnTiming.ttsStartedAt)})` : ''}`);
              console.log(`[GRACE-EVALUATION]   - userStoppedAt: ${previousTurnTiming.userStoppedAt} (${formatTimestamp(previousTurnTiming.userStoppedAt)})`);
            }
            
            gracePeriodResult = context.gracePeriodChain.evaluateTurn(previousTurnNumber, userStartedAt);
            
            console.log(`[GRACE-EVALUATION] ✅ EVALUATION RESULT:`);
            console.log(`[GRACE-EVALUATION]   - inGracePeriod: ${gracePeriodResult.inGracePeriod ? '✅ YES' : '❌ NO'}`);
            console.log(`[GRACE-EVALUATION]   - type: ${gracePeriodResult.type || 'N/A'}`);
            console.log(`[GRACE-EVALUATION]   - gap: ${gracePeriodResult.gap}ms`);
            console.log(`[GRACE-EVALUATION]   - reason: ${gracePeriodResult.reason}`);
            console.log(`[GRACE-EVALUATION]   - calculation: ${gracePeriodResult.debugInfo.calculation}`);
            
            if (gracePeriodResult.inGracePeriod) {
              console.log(`[GRACE-EVALUATION] 🎉 COMBINATION SUCCESS: Turn ${newTurnNumber} will be combined with previous turn ${previousTurnNumber}`);
            } else {
              console.log(`[GRACE-EVALUATION] 🚫 COMBINATION FAILED: Turn ${newTurnNumber} will be processed as separate turn`);
            }
          } else {
            console.log(`[GRACE-EVALUATION] 🔄 FIRST TURN: Turn ${newTurnNumber} is the first turn - no previous turn to interrupt`);
            gracePeriodResult = {
              inGracePeriod: false,
              type: undefined,
              gap: 0,
              reason: 'First turn - no previous turn to interrupt',
              debugInfo: {
                turnIndex: newTurnNumber,
                ttsStarted: false,
                timingGap: 0,
                threshold: -1,
                calculation: 'First turn'
              }
            };
            console.log(`[GRACE-EVALUATION] ✅ FIRST TURN RESULT: Processing as standalone turn`);
          }
          
          console.log(`[GRACE-EVALUATION] ════════════════════════════════════════════════════════════`);
          
          // PHASE 2B: Update chain state based on evaluation result
          const chainState = context.gracePeriodChain.updateChainState(
            newTurnNumber,
            gracePeriodResult,
            transcriptText
          );
          
          console.log(`[GRACE-PERIOD-EVALUATION] Turn ${newTurnNumber} result:`, gracePeriodResult.reason);
          console.log(`[GRACE-PERIOD-CHAIN-STATE] Chain active: ${chainState.isActive}, accumulated: "${chainState.accumulatedText}"`);
          
          // PHASE 2B-3: Determine final text based on grace period result
          const finalText = chainState.isActive ? chainState.accumulatedText : transcriptText;
          const shouldCombine = chainState.isActive && chainState.turnCount > 1;
          
          if (shouldCombine) {
            console.log(`[GRACE-PERIOD-COMBINATION] ✅ COMBINING turns (${chainState.turnCount} utterances): "${finalText}"`);
          } else {
            console.log(`[GRACE-PERIOD-INDIVIDUAL] ⭕ INDIVIDUAL turn: "${finalText}"`);
          }
          
          return {
            lastUserUtterance: finalText,           // Combined text if in grace period, individual otherwise
            tempAccumulatedText: finalText,         // Same as lastUserUtterance for LLM processing
            userStoppedAt: userStoppedAt,
            previousUserStoppedAt: context.userStoppedAt,
            previousTurnFinalizedAt: context.turnFinalizedAt,
            turnNumber: newTurnNumber,
            waitingForUserInput: false,
            turnFinalizedAt: turnFinalizedAt,
            interruptionTimestamp: eventInterruptionTimestamp,
            // Store chain state info for debugging
            _gracePeriodActive: chainState.isActive,
            _gracePeriodTurnCount: chainState.turnCount,
            // CRITICAL FIX: Preserve Grace Period Chain instance to prevent destruction
            gracePeriodChain: context.gracePeriodChain
          };
        } catch (error) {
          console.error(`[GRACE-PERIOD-CHAIN] FATAL: Error in grace period evaluation - this should not happen with fixed architecture:`, error);
          // Root cause elimination: Grace Period Chain should never fail with proper lifecycle management
          // Removing fallback registration that caused "Turn X already registered" violations
          throw error; // Let the error bubble up to identify any remaining root causes
        }
      }),
      
      // ============================================================================
      // CONVERSATION HISTORY UPDATE ACTION
      // ============================================================================
      
      updateConversationWithUserInput: assign(({ context }) => {
        // GRACE PERIOD COMBINATION FIX: Update conversation array with user input
        // This ensures the LLM receives proper conversation context including grace period combinations
        try {
          const finalText = context.lastUserUtterance || '';
          
          if (!finalText.trim()) {
            console.log(`[CONVERSATION-UPDATE] ${context.callSid}: No user utterance to add to conversation`);
            return {};
          }
          
          console.log(`[CONVERSATION-UPDATE] ${context.callSid}: Adding user message to conversation: "${finalText}"`);
          
          const userMessage = {
            speaker: 'caller' as const,
            text: finalText,
            timestamp: new Date().toISOString(),
            xstate_state: getMainState(context.currentState || 'unknown'),
            interrupted: false, // User messages are not interrupted
            turnIndex: context.turnNumber || 0
          };
          
          const updatedConversation = [
            ...(context.conversation || []),
            userMessage
          ];
          
          console.log(`[CONVERSATION-UPDATE] ${context.callSid}: Updated conversation array length: ${updatedConversation.length}`);
          
          return {
            conversation: updatedConversation
          };
          
        } catch (error) {
          console.error(`[CONVERSATION-UPDATE] ${context.callSid}: Error updating conversation with user input:`, error);
          return {}; // Return empty to preserve existing conversation
        }
      }),
      
      // ============================================================================
      // ACTIVITY TIMER ACTIONS (Missing actions fix)
      // ============================================================================
      
      manageActivityTimers: assign(({ context, spawn }) => {
        const now = Date.now();
        
        // TODO: Implement state-based timer control with StateType meta
        // XState v5 meta access pattern needs further investigation
        // For now, always run timers (maintains existing behavior)
        const shouldRunTimers = true;
        
        // Stop any existing activity timers first (XState v5 compatible)
        if (context.activityTracking?.warningTimerRef) {
          context.activityTracking.warningTimerRef.send({ type: 'xstate.stop' });
        }
        if (context.activityTracking?.timeoutTimerRef) {
          context.activityTracking.timeoutTimerRef.send({ type: 'xstate.stop' });
        }
        
        if (!shouldRunTimers) {
          console.log(`[ACTIVITY] Timers disabled for current state (${context.callSid})`);
          return {
            activityTracking: {
              ...context.activityTracking,
              lastActivity: now,
              warningTimerRef: undefined,
              timeoutTimerRef: undefined
            }
          };
        }
        
        // Reduced logging: Only log activity resets periodically to avoid log spam
        const lastLogTime = (context as any).lastActivityLogTime || 0;
        const isReset = context.activityTracking?.lastActivity !== undefined;
        const logMessage = isReset ? 'Activity timers reset' : 'Starting activity timers';
        
        if (now - lastLogTime > 10000) { // Log only every 10 seconds
          console.log(`[ACTIVITY] ${logMessage} for ${context.callSid}`);
          (context as any).lastActivityLogTime = now;
        }
        
        // Start fresh activity timers
        const warningTimerRef = spawn('activityWarningTimer', {
          id: `activityWarning-${context.callSid}-${now}`,
          input: { callSid: context.callSid }
        });
        
        const timeoutTimerRef = spawn('activityTimeoutTimer', {
          id: `activityTimeout-${context.callSid}-${now}`,
          input: { callSid: context.callSid }
        });
        
        if (!isReset) {
          console.log(`[ACTIVITY] ✅ Started both 30s warning and 60s timeout timers for ${context.callSid}`);
        }
        
        return {
          activityTracking: {
            ...context.activityTracking,
            lastActivity: now,
            warningTimerRef,
            timeoutTimerRef
          }
        };
      }),
      
      cancelGracePeriod: assign(({ context }) => {
        const callLog = getCallLogger(context.callSid);
        callLog.gracePeriod('Cancelling grace period');
        if (context.gracePeriodState?.timeoutId) {
          clearTimeout(context.gracePeriodState.timeoutId);
        }
        return {
          gracePeriodState: {
            ...context.gracePeriodState,
            active: false,
            timeoutId: undefined
          }
        };
      }),
      
      sendInactivityWarning: assign(({ context, event }) => {
        const warningNumber = (event as any).warningNumber || 1;
        console.log(`[ACTIVITY] Sending inactivity warning ${warningNumber} for ${context.callSid}`);
        
        // Set appropriate warning message for TTS
        const warningMessage = warningNumber === 1 
          ? "Are you still there? Please respond if you'd like to continue."
          : "This is your final warning. Please respond now or the call will end.";
        
        return {
          responseText: warningMessage,
          ttsAgenda: 'speak',
          activityTracking: {
            ...context.activityTracking,
            warningCount: warningNumber
          }
        };
      }),
      
      sendInactivityWarningToTts: ({ context, self }) => {
        console.log(`[TTS-INACTIVITY] Sending inactivity warning to TTS for ${context.callSid}`);
        if (context.responseText) {
          const connectionType = context.infrastructure?.connection?.type;
          
          // Send GENERATE_TTS event with INACTIVITY_WARNING source to prevent infinite loops
          self.send({ 
            type: 'GENERATE_TTS', 
            text: context.responseText, 
            callSid: context.callSid, 
            turnIndex: context.turnNumber || 0,
            connectionType: connectionType || 'twilio',
            source: TTSSource.INACTIVITY_WARNING
          });
        } else {
          console.warn(`[TTS-INACTIVITY] Warning TTS requested but responseText is empty for ${context.callSid}`);
        }
      },
      
      startTimersAfterTtsCompleteConditional: assign(({ context, spawn }) => {
        // Only start activity timers if the completed TTS was not an inactivity warning
        const currentOp = context.ttsQueue?.currentOperation;
        const ttsSource = currentOp?.source;
        
        if (ttsSource === TTSSource.INACTIVITY_WARNING) {
          console.log(`[ACTIVITY-INACTIVITY] Skipping timer start after inactivity warning TTS completion for ${context.callSid}`);
          return {}; // No state changes
        }
        
        console.log(`[ACTIVITY-NORMAL] Starting timers after normal TTS completion for ${context.callSid}`);
        
        const now = Date.now();
        
        // Stop any existing activity timers first (XState v5 compatible)
        if (context.activityTracking?.warningTimerRef) {
          context.activityTracking.warningTimerRef.send({ type: 'xstate.stop' });
        }
        if (context.activityTracking?.timeoutTimerRef) {
          context.activityTracking.timeoutTimerRef.send({ type: 'xstate.stop' });
        }
        
        // Start fresh activity timers
        const warningTimerRef = spawn('activityWarningTimer', {
          id: `activityWarning-${context.callSid}-${now}`,
          input: { callSid: context.callSid }
        });
        
        const timeoutTimerRef = spawn('activityTimeoutTimer', {
          id: `activityTimeout-${context.callSid}-${now}`,
          input: { callSid: context.callSid }
        });
        
        console.log(`[ACTIVITY] ✅ Started timers after normal TTS completion for ${context.callSid}`);
        
        return {
          activityTracking: {
            ...context.activityTracking,
            lastActivity: now,
            warningTimerRef,
            timeoutTimerRef
          }
        };
      }),
      
      // ============================================================================
      // CENTRALIZED CLEANUP ACTIONS
      // ============================================================================
      initiateCallCleanup: ({ context }) => {
        console.log(`[CLEANUP-ACTION] Initiating call cleanup for ${context.callSid}`);
        
        try {
          // Send WebSocket notification to client immediately when cleanup starts
          const { sendWebSocketMessage } = require('./websocketManager');
          const callEndedMessage = JSON.stringify({
            event: 'call-ended',
            callSid: context.callSid,
            timestamp: new Date().toISOString()
          });
          
          sendWebSocketMessage(context.callSid, callEndedMessage)
            .then((sent) => {
              if (sent) {
                console.log(`[CLEANUP-ACTION] ✅ Call-ended notification sent to client at cleanup start for ${context.callSid}`);
              } else {
                console.warn(`[CLEANUP-ACTION] Failed to send call-ended notification at cleanup start for ${context.callSid}`);
              }
            })
            .catch((error) => {
              console.error(`[CLEANUP-ACTION] Error sending call-ended notification at cleanup start:`, error);
            });

          // Set the isCleaningUp flag immediately to prevent race conditions
          const { calls } = require('./server');
          const callState = calls[context.callSid];
          if (callState) {
            callState.isCleaningUp = true;
            console.log(`[CLEANUP-ACTION] Set isCleaningUp=true for ${context.callSid}`);
          }
        } catch (error) {
          console.error(`[CLEANUP-ACTION] Error setting cleanup flag:`, error);
        }
      },
      
      cleanupLiveKitResources: async ({ context }) => {
        const cleanupStartTime = Date.now();
        console.log(`[CLEANUP-ACTION:${context.callSid}] 🏁 Starting coordinated LiveKit resources cleanup`);
        
        try {
          const { calls } = require('./server');
          const { textToSpeechManagerComplete } = require('./services/TextToSpeechManager');
          const callState = calls[context.callSid];
          
          // PHASE 1: TTS-LiveKit Resource Coordination (NEW)
          console.log(`[CLEANUP-ACTION:${context.callSid}] 🤝 Phase 1: Coordinating with TTS system before LiveKit cleanup`);
          
          try {
            // Step 1: Prepare TTS system for cleanup (cancel tasks, streams)
            const ttsPreparationStartTime = Date.now();
            await textToSpeechManagerComplete.prepareForCleanup(context.callSid);
            const ttsPreparationTime = Date.now() - ttsPreparationStartTime;
            console.log(`[CLEANUP-ACTION:${context.callSid}] ✅ TTS preparation completed (${ttsPreparationTime}ms)`);
            
            // Step 2: Wait for TTS resource release confirmation
            const resourceReleaseStartTime = Date.now();
            const resourcesReleased = await textToSpeechManagerComplete.waitForResourceRelease(context.callSid, 5000);
            const resourceReleaseTime = Date.now() - resourceReleaseStartTime;
            
            if (resourcesReleased) {
              console.log(`[CLEANUP-ACTION:${context.callSid}] ✅ TTS resource release confirmed (${resourceReleaseTime}ms) - Safe to proceed with LiveKit FFI operations`);
            } else {
              console.warn(`[CLEANUP-ACTION:${context.callSid}] ⚠️ TTS resource release timeout (${resourceReleaseTime}ms) - Proceeding with caution`);
            }
            
          } catch (coordinationError) {
            console.error(`[CLEANUP-ACTION:${context.callSid}] ❌ TTS coordination error:`, coordinationError);
            console.log(`[CLEANUP-ACTION:${context.callSid}] 🛡️ Proceeding with LiveKit cleanup despite coordination failure`);
          }
          
          // PHASE 2: LiveKit FFI Cleanup (Enhanced with Coordination)
          if (callState?.livekitRTCHandler) {
            console.log(`[CLEANUP-ACTION:${context.callSid}] 🔌 Phase 2: Found LiveKit RTC handler - attempting coordinated disconnect`);
            console.log(`[CLEANUP-ACTION:${context.callSid}] 🛡️ FFI operations now protected by TTS resource coordination`);
            
            const disconnectStartTime = Date.now();
            try {
              // Enhanced error handling for FFI operations with TTS coordination
              await callState.livekitRTCHandler.disconnect();
              const disconnectTime = Date.now() - disconnectStartTime;
              console.log(`[CLEANUP-ACTION:${context.callSid}] ✅ LiveKit RTC handler disconnect SUCCESS (${disconnectTime}ms) - No FFI panic with coordination!`);
              
              // Safe deletion after successful disconnect
              delete callState.livekitRTCHandler;
              console.log(`[CLEANUP-ACTION:${context.callSid}] ✅ LiveKit RTC handler reference deleted`);
              
            } catch (rtcError) {
              const disconnectTime = Date.now() - disconnectStartTime;
              console.error(`[CLEANUP-ACTION:${context.callSid}] ❌ LiveKit RTC disconnect FAILED (${disconnectTime}ms) despite coordination:`, rtcError);
              console.error(`[CLEANUP-ACTION:${context.callSid}] 🔴 Error type: ${rtcError.name}, message: ${rtcError.message}`);
              
              // Check for FFI-specific errors (should be rare with coordination)
              if (rtcError.message && (rtcError.message.includes('panic') || rtcError.message.includes('FFI') || rtcError.message.includes('Rust'))) {
                console.error(`[CLEANUP-ACTION:${context.callSid}] 🔥 DETECTED FFI PANIC DESPITE COORDINATION - Further investigation needed!`);
                console.error(`[CLEANUP-ACTION:${context.callSid}] 🔍 This suggests a deeper architectural issue beyond TTS resource conflicts`);
              }
              
              // Force delete handler reference even if disconnect failed
              console.log(`[CLEANUP-ACTION:${context.callSid}] 🛠️ Force deleting RTC handler reference despite disconnect failure`);
              delete callState.livekitRTCHandler;
              
              // Don't throw - continue with other cleanup
            }
          } else {
            console.log(`[CLEANUP-ACTION:${context.callSid}] ℹ️ No LiveKit RTC handler found to clean up`);
          }
          
          // Clean up LiveKit room
          const roomCleanupStartTime = Date.now();
          try {
            const { livekitRoomService } = require('./services/livekit/livekit-room-service');
            const roomName = callState?.livekitRoomName || `room-${context.userId}-${context.callSid}`;
            console.log(`[CLEANUP-ACTION:${context.callSid}] 🏠 Cleaning up LiveKit room: ${roomName}`);
            
            await livekitRoomService.cleanupRoom(roomName);
            const roomCleanupTime = Date.now() - roomCleanupStartTime;
            console.log(`[CLEANUP-ACTION:${context.callSid}] ✅ LiveKit room cleanup SUCCESS (${roomCleanupTime}ms)`);
          } catch (roomError) {
            const roomCleanupTime = Date.now() - roomCleanupStartTime;
            console.error(`[CLEANUP-ACTION:${context.callSid}] ❌ LiveKit room cleanup FAILED (${roomCleanupTime}ms):`, roomError);
            // Don't throw - room cleanup failure shouldn't block call cleanup
          }
          
          const totalTime = Date.now() - cleanupStartTime;
          console.log(`[CLEANUP-ACTION:${context.callSid}] 🏆 LiveKit cleanup COMPLETED (total: ${totalTime}ms)`);
          
        } catch (error) {
          const totalTime = Date.now() - cleanupStartTime;
          console.error(`[CLEANUP-ACTION:${context.callSid}] ❌ LiveKit cleanup CRITICAL ERROR (${totalTime}ms):`, error);
          console.error(`[CLEANUP-ACTION:${context.callSid}] 🔴 This error may cause phantom calls!`);
          // Don't throw - we want cleanup to continue
        }
      },
      
      cleanupSTTResources: async ({ context }) => {
        console.log(`[CLEANUP-ACTION] Cleaning up STT resources for ${context.callSid}`);
        
        try {
          // 🚀 NEW: Use SpeechToTextManager for proper STT cleanup
          try {
            const { speechToTextManager } = await import('./services/SpeechToTextManager');
            await speechToTextManager.cleanup(context.callSid);
            console.log(`[CLEANUP-ACTION] ✅ SpeechToTextManager cleanup completed for ${context.callSid}`);
          } catch (sttError) {
            console.warn(`[CLEANUP-ACTION] ⚠️ SpeechToTextManager cleanup warning for ${context.callSid}:`, sttError);
            // Continue with legacy cleanup if modern cleanup fails
          }
          
          // 🔄 LEGACY: Fallback cleanup for old-style STT resources
          const { calls } = require('./server');
          const callState = calls[context.callSid];
          
          if (callState?.sttProvider) {
            console.log(`[CLEANUP-ACTION] Stopping legacy STT provider for ${context.callSid}`);
            await callState.sttProvider.stop?.();
            delete callState.sttProvider;
            console.log(`[CLEANUP-ACTION] ✅ Legacy STT provider cleaned up for ${context.callSid}`);
          }
          
          if (callState?.deepgramConnection) {
            console.log(`[CLEANUP-ACTION] Closing legacy Deepgram connection for ${context.callSid}`);
            callState.deepgramConnection.finish();
            delete callState.deepgramConnection;
            console.log(`[CLEANUP-ACTION] ✅ Legacy Deepgram connection cleaned up for ${context.callSid}`);
          }
          
        } catch (error) {
          console.error(`[CLEANUP-ACTION] Error cleaning up STT resources:`, error);
        }
      },
      
      cleanupWebSocketConnections: async ({ context }) => {
        console.log(`[CLEANUP-ACTION] Cleaning up WebSocket connections for ${context.callSid}`);
        
        try {
          const { calls } = require('./server');
          const callState = calls[context.callSid];
          
          if (callState?.directWebSocket) {
            console.log(`[CLEANUP-ACTION] Closing direct WebSocket for ${context.callSid}`);
            callState.directWebSocket.close();
            delete callState.directWebSocket;
            console.log(`[CLEANUP-ACTION] ✅ Direct WebSocket cleaned up for ${context.callSid}`);
          }
          
        } catch (error) {
          console.error(`[CLEANUP-ACTION] Error cleaning up WebSocket connections:`, error);
        }
      },
      
      finalizeCallCleanup: ({ context }) => {
        console.log(`[CLEANUP-ACTION] Finalizing call cleanup for ${context.callSid}`);
        
        try {
          const { calls } = require('./server');
          const callState = calls[context.callSid];
          
          if (callState) {
            // Mark cleanup as complete
            callState.isCleaningUp = false;
            console.log(`[CLEANUP-ACTION] Set isCleaningUp=false for ${context.callSid}`);
            
            // Delete the call state
            delete calls[context.callSid];
            console.log(`[CLEANUP-ACTION] ✅ Call state deleted for ${context.callSid}`);
          }
          
          // Clean up XState controller
          const { callControllers } = require('./xstateIntegration');
          if (callControllers[context.callSid]) {
            delete callControllers[context.callSid];
            console.log(`[CLEANUP-ACTION] ✅ XState controller deleted for ${context.callSid}`);
          }
          
        } catch (error) {
          console.error(`[CLEANUP-ACTION] Error finalizing cleanup:`, error);
        }
      },
      
      markCallAsEnded: ({ context }) => {
        console.log(`[CLEANUP-ACTION] Call ${context.callSid} has ended - cleanup complete`);
      },
      
      logCleanupSuccess: ({ context }) => {
        console.log(`[CLEANUP-ACTION] ✅ Cleanup completed successfully for ${context.callSid}`);
      },
      
      logCleanupError: ({ context, event }) => {
        console.error(`[CLEANUP-ACTION] ❌ Cleanup failed for ${context.callSid}:`, event);
      },
      
      logRecognizerConnected: ({ context }) => {
        console.log(`[RECOGNIZER-ACTION] ✅ Recognizer connected for ${context.callSid}`);
      },
      
      logCleanupRetry: ({ context }) => {
        console.log(`[CLEANUP-ACTION] 🔄 Retrying cleanup for ${context.callSid}`);
      },
      
      logForcedCleanupComplete: ({ context }) => {
        console.log(`[CLEANUP-ACTION] 🔧 Forced cleanup complete for ${context.callSid}`);
      },
      
      logEventBlockedDuringCleanup: ({ context, event }) => {
        console.log(`[CLEANUP-ACTION] 🚫 Event ${event.type} blocked during cleanup for ${context.callSid}`);
      },
      
      // TODO: Add actions for sending TTS, making LLM calls, etc.
      
      // ============================================================================
      // FAANG-LEVEL XSTATE-CENTRIC TIMING ACTIONS
      // ============================================================================
      
      // XState action: Create individual utterance entity in context
      createIndividualUtteranceEntity: assign(({ context, event }) => {
        const utteranceEvent = event as any; // USER_UTTERANCE event
        const turnIndex = context.turnNumber || 0;
        
        console.log(`[XSTATE-TIMING] Creating individual utterance entity for turn ${turnIndex}: "${utteranceEvent.transcriptText}"`);
        
        // Initialize timing entities if not present
        const timingEntities = context.timingEntities || initializeTimingEntities(context);
        
        const newEntity: IndividualUtteranceEntity = {
          turnIndex,
          text: utteranceEvent.transcriptText || utteranceEvent.transcription || '',
          turnFinalizedAt: utteranceEvent.userStoppedAt || Date.now(),
          ttsStartedAt: undefined, // Will be set by TTS action
          userStartedAt: utteranceEvent.userStartedAt || context.interruptionTimestamp || (utteranceEvent.userStoppedAt || Date.now()) - 1000,
          userStoppedAt: utteranceEvent.userStoppedAt || Date.now(),
          isPartOfCombined: false
        };
        
        // Add to context
        const updatedIndividuals = new Map(timingEntities.individualUtterances);
        updatedIndividuals.set(turnIndex, newEntity);
        
        return {
          timingEntities: {
            ...timingEntities,
            individualUtterances: updatedIndividuals,
            activeTimingReference: {
              type: 'individual' as const,
              entityId: turnIndex,
              referenceText: newEntity.text
            }
          }
        };
      }),
      
      // XState action: Evaluate grace period using context
      evaluateGracePeriodFromContext: assign(({ context, event }) => {
        const utteranceEvent = event as any;
        const currentUtterance = {
          text: utteranceEvent.transcriptText || utteranceEvent.transcription || '',
          userStartedAt: utteranceEvent.userStartedAt || utteranceEvent.interruptionTimestamp || (utteranceEvent.userStoppedAt - 2000) || (Date.now() - 2000), // Fallback: estimate 2s before stop
          userStoppedAt: utteranceEvent.userStoppedAt || Date.now()
        };
        
        console.log(`[XSTATE-GRACE] 🔍 Evaluating grace period for: "${currentUtterance.text}"`);
        console.log(`[XSTATE-GRACE] 📊 Timing entities status: ${context.timingEntities ? 'EXISTS' : 'MISSING'}`);
        
        // Build evaluation context from XState context 
        const evaluationContext = buildEvaluationContext(context, currentUtterance);
        
        if (!evaluationContext) {
          console.log(`[XSTATE-GRACE] No evaluation context available - treating as first utterance`);
          
          // For first utterance, we need to initialize timing entities if they don't exist
          const timingEntities = context.timingEntities || initializeTimingEntities(context);
          
          return {
            timingEntities,
            lastGracePeriodEvaluation: {
              inGracePeriod: false,
              type: undefined,
              gap: 0,
              reason: 'First utterance - no previous reference to evaluate against',
              debugInfo: {
                turnIndex: context.turnNumber || 0,
                ttsStarted: false,
                timingGap: 0,
                threshold: 30000,
                calculation: 'First utterance in conversation'
              }
            }
          };
        }
        
        // ARCHITECTURAL FIX: Use actual previous turn TTS state from XState context
        // Find the actual previous turn timing data instead of creating artificial context
        const currentTurnIndex = context.turnNumber || 0;
        const previousTurnIndex = Math.max(0, currentTurnIndex - 1);
        const actualPreviousTurnTiming = context.turnTimings?.find(t => t.turnIndex === previousTurnIndex);
        
        // Use actual previous turn TTS state, not artificial undefined
        const actualPreviousTtsStarted = actualPreviousTurnTiming?.ttsStartedAt || 
                                        actualPreviousTurnTiming?.audioStreamingStartAt ||
                                        evaluationContext.referenceTtsStartedAt;
        
        console.log(`[XSTATE-GRACE] 🎯 CHECKING PREVIOUS TURN TTS STATE:`);
        console.log(`[XSTATE-GRACE]   Previous turn index: ${previousTurnIndex}`);
        console.log(`[XSTATE-GRACE]   Previous turn TTS state: ${actualPreviousTtsStarted ? `STARTED at ${formatTimestamp(actualPreviousTtsStarted)}` : 'NOT STARTED'}`);
        
        // TIMING CONTAMINATION FIX: Use ONLY previous turn's own timing data
        // The grace period evaluation requires ALL timing data from the same turn
        const previousTurnFinalizedAt = actualPreviousTurnTiming?.turnCompletedAt || 
                                      actualPreviousTurnTiming?.sttFinalizedAt ||
                                      actualPreviousTurnTiming?.userStoppedAt;
                                      
        // ARCHITECTURAL SAFETY: If no previous turn timing, skip grace period evaluation
        if (!actualPreviousTurnTiming || !previousTurnFinalizedAt) {
          console.log(`[XSTATE-GRACE] 🛑 NO PREVIOUS TURN DATA - Skipping grace period evaluation`);
          return {
            graceChain: { isActive: false, accumulatedText: '', turnCount: 0 },
            inGracePeriod: false,
            gracePeriodType: null,
            gracePeriodDebug: { reason: 'No previous turn timing data available' }
          };
        }
        
        console.log(`[XSTATE-GRACE] 🔧 CONTAMINATION PREVENTION:`);
        console.log(`[XSTATE-GRACE]   Previous turn finalized at: ${formatTimestamp(previousTurnFinalizedAt)}`);
        console.log(`[XSTATE-GRACE]   Previous turn TTS started at: ${actualPreviousTtsStarted ? formatTimestamp(actualPreviousTtsStarted) : 'UNDEFINED'}`);
        
        // CRITICAL VALIDATION: Ensure timing makes sense (TTS should start AFTER turn finalization)
        if (actualPreviousTtsStarted && actualPreviousTtsStarted < previousTurnFinalizedAt) {
          console.error(`[XSTATE-GRACE] ❌ TIMING LOGIC ERROR: TTS started (${formatTimestamp(actualPreviousTtsStarted)}) before turn ended (${formatTimestamp(previousTurnFinalizedAt)})`);
          console.error(`[XSTATE-GRACE] ❌ This indicates incorrect timing data - using fallback to avoid contamination`);
          return {
            graceChain: { isActive: false, accumulatedText: '', turnCount: 0 },
            inGracePeriod: false,
            gracePeriodType: null,
            gracePeriodDebug: { reason: 'Timing logic error detected - avoided contamination' }
          };
        }
        
        console.log(`[XSTATE-GRACE]   Timing validation: ✅ CLEAN (TTS after turn end as expected)`);
        
        // Current utterance timing - when current utterance started (interruption timing)
        const currentUtteranceUserStartedAt = evaluationContext.currentUtteranceUserStartedAt;
        const currentUtteranceFinalizedAt = evaluationContext.currentUtteranceUserStartedAt + 1000; // Estimate end time
        
        // CRITICAL FIX: Use new grace period evaluation with correct timing references
        const previousTurnTiming = {
          turnIndex: currentTurnIndex,
          turnFinalizedAt: previousTurnFinalizedAt,       // When PREVIOUS turn actually ended
          ttsStartedAt: actualPreviousTtsStarted,         // When PREVIOUS turn TTS started
          ttsCompletedAt: undefined,                      // Not tracked yet
          userStartedAt: 0,                              // Not used in new evaluation
          userStoppedAt: 0,                              // Not used in new evaluation
          chainPosition: 1,
          isChainContinuation: false
        };
        const gracePeriodResult = bulletproofEvaluateGracePeriod(
          previousTurnTiming,
          currentUtteranceUserStartedAt,                  // When CURRENT utterance STARTED
          DEFAULT_GRACE_PERIOD_CONFIG
        );
        
        console.log(`[XSTATE-GRACE] 🎯 Evaluation against ${context.timingEntities.activeTimingReference?.type || "unknown"} "${context.timingEntities.activeTimingReference?.referenceText || "unknown"}": ${gracePeriodResult.inGracePeriod ? '✅ IN GRACE PERIOD' : '❌ OUTSIDE GRACE PERIOD'}`);
        console.log(`[XSTATE-GRACE] 📏 Gap: ${gracePeriodResult.gap}ms, Type: ${gracePeriodResult.type || 'none'}, Reason: ${gracePeriodResult.reason}`);
        
        return {
          lastGracePeriodEvaluation: gracePeriodResult
        };
      }),
      
      // XState action: Create combined response entity
      createCombinedResponseEntity: assign(({ context, event }) => {
        const utteranceEvent = event as any;
        const currentUtterance = utteranceEvent.transcriptText || utteranceEvent.transcription || '';
        
        console.log(`[XSTATE-TIMING] Creating combined response entity`);
        
        if (!context.timingEntities) {
          console.error(`[XSTATE-TIMING] No timing entities in context for combined response creation`);
          return {};
        }
        
        const currentEntity = getCurrentTimingEntity(context);
        if (!currentEntity) {
          console.error(`[XSTATE-TIMING] No current timing entity for combined response creation`);
          return {};
        }
        
        // Type-safe property access
        const entityTurnIndex = currentEntity.turnIndex;
        const entityText = 'text' in currentEntity ? currentEntity.text : currentEntity.combinedText;
        
        const combinedTurnIndex = Math.max(
          entityTurnIndex || 0, 
          context.turnNumber || 0
        );
        
        const combinedEntity: CombinedResponseEntity = {
          turnIndex: combinedTurnIndex,
          combinedText: `${entityText} ${currentUtterance}`.trim(),
          turnFinalizedAt: Date.now(), // When combination approved
          ttsStartedAt: undefined, // Will be set by TTS action
          utteranceTurnIndices: [entityTurnIndex || 0, context.turnNumber || 0],
        };
        
        // Update context
        const updatedCombined = new Map(context.timingEntities.combinedResponses);
        updatedCombined.set(combinedTurnIndex, combinedEntity);
        
        // Mark individual utterance as part of combined (only if current entity is individual)
        const updatedIndividuals = new Map(context.timingEntities.individualUtterances);
        if ('turnIndex' in currentEntity && currentEntity.turnIndex !== undefined) {
          const individual = updatedIndividuals.get(currentEntity.turnIndex);
          if (individual) {
            individual.isPartOfCombined = true;
            updatedIndividuals.set(currentEntity.turnIndex, individual);
          }
        }
        
        console.log(`[XSTATE-TIMING] Created combined response: "${combinedEntity.combinedText}"`);
        
        return {
          timingEntities: {
            individualUtterances: updatedIndividuals,
            combinedResponses: updatedCombined,
            activeTimingReference: {
              type: 'combined' as const,
              entityId: combinedTurnIndex,
              referenceText: combinedEntity.combinedText
            }
          },
          graceChain: {
            isActive: true,
            accumulatedText: combinedEntity.combinedText,
            turnCount: 2,
            // Legacy compatibility - will be replaced by timing entities
            mostRecentTurnContext: {
              turnFinalizedAt: Date.now(),
              ttsStreamingStartTime: undefined,
              userStoppedAt: utteranceEvent.userStoppedAt || Date.now(),
              utteranceText: currentUtterance
            }
          }
        };
      }),
      
      // XState action: Update TTS timing when TTS starts
      updateTtsTimingInContext: assign(({ context, event }) => {
        const ttsEvent = event as any;
        const timestamp = Date.now();
        
        console.log(`[XSTATE-TIMING] Updating TTS timing for current entity`);
        
        if (!context.timingEntities?.activeTimingReference) {
          console.warn(`[XSTATE-TIMING] No active timing reference for TTS update`);
          return { audioPlaybackStartTime: timestamp };
        }
        
        const activeRef = context.timingEntities.activeTimingReference;
        
        if (activeRef.type === 'combined') {
          // Update combined response TTS timing
          const updatedCombined = new Map(context.timingEntities.combinedResponses);
          const entity = updatedCombined.get(activeRef.entityId);
          if (entity) {
            entity.ttsStartedAt = timestamp;
            updatedCombined.set(activeRef.entityId, entity);
            console.log(`[XSTATE-TIMING] Updated combined response TTS timing: "${entity.combinedText}"`);
            
            // 🔍 INVESTIGATION: Combined response TTS lifecycle
            console.log(`🔍 [TTS-LIFECYCLE] COMBINED RESPONSE TTS STARTED: entityId=${activeRef.entityId}, timestamp=${timestamp}, callSid=${context.callSid}`);
            console.log(`🔍 [TTS-LIFECYCLE] Combined text: "${entity.combinedText?.substring(0, 100)}..."`);
            console.log(`🔍 [TTS-LIFECYCLE] Turn indices in combined response: [${entity.utteranceTurnIndices?.join(', ')}]`);
          }
          
          return {
            timingEntities: {
              ...context.timingEntities,
              combinedResponses: updatedCombined
            },
            audioPlaybackStartTime: timestamp
          };
        } else {
          // Update individual utterance TTS timing
          const updatedIndividuals = new Map(context.timingEntities.individualUtterances);
          const entity = updatedIndividuals.get(activeRef.entityId);
          if (entity) {
            entity.ttsStartedAt = timestamp;
            updatedIndividuals.set(activeRef.entityId, entity);
            console.log(`[XSTATE-TIMING] Updated individual TTS timing: "${entity.text}"`);
            
            // 🔍 INVESTIGATION: Individual utterance TTS lifecycle
            console.log(`🔍 [TTS-LIFECYCLE] INDIVIDUAL TTS STARTED: entityId=${activeRef.entityId}, timestamp=${timestamp}, callSid=${context.callSid}`);
            console.log(`🔍 [TTS-LIFECYCLE] Turn index: ${entity.turnIndex}, text: "${entity.text?.substring(0, 50)}..."`);
          }
          
          return {
            timingEntities: {
              ...context.timingEntities,
              individualUtterances: updatedIndividuals
            },
            audioPlaybackStartTime: timestamp
          };
        }
      }),
      
      // XState action: Cleanup old timing entities for memory management
      cleanupTimingEntityContext: assign(({ context }) => {
        if (!context.timingEntities) {
          return {};
        }
        
        const CLEANUP_THRESHOLD = 10; // Keep last 10 turns
        const currentTurn = context.turnNumber || 0;
        
        // Clean up individual utterances
        const updatedIndividuals = new Map();
        for (const [turnIndex, entity] of context.timingEntities.individualUtterances) {
          if (currentTurn - turnIndex <= CLEANUP_THRESHOLD) {
            updatedIndividuals.set(turnIndex, entity);
          }
        }
        
        // Clean up combined responses  
        const updatedCombined = new Map();
        for (const [turnIndex, entity] of context.timingEntities.combinedResponses) {
          if (currentTurn - turnIndex <= CLEANUP_THRESHOLD) {
            updatedCombined.set(turnIndex, entity);
          }
        }
        
        const individualsBefore = context.timingEntities.individualUtterances.size;
        const combinedBefore = context.timingEntities.combinedResponses.size;
        const individualsAfter = updatedIndividuals.size;
        const combinedAfter = updatedCombined.size;
        
        if (individualsBefore > individualsAfter || combinedBefore > combinedAfter) {
          console.log(`[XSTATE-CLEANUP] Cleaned timing entities: individuals ${individualsBefore}→${individualsAfter}, combined ${combinedBefore}→${combinedAfter}`);
        }
        
        return {
          timingEntities: {
            ...context.timingEntities,
            individualUtterances: updatedIndividuals,
            combinedResponses: updatedCombined
          }
        };
      }),
      
    }, // End of 'actions' block

    // START OF THE SINGLE, CONSOLIDATED GUARDS BLOCK
    guards: {
      // ============================================================================
      // TUTORING SYSTEM GUARDS
      // ============================================================================
      
      isTutoringMode: ({ context }) => {
        // Check if this is a tutoring session based on the sessionType parameter in function closure
        console.log(`[XState Guard isTutoringMode] Checking sessionType: ${sessionType}`);
        return sessionType === 'tutoring';
      },

      isSimulationMode: ({ context }) => {
        console.log(`[XState Guard isSimulationMode] Checking sessionType: ${sessionType}`);
        return sessionType === 'simulation';
      },

      // ============================================================================
      // FAANG-LEVEL XSTATE-CENTRIC TIMING GUARDS
      // ============================================================================
      
      // XState guard: Check if within grace period using context evaluation
      isWithinGracePeriod: ({ context }) => {
        const lastEvaluation = context.lastGracePeriodEvaluation;
        const result = lastEvaluation?.inGracePeriod || false;
        console.log(`[XSTATE-GUARD] isWithinGracePeriod: ${result ? '✅' : '❌'}`);
        return result;
      },

      // XState guard: Should create combined response (individual + grace period)
      shouldCreateCombinedResponse: ({ context }) => {
        const activeRef = context.timingEntities?.activeTimingReference;
        const lastEval = context.lastGracePeriodEvaluation;
        
        const shouldCreate = lastEval?.inGracePeriod && activeRef?.type === 'individual';
        
        // ENHANCED LOGGING: Detailed debugging for grace period failures
        console.log(`[XSTATE-GUARD] shouldCreateCombinedResponse: ${shouldCreate ? '✅' : '❌'}`);
        console.log(`  - Grace period evaluation: ${lastEval?.inGracePeriod ? '✅ PASS' : '❌ FAIL'} (reason: "${lastEval?.reason || 'no evaluation'}")`);
        console.log(`  - Active timing reference type: ${activeRef?.type || 'undefined'} (expected: 'individual')`);
        console.log(`  - Grace period gap: ${lastEval?.gap || 'unknown'}ms (threshold: ${lastEval?.debugInfo?.threshold || 'unknown'}ms)`);
        
        if (!shouldCreate) {
          console.log(`[GRACE-COMBINATION-FAIL] Turn will be processed independently instead of combined`);
        }
        
        return shouldCreate || false;
      },

      // XState guard: Should extend combined response (combined + grace period)  
      shouldExtendCombinedResponse: ({ context }) => {
        const activeRef = context.timingEntities?.activeTimingReference;
        const lastEval = context.lastGracePeriodEvaluation;
        
        const shouldExtend = lastEval?.inGracePeriod && activeRef?.type === 'combined';
        
        // ENHANCED LOGGING: Detailed debugging for extension failures
        console.log(`[XSTATE-GUARD] shouldExtendCombinedResponse: ${shouldExtend ? '✅' : '❌'}`);
        console.log(`  - Grace period evaluation: ${lastEval?.inGracePeriod ? '✅ PASS' : '❌ FAIL'} (reason: "${lastEval?.reason || 'no evaluation'}")`);
        console.log(`  - Active timing reference type: ${activeRef?.type || 'undefined'} (expected: 'combined')`);
        console.log(`  - Grace period gap: ${lastEval?.gap || 'unknown'}ms (threshold: ${lastEval?.debugInfo?.threshold || 'unknown'}ms)`);
        
        if (!shouldExtend) {
          console.log(`[GRACE-EXTENSION-FAIL] Turn will be processed independently instead of extending chain`);
        }
        
        return shouldExtend || false;
      },

      // ============================================================================
      // EXISTING SIMULATION GUARDS
      // ============================================================================

      isUserReady: ({ context, event }) => {
        if (event.type !== 'USER_UTTERANCE') return false;
        const utterance = typeof event.transcription === 'string' ? event.transcription.toLowerCase() : '';
        const isReady = utterance.includes('yes') || utterance.includes('ready') || utterance.includes('ok') || utterance.includes('okay') || utterance.includes('sure') || utterance.includes('yeah') || utterance.includes('yep') || utterance.includes('start');
        if(isReady) console.log(`[XState Guard isUserReady] User is ready: ${utterance}`);
        else console.log(`[XState Guard isUserReady] User is NOT ready: ${utterance}`);
        return isReady;
      },
      isUserReadyViaDtmf: ({ event }) => {
        if (event.type !== 'DTMF_INPUT') return false;
        const isReady = event.digit === '1';
        if(isReady) console.log(`[XState Guard isUserReadyViaDtmf] User is ready via DTMF: ${event.digit}`);
        else console.log(`[XState Guard isUserReadyViaDtmf] User is NOT ready via DTMF: ${event.digit}`);
        return isReady;
      },
      shouldWaitForMoreSilence: ({ context }) => {
        const shouldWait = context.consecutiveSilenceFrames < SILENCE_FRAMES_THRESHOLD;
        // console.log(`[XState Guard shouldWaitForMoreSilence] Consecutive silence: ${context.consecutiveSilenceFrames}, Threshold: ${SILENCE_FRAMES_THRESHOLD}, Should wait: ${shouldWait}`);
        return shouldWait;
      },
      isLongSilence: ({ context }) => {
        const longSilence = context.consecutiveSilenceFrames >= SILENCE_FRAMES_THRESHOLD;
        // console.log(`[XState Guard isLongSilence] Consecutive silence: ${context.consecutiveSilenceFrames}, Threshold: ${SILENCE_FRAMES_THRESHOLD}, Is long silence: ${longSilence}`);
        return longSilence;
      },
      isCaseSelected: ({context}) => {
        const caseSelected = !!context.caseData;
        console.log(`[XState Guard isCaseSelected] Case selected: ${caseSelected}`);
        return caseSelected;
      },
      isRecognizerConnected: ({context}) => context.recognizerReady,
      isAwaitingUserReadinessResponse: ({ context }) => {
        return context.orchestratorTask === 'AWAIT_USER_READINESS_RESPONSE';
      },
      isReadinessDecisionProceed: ({ context }) => {
        return context.readinessDecision === 'PROCEED';
      },
      
      // 🚀 UNIFIED GRACE PERIOD GUARD - Legacy implementation for backward compatibility
      isWithinUnifiedGracePeriod: ({ context, event }) => {
        if (event.type !== 'USER_UTTERANCE' || !event.isFinal) return false;
        
        const now = Date.now();
        const graceEval = evaluateUnifiedGracePeriod(context, now);
        
        console.log(`[SIMPLE-GRACE-GUARD] Phase: ${graceEval.phase}, Type: ${graceEval.gracePeriodType}, Within: ${graceEval.withinGracePeriod}`);
        console.log(`[SIMPLE-GRACE-GUARD] Debug:`, graceEval.debugInfo);
        
        return graceEval.withinGracePeriod;
      },
      
      // CASCADING GRACE PERIOD GUARD (restored from working commit)
      isWithinCascadingGracePeriod: ({ context, event }) => {
        if (event.type !== 'USER_UTTERANCE' || !event.isFinal) return false;
        
        if (!context.turnFinalizedAt) {
          console.log(`[CASCADING-GRACE] No turnFinalizedAt - not in grace period`);
          return false;
        }
        
        // CRITICAL FIX: Don't apply grace period to final transcripts of the same utterance
        // If this USER_UTTERANCE comes in very shortly after LLM processing started,
        // it's likely just the final transcript for the same speech, not a new interruption
        if (context.llmInvokeStartTime && event.timestamp) {
          const timeSinceLlmStart = event.timestamp - context.llmInvokeStartTime;
          if (timeSinceLlmStart < 2000) { // Within 2 seconds of LLM start
            console.log(`[CASCADING-GRACE] Final transcript for same utterance (${timeSinceLlmStart}ms after LLM start) - not an interruption`);
            return false;
          }
        }
        
        const now = Date.now();
        
        // 🚀 UNIFIED GRACE PERIOD CHECK - Replaces all conflicting implementations
        const graceEval = evaluateUnifiedGracePeriod(context, now);
        
        // Enhanced grace period decision logging
        logGracePeriodFlow(context.callSid, {
          phase: graceEval.phase,
          userStoppedAt: context.userStoppedAt || 0,
          aiStartedAt: context.audioPlaybackStartTime,
          now,
          gracePeriodMs: graceEval.debugInfo?.gracePeriodMs || 3000,
          decision: graceEval.withinGracePeriod ? 'COMBINE' : 'NEW_TURN',
          reasoning: graceEval.debugInfo?.reasoning || `${graceEval.gracePeriodType} evaluation completed`
        });
        
        return graceEval.withinGracePeriod;
      },

      // ============================================================================
      // XSTATE TURN AUTHORITY GUARDS (PHASE 4.2)
      // ============================================================================
      
      shouldUseTurnAuthority,
      shouldCentralizeGracePeriod
      
      // Any other guards that were previously defined elsewhere in setup() should be moved here.
    } // END OF THE SINGLE, CONSOLIDATED GUARDS BLOCK
    // Ensure no other 'guards: { ... }' blocks exist between here and the end of setup(),
    // especially any that might have been left from previous edits or were part of the original scaffolding.

  }).createMachine({
    id: 'osce',
    context: ({ input }: { input: OsceContext }) => {
       // The 'input' is the fully formed OsceContext from xstateIntegration.ts
       // It has already been processed by restoreContext there.
       
       // Reduced context logging - only show essential fields instead of full case data
       const inputSummary = {
         callSid: input?.callSid || 'missing',
         userId: input?.userId || 'none',
         currentState: input?.currentState || 'none',
         caseTitle: input?.caseData?.title || input?.caseData?.id || 'none',
         conversationTurns: input?.conversation?.length || 0
       };
       console.log('[XState osceMachine] Initializing context from input:', JSON.stringify(inputSummary));
       
       if (!input || !input.callSid) {
         console.error('[XState osceMachine] CRITICAL: Input or input.callSid is missing for context initialization. Using fallback.');
         // Fallback, though this shouldn't happen if xstateIntegration.ts is correct
         const fallbackCallSid = input?.callSid || 'fallback_unknown_callsid';
         const fallbackUserId = input?.userId || null;
         return createInitialContext({ callSid: fallbackCallSid, userId: fallbackUserId }) as OsceContext;
       }
       // Preserve this definitive initial context immediately.
       preserveContext(input.callSid, input);
       return input; // Directly use the input as it's already the full OsceContext
     },
    initial: 'idle',
    // predictableActionArguments: true, // V5 default
    
    // 🏗️ PHASE 2: Start monitoring actors (SAFE - READ-ONLY)
    invoke: [
      {
        id: 'transcriptMonitor',
        src: 'transcriptMonitor'
      },
      {
        id: 'processMonitor', 
        src: 'processMonitor'
      },
      {
        id: 'vadMonitor',
        src: 'vadMonitor'
      },
      {
        id: 'connectionMonitor',
        src: 'connectionMonitor'
      },
      {
        id: 'timingMonitor',
        src: 'timingMonitor'
      }
    ],
    
    on: { // Global event handlers
      EXTERNAL_FINALIZATION_DONE: {
        actions: assign({
          needsFinalization: false
        })
      },
      
      CLEAR_STREAMING_FLAG: {
        actions: assign({
          wasStreamed: false
        })
      },
      
      AUDIO_PLAYBACK_STARTED: {
        actions: [
          'setAudioPlaybackStartTime',
          'updateTtsTimingInContext', // FAANG-LEVEL: Update timing entities with TTS start
          'markTtsStartedInGracePeriod' // PHASE 3-1: Enable post-TTS 3-second combination window
        ]
      },
      
      // 🚀 PHASE 2: Unified TTS handler using XState queue management (Bootstrap Integration Complete)
      // MIGRATION COMPLETE: Now uses XState-managed TTS queue instead of bootstrap system
      GENERATE_TTS: {
        actions: [
          'enqueueTtsRequest',
          'processNextTtsRequest'
        ]
      },
      
      // 🚀 PHASE 2: Internal TTS processing control events
      INTERNAL_START_TTS_PROCESSING: {
        actions: [
          'startTtsProcessing',
          'processTtsWithProvider'
        ]
      },
      
      INTERNAL_TTS_PROCESSING_SUCCESS: {
        actions: [
          'completeTtsProcessing',
          'sendTtsFinishedEvent', // Send TTS_FINISHED to transition back to listening
          'processNextTtsRequest' // Process next item in queue if any
        ]
      },
      
      INTERNAL_TTS_PROCESSING_ERROR: {
        actions: [
          'completeTtsProcessing',
          'sendTtsFinishedEvent', // Send TTS_FINISHED even on error to prevent machine from getting stuck
          'processNextTtsRequest' // Continue processing queue even after error
        ]
      },
      
      // RECONNECT H3: TTS Queue Cancellation Event Handler
      INTERNAL_CANCEL_TTS_QUEUE: {
        actions: [
          'cancelTtsQueue'
        ]
      },

      // 🚀 PHASE 2: Turn Coordination Event Handlers
      // Internal events for centralized turn coordination
      INTERNAL_TURN_COORDINATION_SUCCESS: {
        actions: [
          'completeTurnCoordination',
          // Continue with normal LLM processing after successful coordination
          ({ context, event, self }) => {
            const coordEvent = event as any;
            const result = coordEvent.result;
            
            if (result && result.finalText) {
              console.log(`[TURN-COORDINATION] ${context.callSid}: Proceeding to LLM processing with text: "${result.finalText}"`);
              
              // Send the standard COORDINATED_TURN_COMPLETE event for LLM processing
              self.send({
                type: 'COORDINATED_TURN_COMPLETE',
                transcriptText: result.finalText,
                userStoppedAt: Date.now(),
                gracePeriodApplied: result.gracePeriodApplied,
                coordinationMetadata: {
                  coordinationTime: result.coordinationTime
                }
              });
            }
          }
        ]
      },

      INTERNAL_TURN_COORDINATION_ERROR: {
        actions: [
          // Log error and attempt recovery
          ({ context, event }) => {
            const errorEvent = event as any;
            console.error(`[TURN-COORDINATION] ${context.callSid}: Coordination error: ${errorEvent.error}`);
          },
          'recoverTurnCoordination'
        ]
      },

      INTERNAL_TURN_COORDINATION_RECOVERED: {
        actions: [
          ({ context }) => {
            console.log(`[TURN-COORDINATION] ${context.callSid}: Coordination recovered, ready for next turn`);
          }
        ]
      },

      // CRITICAL FIX: Sync XState context with centralized turn coordination
      // This ensures legacy grace period logic has the data it needs
      SYNC_GRACE_PERIOD_CONTEXT: {
        actions: [
          assign(({ context, event }) => {
            const syncEvent = event as any;
            
            console.log(`[GRACE-PERIOD-SYNC] ${context.callSid}: Syncing XState context with centralized coordination`);
            console.log(`[GRACE-PERIOD-SYNC] ${context.callSid}: Setting lastUserUtterance="${syncEvent.lastUserUtterance}", userStoppedAt=${syncEvent.userStoppedAt}`);
            
            return {
              lastUserUtterance: syncEvent.lastUserUtterance,
              userStoppedAt: syncEvent.userStoppedAt,
              // Also update any related context for consistency
              turnFinalizedAt: Date.now()
            };
          })
        ]
      },

      // Start turn coordination process
      INTERNAL_START_TURN_COORDINATION: {
        actions: [
          ({ context, event }) => {
            const startEvent = event as any;
            const originalEvent = startEvent.originalEvent;
            
            console.log(`[TURN-COORDINATION] ${context.callSid}: Starting turn coordination for: "${originalEvent?.transcriptText || 'unknown'}"`);
          },
          'coordinateAtomicTurnCompletion'
        ]
      },

      // Use legacy coordination when centralized turns are disabled
      INTERNAL_USE_LEGACY_COORDINATION: {
        actions: [
          ({ context, event }) => {
            const legacyEvent = event as any;
            const originalEvent = legacyEvent.originalEvent;
            
            console.log(`[TURN-COORDINATION] ${context.callSid}: Using legacy coordination for: "${originalEvent?.transcriptText || 'unknown'}"`);
          },
          'canonicalTurnCompletionHandler'
        ]
      },

      // CRITICAL FIX G2: Update XState context with turn lifecycle information
      UPDATE_TURN_LIFECYCLE_CONTEXT: {
        actions: [
          assign(({ context, event }) => {
            const updateEvent = event as any;
            const contextUpdate = updateEvent.contextUpdate;
            const stage = updateEvent.stage;
            const timestamp = updateEvent.timestamp;
            
            console.log(`[TURN-LIFECYCLE-UPDATE] ${context.callSid}: Updating XState context with lifecycle stage ${stage} at ${timestamp}`);
            console.log(`[TURN-LIFECYCLE-UPDATE] ${context.callSid}: Context updates:`, contextUpdate);
            
            const newContext = {
              ...contextUpdate
            };
            
            console.log(`[TURN-LIFECYCLE-UPDATE] ${context.callSid}: New context fields after update:`, {
              turnFinalizedAt: newContext.turnFinalizedAt || context.turnFinalizedAt,
              llmInvokeStartTime: newContext.llmInvokeStartTime || context.llmInvokeStartTime,
              llmResponseReceived: newContext.llmResponseReceived || context.llmResponseReceived,
              audioPlaybackStartTime: newContext.audioPlaybackStartTime || context.audioPlaybackStartTime,
              audioPlaybackStarted: newContext.audioPlaybackStarted || context.audioPlaybackStarted,
              waitingForUserInput: newContext.waitingForUserInput || context.waitingForUserInput
            });
            
            return newContext;
          })
        ]
      },

      // 🚀 PHASE 3: LiveKit Connection State Management
      LIVEKIT_ROOM_CONNECTED: {
        actions: [
          assign(({ event, context }) => {
            console.log(`[LIVEKIT-STATE] ${context.callSid}: Room connected - ${event.roomName}`);
            
            return {
              infrastructure: {
                ...context.infrastructure,
                connection: {
                  ...context.infrastructure?.connection,
                  status: 'connected' as const,
                  type: 'livekit' as const,
                  lastActivity: Date.now(),
                  livekit: {
                    ...context.infrastructure?.connection?.livekit,
                    roomName: event.roomName,
                    participantSid: event.participantSid,
                    participantIdentity: event.participantIdentity,
                    isRoomConnected: true,
                    isParticipantConnected: true,
                    lastRoomEvent: 'room_connected',
                    lastEventTimestamp: Date.now(),
                    reconnectAttempts: 0
                  }
                }
              }
            };
          })
        ]
      },

      LIVEKIT_ROOM_DISCONNECTED: {
        actions: [
          assign(({ event, context }) => {
            console.log(`[LIVEKIT-STATE] ${context.callSid}: Room disconnected - ${event.reason || 'unknown'}`);
            
            return {
              infrastructure: {
                ...context.infrastructure,
                connection: {
                  ...context.infrastructure?.connection,
                  status: 'disconnected' as const,
                  lastActivity: Date.now(),
                  livekit: {
                    ...context.infrastructure?.connection?.livekit,
                    isRoomConnected: false,
                    isParticipantConnected: false,
                    isAudioPublishing: false,
                    isAudioSubscribing: false,
                    audioSourceActive: false,
                    streamingActive: false,
                    rtcHandlerReady: false,
                    lastRoomEvent: 'room_disconnected',
                    lastEventTimestamp: Date.now()
                  }
                }
              }
            };
          })
        ]
      },

      LIVEKIT_RTC_HANDLER_READY: {
        actions: [
          assign(({ context }) => {
            console.log(`[LIVEKIT-STATE] ${context.callSid}: RTC handler ready`);
            
            return {
              infrastructure: {
                ...context.infrastructure,
                connection: {
                  ...context.infrastructure?.connection,
                  lastActivity: Date.now(),
                  livekit: {
                    ...context.infrastructure?.connection?.livekit,
                    rtcHandlerReady: true,
                    lastEventTimestamp: Date.now()
                  }
                }
              }
            };
          })
        ]
      },

      LIVEKIT_AUDIO_STREAMING_STARTED: {
        actions: [
          assign(({ context }) => {
            console.log(`[LIVEKIT-STATE] ${context.callSid}: Audio streaming started`);
            
            return {
              infrastructure: {
                ...context.infrastructure,
                connection: {
                  ...context.infrastructure?.connection,
                  lastActivity: Date.now(),
                  livekit: {
                    ...context.infrastructure?.connection?.livekit,
                    streamingActive: true,
                    streamingCancelled: false,
                    audioSourceActive: true,
                    isAudioPublishing: true,
                    lastEventTimestamp: Date.now()
                  }
                }
              }
            };
          })
        ]
      },

      LIVEKIT_AUDIO_STREAMING_CANCELLED: {
        actions: [
          assign(({ context }) => {
            console.log(`[LIVEKIT-STATE] ${context.callSid}: Audio streaming cancelled`);
            
            return {
              infrastructure: {
                ...context.infrastructure,
                connection: {
                  ...context.infrastructure?.connection,
                  lastActivity: Date.now(),
                  livekit: {
                    ...context.infrastructure?.connection?.livekit,
                    streamingCancelled: true,
                    streamingActive: false,
                    lastEventTimestamp: Date.now()
                  }
                }
              }
            };
          }),
          // CRITICAL FIX: Send TTS_FINISHED event when LiveKit audio is cancelled
          // This ensures the state machine transitions back to listening state
          ({ context, self }) => {
            console.log(`[LIVEKIT-INTERRUPTION] ${context.callSid}: Sending TTS_FINISHED due to LiveKit audio cancellation`);
            self.send({ type: 'TTS_FINISHED' });
          }
        ]
      },

      LIVEKIT_AUDIO_STREAMING_STOPPED: {
        actions: [
          assign(({ context, event }) => {
            console.log(`[LIVEKIT-STATE] ${context.callSid}: Audio streaming stopped - ${event.reason || 'completed'}`);
            
            return {
              infrastructure: {
                ...context.infrastructure,
                connection: {
                  ...context.infrastructure?.connection,
                  lastActivity: Date.now(),
                  livekit: {
                    ...context.infrastructure?.connection?.livekit,
                    streamingActive: false,
                    audioSourceActive: false,
                    isAudioPublishing: false,
                    currentTrackSid: undefined,
                    lastEventTimestamp: Date.now()
                  }
                }
              }
            };
          })
        ]
      },

      // 🏗️ PHASE 2: Monitor events (SAFE - READ-ONLY, NO SIDE EFFECTS)
      OBSERVE_TRANSCRIPT: {
        actions: [
          // Update infrastructure state in parallel to old logic
          assign(({ context, event }) => {
            if (!context.infrastructure?.transcript?.accumulation) return {};
            
            const timestamp = event.timestamp || Date.now();
            const newCount = context.infrastructure.transcript.accumulation.totalProcessed + 1;
            
            return {
              infrastructure: {
                ...context.infrastructure,
                transcript: {
                  accumulation: {
                    ...context.infrastructure.transcript.accumulation,
                    lastProcessedTime: timestamp,
                    totalProcessed: newCount,
                    duplicateCount: context.infrastructure.transcript.accumulation.duplicateCount + 
                      (context.infrastructure.transcript.accumulation.currentText === event.text ? 1 : 0)
                  }
                }
              }
            };
          }),
          // Forward to monitoring actor
          ({ event }) => {
            console.log(`[INFRASTRUCTURE] Observing transcript: "${event.text.substring(0, 30)}..." (isFinal: ${event.isFinal})`);
            // Actor forwarding will be handled by XState automatically
          }
        ]
      },
      
      // 🏗️ PHASE 4: Infrastructure Cutover Event Handlers
      UPDATE_TRANSCRIPT: {
        actions: assign(({ context, event }) => {
          if (!context.infrastructure?.transcript?.accumulation) return {};
          
          const newText = event.text || '';
          
          // CRITICAL FIX: Proper utterance refinement - no auto-accumulation
          // Partials replace partials, finals replace all partials for same utterance
          if (event.isFinal) {
            console.log(`[INFRASTRUCTURE-FINAL] Final transcript replaces all partials: "${newText}"`);
          } else {
            console.log(`[INFRASTRUCTURE-PARTIAL] Partial transcript replaces previous partial: "${newText}"`);
          }
          
          console.log(`[INFRASTRUCTURE-CUTOVER] Updating transcript: "${newText}" (isFinal: ${event.isFinal})`);
          
          return {
            infrastructure: {
              ...context.infrastructure,
              transcript: {
                accumulation: {
                  ...context.infrastructure.transcript.accumulation,
                  currentText: newText, // Always replace with new text - no smart accumulation
                  isAccumulating: !event.isFinal,
                  lastProcessedTime: event.timestamp,
                  totalProcessed: context.infrastructure.transcript.accumulation.totalProcessed + 1
                }
              }
            }
          };
        })
      },
      
      UPDATE_VAD_STATE: {
        actions: assign(({ context, event }) => {
          if (!context.infrastructure) return {};
          
          // Defensive programming: ensure vad exists
          const safeVad = context.infrastructure.vad || {
            metrics: {
              speechFrameCount: 0,
              silenceFrameCount: 0,
              lastActivityTime: Date.now(),
              consecutiveSpeechFrames: 0,
              consecutiveSilenceFrames: 0
            }
          };
          
          return {
            infrastructure: {
              ...context.infrastructure,
              vad: {
                metrics: {
                  ...safeVad.metrics,
                  speechFrameCount: safeVad.metrics.speechFrameCount + (event.isVoice ? 1 : 0),
                  silenceFrameCount: safeVad.metrics.silenceFrameCount + (event.isVoice ? 0 : 1),
                  lastActivityTime: event.timestamp,
                  consecutiveSpeechFrames: event.isVoice ? event.consecutiveFrames : 0,
                  consecutiveSilenceFrames: event.isVoice ? 0 : event.consecutiveFrames
                }
              }
            }
          };
        })
      },
      
      FINALIZE_TURN: {
        actions: [
          assign(({ context, event }) => {
            console.log(`[INFRASTRUCTURE-CUTOVER] Finalizing turn: "${event.transcript}" (reason: ${event.reason})`);
            
            // Add to conversation and reset turn state
            const newConversation = [
              ...context.conversation,
              { 
                speaker: 'caller' as const, 
                text: event.transcript,
                timestamp: new Date().toISOString(),
                xstate_state: getMainState(context.currentState || 'unknown'),
                interrupted: isInterrupted(context.turnNumber || 0, context.turnTimings || []),
                turnIndex: context.turnNumber || 0
              }
            ];
            
            return {
              conversation: newConversation,
              lastUserUtterance: event.transcript,
              // Reset infrastructure VAD state
              infrastructure: context.infrastructure ? {
                ...context.infrastructure,
                vad: {
                  metrics: {
                    ...(context.infrastructure.vad?.metrics || {
                      speechFrameCount: 0,
                      silenceFrameCount: 0,
                      lastActivityTime: Date.now(),
                      consecutiveSpeechFrames: 0,
                      consecutiveSilenceFrames: 0
                    }),
                    consecutiveSpeechFrames: 0,
                    consecutiveSilenceFrames: 0
                  }
                }
              } : context.infrastructure
            };
          }),
          // REMOVED: Don't send duplicate USER_UTTERANCE here - infrastructureHelpers.ts already sends it
          ({ context, event }) => {
            console.log(`[INFRASTRUCTURE-CUTOVER] Transcript received: "${event.transcript}" (USER_UTTERANCE will be sent separately)`);
          }
        ]
      },
      
      RESET_TURN_STATE: {
        actions: assign(({ context }) => {
          console.log(`[INFRASTRUCTURE-CUTOVER] Resetting turn state`);
          
          return {
            // Reset infrastructure turn-specific state
            infrastructure: context.infrastructure ? {
              ...context.infrastructure,
              transcript: {
                accumulation: {
                  ...(context.infrastructure.transcript?.accumulation || {
                    currentText: '',
                    lastProcessedTime: 0,
                    isAccumulating: false,
                    duplicateCount: 0,
                    totalProcessed: 0
                  }),
                  // CRITICAL FIX: Preserve currentText during grace period, don't clear to empty string
                  // currentText: '', // REMOVED - this was overriding our preservation logic
                  isAccumulating: false,
                  lastProcessedTime: Date.now()
                }
              },
              vad: {
                metrics: {
                  ...(context.infrastructure.vad?.metrics || {
                    speechFrameCount: 0,
                    silenceFrameCount: 0,
                    lastActivityTime: Date.now(),
                    consecutiveSpeechFrames: 0,
                    consecutiveSilenceFrames: 0
                  }),
                  consecutiveSpeechFrames: 0,
                  consecutiveSilenceFrames: 0
                }
              }
            } : context.infrastructure
          };
        })
      },
      
      OBSERVE_PROCESS_SPAWN: {
        actions: [
          assign(({ context, event }) => {
            if (!context.infrastructure?.audio?.processTracking) return {};
            
            return {
              infrastructure: {
                ...context.infrastructure,
                audio: {
                  processTracking: {
                    ...context.infrastructure.audio.processTracking,
                    activePids: [...context.infrastructure.audio.processTracking.activePids, event.pid],
                    lastSpawnTime: event.timestamp,
                    processCount: context.infrastructure.audio.processTracking.processCount + 1
                  }
                }
              }
            };
          }),
          ({ event }) => console.log(`[INFRASTRUCTURE] Process spawned: PID=${event.pid}, type=${event.processType}`)
        ]
      },
      
      OBSERVE_VAD_FRAME: {
        actions: [
          assign(({ context, event }) => {
            if (!context.infrastructure?.vad?.metrics) return {};
            
            return {
              infrastructure: {
                ...context.infrastructure,
                vad: {
                  metrics: {
                    ...context.infrastructure.vad.metrics,
                    speechFrameCount: context.infrastructure.vad.metrics.speechFrameCount + (event.isVoice ? 1 : 0),
                    silenceFrameCount: context.infrastructure.vad.metrics.silenceFrameCount + (event.isVoice ? 0 : 1),
                    lastActivityTime: event.timestamp,
                    consecutiveSpeechFrames: event.isVoice ? context.infrastructure.vad.metrics.consecutiveSpeechFrames + 1 : 0,
                    consecutiveSilenceFrames: event.isVoice ? 0 : context.infrastructure.vad.metrics.consecutiveSilenceFrames + 1
                  }
                }
              }
            };
          })
        ]
      },
      
      // 🏗️ PHASE 4: Initialize Infrastructure Event Handler
      INITIALIZE_INFRASTRUCTURE: {
        actions: assign(({ context, event }) => {
          console.log(`[INFRASTRUCTURE-INIT] Initializing infrastructure state for missing context`);
          
          // Create complete infrastructure state if missing, but preserve existing transcript if any
          const existingTranscript = context.infrastructure?.transcript?.accumulation?.currentText || '';
          console.log(`[INFRASTRUCTURE-INIT] Preserving existing transcript: "${existingTranscript}"`);
          
          return {
            infrastructure: {
              transcript: {
                accumulation: {
                  currentText: existingTranscript, // Preserve existing text instead of clearing
                  lastProcessedTime: event.timestamp,
                  isAccumulating: false,
                  duplicateCount: context.infrastructure?.transcript?.accumulation?.duplicateCount || 0,
                  totalProcessed: context.infrastructure?.transcript?.accumulation?.totalProcessed || 0
                }
              },
              audio: {
                processTracking: {
                  activePids: [],
                  lastSpawnTime: event.timestamp,
                  processCount: 0,
                  failureCount: 0
                }
              },
              vad: {
                metrics: {
                  speechFrameCount: 0,
                  silenceFrameCount: 0,
                  lastActivityTime: event.timestamp,
                  consecutiveSpeechFrames: 0,
                  consecutiveSilenceFrames: 0
                }
              },
              connection: {
                status: 'connected' as const,
                type: 'twilio' as const,
                lastActivity: event.timestamp,
                reconnectAttempts: 0,
                messagesSent: 0,
                errorsCount: 0
              },
              timing: {
                phaseStartTime: event.timestamp,
                lastTransitionTime: event.timestamp,
                processingLatencies: [],
                averageLatency: 0
              }
            }
          };
        })
      },
      
      // Update connection type in infrastructure context
      UPDATE_CONNECTION_TYPE: {
        actions: assign(({ context, event }) => {
          console.log(`[INFRASTRUCTURE] Updating connection type to: ${event.connectionType}`);
          
          if (!context.infrastructure?.connection) {
            console.warn(`[INFRASTRUCTURE] No connection infrastructure found, cannot update connection type`);
            return {};
          }
          
          return {
            infrastructure: {
              ...context.infrastructure,
              connection: {
                ...context.infrastructure.connection,
                type: event.connectionType
              }
            }
          };
        })
      },

      // CRITICAL FIX: Centralized Turn Timing Event Handlers
      // These ensure turnTimings stay synchronized between XState and global calls object
      UPDATE_TRANSCRIPT_RECEIVED: {
        actions: 'updateTranscriptReceived'
      },

      UPDATE_AUDIO_STREAMING_START: {
        actions: 'updateAudioStreamingStart'
      },

      UPDATE_AUDIO_STREAMING_END: {
        actions: 'updateAudioStreamingEnd'
      },

      UPDATE_INTERRUPTION_TIMESTAMP: {
        actions: 'updateInterruptionTimestamp'
      },

      UPDATE_SILENCE_DETECTED: {
        actions: 'updateSilenceDetected'
      },
      
      // CANONICAL TURN LIFECYCLE EVENTS - XState as Single Source of Truth
      FINAL_TRANSCRIPT: {
        actions: ['handleFinalTranscript', 'checkBarrierCompletion']
      },
      
      SILENCE_DETECTED: {
        actions: ['handleSilenceDetected', 'checkBarrierCompletion']  
      },
      
      // CANONICAL PLAYBACK LIFECYCLE EVENTS
      TTS_STARTED: {
        actions: 'handleTtsStarted'
      },
      
      TTS_STOPPED: {
        actions: 'handleTtsStopped'
      },
      
      // CANONICAL INTERRUPTION DETECTION
      USER_SPEECH_STARTED: {
        actions: 'handleUserSpeechStart'
      },
      
      // Global activity detection for inactivity management
      ACTIVITY_DETECTED: {
        actions: 'manageActivityTimers'
      },
      
      // Global disconnect handling
      PARTICIPANT_DISCONNECTED: {
        actions: ({ context, event }) => {
          // Only start grace period if this disconnect is NOT due to inactivity termination
          const disconnectReason = (event as any).disconnectData?.reason;
          if (disconnectReason === 'Session ended due to inactivity') {
            console.log(`[GRACE_PERIOD] Skipping grace period for ${context.callSid} - disconnect was due to inactivity termination`);
            return;
          }
          
          // Start grace period for legitimate disconnects (network issues, etc.)
          const duration = (event as any).duration || 10000;
          const reason = (event as any).reason || 'unknown';
          const source = (event as any).source || 'websocket';
          
          console.log(`[GRACE_PERIOD] Starting ${duration}ms grace period for ${context.callSid} - reason: ${reason}`);
          
          // Clear any existing grace period
          if (context.gracePeriodState?.timeoutId) {
            clearTimeout(context.gracePeriodState.timeoutId);
          }
          
          // Set up new grace period timeout
          const timeoutId = setTimeout(() => {
            console.log(`[GRACE_PERIOD] Grace period expired for ${context.callSid}`);
            const { callControllers } = require('./xstateIntegration');
            const controller = callControllers[context.callSid];
            if (controller?.machine) {
              controller.machine.send({ 
                type: 'INACTIVITY_TIMEOUT', 
                reason: 'grace_period_expired',
                callSid: context.callSid 
              });
            }
          }, duration);
          
          return {
            gracePeriodState: {
              active: true,
              startTime: Date.now(),
              timeoutId,
              reason: reason,
              source: source
            }
          };
        }
      },
      
      PARTICIPANT_RECONNECTED: {
        actions: 'cancelGracePeriod'
      },
      
      // WebSocket-specific connection events for directWebSocketHandler integration
      WEBSOCKET_DISCONNECTED: {
        actions: ({ context, event }) => {
          console.log(`[WEBSOCKET] Disconnection detected for ${context.callSid} - code: ${(event as any).code}, reason: ${(event as any).reason}`);
          
          // Send PARTICIPANT_DISCONNECTED event to trigger grace period
          const { callControllers } = require('./xstateIntegration');
          const controller = callControllers[context.callSid];
          if (controller?.machine) {
            controller.machine.send({
              type: 'PARTICIPANT_DISCONNECTED',
              source: 'websocket',
              callSid: context.callSid,
              disconnectData: {
                code: (event as any).code,
                reason: (event as any).reason,
                userInitiated: (event as any).userInitiated || false
              }
            });
          }
        }
      },
      
      WEBSOCKET_RECONNECTED: {
        actions: ({ context, event }) => {
          const gracePeriodUsed = (event as any).gracePeriodUsed || 0;
          console.log(`[WEBSOCKET] Reconnection detected for ${context.callSid} after ${gracePeriodUsed}ms`);
          
          // Send PARTICIPANT_RECONNECTED event to cancel grace period
          const { callControllers } = require('./xstateIntegration');
          const controller = callControllers[context.callSid];
          if (controller?.machine) {
            controller.machine.send({
              type: 'PARTICIPANT_RECONNECTED',
              source: 'websocket',
              callSid: context.callSid,
              gracePeriodUsed
            });
          }
        }
      },
      
      GRACE_PERIOD_EXPIRED: {
        actions: ({ context, event }) => {
          console.log(`[GRACE_PERIOD] Grace period expired for ${context.callSid} - performing cleanup`);
          
          // Send INACTIVITY_TIMEOUT event to terminate the call
          const { callControllers } = require('./xstateIntegration');
          const controller = callControllers[context.callSid];
          if (controller?.machine) {
            controller.machine.send({
              type: 'INACTIVITY_TIMEOUT',
              reason: 'grace_period_expired',
              callSid: context.callSid
            });
          }
        }
      },
      
      // Global inactivity timeouts - work across all states
      INACTIVITY_WARNING: {
        actions: [
          ({ context, event }) => {
            console.log(`[INACTIVITY-DEBUG] Received INACTIVITY_WARNING event for ${context.callSid}:`, event);
            console.log(`[INACTIVITY-DEBUG] Current state: ${context.currentState}`);
            console.log(`[INACTIVITY-DEBUG] Processing inactivity warning`);
          },
          'sendInactivityWarning',
          'sendInactivityWarningToTts'
        ]
      },
      
      INACTIVITY_TIMEOUT: {
        target: '#osce.call_ending',
        actions: [
          ({ context, event }) => {
            console.log(`[TIMEOUT-DEBUG] Received INACTIVITY_TIMEOUT event for ${context.callSid}:`, event);
            console.log(`[TIMEOUT-DEBUG] Current state: ${context.currentState}`);
            console.log(`[TIMEOUT-DEBUG] Terminating call due to inactivity`);
          },
          'logStateEntry'
        ]
      },
      
      // ============================================================================
      // CENTRALIZED CLEANUP EVENTS
      // ============================================================================
      END_CALL: {
        target: '#osce.call_ending',
        actions: [
          ({ context, event }) => {
            console.log(`[CLEANUP-EVENT] END_CALL received for ${context.callSid}, reason: ${event.reason || 'user_initiated'}`);
          },
          'logStateEntry'
        ]
      },
      
      FORCE_CLEANUP: {
        target: '#osce.cleanup_in_progress',
        actions: [
          ({ context, event }) => {
            console.log(`[CLEANUP-EVENT] FORCE_CLEANUP received for ${context.callSid}, reason: ${event.reason || 'forced'}`);
          },
          'logStateEntry'
        ]
      },
      
      CLEANUP_LIVEKIT: {
        actions: [
          ({ context, event }) => {
            console.log(`[CLEANUP-EVENT] CLEANUP_LIVEKIT received for ${context.callSid}`);
          },
          'cleanupLiveKitResources'
        ]
      },
      
      CLEANUP_STT: {
        actions: [
          ({ context, event }) => {
            console.log(`[CLEANUP-EVENT] CLEANUP_STT received for ${context.callSid}`);
          },
          'cleanupSTTResources'
        ]
      },
      
      CLEANUP_WEBSOCKET: {
        actions: [
          ({ context, event }) => {
            console.log(`[CLEANUP-EVENT] CLEANUP_WEBSOCKET received for ${context.callSid}`);
          },
          'cleanupWebSocketConnections'
        ]
      },
      
      CLEANUP_COMPLETE: {
        target: '#osce.call_ended',
        actions: [
          ({ context, event }) => {
            console.log(`[CLEANUP-EVENT] CLEANUP_COMPLETE received for ${context.callSid}`);
          },
          'finalizeCallCleanup'
        ]
      }
    },
    states: {
      idle: {
        meta: { 
          interruptible: false, // Welcome message should not be interruptible
          stateType: StateType.AUDIO_PLAYING // Playing welcome message
        },
        entry: [
          assign({ currentState: 'idle' }),
          'logStateEntry',
          'assignWelcomeMessage',
          'sendToTtsIfNotLiveKit'
        ],
        on: {
          START: {
            target: 'idle',
            actions: [
              assign({ 
                callSid: ({ event, context }) => {
                  // Only set callSid from START event, never fallback to potentially wrong context
                  if (event.type === 'START' && event.callSid) {
                    return event.callSid;
                  }
                  // For safety, preserve existing callSid only if it exists and matches expected pattern
                  if (context.callSid && context.callSid.match(/^[A-F0-9-]{36}$/)) {
                    return context.callSid;
                  }
                  // Fail safely - log error and return null to trigger recovery
                  console.error(`[XState:SAFETY] Invalid callSid state - event.type: ${event.type}, context.callSid: ${context.callSid}`);
                  return null;
                }
              }),
              'manageActivityTimers',
              // Send TTS now that iOS audio ready signal has been received
              ({ context }) => {
                const connectionType = context.infrastructure?.connection?.type;
                if (connectionType === 'livekit') {
                  console.log(`[TTS-START-TRIGGER] ${context.callSid}: START event received for LiveKit connection - now triggering TTS`);
                  console.log(`[TTS-START-TRIGGER] ${context.callSid}: Welcome message: "${context.responseText}"`);
                }
              },
              'sendToTts'
            ]
          },
          TTS_FINISHED: [
            {
              // Route to tutoring flow if sessionType is 'tutoring'
              guard: 'isTutoringMode',
              target: 'tutoring_initialization',
              actions: [
                'resetTurnSpecificContext',
                'initializeTutoringContext',
                assign({
                  ttsFinished: true,
                  currentState: 'tutoring_initialization',
                  currentTurnStartTime: Date.now(),
                  ttsLastTurnDuration: ({ context }) => context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 0
                })
              ]
            },
            {
              // If user already spoke during welcome message, process their input immediately
              guard: ({ context }) => Boolean(context.readyToTransition === true && context.lastUserUtterance),
              target: 'processing_readiness',
              actions: [
                'resetTurnSpecificContext',
                assign(({ context }) => {
                  console.log(`[IDLE-FAST-TRACK] Processing stored user input: "${context.lastUserUtterance}"`);
                  return {
                    ttsFinished: true,
                    waitingForUserInput: false,
                    ttsAgenda: 'process',
                    currentState: 'processing_readiness',
                    currentTurnStartTime: Date.now(),
                    ttsLastTurnDuration: context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 0
                  };
                })
              ]
            },
            {
              // Default to simulation flow (existing behavior)
              target: 'waiting_for_readiness_response',
              actions: [
                'resetTurnSpecificContext',
                assign({
                  ttsFinished: true,
                  waitingForUserInput: true,
                  ttsAgenda: 'listen',
                  currentState: 'waiting_for_readiness_response',
                  currentTurnStartTime: Date.now(),
                  ttsLastTurnDuration: ({ context }) => context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 0
                })
              ]
            }
          ],
          RECOGNIZER_CONNECTED: { actions: 'logRecognizerConnected' },
          RECOGNIZER_DISCONNECTED: { actions: 'logRecognizerDisconnected' },
          RECOGNIZER_ERROR: { actions: 'logRecognizerError' },
          STOP_RECOGNIZER: { actions: 'stopRecognizer' },
          
          // CRITICAL FIX: Handle user input during welcome message
          // Users often speak during TTS, so we need to capture and store their input
          COORDINATED_TURN_COMPLETE: {
            actions: [
              assign(({ context, event }) => {
                console.log(`[IDLE-USER-INPUT] User spoke during welcome message: "${event.transcriptText}"`);
                
                // Store the user input for processing after TTS finishes
                return {
                  lastUserUtterance: event.transcriptText,
                  readyToTransition: true,
                  currentTurnStartTime: Date.now()
                };
              })
            ]
          }
        }
      },
      waiting_for_readiness_response: {
        meta: { 
          interruptible: false, // "Are you ready?" prompt should not be interruptible
          stateType: StateType.USER_ACTIVE // User should respond, run activity timers
        },
        entry: [
          'resetTurnSpecificContext',
          assign({
            waitingForUserInput: true,
            ttsAgenda: 'listen',
            currentState: 'waiting_for_readiness_response',
            currentTurnStartTime: Date.now()
          }),
          'manageActivityTimers'
        ],
        on: {
          SPEECH_FRAME: {
            actions: [
              'manageActivityTimers', // Reset inactivity timers on voice activity
              assign({
                speechFramesInCurrentTurn: ({ context }) => context.speechFramesInCurrentTurn + 1,
                consecutiveSilenceFrames: 0,
                hasSpeechDetected: true,
                turnFinalized: false
              })
            ]
          },
          USER_UTTERANCE: {
            // Always go to LLM for conversational processing - no hard-coded guards
            target: 'processing_readiness',
            actions: [
              'assignUserUtterance', 
              'logUserUtterance',
              'manageActivityTimers', // Reset inactivity timers on user speech
              assign({
                waitingForUserInput: false,
                turnNumber: ({ context }) => (context.turnNumber || 0) + 1,
                currentTurnStartTime: () => Date.now()
                // Removed turnFinalizedAt - should only be set when AI finishes speaking (TTS_FINISHED)
              })
            ]
          },
          // CANONICAL TURN LIFECYCLE EVENTS - needed for barrier synchronization
          FINAL_TRANSCRIPT: {
            actions: ['handleFinalTranscript', 'checkBarrierCompletion']
          },
          SILENCE_DETECTED: {
            actions: ['handleSilenceDetected', 'checkBarrierCompletion']  
          },
          
          COORDINATED_TURN_COMPLETE: {
            target: 'processing_readiness',
            actions: [
              'manageActivityTimers', // Reset inactivity timers on user activity
              assign(({ event, context }) => ({
                lastUserUtterance: event.transcriptText,
                tempAccumulatedText: event.transcriptText, // Also set tempAccumulatedText for processing_readiness
                waitingForUserInput: false,
                turnNumber: (context.turnNumber || 0) + 1,
                currentTurnStartTime: Date.now(),
                coordination: {
                  lastCompletionTime: Date.now(),
                  gracePeriodApplied: event.gracePeriodApplied
                }
              }))
            ]
          },
          DTMF_INPUT: [
            {
              guard: 'isUserReadyViaDtmf',
              target: 'finalize_case_selection',
              actions: ['logDtmfEvent', 'logGuardSuccess']
            },
            {
              actions: [
                'logDtmfEvent',
                'logGuardFailure',
                assign({ responseText: "I'm sorry, I didn't quite catch that. Please press 1 if you're ready, or say 'yes'." }),
                'sendToTts'
              ],
              target: 'prompt_for_readiness.speaking_prompt'
            }
          ],
          SILENCE_FRAME_SHORT: [
            {
              // Condition: Meaningful speech detected, silence threshold met, AND we have accumulated final transcript
              guard: ({ context, event }) => {
                // FIXED: Since SILENCE_FRAME_SHORT is only sent after VAD has confirmed the threshold,
                // we don't need to check consecutiveSilenceFrames - the VAD already did that work
                const connectionType = context.infrastructure?.connection?.type || 'twilio';
                const minFrames = getMinSpeechFrames(connectionType);
                const hasSufficientSpeech = context.hasSpeechDetected && 
                  context.speechFramesInCurrentTurn >= minFrames;
                
                // CRITICAL: Check for final transcript - must have both text AND speech detection
                const hasFinalTranscript = event.type === 'SILENCE_FRAME_SHORT' && 
                  event.accumulatedText && 
                  event.accumulatedText.length > 0 &&
                  context.hasSpeechDetected;
                
                const shouldComplete = hasSufficientSpeech && hasFinalTranscript;
                
                // DEBUG: Always log guard condition evaluation for SILENCE_FRAME_SHORT
                if (event.type === 'SILENCE_FRAME_SHORT') {
                  console.log(`[GUARD-DEBUG] SILENCE_FRAME_SHORT guard evaluation (FIXED):`);
                  console.log(`  - hasSpeechDetected: ${context.hasSpeechDetected}`);
                  console.log(`  - speechFramesInCurrentTurn: ${context.speechFramesInCurrentTurn} (min: ${minFrames}, type: ${connectionType})`);
                  console.log(`  - event.accumulatedText: "${event.accumulatedText}"`);
                  console.log(`  - accumulatedText.length: ${event.accumulatedText?.length || 0}`);
                  console.log(`  - hasSufficientSpeech: ${hasSufficientSpeech}`);
                  console.log(`  - hasFinalTranscript: ${hasFinalTranscript}`);
                  console.log(`  - shouldComplete: ${shouldComplete}`);
                  console.log(`  - NOTE: Removed consecutiveSilenceFrames check since VAD already validated threshold`);
                }
                
                return shouldComplete;
              },
              target: 'processing_readiness', // FIX: Change to processing_readiness state that calls LLM
              actions: [
                assign({
                  consecutiveSilenceFrames: ({ context }) => context.consecutiveSilenceFrames + 1,
                  waitingForUserInput: false,
                  turnFinalized: true, // Mark turn as finalized
                  // Removed turnFinalizedAt - should only be set when AI finishes speaking (TTS_FINISHED), not when user stops speaking
                  turnNumber: ({ context }) => (context.turnNumber || 0) + 1,
                  vadWaitLogged: false, // Reset for next turn
                  // Store the accumulated text temporarily for the entry action to process
                  tempAccumulatedText: ({ event }) => {
                    if (event.type === 'SILENCE_FRAME_SHORT' && event.accumulatedText) {
                      console.log(`[VAD-FINALIZE] Storing accumulated text for processing: "${event.accumulatedText}"`);
                      return event.accumulatedText;
                    }
                    return undefined;
                  },
                  turnTimings: ({ context }) => {
                    const newTurnTimings = [...(context.turnTimings || [])];
                    const turnIndex = (context.turnNumber || 0) + 1;
                    const existingTiming = newTurnTimings.find(t => t.turnIndex === turnIndex);
                    if (existingTiming) {
                      existingTiming.silenceDetectedAt = Date.now();
                    } else {
                      newTurnTimings.push({
                        turnIndex,
                        silenceDetectedAt: Date.now()
                      });
                    }
                    return newTurnTimings;
                  }
                }),
                // CRITICAL: Send event to capture accumulated transcript from server state
                ({ context }) => {
                  console.log(`[VAD-FINALIZE] Sending finalize event for turn completion via silence detection`);
                  // This will trigger the server to capture the accumulated transcript and set it in context
                  sendParent({ 
                    type: 'VAD_TURN_COMPLETE', 
                    callSid: context.callSid,
                    reason: 'silence_threshold'
                  });
                }
              ]
            },
            {
              // Default action: just increment silence frames
              actions: assign({
                consecutiveSilenceFrames: ({ context }) => context.consecutiveSilenceFrames + 1
              })
            }
          ],
          // NEW: Handle FORCE_SET_UTTERANCE event
          FORCE_SET_UTTERANCE: {
            actions: assign({
              lastUserUtterance: ({ event }) => {
                if (event.type === 'FORCE_SET_UTTERANCE') {
                  console.log(`[FORCE_SET_UTTERANCE] Setting lastUserUtterance: "${event.utteranceText}"`);
                  return event.utteranceText;
                }
                return undefined;
              }
            })
          }
        }
      },
      
      // ============================================================================
      // FAANG-LEVEL XSTATE-CENTRIC GRACE PERIOD DECISION STATE
      // ============================================================================
      
      grace_period_decision: {
        meta: { interruptible: false }, // Decision making should not be interrupted
        entry: [
          assign({ currentState: 'grace_period_decision' }),
          'logStateEntry'
        ],
        always: [
          {
            // Create combined response (individual utterance + grace period success)
            guard: 'shouldCreateCombinedResponse',
            target: '#history_taking_id.processing',
            actions: [
              'createCombinedResponseEntity',
              'manageActivityTimers', // Reset inactivity timers
              assign(({ context, event }) => {
                const utteranceEvent = event as any;
                const currentUtterance = utteranceEvent.transcriptText || utteranceEvent.transcription || '';
                
                // Get the combined text from timing entities
                const combinedEntity = getCurrentTimingEntity(context);
                const combinedText = combinedEntity && 'text' in combinedEntity ? combinedEntity.text : 
                                   combinedEntity && 'combinedText' in combinedEntity ? combinedEntity.combinedText : 
                                   currentUtterance;
                
                console.log(`[XSTATE-DECISION] Creating combined response: "${combinedText}"`);
                
                return {
                  lastUserUtterance: combinedText,
                  tempAccumulatedText: combinedText,
                  turnNumber: (context.turnNumber || 0) + 1,
                  audioPlaybackStartTime: undefined, // Clear previous TTS
                  waitingForUserInput: false,
                  hasSpeechDetected: true
                };
              })
            ]
          },
          {
            // Extend combined response (combined response + grace period success)
            guard: 'shouldExtendCombinedResponse',
            target: '#history_taking_id.processing',
            actions: [
              'createCombinedResponseEntity', // This will extend the existing combined response
              'manageActivityTimers',
              assign(({ context, event }) => {
                const utteranceEvent = event as any;
                
                // Get the extended text from timing entities  
                const combinedEntity = getCurrentTimingEntity(context);
                const extendedText = combinedEntity && 'text' in combinedEntity ? combinedEntity.text : 
                                   combinedEntity && 'combinedText' in combinedEntity ? combinedEntity.combinedText : 
                                   context.lastUserUtterance || '';
                
                console.log(`[XSTATE-DECISION] Extending combined response: "${extendedText}"`);
                
                return {
                  lastUserUtterance: extendedText,
                  tempAccumulatedText: extendedText,
                  turnNumber: (context.turnNumber || 0) + 1,
                  audioPlaybackStartTime: undefined, // Clear previous TTS
                  waitingForUserInput: false,
                  hasSpeechDetected: true
                };
              })
            ]
          },
          {
            // Default: Outside grace period - process as independent utterance
            target: '#history_taking_id.processing',
            actions: [
              'createIndividualUtteranceEntity',
              'manageActivityTimers',
              assign(({ context, event }) => {
                const utteranceEvent = event as any;
                const currentUtterance = utteranceEvent.transcriptText || utteranceEvent.transcription || '';
                
                console.log(`[XSTATE-DECISION] Processing independent utterance: "${currentUtterance}"`);
                
                return {
                  lastUserUtterance: currentUtterance,
                  tempAccumulatedText: currentUtterance,
                  turnNumber: (context.turnNumber || 0) + 1,
                  audioPlaybackStartTime: undefined,
                  waitingForUserInput: false,
                  hasSpeechDetected: true
                };
              })
            ]
          }
        ]
      },

      processing_readiness: {
        meta: { 
          interruptible: false, // Processing user readiness should not be interruptible
          stateType: StateType.SYSTEM_PROCESSING // System is processing user input
        },
        entry: [
          assign({ currentState: 'processing_readiness' }),
          // CRITICAL: Move lastUserUtterance assignment here so it happens before actor invocation
          assign({
            lastUserUtterance: ({ context }) => {
              if (context.tempAccumulatedText) {
                console.log(`[processing_readiness] Setting lastUserUtterance from tempAccumulatedText: "${context.tempAccumulatedText}"`);
                return context.tempAccumulatedText;
              }
              console.log(`[processing_readiness] No tempAccumulatedText, keeping existing lastUserUtterance: "${context.lastUserUtterance}"`);
              return context.lastUserUtterance;
            },
            tempAccumulatedText: undefined // Clear temporary storage
          })
        ],
        invoke: {
          id: 'readinessDecisionLlm',
          src: 'transitionOrchestratorLlmActor', // Reuse existing actor
          input: ({ context, event }: any) => {
            // Try to get the accumulated text from the triggering event or context
            let userUtterance = context.lastUserUtterance;
            if (event && event.type === 'SILENCE_FRAME_SHORT' && event.accumulatedText) {
              userUtterance = event.accumulatedText;
              console.log(`[processing_readiness] Using accumulated text from event: "${userUtterance}"`);
            } else {
              console.log(`[processing_readiness] Using context lastUserUtterance: "${userUtterance}"`);
            }
            
            return {
              userUtterance: userUtterance,
              orchestratorTask: 'AWAIT_USER_READINESS_RESPONSE', // Set the task for readiness check
              currentState: context.currentState,
              callSid: context.callSid,
              turnNumber: context.turnNumber
            };
          },
          onDone: [
            {
              // User is ready - proceed to case selection
              guard: ({ event }: any) => event.output?.readinessDecision === 'PROCEED',
              target: 'finalize_case_selection',
              actions: [
                assign({
                  responseText: ({ event }: any) => event.output?.responseText || "Great! Let me get a case ready for you.",
                  waitingForUserInput: false,
                  orchestratorTask: undefined,
                  readinessDecision: 'PROCEED'
                }),
                'sendToTts'
              ]
            },
            {
              // User is not ready or needs clarification - ask again
              target: 'prompt_for_readiness.speaking_prompt',
              actions: [
                assign({
                  responseText: ({ event }: any) => event.output?.responseText || "I'm sorry, I didn't quite catch that. Are you ready to start the scenario?",
                  waitingForUserInput: false,
                  orchestratorTask: undefined,
                  readinessDecision: ({ event }: any) => event.output?.readinessDecision || 'CLARIFY'
                }),
                'sendToTts'
              ]
            }
          ],
          onError: {
            target: 'prompt_for_readiness.speaking_prompt',
            actions: [
              assign({
                responseText: "I'm sorry, I had trouble processing that. Are you ready to start the scenario?",
                waitingForUserInput: false
              }),
              'sendToTts'
            ]
          }
        }
      },
      prompt_for_readiness: {
        meta: { interruptible: false }, // Readiness prompting should not be interruptible
        entry: [
          // PHASE 3 FIX: Removed 'sendToTts' - transition actions already handle TTS to prevent duplicates
          assign({
            currentState: 'prompt_for_readiness',
            waitingForUserInput: false,
            ttsAgenda: 'speak',
            ttsFinished: false,
            currentTurnStartTime: Date.now()
          })
        ],
        initial: 'speaking_prompt',
        states: {
          speaking_prompt: {
            meta: { interruptible: false }, // Speaking readiness prompt should not be interruptible
            on: {
              TTS_FINISHED: [
                {
                  // If the response indicates user is ready, proceed to case selection
                  guard: ({ context }) => {
                    const response = context.responseText || '';
                    return response.includes('Great! Let me get a case ready for you.');
                  },
                  target: '#osce.finalize_case_selection',
                  actions: assign({
                    ttsFinished: true,
                    waitingForUserInput: false,
                    ttsLastTurnDuration: ({ context }) => context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 0
                  })
                },
                {
                  // Default: go back to waiting for response
                  target: 'waiting_for_readiness_response',
                  actions: assign({
                    ttsFinished: true,
                    waitingForUserInput: true,
                    ttsAgenda: 'listen',
                    ttsLastTurnDuration: ({ context }) => context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 0
                  })
                }
              ]
            }
          },
          waiting_for_readiness_response: {
            meta: { 
              interruptible: false, // Nested readiness response waiting should not be interruptible
              stateType: StateType.USER_ACTIVE // User should respond, run activity timers
            },
            entry: [
              'resetTurnSpecificContext',
              assign({
                waitingForUserInput: true,
                ttsAgenda: 'listen',
                currentState: 'prompt_for_readiness.waiting_for_readiness_response',
                currentTurnStartTime: Date.now()
              }),
              'manageActivityTimers'
            ],
            on: {
              SPEECH_FRAME: {
                actions: [
                  'manageActivityTimers', // Reset inactivity timers on voice activity
                  assign({
                    speechFramesInCurrentTurn: ({ context }) => context.speechFramesInCurrentTurn + 1,
                    consecutiveSilenceFrames: 0,
                    hasSpeechDetected: true,
                    turnFinalized: false
                  })
                ]
              },
              USER_UTTERANCE: {
                // Always go to LLM for conversational processing - no hard-coded guards
                target: 'processing_readiness',
                actions: [
                  'assignUserUtterance', 
                  'logUserUtterance',
                  assign({
                    waitingForUserInput: false,
                    turnNumber: ({ context }) => (context.turnNumber || 0) + 1,
                    currentTurnStartTime: () => Date.now()
                    // Removed turnFinalizedAt - should only be set when AI finishes speaking (TTS_FINISHED)
                  })
                ]
              },
              DTMF_INPUT: [
                {
                  guard: 'isUserReadyViaDtmf',
                  target: '#osce.finalize_case_selection',
                  actions: ['logDtmfEvent', 'logGuardSuccess']
                },
                {
                  actions: [
                    'logDtmfEvent',
                    'logGuardFailure',
                    assign({ responseText: "I'm sorry, I didn't quite catch that. Please press 1 if you're ready, or say 'yes'." }),
                    'sendToTts'
                  ],
                  target: 'speaking_prompt'
                }
              ],
              SILENCE_FRAME_SHORT: [
                {
                  // FIXED: Since SILENCE_FRAME_SHORT is only sent after VAD has confirmed the threshold
                  guard: ({ context, event }) => {
                    // FIXED: Since SILENCE_FRAME_SHORT is only sent after VAD has confirmed the threshold,
                    // we don't need to check consecutiveSilenceFrames - the VAD already did that work
                    const connectionType = context.infrastructure?.connection?.type || 'twilio';
                    const minFrames = getMinSpeechFrames(connectionType);
                    const hasSufficientSpeech = context.hasSpeechDetected && 
                      context.speechFramesInCurrentTurn >= minFrames;
                    
                    // CRITICAL: Check for final transcript - must have both text AND speech detection
                    const hasFinalTranscript = event.type === 'SILENCE_FRAME_SHORT' && 
                      event.accumulatedText && 
                      event.accumulatedText.length > 0 &&
                      context.hasSpeechDetected;
                    
                    const shouldComplete = hasSufficientSpeech && hasFinalTranscript;
                    
                    // DEBUG: Always log guard condition evaluation for SILENCE_FRAME_SHORT
                    if (event.type === 'SILENCE_FRAME_SHORT') {
                      console.log(`[GUARD-DEBUG] PROMPT FOR READINESS SILENCE_FRAME_SHORT guard evaluation (FIXED):`);
                      console.log(`  - hasSpeechDetected: ${context.hasSpeechDetected}`);
                      console.log(`  - speechFramesInCurrentTurn: ${context.speechFramesInCurrentTurn} (min: ${minFrames}, type: ${connectionType})`);
                      console.log(`  - event.accumulatedText: "${event.accumulatedText}"`);
                      console.log(`  - accumulatedText.length: ${event.accumulatedText?.length || 0}`);
                      console.log(`  - hasSufficientSpeech: ${hasSufficientSpeech}`);
                      console.log(`  - hasFinalTranscript: ${hasFinalTranscript}`);
                      console.log(`  - shouldComplete: ${shouldComplete}`);
                      console.log(`  - NOTE: Removed consecutiveSilenceFrames check since VAD already validated threshold`);
                    }
                    
                    return shouldComplete;
                  },
                  target: 'processing_readiness', // FIX: Change to processing_readiness state that calls LLM
                  actions: [
                    assign({
                      consecutiveSilenceFrames: ({ context }) => context.consecutiveSilenceFrames + 1,
                      waitingForUserInput: false,
                      turnFinalized: true, // Mark turn as finalized
                      // Removed turnFinalizedAt - should only be set when AI finishes speaking (TTS_FINISHED), not when user stops speaking
                      turnNumber: ({ context }) => (context.turnNumber || 0) + 1,
                      vadWaitLogged: false, // Reset for next turn
                      needsFinalization: true, // Signal server.ts to finalize
                      // Store the accumulated text temporarily for the entry action to process
                      tempAccumulatedText: ({ event }) => {
                        if (event.type === 'SILENCE_FRAME_SHORT' && event.accumulatedText) {
                          console.log(`[VAD-FINALIZE] Storing accumulated text for processing: "${event.accumulatedText}"`);
                          return event.accumulatedText;
                        }
                        return undefined;
                      },
                      turnTimings: ({ context }) => {
                        const newTurnTimings = [...(context.turnTimings || [])];
                        const turnIndex = (context.turnNumber || 0) + 1;
                        const existingTiming = newTurnTimings.find(t => t.turnIndex === turnIndex);
                        if (existingTiming) {
                          existingTiming.silenceDetectedAt = Date.now();
                        } else {
                          newTurnTimings.push({
                            turnIndex,
                            silenceDetectedAt: Date.now()
                          });
                        }
                        return newTurnTimings;
                      }
                    })
                    // REMOVED: sendParent call for VAD_TURN_COMPLETE
                  ]
                },
                {
                  // Default action: just increment silence frames
                  actions: assign({
                    consecutiveSilenceFrames: ({ context }) => context.consecutiveSilenceFrames + 1
                  })
                }
              ],
              // NEW: Handle FORCE_SET_UTTERANCE event
              FORCE_SET_UTTERANCE: {
                actions: assign({
                  lastUserUtterance: ({ event }) => {
                    if (event.type === 'FORCE_SET_UTTERANCE') {
                      console.log(`[FORCE_SET_UTTERANCE] Setting lastUserUtterance: "${event.utteranceText}"`);
                      return event.utteranceText;
                    }
                    return undefined;
                  }
                })
              },
              // CANONICAL TURN LIFECYCLE EVENTS - needed for barrier synchronization
              FINAL_TRANSCRIPT: {
                actions: ['handleFinalTranscript', 'checkBarrierCompletion']
              },
              SILENCE_DETECTED: {
                actions: ['handleSilenceDetected', 'checkBarrierCompletion']  
              },
              
              // CANONICAL: Handle COORDINATED_TURN_COMPLETE with conditional grace period logic
              COORDINATED_TURN_COMPLETE: {
                target: 'processing_readiness',
                actions: ['handleCoordinatedTurnComplete', 'canonicalTurnCompletionHandler']
              }
            }
          },
          // NEW: Add processing_readiness state that calls the LLM
          processing_readiness: {
            meta: { 
              interruptible: false, // Nested readiness processing should not be interruptible
              stateType: StateType.SYSTEM_PROCESSING // System is processing user input
            },
            entry: [
              assign({ currentState: 'prompt_for_readiness.processing_readiness' }),
              // CRITICAL: Move lastUserUtterance assignment here so it happens before actor invocation
              assign({
                lastUserUtterance: ({ context }) => {
                  console.log(`[processing_readiness] ENTRY DEBUG: context.tempAccumulatedText="${context.tempAccumulatedText}", context.lastUserUtterance="${context.lastUserUtterance}"`);
                  if (context.tempAccumulatedText) {
                    console.log(`[processing_readiness] Setting lastUserUtterance from tempAccumulatedText: "${context.tempAccumulatedText}"`);
                    return context.tempAccumulatedText;
                  }
                  console.log(`[processing_readiness] No tempAccumulatedText, keeping existing lastUserUtterance: "${context.lastUserUtterance}"`);
                  return context.lastUserUtterance;
                },
                tempAccumulatedText: undefined // Clear temporary storage
              })
            ],
            invoke: {
              id: 'readinessDecisionLlm',
              src: 'transitionOrchestratorLlmActor', // Reuse existing actor
              input: ({ context, event }: any) => {
                // Try to get the accumulated text from the triggering event or context
                let userUtterance = context.lastUserUtterance;
                if (event && event.type === 'SILENCE_FRAME_SHORT' && event.accumulatedText) {
                  userUtterance = event.accumulatedText;
                  console.log(`[processing_readiness] Using accumulated text from event: "${userUtterance}"`);
                } else {
                  console.log(`[processing_readiness] Using context lastUserUtterance: "${userUtterance}"`);
                }
                
                return {
                  userUtterance: userUtterance,
                  orchestratorTask: 'AWAIT_USER_READINESS_RESPONSE', // Set the task for readiness check
                  currentState: context.currentState
                };
              },
              onDone: [
                {
                  // User is ready - proceed to speaking readiness response, then case selection
                  guard: ({ event }: any) => event.output?.readinessDecision === 'PROCEED',
                  target: 'speaking_readiness_response',
                  actions: [
                    assign({
                      responseText: ({ event }: any) => event.output?.responseText || "Great! Let me get a case ready for you.",
                      waitingForUserInput: false,
                      orchestratorTask: undefined,
                      readinessDecision: 'PROCEED'
                    }),
                    'sendToTts'
                  ]
                },
                {
                  // User is not ready or needs clarification - ask again
                  target: 'speaking_prompt',
                  actions: [
                    assign({
                      responseText: ({ event }: any) => event.output?.responseText || "I'm sorry, I didn't quite catch that. Are you ready to start the scenario?",
                      waitingForUserInput: false,
                      orchestratorTask: undefined,
                      readinessDecision: ({ event }: any) => event.output?.readinessDecision || 'CLARIFY'
                    }),
                    'sendToTts'
                  ]
                }
              ],
              onError: {
                target: 'speaking_prompt',
                actions: [
                  assign({
                    responseText: "I'm sorry, I had trouble processing that. Are you ready to start the scenario?",
                    waitingForUserInput: false
                  }),
                  'sendToTts'
                ]
              }
            }
          },
          speaking_readiness_response: {
            meta: { interruptible: false }, // Readiness response should not be interruptible
            entry: [
              assign({
                currentState: 'prompt_for_readiness.speaking_readiness_response',
                waitingForUserInput: false,
                ttsAgenda: 'speak'
              })
            ],
            on: {
              TTS_FINISHED: {
                target: '#osce.finalize_case_selection',
                actions: [
                  assign({
                    ttsFinished: true
                  })
                ]
              }
            }
          }
        }
      },
      // case_selection state removed as its functionality is now handled by prompt_for_readiness
      finalize_case_selection: {
        meta: { interruptible: false }, // Case selection process should not be interruptible
        initial: 'loading_case',
        states: {
          loading_case: {
            meta: { interruptible: false }, // "Loading your case..." should not be interruptible
            entry: [
              assign({
                currentState: 'finalize_case_selection.loading_case'
                // Removed responseText: 'Loading your case...' to prevent duplicate TTS
                // Only the final case loading completion will set responseText and trigger TTS
              })
            ],
            invoke: {
              id: 'fetchCaseService',
              src: 'fetchCaseForUserActor',
              input: ({ context }) => ({ userId: context.userId, sessionType: context.sessionType }),
              onDone: [
                // Case 1: NEW_CASE_SELECTED - proceed with history taking
                {
                  guard: ({ event }) => event.output.status === 'NEW_CASE_SELECTED',
                  target: '#osce.history_taking_main',
                  actions: [
                    assign({
                      caseData: ({ event }) => {
                        // Type narrowing with status check
                        return event.output.status === 'NEW_CASE_SELECTED' ? event.output.caseData : null;
                      },
                      selectedCaseId: ({ event }) => {
                        // Type narrowing with status check
                        return event.output.status === 'NEW_CASE_SELECTED' ? event.output.caseData.id : null;
                      },
                      responseText: ({ event }) => {
                        // Type narrowing with status check
                        if (event.output.status === 'NEW_CASE_SELECTED') {
                          return event.output.caseData?.patient_agent?.initial_briefing || 
                                'Your case is ready. You can begin taking the patient history.';
                        }
                        return 'Your case is ready.';
                      },
                      caseLoadingError: null,
                      ttsAgenda: 'speak',
                      waitingForUserInput: true,
                      availableCasesToRepeat: undefined // Clear if there was a previous value
                    }),
                    ({ event }) => {
                      if (event.output.status === 'NEW_CASE_SELECTED') {
                        console.log(`[XState] Case selected successfully: ${event.output.caseData.id}`);
                        console.log(`[XState] TTS will be handled by next state transition (not immediately)`);
                      }
                    }
                  ]
                },
                // Case 2: ALL_CASES_COMPLETED - prompt user if they want to repeat a case
                {
                  guard: ({ event }) => event.output.status === 'ALL_CASES_COMPLETED',
                  target: '#osce.transition_orchestrator',
                  actions: [
                    assign({
                      availableCasesToRepeat: ({ event }) => {
                        // Type narrowing with status check
                        return event.output.status === 'ALL_CASES_COMPLETED' ? event.output.availableCasesToRepeat : undefined;
                      },
                      responseText: "You've completed all available cases for your preferences. Would you like to practice one again?",
                      ttsAgenda: 'speak_and_listen',
                      orchestratorTask: 'CONFIRM_REPEAT_CASE',
                      caseData: null,
                      selectedCaseId: null,
                      caseLoadingError: null,
                      waitingForUserInput: true
                    }),
                    'sendToTts',
                    ({ event }) => {
                      if (event.output.status === 'ALL_CASES_COMPLETED') {
                        console.log(`[XState] All cases completed, prompting for repeat. Available to repeat: ${event.output.availableCasesToRepeat.length}`);
                      }
                    }
                  ]
                },
                // Case 3: NO_CASES_MATCHING_PREFERENCES - inform user
                {
                  guard: ({ event }) => event.output.status === 'NO_CASES_MATCHING_PREFERENCES',
                  target: '#osce.error',
                  actions: [
                    assign({
                      responseText: "Sorry, there are no active cases available that match your current preferences. You might want to adjust your preferences or check back later.",
                      caseData: null,
                      selectedCaseId: null,
                      caseLoadingError: 'NO_CASES_MATCHING_PREFERENCES',
                      ttsAgenda: 'speak',
                      waitingForUserInput: false,
                      availableCasesToRepeat: undefined
                    }),
                    'sendToTts',
                    () => console.log('[XState] No cases matching preferences')
                  ]
                },
                // Case 4: ERROR (default) - handle error
                {
                  target: '#osce.error', 
                  actions: [
                    assign({
                      caseLoadingError: ({ event }) => {
                        if (event.output.status === 'ERROR') {
                          return event.output.error instanceof Error ? 
                            event.output.error.message : 
                            String(event.output.error);
                        }
                        return 'Unknown error loading case';
                      },
                      responseText: 'Sorry, I could not load a case for you.',
                      caseData: null,
                      selectedCaseId: null,
                      ttsAgenda: 'speak',
                      waitingForUserInput: false,
                      availableCasesToRepeat: undefined
                    }),
                    'sendToTts',
                    ({ event }) => console.error('[XState] Error selecting case:', 
                      event.output.status === 'ERROR' ? event.output.error : event.output)
                  ]
                }
              ],
              onError: {
                target: '#osce.error',
                actions: [
                  assign({
                    caseLoadingError: ({ event }) => (event.error as Error)?.message || 'Unknown error loading case.',
                    responseText: 'Sorry, I could not load a case for you.',
                    caseData: null,
                    selectedCaseId: null,
                    ttsAgenda: 'speak',
                    waitingForUserInput: false
                  }),
                  'sendToTts',
                  (context) => console.error('[XState] Error in fetchCaseForUserActor:', context)
                ]
              }
            }
          }
        }
      },
      history_taking_main: {
        id: 'history_taking_id',
        initial: 'intro_speaking', // Start with intro message first
        entry: [
          assign({ 
            currentState: 'history_taking_main', // Match state name
            waitingForUserInput: false, // Don't wait for input during intro
            historyTakingStartTime: Date.now()
          }),
          'startHistoryTakingTracking',
          'resetTurnSpecificContext', // CRITICAL: Reset VAD state when entering this state
          'logStateEntry'
        ],
        after: {
          // 7 minutes = 420,000 milliseconds
          420000: {
            target: '.system_prompting_transition',
            actions: [
              assign({
                responseText: "Seven minutes have passed. Would you like to move on to the examination phase?",
                waitingForUserInput: false
              }),
              'setTtsStartTime',
              'sendToTts'
            ]
          }
        },
        states: {
          intro_speaking: {
            meta: { interruptible: false }, // History taking intro message should not be interruptible
            entry: [
              assign({
                currentState: 'history_taking_main.intro_speaking',
                waitingForUserInput: false,
                ttsAgenda: 'speak'
              }),
              'logStateEntry',
              'sendToTts'
            ],
            on: {
              TTS_FINISHED: {
                target: 'waiting_for_user_input',
                actions: [
                  assign({
                    waitingForUserInput: true,
                    ttsAgenda: 'listen',
                    ttsFinished: true,
                    turnFinalizedAt: Date.now() // Set when AI finishes speaking and is ready to listen
                    // FANG-FIX: Removed premature graceChain reset - chain should only reset when grace period expires
                  })
                ]
              }
            }
          },
          waiting_for_user_input: {
            meta: { interruptible: true }, // Regular conversation should be interruptible
            entry: [
              assign({
                currentState: 'history_taking_main.waiting_for_user_input',
                waitingForUserInput: true,
                ttsAgenda: 'listen',
              }),
              'logStateEntry',
              'manageActivityTimers'
              // CRITICAL FIX: Don't reset turn context here - it should only reset when starting a NON-grace-period turn
              // 'resetTurnSpecificContext' // Use existing action to reset VAD counters
            ],
            on: {
              USER_UTTERANCE: [

                {
                  // FAANG-LEVEL XSTATE-CENTRIC GRACE PERIOD FLOW
                  // STEP 1: Always evaluate grace period using context
                  guard: ({ event }) => event.type === 'USER_UTTERANCE' && event.isFinal,
                  actions: ['evaluateGracePeriodFromContext'],
                  target: '#osce.grace_period_decision'
                },
                {
                  // For partial transcripts (event.isFinal === false), just update context if needed, but don't transition.
                  // This is important for live transcript display or other interim processing.
                  guard: ({ event }) => event.type === 'USER_UTTERANCE' && event.isFinal === false,
                  actions: assign({
                    // Example: Update a temporary transcript field if your UI uses it
                    // tempLiveTranscript: ({ event }) => (event as any).transcription,
                    // Ensure hasSpeechDetected is true if we get any utterance event
                    hasSpeechDetected: true 
                  })
                }
              ],
              // INTERRUPT handler for waiting_for_user_input - just log since we're already listening
              INTERRUPT: {
                actions: [
                  ({ context }) => {
                    console.log(`[INTERRUPT-HANDLER-WAITING] Received INTERRUPT while waiting for user input on ${context.callSid} - already in listening state`);
                  }
                ]
              },
              SPEECH_FRAME: {
                actions: [
                  'manageActivityTimers', // Reset inactivity timers on voice activity
                  assign({
                    speechFramesInCurrentTurn: ({ context }) => context.speechFramesInCurrentTurn + 1,
                    consecutiveSilenceFrames: 0,
                    hasSpeechDetected: true,
                    turnFinalized: false // Reset if speech starts
                  })
                ]
              },
              // 🎯 LIVEKIT CENTRALIZATION EVENTS - Move execution thread to XState machine
              LIVEKIT_VAD_DATA: {
                actions: [
                  // First action: Update context with interruption timestamp
                  assign(({ context, event }) => {
                    const typedEvent = event as OsceEvent & { type: 'LIVEKIT_VAD_DATA'; speech: boolean; probability: number; callSid: string };
                    console.log(`🔍 [LIVEKIT-VAD-CENTRALIZED] ✅ VAD event received in XState: speech=${typedEvent.speech}, probability=${typedEvent.probability.toFixed(3)}`);
                    
                    // 🔍 INVESTIGATION: Interruption detection tracing
                    console.log(`🔍 [INTERRUPTION-DETECT] VAD Event Analysis:`);
                    console.log(`🔍 [INTERRUPTION-DETECT] - CallSid: ${typedEvent.callSid}`);
                    console.log(`🔍 [INTERRUPTION-DETECT] - Speech detected: ${typedEvent.speech}`);
                    console.log(`🔍 [INTERRUPTION-DETECT] - Probability: ${typedEvent.probability.toFixed(3)}`);
                    console.log(`🔍 [INTERRUPTION-DETECT] - Current turn number: ${context.turnNumber}`);
                    console.log(`🔍 [INTERRUPTION-DETECT] - Timestamp: ${Date.now()}`);
                    
                    // Import constants synchronously
                    const { INTERRUPT_SPEECH_THRESHOLD_LIVEKIT } = require('./constants');
                    const interruptThreshold = INTERRUPT_SPEECH_THRESHOLD_LIVEKIT;
                    
                    // Only proceed if speech detected above threshold
                    if (!typedEvent.speech || typedEvent.probability <= interruptThreshold) {
                      console.log(`🔍 [INTERRUPTION-DETECT] ❌ No interruption: speech=${typedEvent.speech}, probability=${typedEvent.probability.toFixed(3)} <= threshold=${interruptThreshold}`);
                      return {}; // No changes
                    }
                    
                    // Check if model is speaking (we'll check this again in the async action)
                    const { calls } = require('./server');
                    const callState = calls[typedEvent.callSid];
                    if (!callState?.isModelSpeaking) {
                      console.log(`🔍 [INTERRUPTION-DETECT] ❌ No interruption: Model not speaking (isModelSpeaking=${callState?.isModelSpeaking})`);
                      return {}; // No changes
                    }
                    
                    console.log(`🔍 [INTERRUPTION-DETECT] ✅ INTERRUPTION DETECTED: Proceeding to record interruption timestamp`);
                    console.log(`🔍 [INTERRUPTION-DETECT] - Model is speaking: ${callState.isModelSpeaking}`);
                    console.log(`🔍 [INTERRUPTION-DETECT] - Speech probability: ${typedEvent.probability.toFixed(3)} > ${interruptThreshold}`);
                    
                    console.log(`[LIVEKIT-VAD-CENTRALIZED] Interruption decision made by XState: probability=${typedEvent.probability.toFixed(3)} > threshold=${interruptThreshold}`);
                    
                    // CRITICAL FIX: Record interruption timestamp in XState context
                    const now = Date.now();
                    const currentTurnIndex = context.turnNumber || 0;
                    const newTurnTimings = [...(context.turnTimings || [])];
                    
                    console.log(`[LIVEKIT-VAD-CENTRALIZED] Recording interruption timestamp for turn ${currentTurnIndex} at ${now}`);
                    
                    // 🔍 INVESTIGATION: Interruption recording details
                    console.log(`🔍 [INTERRUPTION-RECORD] Interruption Recording Analysis:`);
                    console.log(`🔍 [INTERRUPTION-RECORD] - Current turn index: ${currentTurnIndex}`);
                    console.log(`🔍 [INTERRUPTION-RECORD] - Interruption timestamp: ${now}`);
                    console.log(`🔍 [INTERRUPTION-RECORD] - Existing turnTimings count: ${context.turnTimings?.length || 0}`);
                    if (context.turnTimings?.length) {
                      context.turnTimings.forEach((t, i) => {
                        console.log(`🔍 [INTERRUPTION-RECORD] - Existing turn ${i}: index=${t.turnIndex}, audioStarted=${!!t.audioStreamingStartAt}, interrupted=${!!t.interruptionTimestamp}`);
                      });
                    }
                    
                    // Use local helper function for turn timing management
                    const { timing } = getOrCreateTurnTiming(context, currentTurnIndex, newTurnTimings);
                    
                    console.log(`🔍 [INTERRUPTION-RECORD] Turn timing before recording: audioStreamingStartAt=${timing.audioStreamingStartAt}, interruptionTimestamp=${timing.interruptionTimestamp}`);
                    
                    // Only set interruption if TTS has actually started and we don't already have one  
                    if (timing.audioStreamingStartAt && !timing.interruptionTimestamp) {
                      timing.interruptionTimestamp = now;
                      timing.audioStreamingEndAt = now;
                      if (timing.audioStreamingStartAt) {
                        timing.audioDurationMs = now - timing.audioStreamingStartAt;
                        console.log(`[LIVEKIT-VAD-CENTRALIZED] Set interruption timestamp ${now} for turn ${currentTurnIndex}, audio duration: ${timing.audioDurationMs}ms`);
                      }
                      
                      console.log(`[LIVEKIT-VAD-CENTRALIZED] Updated turn timings with interruption timestamp`);
                      
                      // 🔍 INVESTIGATION: Interruption recording success
                      console.log(`🔍 [INTERRUPTION-RECORD] ✅ INTERRUPTION RECORDED SUCCESSFULLY:`);
                      console.log(`🔍 [INTERRUPTION-RECORD] - Turn index: ${currentTurnIndex}`);
                      console.log(`🔍 [INTERRUPTION-RECORD] - Interruption timestamp: ${now}`);
                      console.log(`🔍 [INTERRUPTION-RECORD] - Audio duration: ${timing.audioDurationMs}ms`);
                      
                      // 🔍 INVESTIGATION: Race condition detection - recording timing
                      console.log(`🔍 [RACE-DETECT] INTERRUPTION RECORDED - Race Condition Analysis:`);
                      console.log(`🔍 [RACE-DETECT] - Recording thread: LIVEKIT_VAD_DATA handler`);
                      console.log(`🔍 [RACE-DETECT] - Recording timestamp: ${now}`);
                      console.log(`🔍 [RACE-DETECT] - Turn context state: TTS_STARTED (audioStreamingStartAt=${timing.audioStreamingStartAt})`);
                      console.log(`🔍 [RACE-DETECT] - Context update: SYNCHRONOUS (XState assign)`);
                      console.log(`🔍 [RACE-DETECT] - Next retrieval calls to getInterruptionTimestamp() should find: ${now}`);
                      console.log(`🔍 [RACE-DETECT] - Potential race window: If getInterruptionTimestamp() called before this context update completes`);
                      
                      return { turnTimings: newTurnTimings };
                    } else if (!timing.audioStreamingStartAt) {
                      console.warn(`[LIVEKIT-VAD-CENTRALIZED] TTS not started yet for turn ${currentTurnIndex}, skipping interruption timestamp - THIS IS CORRECT FOR PRE-TTS UNLIMITED GRACE PERIOD!`);
                      
                      // 🔍 INVESTIGATION: Interruption recording skipped - PRE-TTS scenario
                      console.log(`🔍 [INTERRUPTION-RECORD] ❌ INTERRUPTION NOT RECORDED - PRE-TTS SCENARIO:`);
                      console.log(`🔍 [INTERRUPTION-RECORD] - Turn index: ${currentTurnIndex}`);
                      console.log(`🔍 [INTERRUPTION-RECORD] - Reason: TTS not started (audioStreamingStartAt=${timing.audioStreamingStartAt})`);
                      console.log(`🔍 [INTERRUPTION-RECORD] - This should result in PRE-TTS unlimited grace period`);
                      
                      // 🔍 INVESTIGATION: Race condition detection - PRE-TTS scenario
                      console.log(`🔍 [RACE-DETECT] NO INTERRUPTION RECORDED - PRE-TTS Race Analysis:`);
                      console.log(`🔍 [RACE-DETECT] - Turn state: PRE-TTS (audioStreamingStartAt=undefined)`);
                      console.log(`🔍 [RACE-DETECT] - Expected behavior: No interruption timestamp needed`);
                      console.log(`🔍 [RACE-DETECT] - Grace period evaluation should use: PRE-TTS unlimited mode`);
                      console.log(`🔍 [RACE-DETECT] - CRITICAL: If grace period shows POST-TTS with NaN, this indicates wrong TTS state detection!`);
                      console.log(`🔍 [RACE-DETECT] - Race condition possibility: TTS state inconsistency between recording and evaluation threads`);
                      
                    } else if (timing.interruptionTimestamp) {
                      console.log(`[LIVEKIT-VAD-CENTRALIZED] Turn ${currentTurnIndex} already has interruption timestamp ${timing.interruptionTimestamp}, skipping`);
                      
                      // 🔍 INVESTIGATION: Interruption recording skipped - already exists
                      console.log(`🔍 [INTERRUPTION-RECORD] ❌ INTERRUPTION NOT RECORDED - ALREADY EXISTS:`);
                      console.log(`🔍 [INTERRUPTION-RECORD] - Turn index: ${currentTurnIndex}`);
                      console.log(`🔍 [INTERRUPTION-RECORD] - Existing timestamp: ${timing.interruptionTimestamp}`);
                    }
                    
                    return {}; // No changes needed
                  }),
                  // Second action: Handle the actual interruption asynchronously
                  async ({ context, event }) => {
                    const typedEvent = event as OsceEvent & { type: 'LIVEKIT_VAD_DATA'; speech: boolean; probability: number; callSid: string };
                    
                    // Import async functions
                    const { validateInterruptionCapability, handleInterruption, calls } = await import('./server');
                    const { INTERRUPT_SPEECH_THRESHOLD_LIVEKIT } = await import('./constants');
                    const interruptThreshold = INTERRUPT_SPEECH_THRESHOLD_LIVEKIT;
                    
                    if (typedEvent.speech && typedEvent.probability > interruptThreshold) {
                      const callState = calls[typedEvent.callSid];
                      if (callState?.isModelSpeaking) {
                        // Validate interruption capability
                        const canInterrupt = await validateInterruptionCapability(typedEvent.callSid, 'livekit' as any);
                        if (canInterrupt) {
                          console.log(`[LIVEKIT-VAD-CENTRALIZED] Calling handleInterruption utility function`);
                          await handleInterruption(typedEvent.callSid, 'livekit' as any);
                        } else {
                          console.log(`[LIVEKIT-VAD-CENTRALIZED] Interruption blocked by capability check`);
                        }
                      }
                    }
                  }
                ]
              },
              LIVEKIT_INIT_STT: {
                actions: [
                  async ({ context, event }) => {
                    const typedEvent = event as OsceEvent & { type: 'LIVEKIT_INIT_STT'; callSid: string };
                    console.log(`🔍 [LIVEKIT-STT-CENTRALIZED] ✅ STT initialization event received for LiveKit call: ${typedEvent.callSid}`);
                    
                    // Move STT provider selection logic here from server.ts
                    const { STT_PROVIDER, initializeAzureStt, initializeDeepgramStt, calls } = await import('./server');
                    console.log(`🔍 [LIVEKIT-STT-CENTRALIZED] Using STT provider: ${STT_PROVIDER}`);
                    
                    try {
                      if (STT_PROVIDER === 'azure') {
                        console.log(`🔍 [LIVEKIT-STT-CENTRALIZED] Initializing Azure STT for LiveKit`);
                        const sttProvider = initializeAzureStt(typedEvent.callSid, 'livekit' as any);
                        if (calls[typedEvent.callSid]) {
                          calls[typedEvent.callSid].sttProvider = sttProvider;
                          console.log(`🔍 [LIVEKIT-STT-CENTRALIZED] ✅ Azure STT provider successfully set for ${typedEvent.callSid}`);
                        } else {
                          console.error(`🔍 [LIVEKIT-STT-CENTRALIZED] ❌ No call state found for ${typedEvent.callSid}`);
                        }
                      } else if (STT_PROVIDER === 'assemblyai') {
                        console.log(`🔍 [LIVEKIT-STT-CENTRALIZED] AssemblyAI STT for LiveKit not implemented yet - defaulting to Azure`);
                        const sttProvider = initializeAzureStt(typedEvent.callSid, 'livekit' as any);
                        if (calls[typedEvent.callSid]) {
                          calls[typedEvent.callSid].sttProvider = sttProvider;
                        }
                      } else if (STT_PROVIDER === 'deepgram') {
                        console.log(`[LIVEKIT-STT-CENTRALIZED] Initializing Deepgram STT for LiveKit`);
                        const sttProvider = await initializeDeepgramStt(typedEvent.callSid, 'livekit' as any);
                        if (calls[typedEvent.callSid]) {
                          calls[typedEvent.callSid].sttProvider = sttProvider;
                        }
                      }
                      console.log(`[LIVEKIT-STT-CENTRALIZED] STT initialization completed`);
                    } catch (error) {
                      console.error(`[LIVEKIT-STT-CENTRALIZED] Error initializing STT:`, error);
                    }
                  }
                ]
              },
              LIVEKIT_PARTICIPANT_CONNECTED: {
                actions: [
                  async ({ context, event }) => {
                    const typedEvent = event as OsceEvent & { type: 'LIVEKIT_PARTICIPANT_CONNECTED'; participantSid: string; participantIdentity: string; callSid?: string };
                    const callSid = typedEvent.callSid || context.callSid;
                    console.log(`🔍 [LIVEKIT-PARTICIPANT-CONNECTED] Participant connected for call ${callSid}: ${typedEvent.participantIdentity} (${typedEvent.participantSid})`);
                    
                    // Initialize STT when participant connects
                    try {
                      const { sendLivekitInitStt } = await import('./xstateIntegration');
                      const sttInitialized = sendLivekitInitStt(callSid);
                      
                      if (sttInitialized) {
                        console.log(`🔍 [LIVEKIT-PARTICIPANT-CONNECTED] ✅ STT initialization triggered for ${callSid}`);
                      } else {
                        console.error(`🔍 [LIVEKIT-PARTICIPANT-CONNECTED] ❌ Failed to trigger STT initialization for ${callSid}`);
                      }
                    } catch (error) {
                      console.error(`🔍 [LIVEKIT-PARTICIPANT-CONNECTED] Error triggering STT initialization for ${callSid}:`, error);
                    }
                  }
                ]
              },
              SILENCE_FRAME_SHORT: [
                {
                  guard: ({ context, event }) => {
                    if (event.type !== 'SILENCE_FRAME_SHORT') return false;
                    
                    const hasFinalTranscript = event.accumulatedText && event.accumulatedText.length > 0;
                    
                    console.log(`[WFI-SILENCE-SHORT-GUARD] HasFinalTranscript: ${hasFinalTranscript}. Accumulated: "${event.accumulatedText}"`);
                    return hasFinalTranscript;
                  },
                  target: '#history_taking_id.processing',
                  actions: [
                    assign(({ context, event }) => {
                      const typedEvent = event as OsceEvent & { type: 'SILENCE_FRAME_SHORT', accumulatedText?: string };
                      console.log(`[TURN-FINALIZED] User stopped speaking - NOT setting turnFinalizedAt (only set on TTS_FINISHED)`);
                      
                      return {
                        consecutiveSilenceFrames: context.consecutiveSilenceFrames + 1,
                        waitingForUserInput: false,
                        turnFinalized: true,
                        turnNumber: (context.turnNumber || 0) + 1,
                        vadWaitLogged: false,
                        needsFinalization: true, 
                        tempAccumulatedText: typedEvent.accumulatedText || '',
                        // turnFinalizedAt: NOT set here - only set when AI finishes speaking (TTS_FINISHED)
                        // turnTimings update if necessary
                      };
                    }),
                    ({ context }) => {
                      sendParent({ type: 'VAD_TURN_COMPLETE', callSid: context.callSid, reason: 'silence_threshold' });
                    }
                  ]
                },
                {
                  actions: assign({
                    consecutiveSilenceFrames: ({ context }) => context.consecutiveSilenceFrames + 1
                  })
                }
              ],
              // REMOVED: SILENCE_FRAME_LONG handler - 8-second timeout not appropriate for history taking
              // Long silences are normal during patient interviews, should not trigger auto-prompts
              DTMF_INPUT: {
                target: '#history_taking_id.processing', 
                actions: assign({
                  lastUserUtterance: ({ event }) => `DTMF: ${(event as any).digit}`,
                  tempAccumulatedText: ({ event }) => `DTMF: ${(event as any).digit}`,
                  waitingForUserInput: false,
                  turnNumber: ({ context }) => (context.turnNumber || 0) + 1,
                  speechFramesInCurrentTurn: 0,
                  consecutiveSilenceFrames: 0,
                  hasSpeechDetected: false, // DTMF is not speech
                  turnFinalized: false,
                  audioPlaybackStartTime: undefined
                })
              },
              // CRITICAL: Handle AUDIO_PLAYBACK_STARTED in waiting_for_user_input state
              AUDIO_PLAYBACK_STARTED: {
                actions: [
                  'updateTtsTimingInContext', // FAANG-LEVEL: Update timing entities with TTS start
                  'markTtsStartedInGracePeriod', // PHASE 3-1: Enable post-TTS 3-second combination window
                  assign(({ context, event }) => {
                    const now = Date.now();
                    console.log(`[AUDIO-PLAYBACK-CONTEXT] Setting audioPlaybackStartTime to ${now} for waiting_for_user_input state`);
                    
                    // CRITICAL FIX: Use safe turn index validation for consistent synchronization
                    const eventTurnIndex = (event as any).turnIndex;
                    const turnIndex = getSafeValidTurnIndex(context, eventTurnIndex);
                    
                    // Also update turn timings with actual audio start time
                    const newTurnTimings = [...(context.turnTimings || [])];
                    const { timing } = getOrCreateTurnTiming(context, turnIndex, newTurnTimings);
                    timing.audioStreamingStartAt = now;
                    console.log(`[AUDIO-PLAYBACK-TIMING] Set audioStreamingStartAt for turn ${turnIndex} to ${now} in waiting_for_user_input (from ${eventTurnIndex !== undefined ? 'event' : 'safe fallback'})`);
                    
                    return {
                      audioPlaybackStarted: true,
                      audioPlaybackStartTime: now, // Track when audio playback started
                      turnTimings: newTurnTimings
                    };
                  })
                ]
              },
              COORDINATED_TURN_COMPLETE: {
                // ATOMIC: Use atomic grace period handler with interruption guard
                guard: ({ context, event }) => {
                  const typedEvent = event as any;
                  
                  // FIXED: More precise interruption detection - only explicit stop commands
                  const isInterruptionCommand = /^(stop|halt|cancel|pause)[\s\W]*$/i.test(typedEvent.transcriptText?.toLowerCase().trim() || '');
                  
                  if (isInterruptionCommand) {
                    console.log(`[COORDINATED-INTERRUPT] Interruption command detected in history_taking_main: "${typedEvent.transcriptText}" - will trigger immediate cancellation`);
                    // Send interrupt event immediately instead of processing normally
                    const { sendInterrupt } = require('./xstateIntegration');
                    setTimeout(() => sendInterrupt(context.callSid, typedEvent.transcriptText), 0);
                    return false; // Block normal processing
                  }
                  
                  return true; // Allow normal processing for non-interruption commands
                },
                target: '#history_taking_id.processing',
                actions: [
                  'handleCoordinatedTurnComplete',
                  'canonicalTurnCompletionHandler',
                  // Additional context updates for this specific handler
                  assign({
                    speechFramesInCurrentTurn: 0,
                    consecutiveSilenceFrames: 0,
                    hasSpeechDetected: false,
                    vadWaitLogged: false
                  })
                ]
              }
            }
          },
          processing: { // State for waiting on LLM
            entry: [
              assign({ currentState: 'history_taking_main.processing' }),
              'setLlmStartTime',
              assign(( { context } ) => {
                console.log(`[PROCESSING-ENTRY] Received turnFinalizedAt: ${context.turnFinalizedAt}`);
                
                // CHAIN FINALIZATION: Finalize any active grace chain before LLM processing
                let lastUserUtterance = context.lastUserUtterance;
                let chainWasActive = false;
                
                try {
                  const activeChain = context.registry.getActiveGraceChain();
                  if (activeChain.isActive) {
                    console.log(`[PROCESSING-ENTRY] 🏁 Finalizing active grace chain before LLM processing`);
                    console.log(`[PROCESSING-ENTRY] 🏁 Active chain: "${activeChain.accumulatedText}" (${activeChain.turnCount} turns)`);
                    
                    const finalizedChain = context.registry.finalizeGraceChain();
                    lastUserUtterance = finalizedChain.finalText;
                    chainWasActive = true;
                    
                    console.log(`[PROCESSING-ENTRY] ✅ Chain finalized: "${finalizedChain.finalText}"`);
                    console.log(`[PROCESSING-ENTRY] ✅ Turn indices processed: [${finalizedChain.turnIndices.join(', ')}]`);
                  } else {
                    console.log(`[PROCESSING-ENTRY] No active grace chain - using individual utterance`);
                  }
                } catch (error) {
                  console.error(`[PROCESSING-ENTRY] ❌ Error finalizing grace chain: ${error.message}`);
                  // Fallback to existing logic
                }
                
                // FALLBACK LOGIC: Use tempAccumulatedText if no chain was active
                if (!chainWasActive && context.tempAccumulatedText) {
                  if (!(context.lastUserUtterance === context.tempAccumulatedText && !context.tempAccumulatedText.includes(' '))) {
                    lastUserUtterance = context.tempAccumulatedText;
                    console.log(`[PROCESSING-ENTRY] Using tempAccumulatedText fallback: "${lastUserUtterance}"`);
                  }
                }
                
                console.log(`[PROCESSING-ENTRY] Final LLM input text: "${lastUserUtterance}"`);
                console.log(`[PROCESSING-ENTRY] Chain was active: ${chainWasActive}`);
                
                const newTurnTimings = [...(context.turnTimings || [])];
                const turnIndex = (context.turnNumber || 0);
                const currentTiming = newTurnTimings.find(t => t.turnIndex === turnIndex);
                if (currentTiming) {
                  currentTiming.ragRequestStartAt = Date.now();
                }

                // REGISTRY-TO-CONTEXT SYNC: Get updated registry state after finalization
                const finalRegistryChain = context.registry.getActiveGraceChain();
                
                const result = {
                  lastUserUtterance: lastUserUtterance,
                  tempAccumulatedText: undefined,
                  // CRITICAL FIX: Don't generate new operation ID here - use existing one from canonicalTurnCompletionHandler
                  // This prevents multiple LLM requests from running simultaneously
                  currentLlmOperationId: context.currentLlmOperationId || generateUniqueOperationId(context.callSid),
                  turnFinalizedAt: context.turnFinalizedAt,
                  previousUserStoppedAt: context.previousUserStoppedAt, // CRITICAL FIX: Preserve previousUserStoppedAt for grace period calculation
                  previousTurnFinalizedAt: context.previousTurnFinalizedAt, // TURN-FINALIZATION-TIMING: Preserve previousTurnFinalizedAt for accurate grace period calculation
                  turnTimings: newTurnTimings,
                  // REGISTRY-TO-CONTEXT SYNC: Update context graceChain with finalized registry state
                  graceChain: {
                    isActive: finalRegistryChain.isActive,
                    accumulatedText: finalRegistryChain.accumulatedText,
                    turnCount: finalRegistryChain.turnCount,
                    mostRecentTurnContext: context.graceChain?.mostRecentTurnContext // Preserve existing context
                  }
                  // REMOVED: Duplicate conversation update - turnBasedGracePeriodHandler is the single source of truth
                };
                
                console.log(`[PROCESSING-ENTRY] Returning turnFinalizedAt: ${result.turnFinalizedAt}`);
                console.log(`[GRACE-PERIOD-TIMING-DEFENSIVE] Processing state entry preserving previousUserStoppedAt=${result.previousUserStoppedAt}`);
                console.log(`[TURN-FINALIZATION-TIMING] Processing state entry preserving previousTurnFinalizedAt=${result.previousTurnFinalizedAt}`);
                return result;
              })
            ],
            invoke: {
              id: 'invokeHistoryLlm',
              src: 'historyTakingLlmActor',
              input: ({ context }) => {
                // 🔍 CRITICAL DEBUG: Verify context propagation from grace period handler
                console.log(`🔍 [LLM-INPUT-DEBUG] ===== AI RESPONSE GENERATION INPUT =====`);
                console.log(`🔍 [LLM-INPUT-DEBUG] lastUserUtterance: "${context.lastUserUtterance}"`);
                console.log(`🔍 [LLM-INPUT-DEBUG] tempAccumulatedText: "${context.tempAccumulatedText || 'undefined'}"`);
                console.log(`🔍 [LLM-INPUT-DEBUG] graceChain.accumulatedText: "${context.graceChain?.accumulatedText || 'undefined'}"`);
                console.log(`🔍 [LLM-INPUT-DEBUG] conversation length: ${context.conversation?.length || 0}`);
                console.log(`🔍 [LLM-INPUT-DEBUG] turnNumber: ${context.turnNumber}`);
                
                if (context.conversation && context.conversation.length > 0) {
                  const lastConversationEntry = context.conversation[context.conversation.length - 1];
                  console.log(`🔍 [LLM-INPUT-DEBUG] last conversation entry: "${lastConversationEntry?.text?.substring(0, 100)}..."`);
                  console.log(`🔍 [LLM-INPUT-DEBUG] last conversation speaker: ${lastConversationEntry?.speaker}`);
                }
                
                console.log(`🔍 [LLM-INPUT-DEBUG] ❓ CRITICAL QUESTION: Does lastUserUtterance contain combined text or just individual utterance?`);
                console.log(`🔍 [LLM-INPUT-DEBUG] ===============================================`);
                
                return {
                  userUtterance: context.lastUserUtterance,
                  caseData: context.caseData,
                  conversationHistory: context.conversation,
                  callSid: context.callSid,
                  turnNumber: context.turnNumber,
                  operationId: context.currentLlmOperationId
                };
              },
              onDone: {
                target: 'responding',
                actions: [
                  assign((args) => {
                    // Check if this response is from the current LLM operation
                    const responseOperationId = args.event.output.operationId?.toString();
                    const currentOperationId = args.context.currentLlmOperationId;
                    
                    if (responseOperationId && currentOperationId && responseOperationId !== currentOperationId) {
                      console.log(`[LLM-STALE-RESPONSE] Ignoring stale LLM response from operation ${responseOperationId} (current: ${currentOperationId})`);
                      // Return unchanged context to ignore this stale response
                      return {};
                    }
                    
                    console.log(`[LLM-CURRENT-RESPONSE] Processing current LLM response from operation ${responseOperationId}`);
                    console.log(`[LLM-ONDONE] Received turnFinalizedAt: ${args.context.turnFinalizedAt}`);
                    
                    const newTurnTimings = [...(args.context.turnTimings || [])];
                    const turnIndex = (args.context.turnNumber || 0);
                    const currentTiming = newTurnTimings.find(t => t.turnIndex === turnIndex);
                    if (currentTiming) {
                      currentTiming.ragResponseReceivedAt = Date.now();
                    }
                    // CRITICAL FIX: The return from this assign was wiping context.
                    // We must merge with the existing context to preserve fields like `turnFinalizedAt`.
                    const result = {
                      ...args.context, // Preserve all existing context
                      responseText: args.event.output.responseText,
                      wasStreamed: args.event.output.wasStreamed || false,
                      llmResponseReceived: true,
                      conversation: [
                        ...(args.context.conversation || []),
                        { 
                          speaker: 'assistant', 
                          text: args.event.output.responseText,
                          timestamp: new Date().toISOString(),
                          xstate_state: getMainState(args.context.currentState || 'unknown'),
                          interrupted: isInterrupted(args.context.turnNumber || 0, args.context.turnTimings || []),
                          turnIndex: args.context.turnNumber || 0
                        }
                      ],
                      turnTimings: newTurnTimings,
                      // CRITICAL FIX: Explicitly preserve fields needed for subsequent states
                      lastUserUtterance: args.context.lastUserUtterance,
                      turnFinalizedAt: args.context.turnFinalizedAt,
                      audioPlaybackStartTime: args.context.audioPlaybackStartTime,
                      audioPlaybackStarted: args.context.audioPlaybackStarted,
                      currentLlmOperationId: args.context.currentLlmOperationId
                    };
                    
                    console.log(`[LLM-ONDONE] Returning turnFinalizedAt: ${result.turnFinalizedAt}`);
                    return result;
                  })
                  // REMOVED: 'sendToTts' - This was causing duplicate TTS generation.
                  // TTS will be triggered by the 'responding' state entry action instead.
                ]
              },
              onError: {
                target: '#osce.error', // Use absolute path to top-level error state
                actions: assign({
                  responseText: 'Error processing request.'
                })
              }
            },
            // Handle additional user input while LLM is processing
            on: {
              // Handle additional speech while processing
              SPEECH_FRAME: {
                actions: [
                  'manageActivityTimers', // Reset inactivity timers on voice activity
                  assign({
                    speechFramesInCurrentTurn: ({ context }) => context.speechFramesInCurrentTurn + 1,
                    consecutiveSilenceFrames: 0,
                    hasSpeechDetected: true
                  })
                ]
              },
              
              // PHASE 2: Event-based interruption handling during processing (LLM) state
              USER_INTERRUPTION_DETECTED: {
                target: 'waiting_for_user_input', // Transition to listening state
                actions: [
                  'updateInterruptionTimestamp', // Record interruption timestamp in turnTimings
                  assign(({ context, event }) => {
                    const interruptEvent = event as any;
                    const now = Date.now();
                    
                    console.log(`[INTERRUPTION-PROCESSING] VAD interruption detected during LLM processing - callSid: ${interruptEvent.callSid}, probability: ${interruptEvent.vadProbability}, timestamp: ${interruptEvent.timestamp}`);
                    
                    // Since we're in processing state, this is Pre-TTS interruption
                    // According to grace period logic: Pre-TTS interruptions should always combine
                    console.log(`[INTERRUPTION-PROCESSING] Pre-TTS interruption - cancelling LLM processing and preparing to capture combined input`);
                    
                    return {
                      // Mark that we're handling an interruption
                      interruptionHandlingStarted: now,
                      interruptionDuringProcessing: true,
                      // Store VAD probability for logging/debugging
                      lastVadProbability: interruptEvent.vadProbability,
                      // Reset turn state for capturing new input
                      waitingForUserInput: true,
                      hasSpeechDetected: false,
                      consecutiveSilenceFrames: 0
                    };
                  }),
                  // Cancel any ongoing LLM processing
                  ({ context, event }) => {
                    const interruptEvent = event as any;
                    console.log(`[INTERRUPTION-PROCESSING] Sending LLM cancellation for operation: ${context.currentLlmOperationId}`);
                    // The LLM actor should handle cancellation via the target transition
                  }
                ]
              },
              // Handle additional transcripts while LLM is processing - CASCADING INTERRUPTION
              USER_UTTERANCE: [
                {
                  // CASCADING GRACE PERIOD: Combine and restart LLM processing within same turn
                  guard: 'isWithinCascadingGracePeriod',
                  target: '#history_taking_id.processing', // Self-transition to restart LLM
                  actions: [
                    'handleCascadingInterruption', // Send cancellation to server
                    'applyCombinedUtterance' // Update context with combined text
                  ]
                },
                {
                  // REMOVED: Cross-turn refinement logic that was incorrectly combining utterances from different turns
                  // This logic was replacing lastUserUtterance with transcripts from completely different conversation turns
                  // Speech recognition refinement should ONLY happen within the grace period (handled above)
                  
                  // For final transcripts outside grace period, just track speech without replacing text
                  guard: ({ event }) => event.isFinal === true,
                  actions: assign({
                    hasSpeechDetected: true,
                    // Track that we received additional input during processing for interruption handling
                    hasAdditionalInput: true
                    // REMOVED: lastUserUtterance replacement - this was causing cross-turn combinations
                  })
                },
                {
                  // For partial transcripts, just track speech
                  actions: assign({
                    hasSpeechDetected: true
                  })
                }
              ],
              // CRITICAL FIX: Handle explicit INTERRUPT events from server using working commit approach
              INTERRUPT: {
                target: 'waiting_for_user_input', // Go to listening state to capture interrupting speech
                actions: [
                  assign(({ context, event }) => {
                    const typedEvent = event as any;
                    const interruptText = typedEvent.transcriptText || '';
                    const interruptTimestamp = typedEvent.interruptTimestamp || Date.now();
                    
                    console.log(`[INTERRUPT-PROCESSING] Interrupt during AI processing with text: "${interruptText}", timestamp: ${interruptTimestamp}`);
                    
                    // CRITICAL FIX: Store interruption timestamp in turnTimings for coordinator access
                    // Use currentlyPlayingTurnIndex instead of turnNumber to match server-side logic
                    const currentTurnIndex = context.currentlyPlayingTurnIndex !== undefined ? context.currentlyPlayingTurnIndex : (context.turnNumber || 0);
                    const newTurnTimings = [...(context.turnTimings || [])];
                    
                    // Find or create timing for current turn
                    let turnTiming = newTurnTimings.find(t => t.turnIndex === currentTurnIndex);
                    if (!turnTiming) {
                      turnTiming = { turnIndex: currentTurnIndex };
                      newTurnTimings.push(turnTiming);
                    }
                    
                    // Set interruption timestamp
                    turnTiming.interruptionTimestamp = interruptTimestamp;
                    console.log(`[INTERRUPT-TIMING-STORED] Set interruption timestamp ${interruptTimestamp} for turn ${currentTurnIndex} in XState context (currentlyPlayingTurnIndex: ${context.currentlyPlayingTurnIndex}, turnNumber: ${context.turnNumber})`);
                    
                    // CRITICAL FIX: Also update the global server state to ensure synchronization
                    try {
                      const { calls } = require('./server');
                      if (calls[context.callSid]) {
                        // Update the global turn timing as well
                        const globalTurnTimings = calls[context.callSid].turnTimings || [];
                        let globalTurnTiming = globalTurnTimings.find(t => t.turnIndex === currentTurnIndex);
                        if (!globalTurnTiming) {
                          globalTurnTiming = { turnIndex: currentTurnIndex };
                          globalTurnTimings.push(globalTurnTiming);
                        }
                        globalTurnTiming.interruptionTimestamp = interruptTimestamp;
                        calls[context.callSid].turnTimings = globalTurnTimings;
                        console.log(`[INTERRUPT-TIMING-SYNC] Synchronized interruption timestamp ${interruptTimestamp} for turn ${currentTurnIndex} to global server state`);
                      }
                    } catch (error) {
                      console.error(`[INTERRUPT-TIMING-SYNC] Error synchronizing interruption timestamp to global state:`, error);
                    }
                    
                    // WORKING COMMIT APPROACH: Distinguish between actual interruptions and grace period utterances
                    const isActualInterruption = /\b(stop|enough|no|wait|pause|cancel|halt)\b/i.test(interruptText.toLowerCase());
                    
                    if (isActualInterruption) {
                      console.log(`[INTERRUPT-ACTUAL] Detected actual interruption command: "${interruptText}" - stopping audio immediately`);
                      
                      // Immediate audio stopping using shared cancellation logic
                      cancelAllPreviousTurnsHelper(context, 'ACTUAL_INTERRUPT', interruptText);
                      
                      return {
                        // For actual interruptions, start fresh without grace period logic
                        lastUserUtterance: '', // Clear to start fresh after interruption
                        tempAccumulatedText: '', // Clear accumulated text
                        waitingForUserInput: true,
                        llmResponseReceived: false,
                        responseText: undefined,
                        turnFinalizedAt: undefined, // Clear to prevent grace period combinations
                        audioPlaybackStartTime: undefined, // Clear audio timing
                        turnNumber: (context.turnNumber || 0) + 1, // Increment for new turn
                        currentTurnStartTime: Date.now(),
                        turnTimings: newTurnTimings // Include updated timing with interruption timestamp
                      };
                    } else {
                      // 🚀 UNIFIED GRACE PERIOD CHECK - For non-interruption utterances
                      const now = Date.now();
                      const graceEval = evaluateUnifiedGracePeriod(context, now);
                      const isWithinGracePeriod = graceEval.withinGracePeriod;
                      const gracePeriodType = graceEval.gracePeriodType;
                      
                      if (isWithinGracePeriod) {
                        console.log(`[INTERRUPT-GRACE-PERIOD] ${gracePeriodType}: Non-interruption utterance within grace period, will combine when user finishes speaking: "${interruptText}"`);
                        
                        // Cancel current processing for grace period combination
                        cancelAllPreviousTurnsHelper(context, 'GRACE_PERIOD', '');
                        
                        return {
                          // Preserve context for grace period combination
                          tempAccumulatedText: '', // Clear to start fresh for interrupting speech
                          waitingForUserInput: true,
                          llmResponseReceived: false,
                          responseText: undefined,
                          // PRESERVE grace period context for combination
                          turnFinalizedAt: context.turnFinalizedAt, // Preserve for grace period logic
                          audioPlaybackStartTime: context.audioPlaybackStartTime, // CRITICAL FIX: PRESERVE for grace period timing calculation
                          turnNumber: context.turnNumber || 0, // Don't increment until we know if we're combining
                          currentTurnStartTime: Date.now(),
                          turnTimings: newTurnTimings // Include updated timing with interruption timestamp
                        };
                      } else {
                        console.log(`[INTERRUPT-SEPARATE] Outside grace period, will process separately: "${interruptText}"`);
                        
                        // Cancel current processing for separate handling
                        cancelAllPreviousTurnsHelper(context, 'SEPARATE_PROCESSING', '');
                        
                        return {
                          // Start fresh for separate processing
                          lastUserUtterance: '', // Clear for fresh start
                          tempAccumulatedText: '',
                          waitingForUserInput: true,
                          llmResponseReceived: false,
                          responseText: undefined,
                          turnFinalizedAt: undefined, // Clear to prevent combinations
                          audioPlaybackStartTime: undefined, // Clear audio timing
                          turnNumber: (context.turnNumber || 0) + 1, // Increment for new turn
                          currentTurnStartTime: Date.now(),
                          turnTimings: newTurnTimings // Include updated timing with interruption timestamp
                        };
                      }
                    }
                  })
                ]
              },
              // Handle silence after additional speech
              SILENCE_FRAME_SHORT: [
                {
                  // If we accumulated additional input, restart processing with combined text
                  guard: ({ context }) => context.hasAdditionalInput === true,
                  target: '#history_taking_id.processing', // Self-transition to restart LLM with combined text
                  actions: [
                    assign({
                      hasAdditionalInput: false, // Reset flag
                      tempAccumulatedText: ({ context }) => context.lastUserUtterance,
                      turnNumber: ({ context }) => (context.turnNumber || 0) + 1
                    }),
                    // Cancel the current LLM request if possible
                    ({ context }) => {
                      console.log(`[PROCESSING-RESTART] Restarting LLM with combined utterance: "${context.lastUserUtterance}"`);
                    }
                  ]
                },
                {
                  // Just track silence count - turnFinalizedAt should only be set when AI finishes speaking (TTS_FINISHED)
                  actions: assign(({ context, event }) => {
                    console.log(`[SILENCE-FRAME] Tracking silence in processing state - turnFinalizedAt should only be set when AI finishes speaking`);
                    return {
                      consecutiveSilenceFrames: context.consecutiveSilenceFrames + 1
                      // Removed turnFinalizedAt - should only be set when AI finishes speaking (TTS_FINISHED), not when user stops speaking
                    };
                  })
                }
              ],
              // Keep other event handlers
              INTERNAL_TRANSITION_ERROR: {
                target: '#osce.error', // Use absolute path to top-level error state
                actions: assign({ responseText: 'Error processing request.' })
              }
            }
          },
          responding: { // State where the agent is speaking
            meta: { interruptible: true }, // Agent responses should be interruptible
            entry: [
              assign((args) => {
                console.log(`[RESPONDING-ENTRY] Preserving turnFinalizedAt: ${args.context.turnFinalizedAt}`);
                return { 
                  audioPlaybackStarted: false, // Track whether audio playback has started
                  // CRITICAL FIX: Preserve turnFinalizedAt and other essential context
                  turnFinalizedAt: args.context.turnFinalizedAt,
                  audioPlaybackStartTime: args.context.audioPlaybackStartTime,
                  lastUserUtterance: args.context.lastUserUtterance
                };
              }),
              'sendToTts'
            ],
            on: {
              // CRITICAL: Track when audio playback actually starts
              AUDIO_PLAYBACK_STARTED: {
                actions: [
                  'updateTtsTimingInContext', // FAANG-LEVEL: Update timing entities with TTS start
                  'markTtsStartedInGracePeriod', // PHASE 3-1: Enable post-TTS 3-second combination window
                  assign(({ context, event }) => {
                    const now = Date.now();
                    console.log(`[AUDIO-PLAYBACK-CONTEXT] Setting audioPlaybackStartTime to ${now} for responding state`);
                    
                    // CRITICAL FIX: Use safe turn index validation for consistent synchronization
                    const eventTurnIndex = (event as any).turnIndex;
                    const turnIndex = getSafeValidTurnIndex(context, eventTurnIndex);
                    
                    // Also update turn timings with actual audio start time
                    const newTurnTimings = [...(context.turnTimings || [])];
                    const { timing } = getOrCreateTurnTiming(context, turnIndex, newTurnTimings);
                    timing.audioStreamingStartAt = now;
                    console.log(`[AUDIO-PLAYBACK-TIMING] Set audioStreamingStartAt for turn ${turnIndex} to ${now} in responding (from ${eventTurnIndex !== undefined ? 'event' : 'safe fallback'})`);
                    
                    return {
                      audioPlaybackStarted: true,
                      audioPlaybackStartTime: now, // Track when audio playback started
                      turnTimings: newTurnTimings
                    };
                  })
                ]
              },
              
              // PHASE 2: Event-based interruption handling during responding (TTS) state
              USER_INTERRUPTION_DETECTED: [
                {
                  // Pre-TTS Grace Period: Turn finalized but TTS hasn't started playing
                  guard: ({ context }) => !context.audioPlaybackStarted,
                  target: '#history_taking_id.processing', // Restart from LLM processing
                  actions: [
                    'updateInterruptionTimestamp', // Record interruption timestamp in turnTimings
                    assign(({ context, event }) => {
                      const interruptEvent = event as any;
                      const now = Date.now();
                      
                      console.log(`[INTERRUPTION-PRE-TTS] VAD interruption before audio playback started - callSid: ${interruptEvent.callSid}, probability: ${interruptEvent.vadProbability}`);
                      console.log(`[INTERRUPTION-PRE-TTS] Pre-TTS interruption - preparing to combine with existing input and restart LLM`);
                      
                      return {
                        // Mark that we're handling an interruption
                        interruptionHandlingStarted: now,
                        interruptionDuringPreTts: true,
                        lastVadProbability: interruptEvent.vadProbability,
                        // Prepare for input combination
                        waitingForUserInput: true,
                        hasSpeechDetected: false,
                        consecutiveSilenceFrames: 0
                      };
                    }),
                    // Cancel TTS generation that hasn't started playing yet
                    ({ context, event }) => {
                      const interruptEvent = event as any;
                      console.log(`[INTERRUPTION-PRE-TTS] Cancelling TTS generation before playback started`);
                      // TTS cancellation will be handled by the target transition
                    }
                  ]
                },
                {
                  // Post-TTS Grace Period: Within 3 seconds of TTS starting (combine input)
                  guard: ({ context }) => {
                    const audioStarted = context.audioPlaybackStarted;
                    const audioStartTime = context.audioPlaybackStartTime;
                    const now = Date.now();
                    const timeSinceAudioStart = audioStartTime ? now - audioStartTime : 0;
                    
                    const withinGracePeriod = audioStarted && timeSinceAudioStart <= 3000;
                    console.log(`[INTERRUPTION-GRACE-CHECK] Audio started: ${audioStarted}, time since start: ${timeSinceAudioStart}ms, within grace period: ${withinGracePeriod}`);
                    
                    return withinGracePeriod;
                  },
                  target: '#history_taking_id.processing', // Restart from LLM processing
                  actions: [
                    'updateInterruptionTimestamp', // Record interruption timestamp in turnTimings
                    assign(({ context, event }) => {
                      const interruptEvent = event as any;
                      const now = Date.now();
                      const audioStartTime = context.audioPlaybackStartTime;
                      const timeSinceAudioStart = audioStartTime ? now - audioStartTime : 0;
                      
                      console.log(`[INTERRUPTION-POST-TTS-GRACE] VAD interruption within 3-second grace period - callSid: ${interruptEvent.callSid}, time since audio: ${timeSinceAudioStart}ms`);
                      console.log(`[INTERRUPTION-POST-TTS-GRACE] Grace period interruption - preparing to combine input and restart LLM`);
                      
                      return {
                        // Mark that we're handling a grace period interruption
                        interruptionHandlingStarted: now,
                        interruptionDuringGracePeriod: true,
                        lastVadProbability: interruptEvent.vadProbability,
                        gracePeriodInterruptionTime: timeSinceAudioStart,
                        // Prepare for input combination
                        waitingForUserInput: true,
                        hasSpeechDetected: false,
                        consecutiveSilenceFrames: 0,
                        // Reset audio playback state
                        audioPlaybackStarted: false,
                        audioPlaybackStartTime: undefined
                      };
                    }),
                    // Cancel ongoing TTS and audio playback
                    ({ context, event }) => {
                      const interruptEvent = event as any;
                      console.log(`[INTERRUPTION-POST-TTS-GRACE] Cancelling TTS and audio playback for grace period interruption`);
                      // Audio/TTS cancellation will be handled by the target transition
                    }
                  ]
                },
                {
                  // Post-Grace-Period: More than 3 seconds after TTS started (new turn)
                  target: '#history_taking_id.processing', // Start new turn processing
                  actions: [
                    assign(({ context, event }) => {
                      const interruptEvent = event as any;
                      const now = Date.now();
                      const audioStartTime = context.audioPlaybackStartTime;
                      const timeSinceAudioStart = audioStartTime ? now - audioStartTime : 0;
                      
                      console.log(`[INTERRUPTION-NEW-TURN] VAD interruption after grace period - callSid: ${interruptEvent.callSid}, time since audio: ${timeSinceAudioStart}ms`);
                      console.log(`[INTERRUPTION-NEW-TURN] Post-grace-period interruption - starting new turn`);
                      
                      return {
                        // Mark that we're handling a new turn interruption
                        interruptionHandlingStarted: now,
                        interruptionNewTurn: true,
                        lastVadProbability: interruptEvent.vadProbability,
                        postGracePeriodInterruptionTime: timeSinceAudioStart,
                        // Increment turn number for new turn
                        turnNumber: (context.turnNumber || 0) + 1,
                        // Reset turn-specific context
                        waitingForUserInput: true,
                        hasSpeechDetected: false,
                        consecutiveSilenceFrames: 0,
                        audioPlaybackStarted: false,
                        audioPlaybackStartTime: undefined,
                        // Clear previous turn context
                        lastUserUtterance: '',
                        tempAccumulatedText: undefined
                      };
                    }),
                    // Cancel ongoing TTS and audio playback
                    ({ context, event }) => {
                      const interruptEvent = event as any;
                      console.log(`[INTERRUPTION-NEW-TURN] Cancelling TTS and audio playback for new turn interruption`);
                      // Audio/TTS cancellation will be handled by the target transition
                    }
                  ]
                }
              ],
              
              // Handle user speech during responding phase - CASCADING INTERRUPTION
              USER_UTTERANCE: [
                {
                  // CASCADING GRACE PERIOD: Combine and restart from LLM within same turn
                  guard: 'isWithinCascadingGracePeriod',
                  target: '#history_taking_id.processing', // Restart from LLM processing
                  actions: [
                    'handleCascadingInterruption', // Send cancellation to server (TTS + audio)
                    'applyCombinedUtterance' // Update context with combined text
                  ]
                },
                {
                  // POST-GRACE-PERIOD: If audio has been playing for more than 0.5s, treat as normal interruption
                  guard: ({ event, context }) => event.isFinal === true && context.audioPlaybackStarted,
                  target: '#history_taking_id.processing',
                  actions: assign((args) => {
                    const now = Date.now();
                    const newTurnTimings = [...(args.context.turnTimings || [])];
                    const currentTurnIndex = getSafeValidTurnIndex(args.context);
                    let currentTiming = newTurnTimings.find(t => t.turnIndex === currentTurnIndex);

                    // Ensure we have a timing object for the current turn
                    if (!currentTiming) {
                      console.warn(`[osceMachine] USER_UTTERANCE: TurnTiming for turn ${currentTurnIndex} not found. Creating a new one.`);
                      currentTiming = {
                        turnIndex: currentTurnIndex,
                        // Initialize all fields to undefined
                        silenceDetectedAt: undefined,
                        transcriptReceivedAt: now, // Set the transcript time
                        ragRequestStartAt: undefined,
                        ragResponseReceivedAt: undefined,
                        audioGenerationStartAt: undefined,
                        audioGenerationEndAt: undefined,
                        audioStreamingStartAt: undefined,
                        audioStreamingEndAt: now, // Set end time
                        audioDurationMs: undefined,
                        interruptionTimestamp: now // Set interruption time
                      };
                      newTurnTimings.push(currentTiming);
                    } else {
                      // Only update if we don't already have an interruption timestamp
                      if (!currentTiming.interruptionTimestamp) {
                        currentTiming.interruptionTimestamp = now;
                        currentTiming.audioStreamingEndAt = now;
                        if (currentTiming.audioStreamingStartAt) {
                          currentTiming.audioDurationMs = now - currentTiming.audioStreamingStartAt;
                        }
                      } else {
                        console.log(`[osceMachine] USER_UTTERANCE: Turn ${currentTurnIndex} already has an interruption timestamp, not updating`);
                      }
                      
                      // Ensure transcript time is set
                      if (!currentTiming.transcriptReceivedAt) {
                        currentTiming.transcriptReceivedAt = now;
                      }
                    }

                    return {
                      lastUserUtterance: args.event.transcription,
                      waitingForUserInput: false,
                      currentTurnStartTime: now,
                      turnNumber: currentTurnIndex + 1,
                      turnTimings: newTurnTimings,
                      // Reset VAD counters for new utterance
                      speechFramesInCurrentTurn: 0,
                      consecutiveSilenceFrames: 0,
                      hasSpeechDetected: false,
                      // CRITICAL: Preserve turnFinalizedAt for grace period functionality
                      turnFinalizedAt: args.context.turnFinalizedAt
                    };
                  })
                }
              ],
              // CRITICAL FIX: Handle explicit INTERRUPT events from server during TTS/audio
              INTERRUPT: {
                target: 'waiting_for_user_input', // Transition to listening state to capture the interrupting utterance
                actions: [
                  assign(({ context, event }) => {
                    const typedEvent = event as any;
                    const interruptTimestamp = typedEvent.interruptTimestamp || Date.now();
                    
                    console.log(`[INTERRUPT-RESPONDING] Interrupt during AI speech with text: "${typedEvent.transcriptText || 'none'}", timestamp: ${interruptTimestamp}`);
                    
                    // CRITICAL FIX: Store interruption timestamp in turnTimings for coordinator access
                    // Use currentlyPlayingTurnIndex instead of turnNumber to match server-side logic
                    const currentTurnIndex = context.currentlyPlayingTurnIndex !== undefined ? context.currentlyPlayingTurnIndex : (context.turnNumber || 0);
                    const newTurnTimings = [...(context.turnTimings || [])];
                    
                    // Find or create timing for current turn
                    let turnTiming = newTurnTimings.find(t => t.turnIndex === currentTurnIndex);
                    if (!turnTiming) {
                      turnTiming = { turnIndex: currentTurnIndex };
                      newTurnTimings.push(turnTiming);
                    }
                    
                    // Set interruption timestamp
                    turnTiming.interruptionTimestamp = interruptTimestamp;
                    console.log(`[INTERRUPT-TIMING-STORED] Set interruption timestamp ${interruptTimestamp} for turn ${currentTurnIndex} in XState context (currentlyPlayingTurnIndex: ${context.currentlyPlayingTurnIndex}, turnNumber: ${context.turnNumber})`);
                    
                    // CRITICAL FIX: Also update the global server state to ensure synchronization
                    try {
                      const { calls } = require('./server');
                      if (calls[context.callSid]) {
                        // Update the global turn timing as well
                        const globalTurnTimings = calls[context.callSid].turnTimings || [];
                        let globalTurnTiming = globalTurnTimings.find(t => t.turnIndex === currentTurnIndex);
                        if (!globalTurnTiming) {
                          globalTurnTiming = { turnIndex: currentTurnIndex };
                          globalTurnTimings.push(globalTurnTiming);
                        }
                        globalTurnTiming.interruptionTimestamp = interruptTimestamp;
                        calls[context.callSid].turnTimings = globalTurnTimings;
                        console.log(`[INTERRUPT-TIMING-SYNC] Synchronized interruption timestamp ${interruptTimestamp} for turn ${currentTurnIndex} to global server state`);
                      }
                    } catch (error) {
                      console.error(`[INTERRUPT-TIMING-SYNC] Error synchronizing interruption timestamp to global state:`, error);
                    }
                    
                    // 🚀 UNIFIED GRACE PERIOD CHECK - Replaces conflicting timing logic
                    const now = Date.now();
                    const graceEval = evaluateUnifiedGracePeriod(context, now);
                    const isWithinGracePeriod = graceEval.withinGracePeriod;
                    
                    console.log(`[INTERRUPT-UNIFIED-GRACE] Phase: ${graceEval.phase}, Type: ${graceEval.gracePeriodType}, Within: ${isWithinGracePeriod}`);
                    console.log(`[INTERRUPT-UNIFIED-GRACE] Debug:`, graceEval.debugInfo);
                    
                    let finalText = typedEvent.transcriptText || '';
                    let shouldCombine = false;
                    
                    // For interrupts during grace period, we don't immediately combine
                    // Instead, we preserve the original text and let COORDINATED_TURN_COMPLETE handle combination
                    if (isWithinGracePeriod) {
                      const timeSinceUserStopped = context.turnFinalizedAt ? now - context.turnFinalizedAt : 0;
                      const timeSinceAIStarted = context.audioPlaybackStartTime ? now - context.audioPlaybackStartTime : 0;
                      console.log(`[INTERRUPT-GRACE-PERIOD] Within grace period (user+${timeSinceUserStopped}ms, ai+${timeSinceAIStarted}ms), will combine when user finishes speaking`);
                      // Keep the original lastUserUtterance for later combination
                      shouldCombine = true;
                    } else {
                      console.log(`[INTERRUPT-SEPARATE-TTS] Outside grace period, will process interrupt separately`);
                      shouldCombine = false;
                    }
                    
                    // Cancel TTS/audio immediately  
                    cancelAllPreviousTurnsHelper(context, 'RESPONDING_INTERRUPT', '');
                    
                    return {
                      waitingForUserInput: true,
                      audioPlaybackStarted: false,
                      responseText: undefined,
                      // CRITICAL: Don't change lastUserUtterance during interrupt
                      // Let COORDINATED_TURN_COMPLETE handle the combination when user finishes speaking
                      // lastUserUtterance: preserved from before interrupt
                      // tempAccumulatedText: cleared to start fresh for interrupting speech
                      tempAccumulatedText: '',
                      // Preserve turnFinalizedAt for continued grace period tracking - don't reset it
                      turnNumber: context.turnNumber || 0, // Don't increment until we know if we're combining
                      turnTimings: newTurnTimings, // Include updated timing with interruption timestamp
                      // CRITICAL FIX: Preserve audioPlaybackStartTime temporarily to enable consecutive interruption detection
                      // The robust detection logic in turnBasedGracePeriodHandler will handle timing validation
                      audioPlaybackStartTime: context.audioPlaybackStartTime
                    };
                  })
                ]
              },
              SPEECH_DETECTED: [
                {
                  // When speech is detected during TTS, mark the interruption time
                  actions: assign(({ context }) => {
                    const now = Date.now();
                    const currentTurnIndex = context.turnNumber || 0;
                    const newTurnTimings = [...(context.turnTimings || [])];
                    
                    console.log(`[SPEECH_DETECTED] Processing speech detection for turn ${currentTurnIndex} at ${now}`);
                    
                    const { timing } = getOrCreateTurnTiming(context, currentTurnIndex, newTurnTimings);
                    
                    // Don't update if we already have an interruption timestamp
                    if (timing.interruptionTimestamp) {
                      console.log(`[SPEECH_DETECTED] Turn ${currentTurnIndex} already has an interruption timestamp, skipping update`);
                      return {}; // No changes needed
                    }
                    
                    // Only set interruption if TTS has actually started
                    if (timing.audioStreamingStartAt) {
                      console.log(`[SPEECH_DETECTED] Setting interruption timestamp for turn ${currentTurnIndex} at ${now}`);
                      timing.interruptionTimestamp = now;
                      timing.audioStreamingEndAt = now; // Also set the end time
                      if (timing.audioStreamingStartAt) {
                        timing.audioDurationMs = now - timing.audioStreamingStartAt;
                        console.log(`[SPEECH_DETECTED] Updated audio duration to ${timing.audioDurationMs}ms`);
                      }
                    } else {
                      console.warn(`[SPEECH_DETECTED] TTS not started yet for turn ${currentTurnIndex}, ignoring interruption`);
                    }
                    
                    logTurnTimings(newTurnTimings, currentTurnIndex);
                    
                    return { turnTimings: newTurnTimings };
                  })
                }
              ],
              TTS_FINISHED: [
                {
                  target: 'waiting_for_user_input',
                  actions: [
                    assign((args) => {
                      const { context } = args;
                      const now = Date.now();
                      const turnIndex = context.turnNumber || 0;
                      const newTurnTimings = [...(context.turnTimings || [])];
                      
                      console.log(`[TTS_FINISHED] Processing TTS finished for turn ${turnIndex} at ${now}`);
                      
                      // Get or create the timing object for the current turn
                      const { timing, isNew } = getOrCreateTurnTiming(context, turnIndex, newTurnTimings);
                      
                      if (isNew) {
                        console.warn(`[TTS_FINISHED] Created new timing object for turn ${turnIndex}, this is unexpected`);
                      }
                      
                      // EMERGENCY FIX D4: Validate timestamps to prevent negative durations
                      // Only set end time if not already set by an interruption, OR if existing end time is invalid
                      const existingEndTime = timing.audioStreamingEndAt;
                      const startTime = timing.audioStreamingStartAt;
                      
                      // Check if existing end time would create negative duration
                      const hasInvalidEndTime = existingEndTime && startTime && (existingEndTime < startTime);
                      
                      if (!existingEndTime || hasInvalidEndTime) {
                        if (hasInvalidEndTime) {
                          console.warn(`[TTS_FINISHED] EMERGENCY FIX D4: Correcting invalid end time ${existingEndTime} < start time ${startTime} for turn ${turnIndex}`);
                        }
                        
                        console.log(`[TTS_FINISHED] Setting audioStreamingEndAt for turn ${turnIndex} to ${now}`);
                        timing.audioStreamingEndAt = now;
                        
                        // Calculate duration if we have a start time
                        if (startTime) {
                          timing.audioDurationMs = now - startTime;
                          console.log(`[TTS_FINISHED] Calculated audio duration: ${timing.audioDurationMs}ms (VALIDATED > 0)`);
                          
                          // Additional validation: ensure positive duration
                          if (timing.audioDurationMs < 0) {
                            console.error(`[TTS_FINISHED] EMERGENCY FIX D4: Negative duration detected! Setting to 0. Start: ${startTime}, End: ${now}`);
                            timing.audioDurationMs = 0;
                          }
                        } else {
                          console.warn(`[TTS_FINISHED] No audioStreamingStartAt for turn ${turnIndex}, cannot calculate duration`);
                        }
                      } else {
                        // Validate existing duration isn't negative
                        const existingDuration = existingEndTime - (startTime || 0);
                        if (existingDuration < 0) {
                          console.error(`[TTS_FINISHED] EMERGENCY FIX D4: Existing negative duration ${existingDuration}ms detected for turn ${turnIndex}! Correcting...`);
                          timing.audioStreamingEndAt = now;
                          timing.audioDurationMs = startTime ? Math.max(0, now - startTime) : 0;
                        } else {
                          console.log(`[TTS_FINISHED] audioStreamingEndAt already set to ${existingEndTime} for turn ${turnIndex} (duration: ${existingDuration}ms)`);
                        }
                      }
                      
                      // Log the current state of all turn timings for debugging
                      logTurnTimings(newTurnTimings, turnIndex);
                      
                      // Calculate AI's speech duration for VAD for the next user turn
                      let ttsLastTurnDurationForVad = 0;
                      if (context.currentTurnStartTime) {
                        ttsLastTurnDurationForVad = now - context.currentTurnStartTime;
                        console.log(`[TTS_FINISHED] Calculated VAD duration: ${ttsLastTurnDurationForVad}ms`);
                      }
                      
                      console.log(`[TTS_FINISHED-GRACE] AI finished speaking - grace period already started when user stopped talking`);
                      
                      return {
                        ttsFinished: true,
                        waitingForUserInput: true,
                        ttsAgenda: 'listen',
                        turnTimings: newTurnTimings,
                        ttsLastTurnDuration: ttsLastTurnDurationForVad,
                        // CRITICAL: Set turnFinalizedAt when AI finishes speaking for grace period logic
                        // CRITICAL FIX: Don't clear audioPlaybackStartTime here - let resetTurnSpecificContext handle it with grace period logic
                        audioPlaybackStarted: false, // Reset audio playback state
                        currentlyPlayingTurnIndex: undefined, // CRITICAL FIX: Clear turn index after audio completion
                        turnFinalizedAt: now // Set when AI finishes speaking and is ready to listen
                      };
                    }),
                    'resetTurnSpecificContext'
                  ]
                }
              ],
              // Handle SILENCE_FRAME_SHORT during responding - just track silence count
              SILENCE_FRAME_SHORT: {
                actions: assign(({ context, event }) => {
                  console.log(`[SILENCE-FRAME] Tracking silence in responding state - turnFinalizedAt should only be set when AI finishes speaking`);
                  return {
                    // Removed turnFinalizedAt - should only be set when AI finishes speaking (TTS_FINISHED), not when user stops speaking
                    consecutiveSilenceFrames: context.consecutiveSilenceFrames + 1
                  };
                })
              },
              // CRITICAL FIX: Handle COORDINATED_TURN_COMPLETE events during responding state
              // This allows grace period combination even when AI is currently speaking
              COORDINATED_TURN_COMPLETE: {
                target: '#history_taking_id.processing',
                actions: [
                  assign(({ context, event }) => {
                    const typedEvent = event as any;
                    console.log(`[COORDINATED-TURN-RESULT] ${context.callSid}: State before COORDINATED_TURN_COMPLETE: ${JSON.stringify(context.currentState)}`);
                    return {};
                  }),
                  'handleCoordinatedTurnComplete', // PHASE 1 FIX: Update context with fresh timing data before grace period handler
                  'canonicalTurnCompletionHandler',
                  'updateConversationWithUserInput', // GRACE PERIOD COMBINATION FIX: Add user input to conversation history
                  assign(({ context, event }) => {
                    const typedEvent = event as any;
                    console.log(`[COORDINATED-TURN-RESULT] ${context.callSid}: State after COORDINATED_TURN_COMPLETE: ${JSON.stringify(context.currentState)}`);
                    console.log(`[COORDINATED-TURN-RESULT] ${context.callSid}: Processing "${typedEvent.transcriptText}" with grace period logic`);
                    return {};
                  })
                ]
              }
            }
          },
          system_prompting_transition: {
            meta: { interruptible: false }, // System transition prompts should not be interruptible
            entry: [
              assign({ 
                currentState: 'history_taking_main.system_prompting_transition',
                waitingForUserInput: false 
              }),
              'logStateEntry'
            ],
            on: {
              TTS_FINISHED: {
                target: 'waiting_for_transition_response',
                actions: assign({
                  waitingForUserInput: true,
                  ttsFinished: true
                })
              }
            }
          },
          waiting_for_transition_response: {
            entry: [
              assign({ 
                currentState: 'history_taking_main.waiting_for_transition_response',
                waitingForUserInput: true 
              }),
              'logStateEntry',
              'manageActivityTimers'
            ],
            on: {
              USER_UTTERANCE: [
                {
                  guard: ({ event }) => event.isFinal === true,
                  target: 'checking_transition_response',
                  actions: assign({
                    lastUserUtterance: ({ event }) => event.transcription,
                    waitingForUserInput: false
                  })
                }
              ],
              SPEECH_FRAME: {
                actions: assign({
                  speechFramesInCurrentTurn: ({ context }) => context.speechFramesInCurrentTurn + 1,
                  consecutiveSilenceFrames: 0,
                  hasSpeechDetected: true
                })
              },
              SILENCE_FRAME_SHORT: [
                {
                  guard: ({ context, event }) => {
                    // FIXED: Since SILENCE_FRAME_SHORT is only sent after VAD has confirmed the threshold,
                    // we don't need to check consecutiveSilenceFrames - the VAD already did that work
                    const connectionType = context.infrastructure?.connection?.type || 'twilio';
                    const minFrames = getMinSpeechFrames(connectionType);
                    const hasSufficientSpeech = context.hasSpeechDetected && 
                      context.speechFramesInCurrentTurn >= minFrames;
                    
                    const hasFinalTranscript = event.accumulatedText && 
                      event.accumulatedText.length > 0;
                    
                    const shouldComplete = hasSufficientSpeech && hasFinalTranscript;
                    
                    // DEBUG: Always log guard condition evaluation for SILENCE_FRAME_SHORT
                    if (event.type === 'SILENCE_FRAME_SHORT') {
                      console.log(`[GUARD-DEBUG] TRANSITION RESPONSE SILENCE_FRAME_SHORT guard evaluation (FIXED):`);
                      console.log(`  - hasSpeechDetected: ${context.hasSpeechDetected}`);
                      console.log(`  - speechFramesInCurrentTurn: ${context.speechFramesInCurrentTurn} (min: ${minFrames}, type: ${connectionType})`);
                      console.log(`  - event.accumulatedText: "${event.accumulatedText}"`);
                      console.log(`  - accumulatedText.length: ${event.accumulatedText?.length || 0}`);
                      console.log(`  - hasSufficientSpeech: ${hasSufficientSpeech}`);
                      console.log(`  - hasFinalTranscript: ${hasFinalTranscript}`);
                      console.log(`  - shouldComplete: ${shouldComplete}`);
                      console.log(`  - NOTE: Removed consecutiveSilenceFrames check since VAD already validated threshold`);
                    }
                    
                    return shouldComplete;
                  },
                  target: 'checking_transition_response',
                  actions: [
                    assign({
                      consecutiveSilenceFrames: ({ context }) => context.consecutiveSilenceFrames + 1,
                      waitingForUserInput: false,
                      lastUserUtterance: ({ event }) => event.accumulatedText || '',
                      tempAccumulatedText: ({ event }) => event.accumulatedText
                    })
                  ]
                },
                {
                  actions: assign({
                    consecutiveSilenceFrames: ({ context }: { context: OsceContext }) => context.consecutiveSilenceFrames + 1
                  })
                }
              ]
            }
          },
          checking_transition_response: {
            entry: [
              assign({ 
                currentState: 'history_taking_main.checking_transition_response' 
              }),
              'logStateEntry'
            ],
            invoke: {
              src: 'systemTransitionCheckActor',
              input: ({ context }) => ({
                callSid: context.callSid,
                userMessage: context.lastUserUtterance,
                conversationHistory: context.conversation,
                caseData: context.caseData
              }),
              onDone: {
                target: 'transition_decision',
                actions: assign({
                  responseText: ({ event }) => event.output?.assistantReply || 'Got it.'
                })
              },
              onError: {
                target: 'waiting_for_user_input',
                actions: assign({
                  responseText: 'Sorry, I had trouble understanding. Let\'s continue with the history taking.',
                  waitingForUserInput: true
                })
              }
            }
          },
          transition_decision: {
            entry: [
              assign({ 
                currentState: 'history_taking_main.transition_decision' 
              }),
              'logStateEntry'
            ],
            always: [
              {
                guard: ({ context }) => 
                  context.responseText?.includes('MOVE_TO_EXAMINATION'),
                target: '#osce.examination',
                actions: [
                  assign({
                    responseText: "Great! Let's move on to the examination phase. The roles have now changed - you're now talking to the examiner.",
                    transitionReason: 'EXAMINATION',
                    currentState: 'examination'
                  }),
                  'setTtsStartTime',
                  'sendToTts'
                ]
              },
              {
                target: 'waiting_for_user_input',
                actions: [
                  assign({
                    responseText: "No problem, let's continue with the history taking. What else would you like to know?",
                    waitingForUserInput: true
                  }),
                  'setTtsStartTime',
                  'sendToTts'
                ]
              }
            ]
          }
        },
        // Define transitions common to all substates of history_taking_main if needed
        on: {
          // Direct state transition events
          TRANSITION_TO_EXAMINATION: {
            target: 'examination',
            actions: [
              assign({
                currentState: 'examination',
                responseText: "Moving to examination phase. What would you like to examine?",
                waitingForUserInput: true,
                transitionReason: 'EXAMINATION'
              }),
              ({ context }) => console.log(`[DIRECT-TRANSITION] ${context.callSid}: Transitioning directly to examination state`),
              'saveHistoryTakingTranscript',
              'startExaminationTracking',
              'logStateEntry'
            ]
          },
          
          TRANSITION_TO_DIAGNOSIS: {
            target: 'diagnosis',
            actions: [
              assign({
                currentState: 'diagnosis',
                responseText: "Now moving to diagnosis. What do you think is the diagnosis based on your findings?",
                waitingForUserInput: true,
                transitionReason: 'DIAGNOSIS'
              }),
              ({ context }) => console.log(`[DIRECT-TRANSITION] ${context.callSid}: Transitioning directly to diagnosis state`),
              'saveExaminationTranscript',
              'startMarkingTracking',
              'logStateEntry'
            ]
          },
          
          TRANSITION_TO_CLOSING: {
            target: 'closing',
            actions: [
              assign({
                currentState: 'closing',
                responseText: "Let's conclude this consultation. Please provide your closing remarks.",
                waitingForUserInput: true,
                transitionReason: 'CLOSING'
              }),
              ({ context }) => console.log(`[DIRECT-TRANSITION] ${context.callSid}: Transitioning directly to closing state`),
              'saveMarkingTranscript',
              'startFeedbackTracking',
              'logStateEntry'
            ]
          }
        }
      },
      // ... other states ...
      transition: {
        entry: [
          // Set state info and prepare transition message
          assign({
            currentState: 'transition',
            waitingForUserInput: false,
            responseText: ({ context }) => {
              // Set message based on transition reason
              if (context.transitionReason === 'USER_READY') {
                return 'Great! I will now play the role of the patient. You can start by taking my history.';  
              } else if (context.transitionReason === TransitionReasons.EXAMINATION) {
                return "Moving to examination phase. What would you like to examine?"; 
              } else if (context.transitionReason === TransitionReasons.DIAGNOSIS) {
                return "Now moving to diagnosis. What do you think is the diagnosis based on your findings?"; 
              } else if (context.transitionReason === TransitionReasons.CLOSING) {
                return "Let's conclude this consultation. Please provide your closing remarks."; 
              }
              return "Transitioning to the next phase..."; 
            }
          }),
          'logStateEntry'
        ],
        on: {
          // When TTS finishes speaking, transition to the appropriate next state
          TTS_FINISHED: [  // Use an array of conditional transitions instead of a dynamic target
            {
              guard: ({ context }) => context.transitionReason === 'USER_READY',
              target: '#history_taking_id.processing',
              actions: [
                assign({ 
                  ttsFinished: true,
                  ttsLastTurnDuration: ({ context }) => context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 0,
                  waitingForUserInput: true
                }),
                'resetTurnSpecificContext' // Apply the reset here as well after transition
              ]
            },
            {
              guard: ({ context }) => context.transitionReason === TransitionReasons.EXAMINATION,
              target: 'examination',
              actions: [
                assign({ 
                  ttsFinished: true,
                  ttsLastTurnDuration: ({ context }) => context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 0,
                  waitingForUserInput: true
                }),
                'resetTurnSpecificContext' // Apply the reset here
              ]
            },
            {
              guard: ({ context }) => context.transitionReason === TransitionReasons.DIAGNOSIS,
              target: 'diagnosis',
              actions: [
                assign({ 
                  ttsFinished: true,
                  ttsLastTurnDuration: ({ context }) => context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 0,
                  waitingForUserInput: true
                }),
                'resetTurnSpecificContext' // Apply the reset here
              ]
            },
            {
              guard: ({ context }) => context.transitionReason === TransitionReasons.CLOSING,
              target: 'closing',
              actions: [
                assign({ 
                  ttsFinished: true,
                  ttsLastTurnDuration: ({ context }) => context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 0,
                  waitingForUserInput: true
                }),
                'resetTurnSpecificContext' // Apply the reset here
              ]
            },
            // Default (fallback) transition if no conditions match
            {
              target: '#history_taking_id.processing',
              actions: [
                assign({ 
                  ttsFinished: true,
                  ttsLastTurnDuration: ({ context }) => context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 0,
                  waitingForUserInput: true
                }),
                'resetTurnSpecificContext' // Apply the reset here
              ]
            }
          ],
          REQUEST_TRANSITION: {
            actions: assign({
              transitionReason: 'USER_READY'
            })
          }
        }
      },
      examination: {
        initial: 'asking_question',
        entry: [
          assign(({ context }) => {
            // Load questions from case data - should be a list
            const questionsPath = context.caseData?.examiner_agent_data?.examination_and_investigations?.questions?.value;
            
            // Debug: Log the actual structure to see what we're getting
            console.log('[EXAMINATION-DEBUG] Raw questions data:', {
              type: typeof questionsPath,
              isArray: Array.isArray(questionsPath),
              value: questionsPath,
              length: questionsPath?.length
            });
            
            // If it's already an array, use it directly
            let questions: string[] = [];
            if (Array.isArray(questionsPath)) {
              questions = questionsPath;
              console.log('[EXAMINATION-DEBUG] Using questions as array:', questions);
            } else if (typeof questionsPath === 'string') {
              // If it's a string, this indicates a data loading problem
              console.error('[EXAMINATION-ERROR] Questions came as string instead of array:', questionsPath);
              questions = [questionsPath]; // Treat as single question for now
            } else {
              console.error('[EXAMINATION-ERROR] Questions data is neither array nor string:', questionsPath);
              questions = [];
            }
            
            return {
              currentState: 'examination',
              examinationQuestions: questions,
              currentQuestionIndex: 0,
              responseText: questions.length > 0 
                ? `The roles have now changed - you're now talking to the examiner. I have ${questions.length} questions for you. I will be unable to answer any questions you have, I can only listen to your responses. Let me know when you've finished answering each question. Here's the first question: ${questions[0]}`
                : "I don't have any questions available for this case.",
              waitingForUserInput: true,
              answerStartTime: Date.now(),
              hasAcknowledgedContinuation: false // Reset for first question
            };
          }),
          'logStateEntry'
        ],
        states: {
          asking_question: {
            entry: [
              assign({ currentState: 'examination.asking_question' }),
              'setTtsStartTime',
              'sendToTtsAndTrackExamination'
            ],
            on: {
              TTS_FINISHED: {
                target: 'waiting_for_completion',
                actions: assign({
                  ttsFinished: true,
                  waitingForUserInput: true,
                  answerStartTime: Date.now(),
                  userIsCurrentlySpeaking: false // Reset speaking flag when ready to listen
                })
              }
              // Removed USER_UTTERANCE - users cannot interrupt questions being read
            }
          },
          waiting_for_completion: {
            entry: [
              assign({ 
                currentState: 'examination.waiting_for_completion',
              infrastructure: ({ context }) => {
                // Defensive programming: ensure infrastructure and vad exist
                if (!context.infrastructure) {
                  // Return full default infrastructure if missing
                  const existingTranscript = context.lastUserUtterance || ''; // Preserve any existing utterance
                  return {
                    transcript: {
                      accumulation: {
                        currentText: existingTranscript, // Use existing utterance if available
                        lastProcessedTime: 0,
                        isAccumulating: false,
                        duplicateCount: 0,
                        totalProcessed: 0
                      }
                    },
                    audio: {
                      processTracking: {
                        activePids: [],
                        lastSpawnTime: 0,
                        processCount: 0,
                        failureCount: 0
                      }
                    },
                    vad: {
                      metrics: {
                        speechFrameCount: 0,
                        silenceFrameCount: 0,
                        lastActivityTime: Date.now(),
                        consecutiveSpeechFrames: 0,
                        consecutiveSilenceFrames: 0
                      }
                    },
                    connection: {
                      status: 'disconnected' as const,
                      type: 'twilio' as const,
                      lastActivity: Date.now(),
                      reconnectAttempts: 0,
                      messagesSent: 0,
                      errorsCount: 0
                    },
                    timing: {
                      phaseStartTime: Date.now(),
                      lastTransitionTime: Date.now(),
                      processingLatencies: [],
                      averageLatency: 0
                    }
                  };
                }
                
                const safeVad = context.infrastructure.vad || {
                  metrics: {
                    speechFrameCount: 0,
                    silenceFrameCount: 0,
                    lastActivityTime: Date.now(),
                    consecutiveSpeechFrames: 0,
                    consecutiveSilenceFrames: 0
                  }
                };
                
                return {
                  ...context.infrastructure,
                  vad: {
                    ...safeVad,
                    metrics: {
                      ...safeVad.metrics,
                      consecutiveSilenceFrames: 0 // Reset silence counter when entering waiting state
                    }
                  }
                };
              }
            }),
            'manageActivityTimers'
          ],
            on: {
              USER_UTTERANCE: [
                {
                  target: 'checking_intent',
                  actions: [
                    assign({
                      lastUserUtterance: ({ event }) => event.transcription,
                      hasSpeechDetected: true
                    }),
                    // Track the user's response in examination
                    ({ context, event }) => {
                      const { addExaminationResponse } = transcriptActions;
                      addExaminationResponse(context, 'caller', event.transcription);
                      console.log(`[EXAMINATION-TRACKING] Added user response: "${event.transcription}"`);
                    }
                  ]
                }
              ],
              // NEW: Handle short silence frames - background LLM checking while continuing to listen
              SILENCE_FRAME_SHORT: [
                {
                  target: 'checking_intent_background',
                  guard: ({ event }) => {
                    // Only trigger background check if we have accumulated text
                    const hasText = event.accumulatedText && event.accumulatedText.trim().length > 0;
                    if (hasText) {
                      console.log(`[EXAMINATION-SHORT] Background LLM check triggered with text: "${event.accumulatedText?.substring(0, 50)}..."`);
                    }
                    return hasText;
                  },
                  actions: assign({
                    tempAccumulatedText: ({ event }) => event.accumulatedText || '',
                    lastUserUtterance: ({ event }) => event.accumulatedText || ''
                  })
                },
                {
                  // Regular short silence frame - just continue listening
                  actions: () => console.log(`[EXAMINATION-SHORT] Continuing to listen...`)
                }
              ],
              // NEW: Handle long silence frames - auto-prompt after 8 seconds
              SILENCE_FRAME_LONG: [
                {
                  target: 'prompting_next',
                  actions: [
                    assign({
                      responseText: "Would you like to move on to the next question?",
                      waitingForUserInput: false
                    }),
                    'setTtsStartTime',
                    'sendToTtsAndTrackExamination',
                    ({ context, event }) => {
                      console.log(`[EXAMINATION-LONG] 8-second timeout reached, prompting for next question`);
                      console.log(`[EXAMINATION-LONG-DEBUG] Current state: ${context.currentState}, questionIndex: ${context.currentQuestionIndex}, accumulatedText: "${(event as any).accumulatedText || 'none'}"`);
                    }
                  ]
                }
              ],
              // Keep original SILENCE_FRAME for backward compatibility with regular frames
              SILENCE_FRAME: [
                {
                  // Default: just increment silence counter
                  actions: assign({
                    infrastructure: ({ context }) => {
                      // Defensive programming: ensure infrastructure exists
                      if (!context.infrastructure?.vad?.metrics) {
                        console.warn('[INFRASTRUCTURE] Using safe fallback for undefined VAD context in SILENCE_FRAME');
                        const existingTranscript = context.lastUserUtterance || '';
                        return context.infrastructure || {
                          transcript: { accumulation: { currentText: existingTranscript, lastProcessedTime: 0, isAccumulating: false, duplicateCount: 0, totalProcessed: 0 } },
                          audio: { processTracking: { activePids: [], lastSpawnTime: 0, processCount: 0, failureCount: 0 } },
                          vad: { metrics: { speechFrameCount: 0, silenceFrameCount: 1, lastActivityTime: Date.now(), consecutiveSpeechFrames: 0, consecutiveSilenceFrames: 1 } },
                          connection: { status: 'disconnected' as const, type: 'twilio' as const, lastActivity: Date.now(), reconnectAttempts: 0, messagesSent: 0, errorsCount: 0 },
                          timing: { phaseStartTime: Date.now(), lastTransitionTime: Date.now(), processingLatencies: [], averageLatency: 0 }
                        };
                      }
                      return {
                        ...context.infrastructure,
                        vad: {
                          ...context.infrastructure.vad,
                          metrics: {
                            ...context.infrastructure.vad.metrics,
                            silenceFrameCount: context.infrastructure.vad.metrics.silenceFrameCount + 1,
                            consecutiveSilenceFrames: context.infrastructure.vad.metrics.consecutiveSilenceFrames + 1
                          }
                        }
                      };
                    }
                  })
                }
              ],
              SPEECH_FRAME: {
                // Reset silence counter when speech is detected
                actions: assign({
                  infrastructure: ({ context }) => {
                    // Defensive programming: ensure infrastructure exists
                    if (!context.infrastructure?.vad?.metrics) {
                      console.warn('[INFRASTRUCTURE] Using safe fallback for undefined VAD context in SPEECH_FRAME');
                      const existingTranscript = context.lastUserUtterance || '';
                      return context.infrastructure || {
                        transcript: { accumulation: { currentText: existingTranscript, lastProcessedTime: 0, isAccumulating: false, duplicateCount: 0, totalProcessed: 0 } },
                        audio: { processTracking: { activePids: [], lastSpawnTime: 0, processCount: 0, failureCount: 0 } },
                        vad: { metrics: { speechFrameCount: 1, silenceFrameCount: 0, lastActivityTime: Date.now(), consecutiveSpeechFrames: 1, consecutiveSilenceFrames: 0 } },
                        connection: { status: 'disconnected' as const, type: 'twilio' as const, lastActivity: Date.now(), reconnectAttempts: 0, messagesSent: 0, errorsCount: 0 },
                        timing: { phaseStartTime: Date.now(), lastTransitionTime: Date.now(), processingLatencies: [], averageLatency: 0 }
                      };
                    }
                    return {
                      ...context.infrastructure,
                      vad: {
                        ...context.infrastructure.vad,
                        metrics: {
                          ...context.infrastructure.vad.metrics,
                          speechFrameCount: context.infrastructure.vad.metrics.speechFrameCount + 1,
                          consecutiveSilenceFrames: 0 // Reset silence counter
                        }
                      }
                    };
                  }
                })
              },
              // CRITICAL FIX: Handle COORDINATED_TURN_COMPLETE events in examination state
              COORDINATED_TURN_COMPLETE: {
                target: 'checking_intent',
                actions: [
                  assign({
                    lastUserUtterance: ({ event }) => event.transcriptText,
                    hasSpeechDetected: true
                  }),
                  // Track the user's response in examination
                  ({ context, event }) => {
                    const { addExaminationResponse } = transcriptActions;
                    addExaminationResponse(context, 'caller', event.transcriptText);
                    console.log(`[EXAMINATION-TRACKING] Added user response via COORDINATED_TURN_COMPLETE: "${event.transcriptText}"`);
                  }
                ]
              }
            }
          },
          checking_intent: {
            entry: assign({ 
              currentState: 'examination.checking_intent',
              userIsCurrentlySpeaking: true // Ensure flag is set when checking intent
            }),
            invoke: {
              id: 'intentChecker',
              src: 'examinationIntentChecker',
              input: ({ context }) => ({
                userUtterance: context.lastUserUtterance,
                currentQuestion: context.examinationQuestions?.[context.currentQuestionIndex || 0] || '',
                callSid: context.callSid
              }),
              onDone: [
                {
                  target: 'prompting_next',
                  guard: ({ event }) => event.output.intent === 'completion_check',
                  actions: [
                    assign({
                      responseText: "Would you like to move on to the next question?",
                      waitingForUserInput: false
                    }),
                    'setTtsStartTime',
                    'sendToTtsAndTrackExamination'
                  ]
                },
                {
                  target: 'repeating_question',
                  guard: ({ event }) => event.output.intent === 'repeat_question',
                  actions: [
                    assign(({ context }) => ({
                      responseText: `Let me repeat the question: ${context.examinationQuestions?.[context.currentQuestionIndex || 0] || 'Question not found'}`,
                      waitingForUserInput: false
                    })),
                    'setTtsStartTime',
                    'sendToTts'
                  ]
                },
                {
                  target: 'waiting_for_completion',
                  guard: ({ event }) => event.output.intent === 'continue_answering',
                  actions: [
                    assign({
                      waitingForUserInput: true
                    }),
                    () => console.log(`[EXAMINATION-INTENT] LLM detected user still answering, continue listening silently`)
                  ]
                }
              ],
              onError: {
                target: 'waiting_for_completion',
                actions: assign({
                  waitingForUserInput: true
                })
              }
            }
          },
          checking_intent_background: {
            entry: assign({ 
              currentState: 'examination.checking_intent_background',
              userIsCurrentlySpeaking: true // Ensure flag is set when checking intent
            }),
            invoke: {
              id: 'backgroundIntentChecker',
              src: 'examinationIntentChecker',
              input: ({ context }) => ({
                userUtterance: context.tempAccumulatedText || context.lastUserUtterance,
                currentQuestion: context.examinationQuestions?.[context.currentQuestionIndex || 0] || '',
                callSid: context.callSid
              }),
              onDone: [
                {
                  target: 'prompting_next',
                  guard: ({ event }) => event.output.intent === 'completion_check',
                  actions: [
                    assign({
                      responseText: "Would you like to move on to the next question?",
                      waitingForUserInput: false
                    }),
                    'setTtsStartTime',
                    'sendToTts',
                    () => console.log(`[EXAMINATION-BACKGROUND] LLM detected completion, prompting for next`)
                  ]
                },
                {
                  target: 'repeating_question',
                  guard: ({ event }) => event.output.intent === 'repeat_question',
                  actions: [
                    assign(({ context }) => ({
                      responseText: `Let me repeat the question: ${context.examinationQuestions?.[context.currentQuestionIndex || 0] || 'Question not found'}`,
                      waitingForUserInput: false
                    })),
                    'setTtsStartTime',
                    'sendToTts'
                  ]
                },
                {
                  // Default: user is still answering, go back to waiting
                  target: 'waiting_for_completion',
                  actions: [
                    assign({
                      waitingForUserInput: true
                    }),
                    () => console.log(`[EXAMINATION-BACKGROUND] LLM detected user still answering, continue listening`)
                  ]
                }
              ],
              onError: {
                target: 'waiting_for_completion',
                actions: [
                  assign({
                    waitingForUserInput: true
                  }),
                  () => console.log(`[EXAMINATION-BACKGROUND] LLM check failed, continue listening`)
                ]
              }
            },
            on: {
              // Continue accepting speech/silence while LLM processes in background
              USER_UTTERANCE: [
                {
                  // For final transcripts, accumulate them with existing utterance
                  guard: ({ event }) => event.isFinal === true,
                  actions: assign({
                    // CRITICAL: Replace the current utterance with the refined input (speech recognition refinement)
                    lastUserUtterance: ({ context, event }) => {
                      const currentUtterance = context.tempAccumulatedText || context.lastUserUtterance || '';
                      const refinedText = event.transcription || '';
                      console.log(`[EXAMINATION-BACKGROUND-REPLACEMENT] Replaced utterance: "${currentUtterance}" -> "${refinedText}"`);
                      return refinedText.trim();
                    },
                    // Update temp accumulated text for the current processing
                    tempAccumulatedText: ({ context, event }) => {
                      const refinedText = event.transcription || '';
                      return refinedText.trim();
                    },
                    hasSpeechDetected: true,
                    hasAdditionalInput: true
                  })
                },
                {
                  // For partial transcripts, just track speech
                  actions: assign({
                    hasSpeechDetected: true
                  })
                }
              ],
              SPEECH_FRAME: {
                actions: assign({
                  infrastructure: ({ context }) => {
                    // Defensive programming: ensure infrastructure exists
                    if (!context.infrastructure?.vad?.metrics) {
                      console.warn('[INFRASTRUCTURE] Using safe fallback for undefined VAD context in SPEECH_FRAME');
                      const existingTranscript = context.lastUserUtterance || '';
                      return context.infrastructure || {
                        transcript: { accumulation: { currentText: existingTranscript, lastProcessedTime: 0, isAccumulating: false, duplicateCount: 0, totalProcessed: 0 } },
                        audio: { processTracking: { activePids: [], lastSpawnTime: 0, processCount: 0, failureCount: 0 } },
                        vad: { metrics: { speechFrameCount: 1, silenceFrameCount: 0, lastActivityTime: Date.now(), consecutiveSpeechFrames: 1, consecutiveSilenceFrames: 0 } },
                        connection: { status: 'disconnected' as const, type: 'twilio' as const, lastActivity: Date.now(), reconnectAttempts: 0, messagesSent: 0, errorsCount: 0 },
                        timing: { phaseStartTime: Date.now(), lastTransitionTime: Date.now(), processingLatencies: [], averageLatency: 0 }
                      };
                    }
                    return {
                      ...context.infrastructure,
                      vad: {
                        ...context.infrastructure.vad,
                        metrics: {
                          ...context.infrastructure.vad.metrics,
                          speechFrameCount: context.infrastructure.vad.metrics.speechFrameCount + 1,
                          consecutiveSilenceFrames: 0
                        }
                      }
                    };
                  }
                })
              },
              SILENCE_FRAME_LONG: [
                {
                  target: 'prompting_next',
                  actions: [
                    assign({
                      responseText: "Would you like to move on to the next question?",
                      waitingForUserInput: false
                    }),
                    'setTtsStartTime',
                    'sendToTts',
                    () => console.log(`[EXAMINATION-BACKGROUND] 8-second timeout reached while background checking`)
                  ]
                }
              ]
            }
          },
          repeating_question: {
            entry: assign({ currentState: 'examination.repeating_question' }),
            on: {
              TTS_FINISHED: {
                target: 'waiting_for_completion',
                actions: assign({
                  ttsFinished: true,
                  waitingForUserInput: true
                })
              }
            }
          },
          prompting_next: {
            entry: assign({ currentState: 'examination.prompting_next' }),
            on: {
              TTS_FINISHED: {
                actions: assign({
                  ttsFinished: true,
                  waitingForUserInput: true
                })
              },
              USER_UTTERANCE: {
                target: 'checking_intent_for_next',
                actions: assign({
                  lastUserUtterance: ({ event }) => event.transcription,
                  hasSpeechDetected: true,
                  waitingForUserInput: false
                })
              },
              // CRITICAL FIX: Handle COORDINATED_TURN_COMPLETE events in examination prompting_next state
              COORDINATED_TURN_COMPLETE: {
                target: 'checking_intent_for_next',
                actions: [
                  assign({
                    lastUserUtterance: ({ event }) => event.transcriptText,
                    hasSpeechDetected: true,
                    waitingForUserInput: false
                  }),
                  // Track the user's response in examination
                  ({ context, event }) => {
                    const { addExaminationResponse } = transcriptActions;
                    addExaminationResponse(context, 'caller', event.transcriptText);
                    console.log(`[EXAMINATION-TRACKING] Added user response via COORDINATED_TURN_COMPLETE in prompting_next: "${event.transcriptText}"`);
                  }
                ]
              }
            }
          },
          checking_intent_for_next: {
            entry: assign({ currentState: 'examination.checking_intent_for_next' }),
            invoke: {
              id: 'nextIntentChecker',
              src: 'examinationIntentChecker',
              input: ({ context }) => ({
                userUtterance: context.lastUserUtterance,
                currentQuestion: "Would you like to move on to the next question?",
                callSid: context.callSid
              }),
              onDone: [
                {
                  target: 'next_question',
                  guard: ({ event }) => event.output.intent === 'completion_check',
                  actions: assign({
                    waitingForUserInput: false
                  })
                },
                {
                  target: 'repeating_question',
                  guard: ({ event }) => event.output.intent === 'repeat_question',
                  actions: [
                    assign(({ context }) => ({
                      responseText: `Let me repeat the question: ${context.examinationQuestions?.[context.currentQuestionIndex || 0] || 'Question not found'}`,
                      waitingForUserInput: false
                    })),
                    'setTtsStartTime',
                    'sendToTts'
                  ]
                },
                {
                  target: 'waiting_for_completion',
                  guard: ({ event }) => event.output.intent === 'continue_answering',
                  actions: [
                    assign({
                      waitingForUserInput: true
                    }),
                    () => console.log(`[EXAMINATION-INTENT-FOR-NEXT] LLM detected user still answering, continue listening silently`)
                  ]
                }
              ],
              onError: {
                target: 'waiting_for_completion',
                actions: assign({
                  waitingForUserInput: true
                })
              }
            }
          },
          processing_next_request: {
            entry: assign({ currentState: 'examination.processing_next_request' }),
            invoke: {
              id: 'nextQuestionLlm',
              src: 'examinationLlmActor',
                             input: ({ context }) => ({
                 userUtterance: context.lastUserUtterance,
                 caseData: context.caseData,
                 conversationHistory: [], // TODO: Pass actual conversation history
                 callSid: context.callSid,
                 turnNumber: context.turnNumber
               }),
              onDone: {
                target: 'responding_to_next_request',
                                 actions: [
                   assign({
                     responseText: ({ event }) => event.output.responseText
                   }),
                   'setTtsStartTime',
                   'sendToTts'
                 ]
              },
              onError: {
                target: 'waiting_for_completion',
                actions: assign({
                  responseText: "I encountered an issue. Please continue with your answer or let me know if you're ready for the next question."
                })
              }
            }
          },
          responding_to_next_request: {
            entry: assign({ currentState: 'examination.responding_to_next_request' }),
            on: {
              TTS_FINISHED: {
                target: 'waiting_for_completion',
                actions: assign({
                  ttsFinished: true,
                  waitingForUserInput: true
                })
              },
              TRANSITION_TO_NEXT_QUESTION: {
                target: 'next_question'
              },
              // CRITICAL FIX: Handle COORDINATED_TURN_COMPLETE events during responding state
              COORDINATED_TURN_COMPLETE: {
                target: 'processing_next_request',
                actions: [
                  'handleCoordinatedTurnComplete',
                  'canonicalTurnCompletionHandler'
                ]
              }
            }
          },
          next_question: {
            always: [
              {
                target: '#osce.diagnosis',
                guard: ({ context }) => {
                  const questions = context.examinationQuestions || [];
                  const currentIndex = context.currentQuestionIndex || 0;
                  const nextIndex = currentIndex + 1;
                  console.log(`[NEXT-QUESTION-DEBUG] currentIndex: ${currentIndex}, nextIndex: ${nextIndex}, questions.length: ${questions.length}, shouldGoToDiagnosis: ${nextIndex >= questions.length}`);
                  return nextIndex >= questions.length;
                },
                actions: [
                  assign({
                    currentState: 'examination.next_question',
                    responseText: "Thank you. The examination questions are now complete. Let's move to the diagnosis phase."
                  }),
                  'setTtsStartTime',
                  'sendToTts'
                ]
              },
              {
                target: 'asking_question',
                actions: [
                  assign(({ context }) => {
                    const questions = context.examinationQuestions || [];
                    const currentIndex = context.currentQuestionIndex || 0;
                    const nextIndex = currentIndex + 1;
                    console.log(`[NEXT-QUESTION-DEBUG] Moving to question ${nextIndex + 1}: ${questions[nextIndex]}`);
                    return {
                      currentState: 'examination.next_question',
                      currentQuestionIndex: nextIndex,
                      responseText: `Here's question ${nextIndex + 1}: ${questions[nextIndex]}`,
                      answerStartTime: Date.now(),
                      hasAcknowledgedContinuation: false // Reset acknowledgment flag for new question
                    };
                  })
                ]
              }
            ]
          }
        }
      },
      diagnosis: {
        initial: 'waiting_for_user_input',
        entry: [
          assign({ 
            currentState: 'diagnosis',
            responseText: "Based on your examination, what do you think is the diagnosis?",
            waitingForUserInput: true
          }),
          'logStateEntry',
          'sendToTts'
        ],
        states: {
          waiting_for_user_input: {
            entry: [
              assign({ currentState: 'diagnosis.waiting_for_user_input' }),
              'manageActivityTimers'
            ],
            on: {
              TTS_FINISHED: {
                actions: assign({
                  ttsFinished: true,
                  waitingForUserInput: true
                })
              },
              USER_UTTERANCE: {
                target: '#history_taking_id.processing',
                actions: assign({
                  lastUserUtterance: ({ event }) => event.transcription,
                  hasSpeechDetected: true,
                  waitingForUserInput: false
                })
              }
            }
          },
          processing: {
            entry: assign({ currentState: 'diagnosis.processing' }),
            invoke: {
              id: 'diagnosisLlm',
              src: 'diagnosisLlmActor',
              input: ({ context }) => ({
                userUtterance: context.lastUserUtterance,
                caseData: context.caseData,
                conversationHistory: context.conversation,
                callSid: context.callSid,
                turnNumber: context.turnNumber
              }),
              onDone: {
                target: 'responding',
                actions: [
                  assign({
                    responseText: ({ event }) => event.output.responseText,
                    waitingForUserInput: false
                  })
                  // REMOVED: 'sendToTts' - This was causing duplicate TTS generation.
                  // TTS will be triggered by the 'responding' state entry action instead.
                ]
              },
              onError: {
                target: '#osce.error',
                actions: assign({ responseText: 'Error processing diagnosis request.' })
              }
            }
          },
          responding: {
            entry: ['setTtsStartTime', 'sendToTts'],
            on: {
              TTS_FINISHED: {
                target: 'waiting_for_user_input',
                actions: assign({
                  ttsFinished: true,
                  waitingForUserInput: true,
                  turnFinalizedAt: Date.now() // Set when AI finishes speaking and is ready to listen
                  // FANG-FIX: Removed premature graceChain reset - chain should only reset when grace period expires
                })
              },
              // CRITICAL FIX: Handle COORDINATED_TURN_COMPLETE events during responding state
              COORDINATED_TURN_COMPLETE: {
                target: '#history_taking_id.processing',
                actions: [
                  'handleCoordinatedTurnComplete',
                  'canonicalTurnCompletionHandler'
                ]
              }
            }
          }
        },
        on: {
          TRANSITION_TO_EXAMINATION: {
            target: 'examination',
            actions: [
              assign({ transitionReason: TransitionReasons.EXAMINATION }),
              ({ context }) => console.log(`[DIRECT-TRANSITION] ${context.callSid}: Transitioning directly to examination state`)
            ]
          },
          TRANSITION_TO_CLOSING: {
            target: 'closing',
            actions: [
              assign({ transitionReason: TransitionReasons.CLOSING }),
              ({ context }) => console.log(`[DIRECT-TRANSITION] ${context.callSid}: Transitioning directly to closing state`)
            ]
          }
        }
      },
      closing: {
        initial: 'waiting_for_user_input',
        entry: [
          assign({ 
            currentState: 'closing',
            responseText: "We've reached the end of this consultation. Would you like to practice with another case?",
            waitingForUserInput: true
          }),
          'saveFeedbackTranscript',
          'completeSession',
          'logStateEntry',
          'sendToTts'
        ],
        states: {
          waiting_for_user_input: {
            entry: [
              assign({ currentState: 'closing.waiting_for_user_input' }),
              'manageActivityTimers'
            ],
            on: {
              TTS_FINISHED: {
                actions: assign({
                  ttsFinished: true,
                  waitingForUserInput: true
                })
              },
              USER_UTTERANCE: {
                target: '#history_taking_id.processing',
                actions: assign({
                  lastUserUtterance: ({ event }) => event.transcription,
                  hasSpeechDetected: true,
                  waitingForUserInput: false
                })
              }
            }
          },
          processing: {
            entry: assign({ currentState: 'closing.processing' }),
            invoke: {
              id: 'closingLlm',
              src: 'transitionOrchestratorLlmActor', // Reuse orchestrator for closing decisions
              input: ({ context }) => ({
                userUtterance: context.lastUserUtterance,
                orchestratorTask: 'HANDLE_CLOSING_RESPONSE',
                currentState: context.currentState,
                callSid: context.callSid,
                turnNumber: context.turnNumber
              }),
              onDone: {
                target: 'responding',
                actions: [
                  assign({
                    responseText: ({ event }) => event.output.responseText,
                    waitingForUserInput: false
                  })
                  // REMOVED: 'sendToTts' - This was causing duplicate TTS generation.
                  // TTS will be triggered by the 'responding' state entry action instead.
                ]
              },
              onError: {
                target: '#osce.error',
                actions: assign({ responseText: 'Error processing closing request.' })
              }
            }
          },
          responding: {
            entry: ['setTtsStartTime', 'sendToTts'],
            on: {
              TTS_FINISHED: {
                target: 'waiting_for_user_input',
                actions: assign({
                  ttsFinished: true,
                  waitingForUserInput: true,
                  turnFinalizedAt: Date.now() // Set when AI finishes speaking and is ready to listen
                  // FANG-FIX: Removed premature graceChain reset - chain should only reset when grace period expires
                })
              },
              // CRITICAL FIX: Handle COORDINATED_TURN_COMPLETE events during responding state
              COORDINATED_TURN_COMPLETE: {
                target: '#history_taking_id.processing',
                actions: [
                  'handleCoordinatedTurnComplete',
                  'canonicalTurnCompletionHandler'
                ]
              }
            }
          }
        },
        on: {
          TRANSITION_TO_EXAMINATION: {
            target: 'examination',
            actions: [
              assign({ transitionReason: TransitionReasons.EXAMINATION }),
              ({ context }) => console.log(`[DIRECT-TRANSITION] ${context.callSid}: Transitioning directly to examination state`)
            ]
          },
          TRANSITION_TO_DIAGNOSIS: {
            target: 'diagnosis',
            actions: [
              assign({ transitionReason: TransitionReasons.DIAGNOSIS }),
              ({ context }) => console.log(`[DIRECT-TRANSITION] ${context.callSid}: Transitioning directly to diagnosis state`)
            ]
          }
        }
      },
      error: {
        entry: assign({ // Simplified error state entry
          currentState: 'error',
          responseText: ({ context }) => context.responseText || 'An unspecified error occurred.' // Preserve specific error message if set
        }),
        // TODO: Maybe transition out or end call?
        on: {
          // Add a timeout to exit after error
          START: {
            target: 'idle' // Return to idle after error
          }
        }
      },
      transition_orchestrator: {
        initial: 'waiting_for_input',
        entry: assign({
          currentState: 'transition_orchestrator', // Add currentState update
          // orchestratorTask is now set by the transitioning state (e.g., greeting or responding)
          waitingForUserInput: false, // Initially, LLM will speak or process
        }),
        states: {
          waiting_for_input: {
            entry: assign({ currentState: 'transition_orchestrator.waiting_for_input' }), // Add currentState update
            on: {
              USER_UTTERANCE: {
                target: '#history_taking_id.processing',
                actions: assign({
                  lastUserUtterance: ({ event }) => event.transcription,
                  hasSpeechDetected: true,
                  waitingForUserInput: false
                })
              },
              COORDINATED_TURN_COMPLETE: {
                target: '#history_taking_id.processing',
                actions: assign(({ event }) => ({
                  lastUserUtterance: event.transcriptText,
                  hasSpeechDetected: true,
                  waitingForUserInput: false,
                  coordination: {
                    lastCompletionTime: Date.now(),
                    gracePeriodApplied: event.gracePeriodApplied
                  }
                }))
              },
              DTMF_INPUT: { // Added DTMF_INPUT handler
                target: '#history_taking_id.processing',
                actions: assign({
                  lastUserUtterance: ({ event }) => event.digit, // Capture digit
                  waitingForUserInput: false
                })
              }
            }
          },
          processing: {
            entry: assign({ currentState: 'transition_orchestrator.processing' }), // Add currentState update
            invoke: {
              id: 'transitionOrchestratorLlm',
              src: 'transitionOrchestratorLlmActor',
              input: ({ context }) => ({
                userUtterance: context.lastUserUtterance,
                orchestratorTask: context.orchestratorTask, // Pass current task
                currentState: context.currentState, // Pass current state
                callSid: context.callSid,
                turnNumber: context.turnNumber
              }),
              onDone: {
                target: 'responding',
                actions: [
                  assign({
                    responseText: ({ event }) => event.output.responseText,
                    orchestratorTask: ({ event }) => event.output.orchestratorTask, // Corrected assignment
                    readinessDecision: ({ event }) => event.output.readinessDecision,
                    waitingForUserInput: false,
                    ttsAgenda: 'speak', // Ensure TTS is triggered in responding state
                    lastUserUtterance: undefined // Clear after processing
                  }),
                  'sendToTts'
                ]
              },
              onError: {
                target: '#osce.error',
                actions: assign({ responseText: 'Error processing request.' })
              }
            }
          },
          responding: {
            entry: [
              assign({ currentState: 'transition_orchestrator.responding' }), // Add currentState update
              'setTtsStartTime', 
              'sendToTts'
            ], 
            on: {
              TTS_FINISHED: [
                // Case 1: User agreed to repeat a case when prompted (CONFIRM_REPEAT_CASE with PROCEED)
                {
                  guard: ({ context }) => 
                    context.orchestratorTask === 'CONFIRM_REPEAT_CASE' && 
                    context.readinessDecision === 'PROCEED' && 
                    !!context.availableCasesToRepeat?.length,
                  target: '#osce.repeat_case_selection',
                  actions: [
                    assign({
                      ttsFinished: true,
                      waitingForUserInput: false, 
                      ttsLastTurnDuration: ({ context }) => 
                        context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 0,
                      responseText: 'Selecting a case for you to repeat...',
                    }),
                    context => console.log('[XState] User agreed to repeat a case. Selecting from available cases.')
                  ]
                },
                // Case 2: User declined to repeat a case when prompted (CONFIRM_REPEAT_CASE with not PROCEED)
                {
                  guard: ({ context }) => 
                    context.orchestratorTask === 'CONFIRM_REPEAT_CASE' && 
                    context.readinessDecision !== 'PROCEED',
                  target: '#osce.idle', // Return to idle state
                  actions: [
                    assign({
                      ttsFinished: true,
                      waitingForUserInput: false, 
                      ttsLastTurnDuration: ({ context }) => 
                        context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 0,
                      responseText: "Okay, no problem. Thank you for participating in the OSCE simulation.",
                      availableCasesToRepeat: undefined, // Clear this as we're exiting the repeat flow
                      orchestratorTask: undefined, // Clear task
                      readinessDecision: undefined, // Clear decision
                    }),
                    'sendToTts', // Speak farewell message
                    context => console.log('[XState] User declined to repeat a case. Returning to idle.')
                  ]
                },
                // Case 3: Standard readiness check with PROCEED (not a repeat case prompt)
                {
                  guard: ({ context }) => 
                    context.orchestratorTask !== 'CONFIRM_REPEAT_CASE' && 
                    context.readinessDecision === 'PROCEED',
                  target: '#osce.finalize_case_selection',
                  actions: [
                    assign({
                      ttsFinished: true,
                      waitingForUserInput: false, 
                      ttsLastTurnDuration: ({ context }) => 
                        context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 0,
                    })
                  ]
                },
                // Case 4: Default - any other scenario goes back to waiting for input
                {
                  target: 'waiting_for_input', 
                  actions: [
                    assign({
                      ttsFinished: true,
                      waitingForUserInput: true,
                      ttsLastTurnDuration: ({ context }) => 
                        context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 0,
                    })
                  ]
                }
              ],
              // CRITICAL FIX: Handle COORDINATED_TURN_COMPLETE events during responding state
              COORDINATED_TURN_COMPLETE: {
                target: '#history_taking_id.processing',
                actions: [
                  'handleCoordinatedTurnComplete',
                  'canonicalTurnCompletionHandler'
                ]
              }
            }
          }
        }
      },
      repeat_case_selection: {
        entry: [
          assign({
            currentState: 'repeat_case_selection',
            responseText: 'Selecting a completed case for you to practice again...',
            ttsAgenda: 'speak'
          }),
          'sendToTts'
        ],
        exit: assign({ ttsFinished: true }),
        on: {
          TTS_FINISHED: { target: 'repeat_case_processing' }
        }
      },
      
      repeat_case_processing: {
        entry: assign({ currentState: 'repeat_case_processing' }),
        always: {
          // Immediately process and select a random case from availableCasesToRepeat
          actions: [
            assign({
              caseData: ({ context }) => {
                // Select a random case from availableCasesToRepeat
                if (context.availableCasesToRepeat && context.availableCasesToRepeat.length > 0) {
                  const randomIndex = Math.floor(Math.random() * context.availableCasesToRepeat.length);
                  const selectedCase = context.availableCasesToRepeat[randomIndex];
                  console.log(`[XState] Selected case to repeat: ${selectedCase.id}`);
                  return selectedCase;
                }
                console.error('[XState] No cases available to repeat');
                return null;
              },
              selectedCaseId: ({ context }) => {
                if (context.availableCasesToRepeat && context.availableCasesToRepeat.length > 0) {
                  const randomIndex = Math.floor(Math.random() * context.availableCasesToRepeat.length);
                  return context.availableCasesToRepeat[randomIndex].id;
                }
                return null;
              },
              responseText: ({ context }) => {
                if (context.availableCasesToRepeat && context.availableCasesToRepeat.length > 0) {
                  const randomIndex = Math.floor(Math.random() * context.availableCasesToRepeat.length);
                  const selectedCase = context.availableCasesToRepeat[randomIndex];
                  return selectedCase.patient_agent_data?.opening_line || 
                         selectedCase.patient_agent_data?.initial_briefing || 
                         'You are now repeating a case. Please begin when you are ready.';
                }
                return 'Sorry, there was an issue selecting a case to repeat.';
              },
              ttsAgenda: 'speak',
              waitingForUserInput: true,
              orchestratorTask: undefined,
              readinessDecision: undefined,
              availableCasesToRepeat: undefined // Clear this now that we've selected a case
            }),
          ],
          target: 'history_taking_main'
        },
        // Fallback in case the 'always' transition doesn't trigger for some reason
        after: {
          // After 1 second, go to history_taking_main anyway
          1000: {
            target: '#history_taking_id.processing',
            actions: [
              assign({ 
                responseText: 'Ready to begin the case again.',
                ttsAgenda: 'speak',
                waitingForUserInput: true
              }),
              'sendToTts'
            ]
          }
        }
      },
      
      information_giving: { 
        // Placeholder for information_giving state
        entry: assign({ currentState: 'information_giving' }),
        on: {
          USER_UTTERANCE: { actions: assign({ responseText: 'Information giving is not yet implemented.' }), target: '#history_taking_id.responding' }
        }
      }, // End of information_giving

      // ============================================================================
      // TUTORING SYSTEM STATES
      // These states run when sessionType === 'tutoring'
      // ============================================================================
      
      tutoring_initialization: {
        meta: { interruptible: false },
        entry: [
          assign({ currentState: 'tutoring_initialization' }),
          'logStateEntry',
          assign({
            responseText: 'Welcome to your personalized medical learning session! I\'m here to help you learn through conversation. Let me set up your session...',
            ttsAgenda: 'speak'
          }),
          'sendToTts'
        ],
        on: {
          TTS_FINISHED: {
            target: 'tutoring_session_setup',
            actions: assign({
              ttsFinished: true,
              currentState: 'tutoring_session_setup'
            })
          }
        }
      },

      tutoring_session_setup: {
        meta: { interruptible: false },
        entry: [
          assign({ currentState: 'tutoring_session_setup' }),
          'logStateEntry'
        ],
        invoke: {
          src: 'tutoringSessionActor',
          input: ({ context }) => ({
            userId: context.userId,
            sessionType: 'tutoring'
          }),
          onDone: {
            target: 'tutoring_active',
            actions: assign({
              tutoringSessionId: ({ event }) => event.output.sessionId,
              tutoringSession: ({ event }) => event.output.session,
              mentalModelReady: ({ event }) => event.output.mentalModelReady,
              responseText: ({ event }) => event.output.initialResponse || 'Perfect! Your learning session is ready. Let\'s continue with your selected topic.',
              ttsAgenda: 'speak',
              currentState: 'tutoring_active'
            })
          },
          onError: {
            target: 'tutoring_error',
            actions: assign({
              errorMessage: ({ event }) => `Failed to initialize tutoring session: ${event.error}`,
              currentState: 'tutoring_error'
            })
          }
        }
      },

      tutoring_active: {
        meta: { interruptible: true },
        entry: [
          assign({ 
            currentState: 'tutoring_active',
            waitingForUserInput: true,
            ttsAgenda: 'listen'
          }),
          'logStateEntry',
          'sendToTts',
          'manageActivityTimers'
        ],
        on: {
          USER_UTTERANCE: {
            target: 'tutoring_processing',
            actions: [
              'assignUserUtterance',
              'logUserUtterance',
              assign({
                waitingForUserInput: false,
                turnNumber: ({ context }) => (context.turnNumber || 0) + 1,
                currentTurnStartTime: () => Date.now()
              })
            ]
          },
          COORDINATED_TURN_COMPLETE: {
            target: 'tutoring_processing',
            actions: [
              assign(({ event, context }) => ({
                lastUserUtterance: event.transcriptText,
                waitingForUserInput: false,
                turnNumber: (context.turnNumber || 0) + 1,
                currentTurnStartTime: Date.now(),
                coordination: {
                  lastCompletionTime: Date.now(),
                  gracePeriodApplied: event.gracePeriodApplied
                }
              }))
            ]
          },
          SPEECH_FRAME: {
            actions: assign({
              speechFramesInCurrentTurn: ({ context }) => context.speechFramesInCurrentTurn + 1,
              consecutiveSilenceFrames: 0,
              hasSpeechDetected: true
            })
          },
          SILENCE_FRAME_SHORT: [
            {
              guard: ({ context, event }) => {
                // Similar logic to simulation, but for tutoring context
                const connectionType = context.infrastructure?.connection?.type || 'twilio';
                const minFrames = getMinSpeechFrames(connectionType);
                const hasSufficientSpeech = context.speechFramesInCurrentTurn >= minFrames;
                const hasFinalTranscript = event.type === 'SILENCE_FRAME_SHORT' && 
                  event.accumulatedText && event.accumulatedText.trim().length > 0;
                return hasSufficientSpeech && hasFinalTranscript;
              },
              target: 'tutoring_processing',
              actions: [
                assign({
                  consecutiveSilenceFrames: ({ context }) => context.consecutiveSilenceFrames + 1,
                  waitingForUserInput: false,
                  turnFinalized: true,
                  // Removed turnFinalizedAt - should only be set when AI finishes speaking (TTS_FINISHED)
                  turnNumber: ({ context }) => (context.turnNumber || 0) + 1,
                  tempAccumulatedText: ({ event }) => {
                    if (event.type === 'SILENCE_FRAME_SHORT' && event.accumulatedText) {
                      return event.accumulatedText;
                    }
                    return undefined;
                  }
                })
              ]
            }
          ]
        }
      },

      tutoring_processing: {
        meta: { interruptible: false },
        entry: [
          assign({ currentState: 'tutoring_processing' }),
          'logStateEntry'
        ],
        invoke: {
          src: 'tutoringConversationActor',
          input: ({ context }) => ({
            sessionId: context.tutoringSessionId,
            userMessage: context.userUtterance || context.tempAccumulatedText || '',
            responseTime: context.currentTurnStartTime ? Date.now() - context.currentTurnStartTime : 60000,
            metadata: {
              turnNumber: context.turnNumber,
              speechFrames: context.speechFramesInCurrentTurn
            }
          }),
          onDone: {
            target: 'tutoring_responding',
            actions: assign({
              responseText: ({ event }) => event.output.tutorMessage,
              tutoringSessionPhase: ({ event }) => event.output.sessionPhase,
              tutoringProgress: ({ event }) => event.output.progressUpdate,
              teachingStrategy: ({ event }) => event.output.teachingStrategy,
              conceptsIntroduced: ({ event }) => event.output.conceptsIntroduced,
              assessmentRequired: ({ event }) => event.output.assessmentRequired,
              shouldEndSession: ({ event }) => event.output.sessionShouldEnd,
              ttsAgenda: 'speak',
              currentState: 'tutoring_responding'
            })
          },
          onError: {
            target: 'tutoring_error',
            actions: assign({
              errorMessage: ({ event }) => `Tutoring processing failed: ${event.error}`,
              currentState: 'tutoring_error'
            })
          }
        }
      },

      tutoring_responding: {
        meta: { interruptible: true },
        entry: [
          assign({ currentState: 'tutoring_responding' }),
          'logStateEntry',
          'sendToTts'
        ],
        on: {
          TTS_FINISHED: [
            {
              guard: ({ context }) => context.shouldEndSession === true,
              target: 'tutoring_completion',
              actions: assign({
                ttsFinished: true,
                currentState: 'tutoring_completion'
              })
            },
            {
              guard: ({ context }) => context.assessmentRequired === true,
              target: 'tutoring_assessment',
              actions: assign({
                ttsFinished: true,
                waitingForUserInput: true,
                ttsAgenda: 'listen',
                currentState: 'tutoring_assessment'
              })
            },
            {
              target: 'tutoring_active',
              actions: assign({
                ttsFinished: true,
                waitingForUserInput: true,
                ttsAgenda: 'listen',
                currentState: 'tutoring_active'
              })
            }
          ],
          // CRITICAL FIX: Handle COORDINATED_TURN_COMPLETE events during responding state
          COORDINATED_TURN_COMPLETE: {
            target: 'tutoring_processing',
            actions: [
              'canonicalTurnCompletionHandler'
            ]
          }
        }
      },

      tutoring_assessment: {
        meta: { interruptible: true },
        entry: [
          assign({ 
            currentState: 'tutoring_assessment',
            waitingForUserInput: true,
            ttsAgenda: 'listen'
          }),
          'logStateEntry',
          'manageActivityTimers'
        ],
        on: {
          USER_UTTERANCE: {
            target: 'tutoring_processing',
            actions: [
              'assignUserUtterance',
              'logUserUtterance',
              assign({
                waitingForUserInput: false,
                turnNumber: ({ context }) => (context.turnNumber || 0) + 1,
                currentTurnStartTime: () => Date.now(),
                assessmentResponse: ({ context }) => context.userUtterance
              })
            ]
          },
          COORDINATED_TURN_COMPLETE: {
            target: 'tutoring_processing',
            actions: [
              assign(({ event, context }) => ({
                lastUserUtterance: event.transcriptText,
                waitingForUserInput: false,
                turnNumber: (context.turnNumber || 0) + 1,
                currentTurnStartTime: Date.now(),
                assessmentResponse: event.transcriptText,
                coordination: {
                  lastCompletionTime: Date.now(),
                  gracePeriodApplied: event.gracePeriodApplied
                }
              }))
            ]
          }
        }
      },

      tutoring_completion: {
        meta: { interruptible: false },
        entry: [
          assign({ currentState: 'tutoring_completion' }),
          'logStateEntry'
        ],
        invoke: {
          src: 'tutoringCompletionActor',
          input: ({ context }) => ({
            sessionId: context.tutoringSessionId,
            conceptsLearned: context.conceptsIntroduced || [],
            objectivesAchieved: [], // Will be calculated by completion actor
            finalScore: context.tutoringProgress || 0,
            timeSpent: context.sessionStartTime ? Math.round((Date.now() - context.sessionStartTime) / 60000) : 30
          }),
          onDone: {
            target: 'tutoring_finished',
            actions: assign({
              responseText: ({ event }) => 
                `Excellent work! You've completed your learning session. ` +
                `You earned ${event.output.experienceGained} experience points` +
                (event.output.prestigeLevelUp ? ` and leveled up to ${event.output.badge}!` : '!') +
                ` Thank you for learning with me today.`,
              experienceGained: ({ event }) => event.output.experienceGained,
              prestigeLevelUp: ({ event }) => event.output.prestigeLevelUp,
              achievementsUnlocked: ({ event }) => event.output.achievements,
              ttsAgenda: 'speak',
              currentState: 'tutoring_finished'
            })
          },
          onError: {
            target: 'tutoring_error',
            actions: assign({
              errorMessage: ({ event }) => `Failed to complete tutoring session: ${event.error}`,
              currentState: 'tutoring_error'
            })
          }
        }
      },

      tutoring_finished: {
        meta: { interruptible: false },
        entry: [
          assign({ currentState: 'tutoring_finished' }),
          'logStateEntry',
          'sendToTts'
        ],
        on: {
          TTS_FINISHED: {
            actions: [
              'logTutoringSessionComplete',
              assign({ ttsFinished: true })
            ]
          }
        }
      },

      tutoring_error: {
        meta: { interruptible: false },
        entry: [
          assign({ 
            currentState: 'tutoring_error',
            responseText: ({ context }) => 
              context.errorMessage || 'I apologize, but there was an issue with your learning session. Please try again.',
            ttsAgenda: 'speak'
          }),
          'logStateEntry',
          'logTutoringError',
          'sendToTts'
        ],
        on: {
          TTS_FINISHED: {
            target: 'idle',
            actions: assign({
              ttsFinished: true,
              currentState: 'idle'
            })
          }
        }
      },
      
      // ============================================================================
      // CENTRALIZED CLEANUP STATES
      // ============================================================================
      call_ending: {
        meta: { interruptible: false },
        entry: [
          assign({ currentState: 'call_ending' }),
          'logStateEntry',
          'cleanupGracePeriodChain', // PHASE 3-2: Clean up grace period chain early in cleanup process
          'initiateCallCleanup'
        ],
        always: {
          target: 'cleanup_in_progress'
        }
      },
      
      cleanup_in_progress: {
        meta: { interruptible: false },
        entry: [
          assign({ currentState: 'cleanup_in_progress' }),
          'logStateEntry'
        ],
        invoke: {
          src: 'cleanupCallResources',
          input: ({ context }) => ({ 
            callSid: context.callSid, 
            userId: context.userId 
          }),
          onDone: {
            target: 'cleanup_complete',
            actions: [
              // CRITICAL FIX: Clean up XState machine after service completes successfully
              ({ context, event }) => {
                console.log(`[XSTATE-CLEANUP] ✅ Cleanup service completed successfully for ${context.callSid}`);
                console.log(`[XSTATE-CLEANUP] Service result:`, event.output);
                
                // Now it's safe to clean up machine references since service is done
                try {
                  const { callControllers } = require('./xstateIntegration');
                  const controller = callControllers[context.callSid];
                  if (controller) {
                    // Remove controller reference - machine will naturally terminate after this state
                    delete callControllers[context.callSid];
                    console.log(`[XSTATE-CLEANUP] ✅ XState controller reference cleaned up for ${context.callSid}`);
                  }
                } catch (error) {
                  console.error(`[XSTATE-CLEANUP] ⚠️ Error cleaning up controller reference:`, error);
                }
              },
              assign({ currentState: 'cleanup_complete' }),
              'logCleanupSuccess'
            ]
          },
          onError: {
            target: 'cleanup_error',
            actions: [
              assign({ 
                currentState: 'cleanup_error',
                responseText: 'Cleanup failed, retrying...'
              }),
              'logCleanupError'
            ]
          }
        },
        on: {
          // Allow critical audio coordination events that are essential for safe resource cleanup
          LIVEKIT_AUDIO_STREAMING_CANCELLED: {
            actions: [
              assign(({ context }) => {
                console.log(`[CLEANUP-AUDIO] ${context.callSid}: Processing LIVEKIT_AUDIO_STREAMING_CANCELLED during cleanup`);
                
                return {
                  infrastructure: {
                    ...context.infrastructure,
                    connection: {
                      ...context.infrastructure?.connection,
                      lastActivity: Date.now(),
                      livekit: {
                        ...context.infrastructure?.connection?.livekit,
                        streamingCancelled: true,
                        streamingActive: false,
                        lastEventTimestamp: Date.now()
                      }
                    }
                  }
                };
              })
            ]
          },
          
          // Allow TTS_FINISHED events to complete ongoing TTS operations safely
          TTS_FINISHED: {
            actions: [
              assign(({ context }) => {
                console.log(`[CLEANUP-TTS] ${context.callSid}: Processing TTS_FINISHED during cleanup`);
                
                return {
                  ttsFinished: true,
                  waitingForUserInput: true
                };
              })
            ]
          },
          
          // Allow LiveKit natural disconnection to prevent double cleanup race condition
          LIVEKIT_ROOM_DISCONNECTED: {
            actions: [
              assign(({ context }) => {
                console.log(`[CLEANUP-ROOM] ${context.callSid}: LiveKit room naturally disconnected during cleanup`);
                
                return {
                  infrastructure: {
                    ...context.infrastructure,
                    connection: {
                      ...context.infrastructure?.connection,
                      lastActivity: Date.now(),
                      livekit: {
                        ...context.infrastructure?.connection?.livekit,
                        roomConnected: false,
                        naturalDisconnect: true, // Flag that natural disconnect occurred
                        lastEventTimestamp: Date.now()
                      }
                    }
                  }
                };
              })
            ]
          },
          
          // Block all non-critical events during cleanup
          '*': {
            actions: ['logEventBlockedDuringCleanup']
          }
        }
      },
      
      cleanup_complete: {
        meta: { interruptible: false },
        entry: [
          assign({ currentState: 'cleanup_complete' }),
          'logStateEntry',
          'cleanupGracePeriodChain', // PHASE 3-2: Ensure grace period chain is cleaned up
          'finalizeCallCleanup'
        ],
        always: {
          target: 'call_ended'
        }
      },
      
      cleanup_error: {
        meta: { interruptible: false },
        entry: [
          assign({ currentState: 'cleanup_error' }),
          'logStateEntry'
        ],
        after: {
          2000: {
            target: 'cleanup_in_progress',
            actions: ['logCleanupRetry']
          }
        },
        on: {
          CLEANUP_COMPLETE: {
            target: 'call_ended',
            actions: ['logForcedCleanupComplete']
          }
        }
      },
      
      call_ended: {
        meta: { interruptible: false },
        entry: [
          assign({ currentState: 'call_ended' }),
          'logStateEntry',
          'markCallAsEnded'
        ],
      }
      
      // Add other states like summarization, feedback here if needed
    } // Closing brace for the 'states' object
  } // Closing brace for the machine config object
  ); // Closing parenthesis and semicolon for the setup call
  return machine;
}

// Export the machine for use in the application
export default createOsceMachine;
