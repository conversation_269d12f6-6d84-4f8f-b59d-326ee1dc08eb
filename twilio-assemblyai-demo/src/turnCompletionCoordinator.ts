/**
 * Turn Completion Coordinator - Barrier Synchronization Pattern
 * 
 * Eliminates race conditions between VAD silence detection and STT final transcripts
 * by requiring BOTH conditions before processing turn completion.
 * 
 * Enhanced with comprehensive timing measurements for performance analysis.
 */

import fs from 'fs';
import path from 'path';
import { conversationTimingService, ConversationPhase } from './services/ConversationTimingService';

/**
 * Comprehensive pipeline logging system
 * Captures every stage of processing with full context
 */
interface LogContext {
  callSid: string;
  turnNumber?: number;
  component: string;
  stage: string;
  operation?: string;
  timestamp: number;
  inputData?: any;
  outputData?: any;
  transformations?: any;
  data?: any;
  metadata?: any;
  errors?: any;
  performance?: any;
}

/**
 * Enhanced logging that captures complete pipeline flow
 */
function logPipelineStage(context: LogContext, message: string, ...args: any[]): void {
  const timestamp = new Date().toISOString();
  const ms = Date.now();

  // Create structured log entry
  const logEntry = {
    timestamp,
    timestampMs: ms,
    callSid: context.callSid,
    turnNumber: context.turnNumber || 'unknown',
    component: context.component,
    stage: context.stage,
    message,
    args: args.length > 0 ? args : undefined,
    data: context.data,
    metadata: context.metadata
  };

  // Format for maximum debugging utility
  const operation = context.operation ? `:${context.operation}` : '';
  const humanReadable = `[${timestamp}] [${context.component}:${context.stage}${operation}] Turn ${context.turnNumber || '?'}: ${message}`;

  const argsStr = args.length > 0 ? ' | ' + args.map(arg =>
    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
  ).join(' ') : '';

  // Build comprehensive details section
  let details = '';
  if (context.inputData) {
    details += `\n  INPUT: ${typeof context.inputData === 'string' ? context.inputData : JSON.stringify(context.inputData, null, 2)}`;
  }
  if (context.outputData) {
    details += `\n  OUTPUT: ${typeof context.outputData === 'string' ? context.outputData : JSON.stringify(context.outputData, null, 2)}`;
  }
  if (context.transformations) {
    details += `\n  TRANSFORMS: ${JSON.stringify(context.transformations, null, 2)}`;
  }
  if (context.data) {
    details += `\n  DATA: ${JSON.stringify(context.data, null, 2)}`;
  }
  if (context.metadata) {
    details += `\n  META: ${JSON.stringify(context.metadata, null, 2)}`;
  }
  if (context.errors) {
    details += `\n  ERRORS: ${JSON.stringify(context.errors, null, 2)}`;
  }
  if (context.performance) {
    details += `\n  PERFORMANCE: ${JSON.stringify(context.performance, null, 2)}`;
  }

  const structuredLog = `\n  STRUCTURED: ${JSON.stringify(logEntry)}`;
  const separator = '\n' + '='.repeat(100);

  const fullLogLine = humanReadable + argsStr + details + structuredLog + separator;

  // Log to console with full context
  console.log(humanReadable + argsStr);
  if (details) console.log(details);

  // Log to file
  try {
    // Find project root by looking for package.json
    let projectRoot = __dirname;
    while (projectRoot !== '/' && !fs.existsSync(path.join(projectRoot, 'package.json'))) {
      projectRoot = path.dirname(projectRoot);
    }

    const logDir = path.join(projectRoot, 'conversations', context.callSid);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    const logFile = path.join(logDir, 'logs.txt');
    fs.appendFileSync(logFile, fullLogLine + '\n');
  } catch (error) {
    // Don't use console.error here to avoid infinite recursion
  }
}

/**
 * Specialized logging functions for critical pipeline stages
 */

// Log turn coordination flow
function logTurnCoordination(callSid: string, turnNumber: number, stage: string, inputData: any, outputData?: any, metadata?: any): void {
  logPipelineStage({
    callSid,
    turnNumber,
    component: 'TURN_COORDINATOR',
    stage,
    operation: 'COORDINATE',
    timestamp: Date.now(),
    inputData,
    outputData,
    metadata
  }, `Turn coordination: ${stage}`);
}

// Log XState machine transitions
function logXStateTransition(callSid: string, turnNumber: number, fromState: string, toState: string, event: string, context?: any): void {
  logPipelineStage({
    callSid,
    turnNumber,
    component: 'XSTATE_MACHINE',
    stage: 'TRANSITION',
    operation: event,
    timestamp: Date.now(),
    inputData: { fromState, event, context },
    outputData: { toState },
    metadata: { transitionType: `${fromState} -> ${toState}` }
  }, `State transition: ${fromState} -> ${toState} via ${event}`);
}

// Log grace period evaluation
function logGracePeriod(callSid: string, turnNumber: number, operation: string, previousTurn?: any, currentTurn?: any, result?: any, errors?: any): void {
  logPipelineStage({
    callSid,
    turnNumber,
    component: 'GRACE_PERIOD',
    stage: 'EVALUATION',
    operation,
    timestamp: Date.now(),
    inputData: { previousTurn, currentTurn },
    outputData: result,
    errors,
    metadata: { gracePeriodActive: !!previousTurn }
  }, `Grace period: ${operation}`);
}

// Log TTS pipeline
function logTTS(callSid: string, turnNumber: number, stage: string, text?: string, audioData?: any, metadata?: any, errors?: any): void {
  logPipelineStage({
    callSid,
    turnNumber,
    component: 'TTS_PIPELINE',
    stage,
    operation: 'SYNTHESIZE',
    timestamp: Date.now(),
    inputData: text ? { text, textLength: text.length } : undefined,
    outputData: audioData,
    metadata,
    errors
  }, `TTS: ${stage}`);
}

// Log text transformations
function logTextTransform(callSid: string, turnNumber: number, stage: string, inputText: string, outputText: string, transformations?: any): void {
  logPipelineStage({
    callSid,
    turnNumber,
    component: 'TEXT_PROCESSOR',
    stage,
    operation: 'TRANSFORM',
    timestamp: Date.now(),
    inputData: { text: inputText, length: inputText.length },
    outputData: { text: outputText, length: outputText.length },
    transformations,
    metadata: {
      lengthChange: outputText.length - inputText.length,
      textChanged: inputText !== outputText
    }
  }, `Text transform: ${stage}`);
}

// Log errors with full context
function logError(callSid: string, turnNumber: number, component: string, operation: string, error: any, context?: any): void {
  logPipelineStage({
    callSid,
    turnNumber,
    component,
    stage: 'ERROR',
    operation,
    timestamp: Date.now(),
    inputData: context,
    errors: {
      message: error.message,
      stack: error.stack,
      name: error.name,
      fullError: error
    },
    metadata: { errorType: error.constructor.name }
  }, `ERROR in ${component}: ${error.message}`);
}

/**
 * Simple logging for backwards compatibility
 */
function logToFile(callSid: string, level: string, message: string, ...args: any[]): void {
  logPipelineStage({
    callSid,
    component: 'SYSTEM',
    stage: level,
    timestamp: Date.now(),
    metadata: args.length > 0 ? { args } : undefined
  }, message, ...args);
}

/**
 * Enhanced console wrapper that logs everything to file
 */
function createConsoleWrapper(callSid: string) {
  const originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.info,
    debug: console.debug
  };

  return {
    log: (...args: any[]) => {
      originalConsole.log(...args);
      logToFile(callSid, 'LOG', args.join(' '));
    },
    error: (...args: any[]) => {
      originalConsole.error(...args);
      logToFile(callSid, 'ERROR', args.join(' '));
    },
    warn: (...args: any[]) => {
      originalConsole.warn(...args);
      logToFile(callSid, 'WARN', args.join(' '));
    },
    info: (...args: any[]) => {
      originalConsole.info(...args);
      logToFile(callSid, 'INFO', args.join(' '));
    },
    debug: (...args: any[]) => {
      originalConsole.debug(...args);
      logToFile(callSid, 'DEBUG', args.join(' '));
    }
  };
}

/**
 * Diagnostic logging helper for delay investigation
 * Writes to both console and logs.txt file for comprehensive tracking
 */
function logDiagnostic(callSid: string, category: string, message: string, data?: any): void {
  const timestamp = new Date().toISOString();
  const logLine = `[${timestamp}] [${category}] ${callSid}: ${message}${data ? ' | ' + JSON.stringify(data) : ''}`;

  // Log to console
  console.log(logLine);

  // Log to file for persistent analysis
  logToFile(callSid, category, `${callSid}: ${message}`, data);
}

interface TurnConditions {
  silenceDetected: boolean;
  finalTranscriptReceived: boolean;
  transcriptText: string;
  silenceTimestamp?: number;
  transcriptTimestamp?: number;
  // Timing tracking
  turnNumber?: number;
  firstConditionTimestamp?: number;
  timingMeasurementId?: string;
}

class TurnCompletionCoordinator {
  private conditions: Record<string, TurnConditions> = {};
  // FANG-STYLE: No artificial timeouts - trust STT provider timing
  // STT providers own their timing contracts, we handle results correctly
  private timeouts: Record<string, NodeJS.Timeout> = {};
  private consoleWrappers: Record<string, any> = {};

  /**
   * Get or create console wrapper for a call
   */
  private getConsole(callSid: string) {
    if (!this.consoleWrappers[callSid]) {
      this.consoleWrappers[callSid] = createConsoleWrapper(callSid);
    }
    return this.consoleWrappers[callSid];
  }

  /**
   * Called when VAD detects silence
   */
  async onSilenceDetected(callSid: string): Promise<void> {
    const silenceTimestamp = Date.now();
    const logger = this.getConsole(callSid);
    logger.log(`[TURN-COORDINATOR] ${callSid}: Silence detected at ${silenceTimestamp}`);
    
    // BARRIER-SYNC: Log silence detection state
    const existingConditions = this.conditions[callSid];
    if (!existingConditions) {
      console.log(`[BARRIER-SYNC] ${callSid}: VAD silence detected at ${silenceTimestamp}, waiting for STT final transcript`);
    } else if (!existingConditions.finalTranscriptReceived) {
      console.log(`[BARRIER-SYNC] ${callSid}: VAD silence detected at ${silenceTimestamp}, still waiting for STT final transcript`);
    } else {
      const waitTime = silenceTimestamp - (existingConditions.transcriptTimestamp || silenceTimestamp);
      console.log(`[BARRIER-SYNC] ${callSid}: VAD silence detected at ${silenceTimestamp}, STT already received ${waitTime}ms ago`);
    }
    
    // DIAGNOSTIC: Track silence detection entry
    logDiagnostic(callSid, 'COORDINATOR-SILENCE-ENTRY', 'onSilenceDetected called', {
      silenceTimestamp,
      hasExistingConditions: !!this.conditions[callSid],
      conditionsState: this.conditions[callSid] ? {
        silenceDetected: this.conditions[callSid].silenceDetected,
        finalTranscriptReceived: this.conditions[callSid].finalTranscriptReceived,
        transcriptLength: this.conditions[callSid].transcriptText?.length || 0,
        turnNumber: this.conditions[callSid].turnNumber,
        firstConditionTimestamp: this.conditions[callSid].firstConditionTimestamp
      } : null
    });
    
    if (!this.conditions[callSid]) {
      // Get turn number for timing tracking
      const turnNumber = this.getCurrentTurnNumber(callSid);
      
      this.conditions[callSid] = {
        silenceDetected: false,
        finalTranscriptReceived: false,
        transcriptText: "",
        turnNumber,
        firstConditionTimestamp: silenceTimestamp
      };
      
      // Start timing turn completion phase (first condition met)
      const timingId = conversationTimingService.startPhase(
        ConversationPhase.TURN_COMPLETION,
        callSid,
        turnNumber,
        { 
          firstCondition: 'silence_detected',
          silenceDetectionDelay: this.calculateSilenceDetectionDelay(callSid)
        }
      );
      this.conditions[callSid].timingMeasurementId = timingId;
      
      // Start total response time measurement (end-to-end timing)
      const totalTimingId = conversationTimingService.startPhase(
        ConversationPhase.TOTAL_RESPONSE_TIME,
        callSid,
        turnNumber,
        {
          firstCondition: 'silence_detected',
          measurementType: 'end_to_end_response_time'
        }
      );
      // Note: Total timing will be completed when audio streaming begins
      
      // Start sub-phase for barrier synchronization
      conversationTimingService.startSubPhase(timingId, 'barrier_synchronization', {
        condition: 'silence_detected',
        timestamp: silenceTimestamp
      });
    }
    
    this.conditions[callSid].silenceDetected = true;
    this.conditions[callSid].silenceTimestamp = silenceTimestamp;
    
    // Track coordination time if this is the second condition
    if (this.conditions[callSid].finalTranscriptReceived && this.conditions[callSid].timingMeasurementId) {
      const coordinationTime = silenceTimestamp - (this.conditions[callSid].firstConditionTimestamp || silenceTimestamp);
      conversationTimingService.endSubPhase(this.conditions[callSid].timingMeasurementId, 'barrier_synchronization');
      
      console.log(`[BARRIER-SYNC] ${callSid}: Coordination completed in ${coordinationTime}ms, trigger: VAD_SILENCE_SECOND`);
      console.log(`[TURN-COORDINATOR-TIMING] ${callSid}: Barrier coordination completed in ${coordinationTime}ms (silence second)`);
    }
    
    try {
      await this.checkTurnComplete(callSid);
    } catch (error) {
      const logger = this.getConsole(callSid);
      logger.error(`[TURN-COORDINATOR] ${callSid}: Error in checkTurnComplete from onSilenceDetected:`, error);
    }
    // FANG-STYLE: No timeout needed - wait for natural STT completion
  }

  /**
   * Called when STT provides final transcript
   */
  async onFinalTranscript(callSid: string, text: string): Promise<void> {
    const transcriptTimestamp = Date.now();
    const logger = this.getConsole(callSid);
    logger.log(`[TURN-COORDINATOR] ${callSid}: Final transcript received: "${text}"`);
    
    // BARRIER-SYNC: Log STT final transcript state
    const existingConditions = this.conditions[callSid];
    if (!existingConditions) {
      console.log(`[BARRIER-SYNC] ${callSid}: STT final transcript received at ${transcriptTimestamp}, waiting for VAD silence detection`);
    } else if (!existingConditions.silenceDetected) {
      console.log(`[BARRIER-SYNC] ${callSid}: STT final transcript received at ${transcriptTimestamp}, still waiting for VAD silence detection`);
    } else {
      const waitTime = transcriptTimestamp - (existingConditions.silenceTimestamp || transcriptTimestamp);
      console.log(`[BARRIER-SYNC] ${callSid}: STT final transcript received at ${transcriptTimestamp}, VAD already detected ${waitTime}ms ago`);
    }
    
    // DIAGNOSTIC: Track transcript entry
    logDiagnostic(callSid, 'COORDINATOR-TRANSCRIPT-ENTRY', 'onFinalTranscript called', {
      transcriptTimestamp,
      textLength: text?.length || 0,
      textPreview: text?.substring(0, 50) + '...',
      hasExistingConditions: !!this.conditions[callSid],
      conditionsState: this.conditions[callSid] ? {
        silenceDetected: this.conditions[callSid].silenceDetected,
        finalTranscriptReceived: this.conditions[callSid].finalTranscriptReceived,
        existingTranscriptLength: this.conditions[callSid].transcriptText?.length || 0,
        turnNumber: this.conditions[callSid].turnNumber,
        firstConditionTimestamp: this.conditions[callSid].firstConditionTimestamp
      } : null
    });
    
    if (!this.conditions[callSid]) {
      // Get turn number for timing tracking
      const turnNumber = this.getCurrentTurnNumber(callSid);
      
      this.conditions[callSid] = {
        silenceDetected: false,
        finalTranscriptReceived: false,
        transcriptText: "",
        turnNumber,
        firstConditionTimestamp: transcriptTimestamp
      };
      
      // Start timing turn completion phase (first condition met)
      const timingId = conversationTimingService.startPhase(
        ConversationPhase.TURN_COMPLETION,
        callSid,
        turnNumber,
        { 
          firstCondition: 'final_transcript',
          textLength: text.length
        }
      );
      this.conditions[callSid].timingMeasurementId = timingId;
      
      // Start total response time measurement (end-to-end timing)
      const totalTimingId = conversationTimingService.startPhase(
        ConversationPhase.TOTAL_RESPONSE_TIME,
        callSid,
        turnNumber,
        {
          firstCondition: 'final_transcript',
          measurementType: 'end_to_end_response_time',
          textLength: text.length
        }
      );
      // Note: Total timing will be completed when audio streaming begins
      
      // Start sub-phase for barrier synchronization
      conversationTimingService.startSubPhase(timingId, 'barrier_synchronization', {
        condition: 'final_transcript',
        timestamp: transcriptTimestamp,
        textLength: text.length
      });
    }
    
    this.conditions[callSid].finalTranscriptReceived = true;
    
    // CORRECT BEHAVIOR: Accumulate multiple STT finals within the same turn
    // Multiple finals can arrive before VAD detects silence - they belong to the same turn
    const existingText = this.conditions[callSid].transcriptText || '';
    if (existingText) {
      // Accumulate all finals with space separation
      this.conditions[callSid].transcriptText = `${existingText} ${text}`.trim();
      console.log(`[TURN-COORDINATOR] ${callSid}: Accumulated turn transcript: "${existingText}" + "${text}" = "${this.conditions[callSid].transcriptText}"`);
    } else {
      // First final for this turn
      this.conditions[callSid].transcriptText = text;
      console.log(`[TURN-COORDINATOR] ${callSid}: First transcript for turn: "${text}"`);
    }
    
    this.conditions[callSid].transcriptTimestamp = transcriptTimestamp;
    
    // Track coordination time if this is the second condition
    if (this.conditions[callSid].silenceDetected && this.conditions[callSid].timingMeasurementId) {
      const coordinationTime = transcriptTimestamp - (this.conditions[callSid].firstConditionTimestamp || transcriptTimestamp);
      conversationTimingService.endSubPhase(this.conditions[callSid].timingMeasurementId, 'barrier_synchronization');
      
      console.log(`[BARRIER-SYNC] ${callSid}: Coordination completed in ${coordinationTime}ms, trigger: STT_FINAL_SECOND`);
      console.log(`[TURN-COORDINATOR-TIMING] ${callSid}: Barrier coordination completed in ${coordinationTime}ms (transcript second)`);
    }
    
    try {
      await this.checkTurnComplete(callSid);
    } catch (error) {
      const logger = this.getConsole(callSid);
      logger.error(`[TURN-COORDINATOR] ${callSid}: Error in checkTurnComplete from onFinalTranscript:`, error);
    }
    // FANG-STYLE: No timeout needed - wait for natural STT completion
  }

  /**
   * Check if both conditions are met and process turn atomically
   */
  private async checkTurnComplete(callSid: string): Promise<void> {
    const conditions = this.conditions[callSid];
    if (!conditions) return;
    
    const isReady = conditions.silenceDetected && conditions.finalTranscriptReceived;
    
    // DIAGNOSTIC: Track turn completion check
    logDiagnostic(callSid, 'COORDINATOR-COMPLETION-CHECK', 'Checking if both conditions met', {
      checkTimestamp: Date.now(),
      silenceDetected: conditions.silenceDetected,
      finalTranscriptReceived: conditions.finalTranscriptReceived,
      isReady,
      transcriptLength: conditions.transcriptText?.length || 0,
      turnNumber: conditions.turnNumber,
      timeSinceFirstCondition: conditions.firstConditionTimestamp ? Date.now() - conditions.firstConditionTimestamp : 'unknown',
      silenceTimestamp: conditions.silenceTimestamp,
      transcriptTimestamp: conditions.transcriptTimestamp
    });
    
    const logger = this.getConsole(callSid);
    logger.log(`[TURN-COORDINATOR] ${callSid}: Checking completion - Silence: ${conditions.silenceDetected}, Transcript: ${conditions.finalTranscriptReceived}, Ready: ${isReady}`);
    
    if (isReady) {
      // DIAGNOSTIC: Track turn processing start
      logDiagnostic(callSid, 'COORDINATOR-PROCESSING-START', 'Both conditions met, starting turn processing', {
        processingStartTime: Date.now(),
        totalCoordinationTime: conditions.firstConditionTimestamp ? Date.now() - conditions.firstConditionTimestamp : 'unknown'
      });

      await this.processTurnComplete(callSid, conditions.transcriptText, conditions);
    }
  }

  /**
   * Atomic turn completion processing
   */
  private async processTurnComplete(callSid: string, transcriptText: string, conditions: TurnConditions): Promise<void> {
    const logger = this.getConsole(callSid);
    const turnNumber = conditions.turnNumber || 0;

      logger.log(`[TURN-COORDINATOR] ${callSid}: processTurnComplete ENTRY - Starting atomic turn completion`);

      // ULTRA TIMING FIX: Use actual user stop time, not processing time
      const userStoppedAt = conditions.silenceTimestamp
        ? conditions.silenceTimestamp - 660  // Remove VAD detection delay (0.66s)
        : Date.now(); // Fallback to processing time if no silence timestamp

      logTurnCoordination(callSid, turnNumber, 'TIMING_CALCULATION', {
        silenceTimestamp: conditions.silenceTimestamp,
        vadDelay: 660
      }, {
        userStoppedAt,
        calculationMethod: conditions.silenceTimestamp ? 'silence_based' : 'fallback'
      });

      logger.log(`[TURN-COORDINATOR] ${callSid}: ATOMIC TURN COMPLETION with text: "${transcriptText}", userStoppedAt: ${userStoppedAt}`);

    // Start turn processing sub-phase
    if (conditions?.timingMeasurementId) {
      conversationTimingService.startSubPhase(conditions.timingMeasurementId, 'turn_processing', {
        transcriptLength: transcriptText.length,
        userStoppedAt,
        processingStartTime: performance.now()
      });
    }
    
    // XSTATE-FIRST: Get interruption timestamp from clean XState context
    // XState context now uses immutable turnTimings that prevent contamination
    let interruptionTimestamp: number | undefined;
    
    const getInterruptionTimestamp = (): number | undefined => {
      // 🔍 INVESTIGATION: Interruption timestamp retrieval analysis
      const retrievalTime = Date.now();
      console.log(`🔍 [TIMESTAMP-RETRIEVAL] getInterruptionTimestamp called for ${callSid} at ${retrievalTime}`);
      
      // 🔍 INVESTIGATION: Race condition detection - retrieval timing
      console.log(`🔍 [RACE-DETECT] INTERRUPTION RETRIEVAL - Race Condition Analysis:`);
      console.log(`🔍 [RACE-DETECT] - Retrieval thread: turnCompletionCoordinator`);
      console.log(`🔍 [RACE-DETECT] - Retrieval timestamp: ${retrievalTime}`);
      console.log(`🔍 [RACE-DETECT] - Context access: XState machine snapshot (should be current)`);
      console.log(`🔍 [RACE-DETECT] - Looking for: Most recent interruption timestamp in turnTimings`);
      
      const { callControllers } = require('./xstateIntegration');
      
      const controller = callControllers[callSid];
      if (!controller?.machine) {
        console.warn(`[TURN-COORDINATOR] No XState controller found for ${callSid}`);
        console.log(`🔍 [TIMESTAMP-RETRIEVAL] ❌ No controller found - returning undefined`);
        return undefined;
      }
      
      const snapshot = controller.machine.getSnapshot();
      const turnTimings = snapshot.context.turnTimings;
      
      // 🔍 INVESTIGATION: XState context analysis
      console.log(`🔍 [TIMESTAMP-RETRIEVAL] XState Context Analysis:`);
      console.log(`🔍 [TIMESTAMP-RETRIEVAL] - Snapshot available: ${!!snapshot}`);
      console.log(`🔍 [TIMESTAMP-RETRIEVAL] - Context available: ${!!snapshot.context}`);
      console.log(`🔍 [TIMESTAMP-RETRIEVAL] - TurnTimings available: ${!!turnTimings}`);
      console.log(`🔍 [TIMESTAMP-RETRIEVAL] - TurnTimings type: ${Array.isArray(turnTimings) ? 'array' : typeof turnTimings}`);
      console.log(`🔍 [TIMESTAMP-RETRIEVAL] - TurnTimings length: ${turnTimings?.length || 0}`);
      
      if (!turnTimings || !Array.isArray(turnTimings)) {
        console.log(`[TURN-COORDINATOR] No turn timings found in XState context for ${callSid}`);
        console.log(`🔍 [TIMESTAMP-RETRIEVAL] ❌ No valid turnTimings array - returning undefined`);
        return undefined;
      }
      
      // Find the most recent turn timing with an interruption
      console.log(`[TURN-COORDINATOR] Searching through ${turnTimings.length} turn timings for interruption:`);
      console.log(`🔍 [TIMESTAMP-RETRIEVAL] Detailed Turn Timings Analysis:`);
      
      // CRITICAL LOGGING: Log complete turnTimings array for root cause analysis
      logDiagnostic(callSid, 'INTERRUPTION-TIMESTAMP-SEARCH', 'Complete turnTimings array analysis', {
        totalTurns: turnTimings.length,
        searchTime: retrievalTime,
        turnTimings: turnTimings.map((timing, index) => ({
          arrayIndex: index,
          turnIndex: timing.turnIndex,
          hasInterruption: !!timing.interruptionTimestamp,
          interruptionTimestamp: timing.interruptionTimestamp,
          interruptionAge: timing.interruptionTimestamp ? retrievalTime - timing.interruptionTimestamp : null,
          audioStreamingStartAt: timing.audioStreamingStartAt,
          sttFinalizedAt: timing.sttFinalizedAt,
          userStoppedAt: timing.userStoppedAt
        }))
      });
      
      for (let i = turnTimings.length - 1; i >= 0; i--) {
        const timing = turnTimings[i];
        
        console.log(`🔍 [TIMESTAMP-RETRIEVAL] - Turn ${i}: index=${timing.turnIndex}, audioStarted=${!!timing.audioStreamingStartAt}, interrupted=${!!timing.interruptionTimestamp}`);
        if (timing.interruptionTimestamp) {
          const age = retrievalTime - timing.interruptionTimestamp;
          console.log(`🔍 [TIMESTAMP-RETRIEVAL] - Turn ${i} interruption timestamp: ${timing.interruptionTimestamp} (age: ${age}ms)`);
          
          // CRITICAL LOGGING: Log when we find ancient timestamps
          logDiagnostic(callSid, 'ANCIENT-TIMESTAMP-FOUND', 'Found interruption timestamp', {
            arrayIndex: i,
            turnIndex: timing.turnIndex,
            interruptionTimestamp: timing.interruptionTimestamp,
            timestampAge: age,
            isAncient: age > 30000,
            retrievalTime
          });
        }
        
        if (timing.interruptionTimestamp) {
          console.log(`[TURN-COORDINATOR] Found interruption timestamp: ${timing.interruptionTimestamp} for turn ${timing.turnIndex}`);
          console.log(`🔍 [TIMESTAMP-RETRIEVAL] ✅ FOUND interruption timestamp: ${timing.interruptionTimestamp} for turn ${timing.turnIndex}`);
          
          return timing.interruptionTimestamp;
        }
      }
      
      console.log(`🔍 [TIMESTAMP-RETRIEVAL] ❌ NO interruption timestamp found in any turn - returning undefined`);
      
      // 🔍 INVESTIGATION: Race condition detection - no timestamp found
      console.log(`🔍 [RACE-DETECT] ❌ NO INTERRUPTION FOUND - Race Analysis:`);
      console.log(`🔍 [RACE-DETECT] - Searched turns: ${turnTimings.length}`);
      console.log(`🔍 [RACE-DETECT] - Retrieval time: ${retrievalTime}`);
      console.log(`🔍 [RACE-DETECT] - Possible race conditions:`);
      console.log(`🔍 [RACE-DETECT]   1. RECORDING RACE: Interruption timestamp recorded AFTER getInterruptionTimestamp() called`);
      console.log(`🔍 [RACE-DETECT]   2. PRE-TTS SCENARIO: No interruption timestamp needed (TTS hasn't started)`);
      console.log(`🔍 [RACE-DETECT]   3. CONTEXT LAG: XState context update hasn't propagated yet`);
      console.log(`🔍 [RACE-DETECT] - Expected result: This will cause NaN in POST-TTS grace period calculation!`);
      console.log(`🔍 [RACE-DETECT] - Investigation needed: Check LIVEKIT_VAD_DATA logs to see if interruption was recorded`);
      
      return undefined;
    };
    
    // Start interruption timestamp retrieval sub-phase
    if (conditions?.timingMeasurementId) {
      conversationTimingService.startSubPhase(conditions.timingMeasurementId, 'interruption_timestamp_retrieval');
    }
    
    // SIMPLIFIED: Direct call since timing data is now synchronized
    try {
      interruptionTimestamp = getInterruptionTimestamp();
      
      if (interruptionTimestamp !== undefined) {
        console.log(`[TURN-COORDINATOR] Found interruption timestamp: ${interruptionTimestamp}`);
      } else {
        console.log(`[TURN-COORDINATOR] No recent interruption timestamp found for ${callSid}`);
      }
    } catch (error) {
      console.error(`[TURN-COORDINATOR] Error getting interruption timestamp:`, error);
      interruptionTimestamp = undefined;
    }
    
    // End interruption timestamp retrieval sub-phase
    if (conditions?.timingMeasurementId) {
      conversationTimingService.endSubPhase(conditions.timingMeasurementId, 'interruption_timestamp_retrieval');
    }
    
    
    // FANG-STYLE: No timeout to clear - pure barrier synchronization
    
    // CRITICAL FIX: Use TurnCoordinationService instead of direct sendCoordinatedTurnComplete
    // This ensures turnLifecycleContext is built for grace period detection
    const { turnCoordinationService } = require('./services/TurnCoordinationService');
    
    // Start coordination service call sub-phase
    if (conditions?.timingMeasurementId) {
      conversationTimingService.startSubPhase(conditions.timingMeasurementId, 'coordination_service_call');
    }
    
    try {
      // Get actual connection type from call state
      let connectionType = 'twilio'; // Default fallback
      try {
        const { calls } = require('./server');
        const callState = calls[callSid];
        if (callState?.connectionType) {
          connectionType = callState.connectionType;
        }
      } catch (error) {
        console.warn(`[TURN-COORDINATOR] ${callSid}: Could not determine connection type, using default: ${connectionType}`);
      }
      
      console.log(`[TURN-COORDINATOR] ${callSid}: Using connection type: ${connectionType}`);
      
      // Log coordination service call with full context
      logTurnCoordination(callSid, turnNumber, 'COORDINATION_SERVICE_CALL', {
        userStoppedAt,
        transcriptText,
        connectionType,
        interruptionTimestamp,
        callTime: Date.now()
      }, undefined, {
        service: 'turnCoordinationService',
        method: 'coordinateAtomicTurnCompletion'
      });

      // Use the proper coordination service that builds turn lifecycle context
      const result = await turnCoordinationService.coordinateAtomicTurnCompletion(
        callSid,
        userStoppedAt,
        transcriptText,
        connectionType,
        interruptionTimestamp
      );

      // Log coordination service result
      logTurnCoordination(callSid, turnNumber, 'COORDINATION_SERVICE_RESULT', {
        callCompleted: Date.now()
      }, result, {
        success: result.success,
        coordinationTime: result.coordinationTime
      });
      
      // End coordination service call sub-phase
      if (conditions?.timingMeasurementId) {
        conversationTimingService.endSubPhase(conditions.timingMeasurementId, 'coordination_service_call');
      }
      
      if (result.success) {
        console.log(`[TURN-COORDINATOR] ${callSid}: Successfully completed atomic turn coordination`);
        console.log(`[TURN-COORDINATOR] ${callSid}: Turn ${result.turnNumber}, coordination time: ${result.coordinationTime}ms`);
        
        // End turn processing and complete turn completion timing
        if (conditions?.timingMeasurementId) {
          conversationTimingService.endSubPhase(conditions.timingMeasurementId, 'turn_processing');
          conversationTimingService.endPhase(conditions.timingMeasurementId, {
            coordinationServiceTime: result.coordinationTime,
            totalTranscriptLength: transcriptText.length,
            turnNumber: result.turnNumber
          });
        }
        
        this.reset(callSid);
      } else {
        console.error(`[TURN-COORDINATOR] ${callSid}: Atomic turn coordination failed: ${result.error}`);
        
        // End failed coordination
        if (conditions?.timingMeasurementId) {
          conversationTimingService.endSubPhase(conditions.timingMeasurementId, 'coordination_service_call');
          conversationTimingService.endSubPhase(conditions.timingMeasurementId, 'turn_processing');
          conversationTimingService.endPhase(conditions.timingMeasurementId, {
            error: result.error,
            failed: true
          });
        }
        
        // CRITICAL: Reset immediately even on failure to prevent stuck states
        this.reset(callSid);
      }
    } catch (error) {
      // Log comprehensive error details
      // logError(callSid, turnNumber, 'TURN_COORDINATOR', 'processTurnComplete', error, {
      //   transcriptText,
      //   conditions,
      //   userStoppedAt: conditions.silenceTimestamp ? conditions.silenceTimestamp - 660 : Date.now(),
      //   processingStage: 'atomic_turn_coordination'
      // });

      console.error(`[TURN-COORDINATOR] ${callSid}: Error during atomic turn coordination:`, error);
      console.error(`[TURN-COORDINATOR] ${callSid}: Error stack:`, error.stack);
      console.error(`[TURN-COORDINATOR] ${callSid}: Error context:`, {
        transcriptText,
        conditions,
        turnNumber
      });

      // End failed coordination
      if (conditions?.timingMeasurementId) {
        conversationTimingService.endSubPhase(conditions.timingMeasurementId, 'coordination_service_call');
        conversationTimingService.endSubPhase(conditions.timingMeasurementId, 'turn_processing');
        conversationTimingService.endPhase(conditions.timingMeasurementId, {
          error: error.message,
          failed: true
        });
      }

      // CRITICAL: Reset immediately even on exception to prevent stuck states
      this.reset(callSid);
    }
  }

  // TURN COORDINATOR ARCHITECTURE: Immediate reset for clean turn boundaries
  // Turn Coordinator handles intra-turn combination only
  // Grace period logic is handled by Grace Period Chain in XState

  /**
   * Reset conditions for next turn
   */
  private reset(callSid: string): void {
    const logger = this.getConsole(callSid);
    logger.log(`[TURN-COORDINATOR] ${callSid}: Resetting conditions for next turn`);

    delete this.conditions[callSid];
    delete this.consoleWrappers[callSid];
  }

  /**
   * Force reset (called when call ends)
   */
  public forceReset(callSid: string): void {
    const logger = this.getConsole(callSid);
    logger.log(`[TURN-COORDINATOR] ${callSid}: Force reset - cleaning up conditions`);
    
    // PHASE 3-2: Clean up grace period chain from XState context
    try {
      const { callControllers } = require('./xstateIntegration');
      const controller = callControllers[callSid];
      if (controller?.machine) {
        const snapshot = controller.machine.getSnapshot();
        if (snapshot.context.gracePeriodChain) {
          console.log(`[TURN-COORDINATOR] ${callSid}: Resetting grace period chain during force reset`);
          
          // CRITICAL FIX: Don't destroy the gracePeriodChain instance, just reset it
          // This prevents the "GracePeriodChain not initialized" error
          snapshot.context.gracePeriodChain.reset();
          
          console.log(`[TURN-COORDINATOR] ${callSid}: Grace period chain reset during force reset`);
        }
      }
    } catch (gracePeriodError) {
      console.error(`[TURN-COORDINATOR] ${callSid}: Error cleaning up grace period chain during force reset:`, gracePeriodError);
    }
    
    this.reset(callSid);
  }

  /**
   * Get current state for debugging
   */
  public getState(callSid: string): TurnConditions | null {
    return this.conditions[callSid] || null;
  }

  /**
   * Get current turn number for timing tracking
   * XSTATE-FIRST: Use clean XState context with immutable turnTimings
   */
  private getCurrentTurnNumber(callSid: string): number {
    try {
      const { callControllers } = require('./xstateIntegration');
      const controller = callControllers[callSid];
      
      if (controller?.machine) {
        const snapshot = controller.machine.getSnapshot();
        const context = snapshot.context;
        
        // First try context.turnNumber (most direct)
        if (context.turnNumber !== undefined) {
          return context.turnNumber;
        }
        
        // Then try turnTimings length (immutable array)
        if (context.turnTimings && Array.isArray(context.turnTimings)) {
          return context.turnTimings.length;
        }
        
        // Fallback to conversation history length
        if (context.conversationHistory && Array.isArray(context.conversationHistory)) {
          return Math.floor(context.conversationHistory.length / 2); // Approximate turn number
        }
      }
    } catch (error) {
      console.warn(`[TURN-COORDINATOR] Error getting turn number for ${callSid}:`, error);
    }
    
    // Default to 1 if we can't determine
    return 1;
  }

  /**
   * Calculate silence detection delay (time from user stop speaking to detection)
   */
  private calculateSilenceDetectionDelay(callSid: string): number {
    try {
      const { calls } = require('./server');
      const callState = calls[callSid];
      
      if (callState?.lastSpeechEndTime) {
        return Date.now() - callState.lastSpeechEndTime;
      }
    } catch (error) {
      console.warn(`[TURN-COORDINATOR] Error calculating silence detection delay for ${callSid}:`, error);
    }
    
    return 0; // Default if we can't calculate
  }
}

// Export singleton instance
export const turnCompletionCoordinator = new TurnCompletionCoordinator();
