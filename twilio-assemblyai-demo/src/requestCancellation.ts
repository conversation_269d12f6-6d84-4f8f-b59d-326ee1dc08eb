/**
 * Production-grade HTTP request cancellation system
 * Tracks and cancels in-flight LLM requests during cascading interrupts
 */

import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

interface TrackedRequest {
  operationId: string;
  callSid: string;
  endpoint: string;
  abortController: AbortController;
  timestamp: number;
  stage: string; // 'processing', 'responding', etc.
}

class RequestCancellationManager {
  private static instance: RequestCancellationManager;
  private activeRequests: Map<string, TrackedRequest> = new Map();

  private constructor() {}

  static getInstance(): RequestCancellationManager {
    if (!RequestCancellationManager.instance) {
      RequestCancellationManager.instance = new RequestCancellationManager();
    }
    return RequestCancellationManager.instance;
  }

  /**
   * Make a cancellable HTTP request
   */
  async makeRequest<T = any>(
    config: AxiosRequestConfig,
    operationId: string,
    callSid: string,
    endpoint: string,
    stage: string = 'processing'
  ): Promise<AxiosResponse<T>> {
    // XState-driven pre-validation: Check for interruption before making request
    const isInterrupted = await this.checkXStateInterruption(callSid);
    if (isInterrupted) {
      console.log(`[REQUEST-CANCEL] Request prevented due to interruption for call: ${callSid}, operation: ${operationId}`);
      throw new Error(`REQUEST_CANCELLED_PREEMPTIVELY: ${operationId}`);
    }
    
    const abortController = new AbortController();
    
    // Track this request
    const trackedRequest: TrackedRequest = {
      operationId,
      callSid,
      endpoint,
      abortController,
      timestamp: Date.now(),
      stage
    };
    
    const requestKey = `${callSid}-${operationId}`;
    this.activeRequests.set(requestKey, trackedRequest);
    
    console.log(`[REQUEST-CANCEL] Tracking request: ${requestKey} (${endpoint}) - Stage: ${stage}, Total active: ${this.activeRequests.size}`);
    
    try {
      // Add abort signal to axios config
      const requestConfig: AxiosRequestConfig = {
        ...config,
        signal: abortController.signal
      };
      
      const response = await axios(requestConfig);
      
      // Request completed successfully, remove from tracking
      this.activeRequests.delete(requestKey);
      console.log(`[REQUEST-CANCEL] Completed request: ${requestKey}`);
      
      return response;
      
    } catch (error) {
      // Remove from tracking regardless of error type
      this.activeRequests.delete(requestKey);
      
      if (axios.isCancel(error)) {
        console.log(`[REQUEST-CANCEL] Request cancelled: ${requestKey}`);
        throw new Error(`REQUEST_CANCELLED: ${operationId}`);
      } else {
        console.error(`[REQUEST-CANCEL] Request failed: ${requestKey}`, error);
        throw error;
      }
    }
  }

  /**
   * Cancel all requests for a specific call and stage
   */
  cancelRequestsForCallAndStage(callSid: string, stage: string): number {
    let cancelledCount = 0;
    
    // Enhanced debugging: Log all active requests before attempting cancellation
    console.log(`[REQUEST-CANCEL] Attempting to cancel requests for ${callSid} in stage: ${stage}`);
    console.log(`[REQUEST-CANCEL] Total active requests: ${this.activeRequests.size}`);
    
    if (this.activeRequests.size > 0) {
      console.log(`[REQUEST-CANCEL] Active requests breakdown:`);
      for (const [requestKey, request] of this.activeRequests.entries()) {
        console.log(`  - ${requestKey}: callSid=${request.callSid}, stage=${request.stage}, endpoint=${request.endpoint}, age=${Date.now() - request.timestamp}ms`);
      }
    }
    
    for (const [requestKey, request] of this.activeRequests.entries()) {
      if (request.callSid === callSid && request.stage === stage) {
        console.log(`[REQUEST-CANCEL] Cancelling request: ${requestKey} (${request.endpoint})`);
        request.abortController.abort();
        this.activeRequests.delete(requestKey);
        cancelledCount++;
      }
    }
    
    console.log(`[REQUEST-CANCEL] Cancelled ${cancelledCount} requests for ${callSid} in stage: ${stage}`);
    return cancelledCount;
  }

  /**
   * Cancel all requests for a specific call
   */
  cancelAllRequestsForCall(callSid: string): number {
    let cancelledCount = 0;
    
    for (const [requestKey, request] of this.activeRequests.entries()) {
      if (request.callSid === callSid) {
        console.log(`[REQUEST-CANCEL] Cancelling request: ${requestKey} (${request.endpoint})`);
        request.abortController.abort();
        this.activeRequests.delete(requestKey);
        cancelledCount++;
      }
    }
    
    console.log(`[REQUEST-CANCEL] Cancelled ${cancelledCount} total requests for ${callSid}`);
    return cancelledCount;
  }

  /**
   * Cancel a specific request by operation ID
   */
  cancelRequestByOperation(callSid: string, operationId: string): boolean {
    const requestKey = `${callSid}-${operationId}`;
    const request = this.activeRequests.get(requestKey);
    
    if (request) {
      console.log(`[REQUEST-CANCEL] Cancelling specific request: ${requestKey} (${request.endpoint})`);
      request.abortController.abort();
      this.activeRequests.delete(requestKey);
      return true;
    }
    
    console.log(`[REQUEST-CANCEL] Request not found for cancellation: ${requestKey}`);
    return false;
  }

  /**
   * Get active request count for debugging
   */
  getActiveRequestCount(callSid?: string): number {
    if (callSid) {
      return Array.from(this.activeRequests.values()).filter(r => r.callSid === callSid).length;
    }
    return this.activeRequests.size;
  }

  /**
   * Debug function to log all active requests
   */
  logActiveRequests(callSid?: string): void {
    console.log(`[REQUEST-CANCEL-DEBUG] Active requests: ${this.activeRequests.size}`);
    for (const [requestKey, request] of this.activeRequests.entries()) {
      if (!callSid || request.callSid === callSid) {
        const age = Date.now() - request.timestamp;
        console.log(`  - ${requestKey}: callSid=${request.callSid}, stage=${request.stage}, endpoint=${request.endpoint}, age=${age}ms`);
      }
    }
  }

  /**
   * Clean up old completed/failed requests that might have been missed
   */
  cleanupStaleRequests(maxAgeMs: number = 30000): number {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [requestKey, request] of this.activeRequests.entries()) {
      if (now - request.timestamp > maxAgeMs) {
        console.log(`[REQUEST-CANCEL] Cleaning up stale request: ${requestKey}`);
        request.abortController.abort();
        this.activeRequests.delete(requestKey);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`[REQUEST-CANCEL] Cleaned up ${cleanedCount} stale requests`);
    }
    
    return cleanedCount;
  }

  /**
   * Check XState context for interruption indicators
   * Centralized interruption detection through XState machine
   */
  private async checkXStateInterruption(callSid: string): Promise<boolean> {
    try {
      // Import XState integration dynamically to avoid circular dependencies
      const { callControllers } = await import('./xstateIntegration');
      
      const controller = callControllers[callSid];
      if (!controller?.machine) {
        return false; // No XState controller means no interruption tracking
      }
      
      const snapshot = controller.machine.getSnapshot();
      const context = snapshot.context;
      
      // Check multiple XState interruption indicators
      const hasInterruption = !!(
        context.interruptionTimestamp ||
        context.gracePeriodActive ||
        context.isTransitioningToGracePeriod
      );
      
      if (hasInterruption) {
        console.log(`[REQUEST-CANCEL] XState interruption detected for ${callSid}:`, {
          interruptionTimestamp: context.interruptionTimestamp,
          gracePeriodActive: context.gracePeriodActive,
          isTransitioningToGracePeriod: context.isTransitioningToGracePeriod
        });
      }
      
      return hasInterruption;
      
    } catch (error) {
      console.error(`[REQUEST-CANCEL] Error checking XState for interruption:`, error);
      return false; // Default to no interruption on error
    }
  }
}

// Export singleton instance
export const requestCancellationManager = RequestCancellationManager.getInstance();

// Convenience functions
export async function makeCancellableRequest<T = any>(
  config: AxiosRequestConfig,
  operationId: string,
  callSid: string,
  endpoint: string,
  stage: string = 'processing'
): Promise<AxiosResponse<T>> {
  return requestCancellationManager.makeRequest<T>(config, operationId, callSid, endpoint, stage);
}

export function cancelRequestsForStage(callSid: string, stage: string): number {
  return requestCancellationManager.cancelRequestsForCallAndStage(callSid, stage);
}

export function cancelAllRequests(callSid: string): number {
  return requestCancellationManager.cancelAllRequestsForCall(callSid);
}

export function cancelRequestByOperation(callSid: string, operationId: string): boolean {
  return requestCancellationManager.cancelRequestByOperation(callSid, operationId);
}

export function logActiveRequests(callSid?: string): void {
  return requestCancellationManager.logActiveRequests(callSid);
}