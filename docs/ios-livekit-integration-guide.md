# iOS LiveKit Integration Guide

This guide provides the exact iOS implementation needed to work with our LiveKit WebRTC backend integration.

## Overview

Our backend LiveKit integration provides:
- **Room Creation API**: `POST /api/livekit/create-room` 
- **Audio Input Processing**: iOS mic → LiveKit Room → Backend VAD/STT/XState pipeline
- **Audio Output Delivery**: Backend TTS → LiveKit Room → iOS speaker
- **Real-time Communication**: Bidirectional audio streaming with lowest latency

## Architecture

```
iOS App ←→ LiveKit Room ←→ Backend (AI Participant)
    ↓                           ↓
Audio I/O                   VAD/STT/XState/TTS
```

## Prerequisites

### 1. Add LiveKit Swift SDK

Add to your `Package.swift` or Xcode Package Manager:

```swift
dependencies: [
    .package(url: "https://github.com/livekit/client-sdk-swift", from: "2.0.0")
]
```

### 2. Required Permissions

Add to `Info.plist`:

```xml
<key>NSMicrophoneUsageDescription</key>
<string>This app needs microphone access for voice communication</string>
```

## Implementation

### 1. LiveKit Service Class

Create `LiveKitService.swift`:

```swift
import LiveKit
import AVFoundation

class LiveKitService: ObservableObject {
    private var room: Room?
    private var localAudioTrack: LocalAudioTrack?
    
    @Published var isConnected = false
    @Published var isRecording = false
    @Published var connectionError: String?
    
    // Backend configuration - UPDATE THESE FOR YOUR ENVIRONMENT
    private let backendBaseURL = "http://*************:5000" // Your backend server
    private let livekitServerURL = "wss://*************" // Your LiveKit server
    
    init() {
        setupAudioSession()
    }
    
    // MARK: - Audio Session Setup
    
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, 
                                       mode: .voiceChat, 
                                       options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    // MARK: - Room Connection
    
    func createRoomAndConnect(userId: String) async {
        do {
            // Step 1: Request room creation from backend
            let roomData = try await requestRoomCreation(userId: userId)
            
            // Step 2: Connect to LiveKit room
            try await connectToRoom(roomData: roomData)
            
        } catch {
            DispatchQueue.main.async {
                self.connectionError = "Connection failed: \(error.localizedDescription)"
            }
        }
    }
    
    private func requestRoomCreation(userId: String) async throws -> RoomCreationResponse {
        guard let url = URL(string: "\(backendBaseURL)/api/livekit/create-room") else {
            throw LiveKitError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let requestBody = RoomCreationRequest(
            userId: userId,
            sessionId: UUID().uuidString
        )
        
        request.httpBody = try JSONEncoder().encode(requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw LiveKitError.roomCreationFailed
        }
        
        return try JSONDecoder().decode(RoomCreationResponse.self, from: data)
    }
    
    private func connectToRoom(roomData: RoomCreationResponse) async throws {
        // Create room instance
        let room = Room()
        
        // Set up event handlers BEFORE connecting
        setupRoomEventHandlers(room: room)
        
        // Connect to LiveKit room
        try await room.connect(url: livekitServerURL, token: roomData.token)
        
        // Enable audio publishing
        try await enableAudioPublishing(room: room)
        
        self.room = room
        
        DispatchQueue.main.async {
            self.isConnected = true
            self.connectionError = nil
        }
    }
    
    // MARK: - Room Event Handlers
    
    private func setupRoomEventHandlers(room: Room) {
        // Handle participant connections
        room.add(delegate: self)
        
        // Handle track subscriptions (for receiving TTS audio)
        room.add(delegate: self)
    }
    
    // MARK: - Audio Publishing
    
    private func enableAudioPublishing(room: Room) async throws {
        // Create local audio track for microphone
        let audioTrack = try await LocalAudioTrack.createTrack(
            options: AudioCaptureOptions(
                sampleRate: 48000, // LiveKit standard
                channelCount: 1     // Mono
            )
        )
        
        // Publish the audio track
        try await room.localParticipant.publish(audioTrack: audioTrack)
        
        self.localAudioTrack = audioTrack
        
        print("✅ Audio track published successfully")
    }
    
    // MARK: - Recording Control
    
    func startRecording() {
        guard let audioTrack = localAudioTrack else { return }
        
        audioTrack.mute(false)
        
        DispatchQueue.main.async {
            self.isRecording = true
        }
        
        print("🎤 Recording started")
    }
    
    func stopRecording() {
        guard let audioTrack = localAudioTrack else { return }
        
        audioTrack.mute(true)
        
        DispatchQueue.main.async {
            self.isRecording = false
        }
        
        print("🛑 Recording stopped")
    }
    
    // MARK: - Cleanup
    
    func disconnect() async {
        await room?.disconnect()
        room = nil
        localAudioTrack = nil
        
        DispatchQueue.main.async {
            self.isConnected = false
            self.isRecording = false
        }
        
        print("📱 Disconnected from LiveKit room")
    }
}

// MARK: - RoomDelegate

extension LiveKitService: RoomDelegate {
    func room(_ room: Room, didConnect isReconnect: Bool) {
        print("✅ Connected to LiveKit room (reconnect: \(isReconnect))")
    }
    
    func room(_ room: Room, didDisconnect error: Error?) {
        if let error = error {
            print("❌ Disconnected with error: \(error)")
            DispatchQueue.main.async {
                self.connectionError = "Disconnected: \(error.localizedDescription)"
            }
        } else {
            print("📱 Disconnected from room")
        }
        
        DispatchQueue.main.async {
            self.isConnected = false
            self.isRecording = false
        }
    }
    
    func room(_ room: Room, participant: RemoteParticipant, didSubscribe publication: RemoteTrackPublication, track: Track) {
        print("🔊 Subscribed to track: \(track.kind) from \(participant.identity)")
        
        // Handle TTS audio from backend AI participant
        if track.kind == .audio {
            // Audio will automatically play through device speakers
            print("🎵 Receiving TTS audio from backend")
        }
    }
    
    func room(_ room: Room, participant: RemoteParticipant, didUnsubscribe publication: RemoteTrackPublication, track: Track) {
        print("🔇 Unsubscribed from track: \(track.kind) from \(participant.identity)")
    }
    
    func room(_ room: Room, didUpdate connectionQuality: ConnectionQuality, for participant: Participant) {
        print("📶 Connection quality for \(participant.identity): \(connectionQuality)")
    }
}

// MARK: - Data Models

struct RoomCreationRequest: Codable {
    let userId: String
    let sessionId: String
}

struct RoomCreationResponse: Codable {
    let success: Bool
    let roomName: String
    let token: String
    let wsUrl: String
    let callSid: String
    let sessionId: String
}

enum LiveKitError: Error {
    case invalidURL
    case roomCreationFailed
    case connectionFailed
    
    var localizedDescription: String {
        switch self {
        case .invalidURL:
            return "Invalid backend URL"
        case .roomCreationFailed:
            return "Failed to create room"
        case .connectionFailed:
            return "Failed to connect to LiveKit"
        }
    }
}
```

### 2. SwiftUI View Implementation

Create `VoiceCallView.swift`:

```swift
import SwiftUI

struct VoiceCallView: View {
    @StateObject private var liveKitService = LiveKitService()
    @State private var userId = "ios_user_\(UUID().uuidString.prefix(8))"
    @State private var showingAlert = false
    
    var body: some View {
        VStack(spacing: 30) {
            // Connection Status
            VStack {
                Text("LiveKit Voice Call")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("User ID: \(userId)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                connectionStatusView
            }
            
            Spacer()
            
            // Recording Controls
            if liveKitService.isConnected {
                recordingControls
            } else {
                connectButton
            }
            
            Spacer()
            
            // Disconnect Button
            if liveKitService.isConnected {
                disconnectButton
            }
        }
        .padding()
        .alert("Connection Error", isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(liveKitService.connectionError ?? "Unknown error")
        }
        .onChange(of: liveKitService.connectionError) { error in
            showingAlert = error != nil
        }
    }
    
    private var connectionStatusView: some View {
        HStack {
            Circle()
                .fill(liveKitService.isConnected ? .green : .red)
                .frame(width: 12, height: 12)
            
            Text(liveKitService.isConnected ? "Connected" : "Disconnected")
                .font(.subheadline)
        }
    }
    
    private var connectButton: some View {
        Button(action: {
            Task {
                await liveKitService.createRoomAndConnect(userId: userId)
            }
        }) {
            Text("Connect to Voice Call")
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .cornerRadius(10)
        }
    }
    
    private var recordingControls: some View {
        VStack(spacing: 20) {
            // Recording Status
            Text(liveKitService.isRecording ? "🎤 Recording..." : "🔇 Tap to speak")
                .font(.headline)
                .foregroundColor(liveKitService.isRecording ? .red : .secondary)
            
            // Push-to-Talk Button
            Button(action: {}) {
                Circle()
                    .fill(liveKitService.isRecording ? Color.red : Color.gray)
                    .frame(width: 120, height: 120)
                    .overlay(
                        Image(systemName: "mic.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.white)
                    )
                    .scaleEffect(liveKitService.isRecording ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 0.1), value: liveKitService.isRecording)
            }
            .onLongPressGesture(
                minimumDuration: 0,
                maximumDistance: .infinity,
                pressing: { pressing in
                    if pressing {
                        liveKitService.startRecording()
                    } else {
                        liveKitService.stopRecording()
                    }
                },
                perform: {}
            )
            
            Text("Hold to speak")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private var disconnectButton: some View {
        Button(action: {
            Task {
                await liveKitService.disconnect()
            }
        }) {
            Text("Disconnect")
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.red)
                .cornerRadius(10)
        }
    }
}

#Preview {
    VoiceCallView()
}
```

### 3. App Integration

Update your `App.swift`:

```swift
import SwiftUI

@main
struct YourApp: App {
    var body: some Scene {
        WindowGroup {
            VoiceCallView()
        }
    }
}
```

## Configuration

### 1. Backend URL Configuration

Update the URLs in `LiveKitService.swift` to match your environment:

```swift
// Update these for your environment
private let backendBaseURL = "http://YOUR_BACKEND_IP:5000"
private let livekitServerURL = "wss://YOUR_LIVEKIT_SERVER_IP"
```

### 2. Audio Quality Settings

The implementation uses optimal settings that match our backend:

- **Sample Rate**: 48kHz (LiveKit standard, converted to 8kHz by backend)
- **Channels**: Mono (1 channel)
- **Audio Session**: Voice chat mode with speaker output
- **Latency**: Interactive mode for lowest latency

## Usage Flow

1. **App Launch**: User opens the voice call view
2. **Connect**: Tap "Connect to Voice Call" button
3. **Room Creation**: App requests room creation from backend API
4. **LiveKit Connection**: App connects to LiveKit room using provided token
5. **Audio Setup**: App publishes microphone track and subscribes to backend audio
6. **Voice Interaction**: 
   - Hold microphone button to speak
   - Release to stop recording
   - Backend processes speech and responds with TTS audio
7. **Disconnect**: Tap "Disconnect" to end the call

## Backend Integration Points

This iOS implementation works with these backend endpoints:

### Room Creation API
```
POST /api/livekit/create-room
Content-Type: application/json

{
  "userId": "ios_user_12345678",
  "sessionId": "uuid-session-id"
}
```

**Response:**
```json
{
  "success": true,
  "roomName": "room_ios_user_12345678_timestamp",
  "token": "jwt-token-for-room-access",
  "wsUrl": "wss://*************",
  "callSid": "room_ios_user_12345678_timestamp",
  "sessionId": "uuid-session-id"
}
```

### Audio Processing Pipeline

1. **iOS Microphone** → LiveKit Room → Backend AI Participant
2. **Backend VAD/STT** → XState Machine → RAG/LLM Processing
3. **Backend TTS** → LiveKit Room → iOS Speaker

## Troubleshooting

### Common Issues

1. **Connection Failed**: Check backend URL and ensure server is running
2. **No Audio**: Verify microphone permissions and audio session setup
3. **Poor Quality**: Check network connection and LiveKit server status
4. **Backend Not Responding**: Ensure backend LiveKit integration is running

### Debug Logging

Enable detailed logging by adding to `LiveKitService.swift`:

```swift
// Add to init()
Room.loggingLevel = .debug
```

### Network Requirements

- **Backend API**: HTTP/HTTPS access to your backend server
- **LiveKit Server**: WebSocket access to LiveKit server
- **Firewall**: Ensure ports are open for WebRTC traffic

## Testing

### 1. Basic Connection Test

1. Start your backend server
2. Launch iOS app
3. Tap "Connect to Voice Call"
4. Verify connection status shows "Connected"

### 2. Audio Pipeline Test

1. Connect to room
2. Hold microphone button and speak
3. Verify backend logs show audio processing
4. Listen for TTS response through device speakers

### 3. Error Handling Test

1. Disconnect backend server
2. Try to connect from iOS
3. Verify error message is displayed
4. Reconnect backend and retry

## Production Considerations

### Security
- Use HTTPS for backend API calls
- Implement proper authentication
- Validate JWT tokens on backend

### Performance
- Monitor connection quality
- Implement reconnection logic
- Handle background/foreground transitions

### User Experience
- Add loading states
- Implement proper error messages
- Consider push-to-talk vs voice activation options

This implementation provides a complete iOS client that works exactly with your LiveKit WebRTC backend integration, supporting full bidirectional audio communication through the existing VAD/STT/XState/TTS pipeline. 