{"permissions": {"allow": ["Bash(grep:*)", "Bash(find:*)", "Bash(rg:*)", "Bash(npm run build:*)", "Bash(timeout 10s npm start:*)", "Bash(npx tsc:*)", "Bash(node:*)", "Bash(ls:*)", "WebFetch(domain:docs.livekit.io)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm install:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "Bash(npm run typecheck:*)", "Bash(npm run test:*)", "Bash(npm run lint:*)", "Bash(awk:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(journalctl:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(npm run dev:*)", "Bash(ts-node:*)", "Bash(npm run check-types:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(pm2 restart:*)", "Bash(./node_modules/.bin/tsc:*)", "WebFetch(domain:github.com)", "WebFetch(domain:www.npmjs.com)", "Bash(npm update:*)", "WebFetch(domain:developers.deepgram.com)", "<PERSON><PERSON>(python3:*)", "Bash(pip3 list:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(python:*)", "Bash(kill:*)", "Bash(ss:*)", "Bash(rm:*)", "Bash(npm uninstall:*)", "WebFetch(domain:rochars.github.io)", "Bash(git ls-tree:*)", "Bash(git checkout:*)", "Bash(git stash:*)", "WebFetch(domain:stately.ai)", "<PERSON><PERSON>(env)", "WebFetch(domain:crates.io)", "WebFetch(domain:docs.rs)", "<PERSON><PERSON>(wget:*)", "Bash(cargo:*)", "Bash(./target/release/vad_worker:*)", "Bash(cp:*)", "Bash(git rev-parse:*)", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(cat:*)", "WebFetch(domain:ai-sdk.dev)", "Bash(npx ts-node:*)", "<PERSON><PERSON>(timeout:*)", "Bash(time npx ts-node test_latency.ts)", "Bash(npx tsx:*)", "WebFetch(domain:stackoverflow.com)", "WebFetch(domain:v5.ai-sdk.dev)", "Bash(npx:*)", "Bash(npm view:*)", "WebFetch(domain:supabase.com)", "WebFetch(domain:coolify.io)"]}, "enableAllProjectMcpServers": false}