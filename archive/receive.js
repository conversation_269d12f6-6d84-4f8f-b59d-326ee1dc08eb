const express = require('express');
const bodyParser = require('body-parser');

const app = express();
app.use(bodyParser.json());

// Middleware to log every request
app.use((req, res, next) => {
    console.log(`${req.method} ${req.url}`);
    console.log('Headers:', JSON.stringify(req.headers, null, 2));
    console.log('Body:', JSON.stringify(req.body, null, 2));
    next();
});

// Main webhook endpoint
app.post('/webhook', (req, res) => {
    const eventType = req.body.event_type;
    switch (eventType) {
        case 'call.initiated':
            console.log(`Call initiated from ${req.body.from}`);
            break;
        case 'call.answered':
            console.log(`Call answered from ${req.body.from}`);
            break;
        case 'call.completed':
            console.log(`Call completed from ${req.body.from}`);
            break;
        // Add more cases as needed
        default:
            console.log(`Unhandled event type: ${eventType}`);
    }
    res.status(200).send('Received');
});

// Handle other routes
app.use((req, res) => {
    res.status(404).send('Not Found');
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).send('Something broke!');
});

const port = 5000;
app.listen(port, () => console.log(`App running on port ${port}`));
