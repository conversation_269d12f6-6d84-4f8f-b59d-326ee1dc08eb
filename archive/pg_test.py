import psycopg2
from psycopg2 import sql
from langchain_community.chat_message_histories import PostgresChatMessageHistory
import uuid

# Define the connection string
conn_string = "postgresql://pyagent:Andromeda2011!@localhost/chat_history"

try:
    # Test connection
    conn = psycopg2.connect(conn_string)
    print("Connection successful!")
    conn.close()
except Exception as e:
    print(f"Connection failed: {e}")

# Generate a unique session ID
session_id = str(uuid.uuid4())
print(f"Session ID: {session_id}")

# Initialize PostgresChatMessageHistory
history = PostgresChatMessageHistory(
    connection_string=conn_string,
    session_id=session_id,
    table_name="chat_history"  # Specify the table name explicitly
)

# Add messages to the chat history
print("Adding messages...")
history.add_user_message("hi!")
history.add_ai_message("what's up?")
print("Messages added.")

# Function to view the entire chat_history table
def view_chat_history_table():
    try:
        # Establish connection using psycopg2
        conn = psycopg2.connect(conn_string)
        cursor = conn.cursor()

        # SQL query to select all records from chat_history table
        select_query = "SELECT * FROM chat_history"

        # Execute the query
        cursor.execute(select_query)

        # Fetch all rows from the table
        rows = cursor.fetchall()

        # Check if any rows were retrieved
        if not rows:
            print("No messages found in the chat history table.")
        else:
            # Print the rows
            print("\nChat History Table Contents:")
            for row in rows:
                print(row)

        # Close the cursor and connection
        cursor.close()
        conn.close()

    except Exception as e:
        print(f"An error occurred while retrieving the chat history table: {e}")

# Call the function to view the table
view_chat_history_table()

# Retrieve and print messages using the history object
print("\nRetrieved messages:")
for message in history.messages:
    print(f"{message.type}: {message.content}")