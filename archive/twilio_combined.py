from flask import Flask, Response, request
from flask_sockets import Sockets
import json
import base64
import logging
from datetime import datetime
import socket
import sys

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

HTTP_SERVER_PORT = 5000

app = Flask(__name__)
sockets = Sockets(app)

# Store active calls
active_calls = {}

@app.route('/voice', methods=['POST'])  # Changed from /twiml to /voice
def voice():
    """Initial TwiML response that starts the stream"""
    call_sid = request.values.get('CallSid')
    logger.info(f"Initial voice request for call {call_sid}")
    
    active_calls[call_sid] = {
        'start_time': datetime.now(),
        'status': 'starting'
    }

    # Log all request details for debugging
    logger.debug(f"Request values: {request.values}")
    logger.debug(f"Request headers: {dict(request.headers)}")

    # Always use wss:// for Twilio Media Streams as they require secure WebSocket
    twiml = f"""<?xml version="1.0" encoding="UTF-8"?>
    <Response>
        <Start>
            <Stream url="wss://{request.host}/stream"/>
        </Start>
        <Redirect method="POST">/continue-call</Redirect>
    </Response>
    """
    logger.info(f"Sending TwiML response: {twiml}")
    return Response(twiml, mimetype='text/xml')

@app.route('/continue-call', methods=['POST'])
def continue_call():
    """Keeps the call alive by continuously redirecting"""
    call_sid = request.values.get('CallSid')
    logger.info(f"Continuing call {call_sid}")
    
    if call_sid in active_calls:
        if active_calls[call_sid].get('status') == 'finished':
            twiml = """<?xml version="1.0" encoding="UTF-8"?>
            <Response>
                <Hangup/>
            </Response>
            """
        else:
            twiml = """<?xml version="1.0" encoding="UTF-8"?>
            <Response>
                <Gather timeout="10" action="/continue-call" method="POST"/>
                <Redirect method="POST">/continue-call</Redirect>
            </Response>
            """
    else:
        twiml = """<?xml version="1.0" encoding="UTF-8"?>
        <Response>
            <Hangup/>
        </Response>
        """
    
    return Response(twiml, mimetype='text/xml')

@sockets.route('/stream')
def stream_socket(ws):
    logger.info("WebSocket connection accepted")
    count = 0
    has_seen_media = False
    call_sid = None

    while not ws.closed:
        message = ws.receive()
        if message is None:
            logger.warning("No message received...")
            continue

        try:
            data = json.loads(message)
            event_type = data.get('event')
            
            if event_type == "connected":
                logger.info("Connected Message received: %s", message)
            
            elif event_type == "start":
                logger.info("Start Message received: %s", message)
                call_sid = data.get('streamSid', '').split('.')[0]
                if call_sid in active_calls:
                    active_calls[call_sid]['status'] = 'streaming'
                    # Send acknowledgment
                    ws.send(json.dumps({
                        "event": "start_ack",
                        "call_sid": call_sid
                    }))
            
            elif event_type == "media":
                if not has_seen_media:
                    logger.info("First Media message received")
                    has_seen_media = True
                if 'media' in data:
                    media_data = data['media']['payload']
                    # Process audio data here
                    try:
                        # Decode base64 audio data
                        audio_bytes = base64.b64decode(media_data)
                        logger.debug(f"Received audio chunk: {len(audio_bytes)} bytes")
                    except Exception as e:
                        logger.error(f"Error processing audio data: {e}")
            
            elif event_type == "closed":
                logger.info("Closed Message received: %s", message)
                if call_sid in active_calls:
                    active_calls[call_sid]['status'] = 'finished'
                break
            
            count += 1

        except json.JSONDecodeError as e:
            logger.error("Failed to parse message: %s", e)
        except Exception as e:
            logger.error("Error processing message: %s", e)

    logger.info("Connection closed. Received %d messages", count)
    if call_sid in active_calls:
        active_calls[call_sid]['status'] = 'finished'

@app.route('/status')
def get_status():
    """Endpoint to check current calls status"""
    return {
        'active_calls': len(active_calls),
        'calls': {
            sid: {
                'status': data['status'],
                'duration': str(datetime.now() - data['start_time'])
            }
            for sid, data in active_calls.items()
        }
    }

def find_available_port(start_port, max_attempts=10):
    """Find an available port starting from start_port"""
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    return None

if __name__ == '__main__':
    from gevent import pywsgi
    from geventwebsocket.handler import WebSocketHandler
    
    # Try to find an available port
    port = find_available_port(HTTP_SERVER_PORT)
    if port is None:
        logger.error("Could not find an available port. Exiting.")
        sys.exit(1)
    
    logger.info(f"Starting server on port {port}")
    try:
        server = pywsgi.WSGIServer(
            ('', port),
            app,
            handler_class=WebSocketHandler
        )
        server.serve_forever()
    except OSError as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
        sys.exit(0)
