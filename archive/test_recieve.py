from flask import Flask, request, jsonify
import telnyx

app = Flask(__name__)

telnyx.api_key = "**********************************************************"

@app.route('/webhook', methods=['POST'])
def webhook():
    data = request.json
    print(data)  # For debugging purposes, print the data

    if data and data.get('data') and data['data'].get('event_type') == 'call.initiated':
        call_control_id = data['data']['payload']['call_control_id']

        # Answer the call
        response = telnyx.Call.answer(call_control_id)
        print(response)

    # Process the data as needed
    return jsonify({"status": "received"}), 200

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)