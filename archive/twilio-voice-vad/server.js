/*******************************************************************
 * server.js
 *
 * Extends your Twilio => Node => Deepgram pipeline to also record
 * the incoming audio as a WAV for diagnostic logging.
 * Now uses "CloseStream" + manual ws.close() instead of .close().
 *******************************************************************/

const express = require("express");
const bodyParser = require("body-parser");
const { VoiceResponse } = require("twilio").twiml;
const http = require("http");
const WebSocket = require("ws");
const { spawn } = require("child_process");

// ---- Deepgram v3 (example) ----
const { createClient, LiveTranscriptionEvents } = require("@deepgram/sdk");
const DEEPGRAM_API_KEY = "****************************************";
const deepgram = createClient(DEEPGRAM_API_KEY);

const app = express();
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

// Track active Deepgram connections & ffmpeg processes by callSid
const deepgramConnections = {};
const ffmpegProcesses = {}; 

// ------------------
// 1) Twilio -> /voice
// ------------------
app.post("/voice", (req, res) => {
  const twiml = new VoiceResponse();

  // (A) Start Media Stream -> points Twilio to wss://<host>/media
  twiml
    .start({ action: "/voiceStatusCallback" })
    .stream({
      url: `wss://${req.headers.host}/media`,
      track: "inbound_track",
    });

  // (B) Greet user
  twiml.say(
    "Hello! We are streaming your audio to Deepgram, and also recording a diagnostic WAV file."
  );

  // (C) Keep call open with a long pause
  twiml.pause({ length: 3600 });

  res.type("text/xml");
  res.send(twiml.toString());
});

// Optional <Start> callback
app.post("/voiceStatusCallback", (req, res) => {
  console.log("Media stream started. Call SID:", req.body.CallSid);
  res.sendStatus(200);
});

// ------------------
// 2) WebSocket /media
// ------------------
const server = http.createServer(app);
const wss = new WebSocket.Server({ server, path: "/media" });

wss.on("connection", (ws) => {
  console.log("Twilio connected to /media WebSocket.");

  let callSid = null;

  // Deepgram Live connection
  let dgConnection = null;
  // ffmpeg process
  let ffmpegProcess = null;

  ws.on("message", async (msg) => {
    let data;
    try {
      data = JSON.parse(msg);
    } catch (err) {
      console.error("Failed to parse WS message:", err);
      return;
    }

    switch (data.event) {
      case "connected":
        console.log("Twilio event: connected");
        break;

      case "start":
        callSid = data.start.callSid;
        console.log("Media start event for callSid:", callSid);

        // ---------- 1) Start ffmpeg recording ----------
        try {
          const outPath = `./recordings/${callSid}.wav`;
          ffmpegProcess = spawn("ffmpeg", [
            "-f",
            "mulaw",
            "-ar",
            "8000",
            "-i",
            "pipe:0",
            "-ac",
            "1",
            "-ar",
            "8000",
            "-f",
            "wav",
            outPath,
          ]);

          ffmpegProcess.on("close", (code) => {
            console.log(`ffmpeg for ${callSid} closed with code ${code}`);
          });
          ffmpegProcess.on("error", (err) => {
            console.error(`ffmpeg error for ${callSid}:`, err);
          });

          ffmpegProcesses[callSid] = ffmpegProcess;
        } catch (err) {
          console.error("Error launching ffmpeg:", err);
        }

        // ---------- 2) Deepgram Live Transcription ----------
        try {
          dgConnection = deepgram.listen.live({
            encoding: "mulaw",
            sample_rate: 8000,
            model: "nova-2",
            interim_results: true,
            punctuate: true,
            smart_format: true,
            vad: true,
          });

          dgConnection.on(LiveTranscriptionEvents.Open, () => {
            console.log("Deepgram socket opened for callSid:", callSid);
          });

          dgConnection.on(LiveTranscriptionEvents.Transcript, (dgData) => {
            if (!dgData.channel?.alternatives) return;
            const alt = dgData.channel.alternatives[0];
            if (!alt.transcript) return;

            if (dgData.is_final) {
              console.log(`[Deepgram FINAL] ${alt.transcript}`);
            } else {
              console.log(`[Deepgram PARTIAL] ${alt.transcript}`);
            }
          });

          dgConnection.on(LiveTranscriptionEvents.Close, () => {
            console.log(`Deepgram socket closed for callSid: ${callSid}`);
          });

          dgConnection.on(LiveTranscriptionEvents.Error, (err) => {
            console.error("Deepgram Error:", err);
          });

          // Store the connection object for cleanup
          deepgramConnections[callSid] = dgConnection;
        } catch (err) {
          console.error("Error creating Deepgram live connection:", err);
        }
        break;

      case "media": {
        // Raw audio from Twilio (base64)
        if (!callSid) break;
        const audioBuffer = Buffer.from(data.media.payload, "base64");

        // 1) Write chunk to ffmpeg
        if (ffmpegProcess) {
          ffmpegProcess.stdin.write(audioBuffer);
        }
        // 2) Forward chunk to Deepgram
        if (dgConnection) {
          dgConnection.send(audioBuffer);
        }
        break;
      }

      case "stop":
        console.log("Twilio media stop event for callSid:", callSid);

        // --- Close ffmpeg gracefully ---
        if (ffmpegProcess) {
          ffmpegProcess.stdin.end();
          ffmpegProcess = null;
        }

        // --- Gracefully finalize Deepgram ---
        if (deepgramConnections[callSid]) {
          // 1) Ask Deepgram to finalize and close
          deepgramConnections[callSid].send(
            JSON.stringify({ type: "CloseStream" })
          );

          // 2) Give it a moment, then close the underlying socket
          setTimeout(() => {
            if (deepgramConnections[callSid]?.ws?.close) {
              deepgramConnections[callSid].ws.close();
            }
            delete deepgramConnections[callSid];
          }, 500);
        }
        break;

      default:
        console.log("Unknown Twilio event:", data.event);
    }
  });

  // 3) If user hangs up abruptly => WebSocket 'close'
  ws.on("close", () => {
    console.log("Twilio WebSocket disconnected.");
    if (callSid) {
      // Close ffmpeg
      if (ffmpegProcesses[callSid]) {
        ffmpegProcesses[callSid].stdin.end();
        ffmpegProcesses[callSid] = null;
      }

      // Graceful close Deepgram
      if (deepgramConnections[callSid]) {
        deepgramConnections[callSid].send(JSON.stringify({ type: "CloseStream" }));
        setTimeout(() => {
          if (deepgramConnections[callSid]?.ws?.close) {
            deepgramConnections[callSid].ws.close();
          }
          delete deepgramConnections[callSid];
        }, 500);
      }
    }
  });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
