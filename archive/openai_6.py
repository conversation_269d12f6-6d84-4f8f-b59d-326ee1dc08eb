import os
import re
import time
from dotenv import load_dotenv
from openai import OpenAI
from elasticsearch import Elasticsearch
from langchain_community.vectorstores import ElasticsearchStore
from langchain_openai import OpenAIEmbeddings
from langchain_community.document_loaders import DirectoryLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter

load_dotenv()

# Set the API key
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

# Initialize the client
client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))

# Check and establish connection to Elasticsearch
def check_elasticsearch():
    try:
        es_connection = Elasticsearch(
            "http://localhost:9200",
            basic_auth=("elastic", "changeme"),  # Update credentials as necessary
            verify_certs=False
        )
        if es_connection.ping():
            print("Successfully connected to Elasticsearch")
            return es_connection
        else:
            print("Elasticsearch is not responding.")
            return None
    except Exception as e:
        print(f"Error connecting to Elasticsearch: {e}")
        return None

# Document preparation
def prepare_documents(es_connection):
    start_time = time.perf_counter()
    index_name = "energyplus_docs"
    
    if not es_connection.indices.exists(index=index_name):
        loader = DirectoryLoader('RAG', glob="**/*.txt")
        documents = loader.load()
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=0)
        texts = text_splitter.split_documents(documents)
        
        vectorstore = ElasticsearchStore(
            embedding=OpenAIEmbeddings(),
            index_name=index_name,
            es_connection=es_connection
        )
        vectorstore.add_documents(texts)
        print("Vector store created and saved in Elasticsearch.")
    else:
        print("Vector store already exists in Elasticsearch.")
    
    end_time = time.perf_counter()
    print(f"Document preparation time: {end_time - start_time:.4f} seconds")

# Expand query using OpenAI
def expand_query(query):
    start_time = time.perf_counter()
    expansion_prompt = f"""Given the customer query: '{query}', generate 3 related questions that would help retrieve relevant information about EnergyPlus Utilities' services, rates, and policies. Format the output as a comma-separated list."""
    
    expansion_message = client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[
            {"role": "system", "content": "You are a helpful assistant that generates related questions for customer queries."},
            {"role": "user", "content": expansion_prompt}
        ],
        max_tokens=300,
        temperature=0
    )
    
    expanded_queries = [q.strip() for q in expansion_message.choices[0].message.content.split(',')]
    end_time = time.perf_counter()
    print(f"Query expansion time: {end_time - start_time:.4f} seconds")
    return expanded_queries

# Retrieve and aggregate documents from Elasticsearch
def get_rag_response(query, es_connection):
    start_time = time.perf_counter()
    expanded_queries = expand_query(query)
    
    vectorstore = ElasticsearchStore(
        embedding=OpenAIEmbeddings(),
        index_name="energyplus_docs",
        es_connection=es_connection
    )
    
    all_docs = []
    for q in expanded_queries:
        docs = vectorstore.similarity_search(q, k=5)
        all_docs.extend(docs)
    
    context = "\n".join(set([doc.page_content for doc in all_docs]))
    end_time = time.perf_counter()
    print(f"Document retrieval time: {end_time - start_time:.4f} seconds")
    return context

# Main function
def main():
    es_connection = check_elasticsearch()
    if es_connection:
        prepare_documents(es_connection)
        query = "Hi, I'm interested in signing up for your electricity service. Can you tell me about your rates?"
        context = get_rag_response(query, es_connection)
        print("Context from the knowledge base:", context)
    else:
        print("Failed to connect to Elasticsearch. Check the configuration.")

if __name__ == "__main__":
    main()