import json
import os
import telnyx
from flask import Flask, request
import openai
from dotenv import load_dotenv
import time
import re
import sys
import socket
import struct
import wave
import threading
import traceback
import audioop
from collections import OrderedDict
import torch
from multiprocessing import Process, Event, Manager
from queue import Empty
from pyannote.audio import Pipeline

load_dotenv()
telnyx.api_key = os.getenv("TELNYX_API_KEY")  # Make sure to set your Telnyx API Key
openai.api_key = os.getenv("OPENAI_API_KEY")  # Set your OpenAI API key

RECORDINGS_DIR = "recordings"
os.makedirs(RECORDINGS_DIR, exist_ok=True)

app = Flask(__name__)

# Define the IP and port you want to listen on for RTP
UDP_PORT = 5004
UDP_IP = "0.0.0.0"  # Listen on all available interfaces
UDP_TRANSMIT = "127.0.0.1"  # Use localhost if processes are on the same machine

assistant_id = 'asst_dLuE0A9zTpOYkz9m85NWz14x'  # Replace with your assistant ID

# Dictionary to store call handlers
call_handlers = {}
# Set to track processed event IDs
processed_event_ids = set()

# Create a global manager for shared resources
manager = Manager()


class CallHandler:
    def __init__(self, call_control_id):
        self.call_control_id = call_control_id
        self.thread = None
        self.transcript_buffer = ""
        self.call_leg_id = None
        self.wav_filename = None
        self.rtp_process = None
        self.is_receiving = manager.Value('b', True)  # Shared boolean between processes
        self.sample_width = 2  # 16-bit audio
        self.channels = 1  # Mono
        self.sample_rate = 8000  # G.711 sample rate
        self.lock = threading.Lock()
        self.audio_queue = manager.Queue()
        self.vad_process = None
        self.processing_voice = Event()
        self.voice_event_queue = manager.Queue()  # Queue for VAD to communicate with main process
        # Queues for LLM and TTS processes
        self.llm_response_queue = manager.Queue()
        self.tts_queue = manager.Queue()
        self.llm_process = None
        self.tts_process = None
        print(f"CallHandler created for call_control_id: {call_control_id}")

    def handle_answered(self):
        print('handle_answered func triggered')
        try:
            print('trying handle_answered')
            # Log the current call control ID
            print(f"Handling answered call for call_control_id: {self.call_control_id}")
            # Create the Telnyx call object
            call = telnyx.Call()
            call.call_control_id = self.call_control_id
            # Start transcription
            print("Starting transcription for call.")
            call.transcription_start(language="en", interim_results=True)
            # Assign a unique UDP port for this call
            self.udp_port = UDP_PORT
            print(f"Assigned UDP port {self.udp_port} for call {self.call_control_id}")
            # Start media forking
            call.fork_start(
                target=f"udp:{UDP_TRANSMIT}:{self.udp_port}",
                rx=f"udp:{UDP_TRANSMIT}:{self.udp_port}",
                tx=f"udp:{UDP_TRANSMIT}:{self.udp_port+1}",
            )

            # Create a new OpenAI thread
            print("Creating thread for call.")
            self.thread = openai.Thread.create()
            print(f"Thread creation response: {self.thread}")
            if not self.thread:
                raise Exception("Thread creation failed. No thread object returned.")
            print(f"Thread created successfully for call {self.call_control_id}: {self.thread['id']}")
        except Exception as e:
            print(f"Error in handle_answered for call {self.call_control_id}: {e}")
            traceback.print_exc()

    def handle_transcription(self, transcript):
        with self.lock:
            print(f"Transcribed Text for call {self.call_control_id}: {transcript}")
            self.transcript_buffer += " " + transcript

    def handle_dtmf(self, digit):
        if digit == "#":
            print(f'---------# PRESSED for call {self.call_control_id}--------------------')
            self.on_voice_detected()
        elif digit == "*":
            self.hangup()

    def on_voice_detected(self):
        if not self.processing_voice.is_set():
            self.processing_voice.set()
            threading.Thread(target=self.process_voice_activity).start()

    def process_voice_activity(self):
        try:
            with self.lock:
                transcript = self.transcript_buffer.strip() + " "
                self.transcript_buffer = ""
            if not transcript.strip():
                print(f"No transcript available for call {self.call_control_id}")
                self.processing_voice.clear()
                return

            # Start the LLM process
            self.llm_process = Process(target=self.send_to_llm_process, args=(transcript,))
            self.llm_process.start()
            self.llm_process.join()

            # Get the response from the LLM
            response = self.llm_response_queue.get()

            # Start the TTS process
            self.tts_process = Process(target=self.play_tts_process, args=(response,))
            self.tts_process.start()
            self.tts_process.join()

        except Exception as e:
            print(f"Error in process_voice_activity for call {self.call_control_id}: {e}")
            traceback.print_exc()
        finally:
            self.processing_voice.clear()

    def send_to_llm_process(self, transcript):
        # Implement your LLM logic here
        # For example, using OpenAI API to get a response
        try:
            print(f"Sending to LLM for call {self.call_control_id}, Thread ID: {self.thread['id']}")
            response = openai.Completion.create(
                engine="davinci",
                prompt=transcript,
                max_tokens=150
            )
            last_assistant_text = response.choices[0].text.strip()
            print(f"LLM response for call {self.call_control_id}: {last_assistant_text}")
            # Put the response into the queue
            self.llm_response_queue.put(last_assistant_text)
        except Exception as e:
            print(f"Error in send_to_llm_process for call {self.call_control_id}: {e}")
            traceback.print_exc()

    def play_tts_process(self, response_text):
        # Implement your TTS playback logic here
        try:
            print(f"Playing TTS for call {self.call_control_id}")
            call = telnyx.Call()
            call.call_control_id = self.call_control_id
            # Play TTS asynchronously
            call.speak(
                payload=response_text,
                language="en-US",
                voice="Polly.Amy"
            )
        except Exception as e:
            print(f"Error in play_tts_process for call {self.call_control_id}: {e}")
            traceback.print_exc()

    def hangup(self):
        # Call hangup on the Telnyx call
        call = telnyx.Call()
        call.call_control_id = self.call_control_id
        call.hangup()
        # Stop the RTP listener and VAD process
        self.stop_processes()
        # Remove this call handler from the dictionary
        if self.call_control_id in call_handlers:
            del call_handlers[self.call_control_id]

    def start_rtp_listener(self, call_leg_id):
        self.call_leg_id = call_leg_id
        try:
            # Create filename with full path
            self.wav_filename = os.path.join(RECORDINGS_DIR, f'output_{call_leg_id}.wav')
            print(f"Attempting to create WAV file: {self.wav_filename}")
            # Start RTP listener in a separate process
            self.rtp_process = Process(target=receive_rtp_process, args=(
                UDP_IP, self.udp_port, self.wav_filename, self.is_receiving,
                self.sample_width, self.channels, self.sample_rate,
                self.call_control_id, self.audio_queue
            ))
            self.rtp_process.start()
            print(f"RTP listener process started for call_leg_id: {call_leg_id}")
            # Start VAD process
            print("Starting VAD process.")
            self.vad_process = Process(target=vad_process, args=(
                self.is_receiving, self.sample_rate, self.sample_width,
                self.audio_queue, self.voice_event_queue, self.call_control_id
            ))
            self.vad_process.start()
            print("VAD process started.")
            # Start a thread to monitor voice events
            threading.Thread(target=self.monitor_voice_events, daemon=True).start()
        except Exception as e:
            print(f"Error starting RTP listener: {e}")
            traceback.print_exc()

    def stop_processes(self):
        self.is_receiving.value = False
        # Terminate RTP process
        if self.rtp_process and self.rtp_process.is_alive():
            self.rtp_process.join(timeout=5)
            if self.rtp_process.is_alive():
                self.rtp_process.terminate()
        # Terminate VAD process
        if self.vad_process and self.vad_process.is_alive():
            self.vad_process.join(timeout=5)
            if self.vad_process.is_alive():
                self.vad_process.terminate()
        # Terminate LLM process
        if self.llm_process and self.llm_process.is_alive():
            self.llm_process.terminate()
        # Terminate TTS process
        if self.tts_process and self.tts_process.is_alive():
            self.tts_process.terminate()
        print(f"Processes stopped for call_leg_id: {self.call_leg_id}")

    def monitor_voice_events(self):
        while self.is_receiving.value:
            try:
                event = self.voice_event_queue.get(timeout=0.5)
                if event == "voice_detected":
                    print(f"Voice event detected for call {self.call_control_id}")
                    self.on_voice_detected()
            except Empty:
                continue


def vad_process(is_receiving, sample_rate, sample_width, audio_queue, voice_event_queue, call_control_id):
    try:
        # Load the pretrained Voice Activity Detection pipeline
        vad_pipeline = Pipeline.from_pretrained(
            "pyannote/voice-activity-detection",
            use_auth_token=os.getenv("HUGGINGFACE_TOKEN")
        )
        # Adjust VAD parameters to reduce false positives
        vad_pipeline.threshold = 0.7  # Increase threshold to be less sensitive
        audio_buffer = b''
        MIN_AUDIO_DURATION = 1.0  # seconds
        last_voice_detection = time.time()
        VOICE_TIMEOUT = 2.0  # seconds between voice detections
        while is_receiving.value:
            try:
                pcm_data = audio_queue.get(timeout=0.1)
                audio_buffer += pcm_data
                num_samples = len(audio_buffer) // sample_width
                duration = num_samples / sample_rate
                if duration >= MIN_AUDIO_DURATION:
                    try:
                        # Convert audio buffer to tensor
                        waveform = torch.frombuffer(audio_buffer, dtype=torch.int16).float()
                        waveform /= 32768  # Normalize to [-1, 1]
                        waveform = waveform.unsqueeze(0)
                        # Create a dict as expected by pyannote.audio
                        audio = {'waveform': waveform, 'sample_rate': sample_rate}
                        # Perform VAD with error handling
                        try:
                            vad_scores = vad_pipeline(audio)
                            speech_regions = vad_scores.get_timeline().support()
                            current_time = time.time()
                            if speech_regions and (current_time - last_voice_detection) >= VOICE_TIMEOUT:
                                print(f"Voice detected in call {call_control_id}")
                                voice_event_queue.put("voice_detected")
                                last_voice_detection = current_time
                            else:
                                print(f"No speech regions detected in call {call_control_id}")
                        except Exception as e:
                            print(f"VAD pipeline error in call {call_control_id}: {e}")
                            continue
                        # Keep a rolling buffer
                        max_buffer_size = int(sample_rate * sample_width * MIN_AUDIO_DURATION)
                        audio_buffer = audio_buffer[-max_buffer_size:]
                    except Exception as e:
                        print(f"Error processing audio chunk in call {call_control_id}: {e}")
                        audio_buffer = b''  # Reset buffer on error
                        continue
                time.sleep(0.01)
            except Empty:
                continue
            except Exception as e:
                print(f"Error processing audio data in VAD for call {call_control_id}: {e}")
                traceback.print_exc()
                time.sleep(0.1)
    except Exception as e:
        print(f"Fatal error in VAD process for call {call_control_id}: {e}")
        traceback.print_exc()
    finally:
        print(f"VAD process ending for call {call_control_id}")


def receive_rtp_process(
    udp_ip, udp_port, wav_filename, is_receiving,
    sample_width, channels, sample_rate, call_control_id, audio_queue
):
    packet_buffer = OrderedDict()
    expected_seq = None
    packet_count = 0

    try:
        # Open WAV file within the process
        with wave.open(wav_filename, 'wb') as wav_file:
            wav_file.setnchannels(channels)
            wav_file.setsampwidth(sample_width)
            wav_file.setframerate(sample_rate)
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.bind((udp_ip, udp_port))
            sock.settimeout(1.0)  # Set a timeout for the socket
            print(f"Listening for RTP on {udp_ip}:{udp_port}")
            while is_receiving.value:
                try:
                    data, addr = sock.recvfrom(1500)
                    rtp_payload = data
                    if rtp_payload:
                        expected_seq = process_rtp_packet(
                            rtp_payload, packet_buffer, expected_seq,
                            wav_file, sample_width, call_control_id, audio_queue
                        )
                        packet_count += 1
                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"Error receiving RTP for call {call_control_id}: {e}")
                    traceback.print_exc()
            sock.close()
            wav_file.close()
    except Exception as e:
        print(f"Error in receive_rtp_process for call {call_control_id}: {e}")
        traceback.print_exc()


def process_rtp_packet(
    rtp_payload, packet_buffer, expected_seq, wav_file,
    sample_width, call_control_id, audio_queue
):
    # Parse RTP header
    if len(rtp_payload) < 12:
        print(f"RTP payload too short: {len(rtp_payload)} bytes")
        return expected_seq

    try:
        rtp_header = struct.unpack('!BBHII', rtp_payload[:12])
        payload_type = rtp_header[1] & 0x7F
        sequence_number = rtp_header[2]
        timestamp = rtp_header[3]
        # Extract audio data
        audio_data = rtp_payload[12:]
        # Determine codec
        if payload_type == 0:
            codec = 'PCMU'
        elif payload_type == 8:
            codec = 'PCMA'
        elif payload_type == 101:
            # DTMF or other control payload
            return expected_seq  # Skip processing this packet
        else:
            # Unsupported payload type
            return expected_seq
        # Buffer the packet
        packet_buffer[sequence_number] = (codec, audio_data)
        # Process buffered packets
        expected_seq = process_buffered_packets(
            packet_buffer, expected_seq, wav_file,
            sample_width, call_control_id, audio_queue
        )
    except Exception as e:
        print(f"Error processing RTP packet in call {call_control_id}: {e}")
    return expected_seq


def process_buffered_packets(
    packet_buffer, expected_seq, wav_file,
    sample_width, call_control_id, audio_queue
):
    while packet_buffer:
        if expected_seq is None:
            expected_seq = min(packet_buffer.keys())

        if expected_seq not in packet_buffer:
            break
        codec, audio_data = packet_buffer.pop(expected_seq)
        if codec == 'PCMU':
            pcm_data = audioop.ulaw2lin(audio_data, sample_width)
        elif codec == 'PCMA':
            pcm_data = audioop.alaw2lin(audio_data, sample_width)
        else:
            # Unsupported codec
            continue
        if sys.byteorder == 'big':
            pcm_data = audioop.byteswap(pcm_data, sample_width)
        try:
            if wav_file:
                wav_file.writeframes(pcm_data)
                # Put pcm_data into the queue
                audio_queue.put(pcm_data)
            else:
                print("Warning: wav_file is None")
        except Exception as e:
            print(f"Error writing to WAV file for call {call_control_id}: {e}")
            traceback.print_exc()
        expected_seq = (expected_seq + 1) & 0xFFFF
    return expected_seq


@app.route('/webhook', methods=['POST'])
def inbound():
    body = request.get_json()
    event = body.get("data").get("event_type")
    call_control_id = body.get("data").get("payload").get("call_control_id")

    print(f"Received event: {event} for call_control_id: {call_control_id}")
    try:
        if event == "call.initiated":
            handle_call_initiated(body)
        elif event == "call.answered":
            handle_call_answered(body)
        elif event == "call.transcription":
            handle_call_transcription(body)
        elif event == 'call.dtmf.received':
            handle_call_dtmf_received(body)
        elif event == 'call.hangup':
            handle_call_hangup(body)
        elif event == 'call.fork.started':
            handle_call_fork_started(body)
        elif event == 'call.fork.stopped':
            handle_call_fork_stopped(body)
    except Exception as e:
        print(f"Error: {e}")
        return json.dumps({"success": False}), 500, {"ContentType": "application/json"}
    return json.dumps({"success": True}), 200, {"ContentType": "application/json"}


def handle_call_initiated(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    call = telnyx.Call()
    call.call_control_id = call_control_id
    call.answer()
    print('call answered')


def handle_call_answered(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    if call_control_id not in call_handlers:
        print(f'Creating CallHandler for call_control_id: {call_control_id}')
        call_handlers[call_control_id] = CallHandler(call_control_id)
    call_handlers[call_control_id].handle_answered()
    print('CallHandler.handle_answered() called successfully')


def handle_call_transcription(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    transcript = body.get("data").get("payload").get("transcription_data").get("transcript")
    if call_control_id in call_handlers:
        call_handlers[call_control_id].handle_transcription(transcript)


def handle_call_dtmf_received(body):
    event_id = body.get("data").get("id")
    if event_id in processed_event_ids:
        print(f"Event {event_id} already processed.")
        return
    processed_event_ids.add(event_id)
    call_control_id = body.get("data").get("payload").get("call_control_id")
    dtmf = body.get("data").get("payload").get("digit")
    if call_control_id in call_handlers:
        call_handlers[call_control_id].handle_dtmf(dtmf)


def handle_call_hangup(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    if call_control_id in call_handlers:
        handler = call_handlers[call_control_id]
        handler.stop_processes()
        del call_handlers[call_control_id]
        print(f"Call handler for {call_control_id} cleaned up.")


def handle_call_fork_started(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    call_leg_id = body.get("data").get("payload").get('call_leg_id')
    print(f"Fork started event received. call_control_id: {call_control_id}, call_leg_id: {call_leg_id}")
    if call_control_id in call_handlers and call_leg_id:
        call_handlers[call_control_id].start_rtp_listener(call_leg_id)
    else:
        print(f"Error: CallHandler not found or call_leg_id missing for call_control_id: {call_control_id}")


def handle_call_fork_stopped(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    print(f"Fork stopped event received. call_control_id: {call_control_id}")
    # Handle fork stopped event if necessary


if __name__ == '__main__':
    app.run(debug=True, port=5000)