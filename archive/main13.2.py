import json
import os
import telnyx
from flask import Flask, request
from openai import OpenAI
from dotenv import load_dotenv
import time
import re
import socket
import struct
import wave
from pydub import AudioSegment
from io import BytesIO
import threading
import audioop

# Flask app setup
app = Flask(__name__)

# Load environment variables
load_dotenv()
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

client = OpenAI()
assistant_id = 'asst_dLuE0A9zTpOYkz9m85NWz14x'

# Dictionary to store call handlers
call_handlers = {}

# Set to track processed event IDs
processed_event_ids = set()

# Create a lock for file writing to avoid conflicts
file_lock = threading.Lock()

# Define the IP and port you want to listen on
UDP_IP = "*************"
UDP_PORT = 5004

audio_buffers = {}


def process_strep_packet(packet):
    """Extracts the RTP payload from a STREP encapsulated packet."""
    strep_header = packet[:24]
    header_info = struct.unpack("!H", strep_header[:2])[0]
    version = (header_info >> 10) & 0xF
    leg = (header_info >> 5) & 0x1
    direction = (header_info >> 4) & 0x1
    header_len = header_info & 0xFF
    call_leg_id = strep_header[8:24]
    rtp_packet = packet[header_len:]
    return rtp_packet, version, leg, direction, call_leg_id

def decode_g711_to_pcmu(rtp_payload):
    """Decode G.711 PCMU data to PCM using pydub."""
    audio_segment = AudioSegment(
        data=rtp_payload,
        sample_width=1,
        frame_rate=8000,
        channels=1
    )
    pcm_audio = BytesIO()
    audio_segment.export(pcm_audio, format="wav")
    return pcm_audio.getvalue()

def receive_rtp(call_leg_id):
    """Main loop to receive RTP packets and save to a WAV file for each call."""
    print(f"Listening for RTP on {UDP_IP}:{UDP_PORT} for call leg: {call_leg_id}")
    
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.bind((UDP_IP, UDP_PORT))
    
    with wave.open(f'output_{call_leg_id}.wav', 'wb') as wav_file:
        wav_file.setnchannels(1)
        wav_file.setsampwidth(2)
        wav_file.setframerate(8000)
        
        while True:
            data, addr = sock.recvfrom(1500)
            rtp_packet, version, leg, direction, packet_call_leg_id = process_strep_packet(data)
            if packet_call_leg_id == call_leg_id:
                pcm_data = decode_g711_to_pcmu(rtp_packet)
                
                with file_lock:
                    wav_file.writeframes(pcm_data)
                
                print(f"Received RTP from {addr} - Version: {version}, Leg: {leg}, Direction: {direction}")
            else:
                print(f"Received packet for unknown call leg: {packet_call_leg_id}")

def start_rtp_listener(call_leg_id):
    """Start a thread to listen for RTP packets."""
    listener_thread = threading.Thread(target=receive_rtp, args=(call_leg_id,), daemon=True)
    listener_thread.start()

class CallHandler:
    def __init__(self, call_control_id):
        self.call_control_id = call_control_id
        self.call_leg_id = None
        self.call_session_id = None
        self.thread = None
        self.transcript_buffer = ""

    def handle_answered(self):
        call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
        call.call_control_id = self.call_control_id
        call.transcription_start(language="en", interim_results=True)
        fork_response = call.fork_start(target=f"udp:{UDP_IP}:{UDP_PORT}")
        print(f"Fork response for call {self.call_control_id}: {fork_response}")
        self.thread = client.beta.threads.create()
        print(f"Thread created for call {self.call_control_id}: {self.thread.id}")

    def update_call_leg_id(self, call_leg_id):
        self.call_leg_id = call_leg_id
        if self.call_leg_id:
            start_rtp_listener(self.call_leg_id)
            print(f"Started RTP listener for call_leg_id: {self.call_leg_id}")
        else:
            print(f"Warning: Attempted to update call_leg_id with None for call_control_id: {self.call_control_id}")

    def handle_transcription(self, transcript):
        print(f"Transcribed Text for call {self.call_control_id}: {transcript}")
        self.transcript_buffer += " " + transcript

    def handle_dtmf(self, digit):
        if digit == "#":
            print(f'---------# PRESSED for call {self.call_control_id}--------------------')
            response = self.send_to_llm(self.transcript_buffer.strip() + " hello there, how can you help?")
            self.play_tts(response)
            self.transcript_buffer = ""
        elif digit == "*":
            self.hangup()

    def send_to_llm(self, transcript):
        print(f"Sending to LLM for call {self.call_control_id}, Thread ID: {self.thread.id}")
        message = client.beta.threads.messages.create(
            thread_id=self.thread.id,
            role="user",
            content=transcript,
        )
        run = client.beta.threads.runs.create(
            thread_id=self.thread.id,
            assistant_id=assistant_id,
            tool_choice='required',
        )
        run = self.wait_on_run(run)
        openai_output_raw = self.pretty_str(self.get_response())
        last_assistant_text = openai_output_raw.split('assistant:')[-1].strip()
        last_assistant_text = re.sub(r'【[^】]*】', '', last_assistant_text)
        print(f"LLM response for call {self.call_control_id}: {last_assistant_text}")
        return last_assistant_text

    def wait_on_run(self, run):
        while run.status == "queued" or run.status == "in_progress":
            run = client.beta.threads.runs.retrieve(
                thread_id=self.thread.id,
                run_id=run.id,
            )
            time.sleep(0.1)
        return run

    def pretty_str(self, messages):
        formatted_str = ""
        for m in messages:
            formatted_str += f"{m.role}: {m.content[0].text.value}\n"
        return formatted_str

    def get_response(self):
        return client.beta.threads.messages.list(thread_id=self.thread.id, order="asc")

    def play_tts(self, response_text):
        call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
        call.call_control_id = self.call_control_id
        call.speak(
            payload=response_text,
            language="en-US",
            voice="Polly.Amy"
        )

    def hangup(self):
        call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
        call.call_control_id = self.call_control_id
        call.hangup()
        
        # Flush any remaining audio data to the file
        if self.call_leg_id in audio_buffers:
            with wave.open(f'output_{self.call_leg_id}.wav', 'ab') as wav_file:
                with file_lock:
                    wav_file.writeframes(audio_buffers[self.call_leg_id])
            del audio_buffers[self.call_leg_id]
        
        del call_handlers[self.call_control_id]

@app.route('/webhook', methods=['POST'])
def inbound():
    body = json.loads(request.data)
    event = body.get("data", {}).get("event_type")
    payload = body.get("data", {}).get("payload", {})
    call_control_id = payload.get("call_control_id")
    
    print(f"Received webhook: {event} for call_control_id: {call_control_id}")

    try:
        if event == "call.initiated":
            handle_call_initiated(payload)
        elif event == "call.answered":
            handle_call_answered(payload)
        elif event == "call.transcription":
            handle_call_transcription(payload)
        elif event == 'call.dtmf.received':
            handle_call_dtmf_received(payload)
        elif event == 'call.hangup':
            handle_call_hangup(payload)
        elif event == 'call.fork.started':
            handle_call_fork_started(payload)
        else:
            print(f"Unhandled event type: {event}")
    except Exception as e:
        print(f"Error processing webhook: {e}")
        return json.dumps({"success": False}), 500, {"ContentType": "application/json"}
    return json.dumps({"success": True}), 200, {"ContentType": "application/json"}

def handle_call_initiated(payload):
    call_control_id = payload.get("call_control_id")
    call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
    call.call_control_id = call_control_id
    call.answer()
    call_handlers[call_control_id] = CallHandler(call_control_id)

def handle_call_answered(payload):
    call_control_id = payload.get("call_control_id")
    if call_control_id in call_handlers:
        call_handlers[call_control_id].handle_answered()

def handle_call_transcription(payload):
    call_control_id = payload.get("call_control_id")
    transcript = payload.get("transcription_data", {}).get("transcript")
    if call_control_id in call_handlers:
        call_handlers[call_control_id].handle_transcription(transcript)

def handle_call_dtmf_received(payload):
    event_id = payload.get("id")
    if event_id in processed_event_ids:
        print(f"Event {event_id} already processed.")
        return
    processed_event_ids.add(event_id)

    call_control_id = payload.get("call_control_id")
    dtmf = payload.get("digit")
    if call_control_id in call_handlers:
        call_handlers[call_control_id].handle_dtmf(dtmf)

def handle_call_hangup(payload):
    call_control_id = payload.get("call_control_id")
    if call_control_id in call_handlers:
        del call_handlers[call_control_id]

def handle_call_fork_started(payload):
    call_control_id = payload.get("call_control_id")
    call_leg_id = payload.get("call_leg_id")
    print(f"Fork started - call_control_id: {call_control_id}, call_leg_id: {call_leg_id}")
    if call_control_id in call_handlers and call_leg_id:
        call_handlers[call_control_id].update_call_leg_id(call_leg_id)
    else:
        print(f"Warning: Unable to update call_leg_id. call_control_id: {call_control_id}, call_leg_id: {call_leg_id}")

if __name__ == '__main__':
    app.run(debug=True, port=5000)