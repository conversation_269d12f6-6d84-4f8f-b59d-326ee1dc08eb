import json
import os
import telnyx
from flask import Flask, request
from openai import OpenAI
from dotenv import load_dotenv
import time
import re

app = Flask(__name__)

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

client = OpenAI()
assistant_id = 'asst_dLuE0A9zTpOYkz9m85NWz14x'

# Dictionary to store call handlers
call_handlers = {}

# Set to track processed event IDs
processed_event_ids = set()

class CallHandler:
    def __init__(self, call_control_id):
        self.call_control_id = call_control_id
        self.thread = None
        self.transcript_buffer = ""

    # def handle_answered(self):
    #     call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
    #     call.call_control_id = self.call_control_id
    #     call.transcription_start(language="en", interim_results=True)
    #     self.thread = client.beta.threads.create()
    #     print(f"Thread created for call {self.call_control_id}: {self.thread.id}")
    def handle_answered(self):
        print('handle_answered func triggered')
        try:
            print('trying handle_answered')
            # Debugging: Log the current call control ID
            print(f"Handling answered call for call_control_id: {self.call_control_id}")
            
            # Create the Telnyx call object
            call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
            call.call_control_id = self.call_control_id
            
            # Debugging: Log before starting transcription
            print("Starting transcription for call.")
            call.transcription_start(language="en", interim_results=True)
            
            # Debugging: Log before thread creation
            print("Creating thread for call.")
            self.thread = client.beta.threads.create()
            
            # Debugging: Check and log thread creation
            if not self.thread:
                raise Exception("Thread creation failed. No thread object returned.")
            
            # Debugging: Log thread ID
            print(f"Thread created successfully for call {self.call_control_id}: {self.thread.id}")
        except Exception as e:
            # Debugging: Log any errors encountered during execution
            print(f"Error in handle_answered for call {self.call_control_id}: {e}")
            

    def handle_transcription(self, transcript):
        print(f"Transcribed Text for call {self.call_control_id}: {transcript}")
        self.transcript_buffer += " " + transcript

    def handle_dtmf(self, digit):
        if digit == "#":
            print(f'---------# PRESSED for call {self.call_control_id}--------------------')
            response = self.send_to_llm(self.transcript_buffer.strip()+ " ")
            self.play_tts(response)
            self.transcript_buffer = ""
        elif digit == "*":
            self.hangup()

    def send_to_llm(self, transcript):
        print(f"Sending to LLM for call {self.call_control_id}, Thread ID: {self.thread.id}")

        message = client.beta.threads.messages.create(
            thread_id=self.thread.id,
            role="user",
            content=transcript,
#             attachments=[
#       {
#         "file_id": "file-6HjO1PG0UmCbw9AKOzrcaMoC",
#         "tools": [
#             {"type": "file_search"},  # Tool type 'file_search' for FileSearch tool
#             #{"type": "code_interpreter"}  # Assuming there's a tool type for the Code interpreter
#         ]
#       }
#   ]
)

#vs_PoSM4mPvxPaYTA85gCcDIggG



        run = client.beta.threads.runs.create(
            thread_id=self.thread.id,
            assistant_id=assistant_id,
            tool_choice = 'required',
        )
                
        run = self.wait_on_run(run)

        openai_output_raw = self.pretty_str(self.get_response())

        last_assistant_text = openai_output_raw.split('assistant:')[-1].strip()
        last_assistant_text = re.sub(r'【[^】]*】', '', last_assistant_text)

        print(f"LLM response for call {self.call_control_id}: {last_assistant_text}")
        return last_assistant_text

    def wait_on_run(self, run):
        while run.status == "queued" or run.status == "in_progress":
            run = client.beta.threads.runs.retrieve(
                thread_id=self.thread.id,
                run_id=run.id,
            )
            time.sleep(0.1)
        return run

    def pretty_str(self, messages):
        formatted_str = ""
        for m in messages:
            formatted_str += f"{m.role}: {m.content[0].text.value}\n"
        return formatted_str

    def get_response(self):
        return client.beta.threads.messages.list(thread_id=self.thread.id, order="asc")

    def play_tts(self, response_text):
        call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
        call.call_control_id = self.call_control_id
        call.speak(
            payload=response_text,
            language="en-US",
            voice="Polly.Amy")

    def hangup(self):
        call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
        call.call_control_id = self.call_control_id
        call.hangup()
        # Remove this call handler from the dictionary
        del call_handlers[self.call_control_id]

@app.route('/webhook', methods=['POST'])
def inbound():
    body = json.loads(request.data)
    event = body.get("data").get("event_type")
    call_control_id = body.get("data").get("payload").get("call_control_id")

    try:
        if event == "call.initiated":
            handle_call_initiated(body)
        elif event == "call.answered":
            handle_call_answered(body)
        elif event == "call.transcription":
            handle_call_transcription(body)
        elif event == 'call.dtmf.received':
            handle_call_dtmf_received(body)
        elif event == 'call.hangup':
            handle_call_hangup(body)
    except Exception as e:
        print(f"Error: {e}")
        return json.dumps({"success": False}), 500, {"ContentType": "application/json"}
    return json.dumps({"success": True}), 200, {"ContentType": "application/json"}

def handle_call_initiated(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
    call.call_control_id = call_control_id
    call.answer()
    print('call answered')
    call_handlers[call_control_id] = CallHandler(call_control_id)
    print(call_handlers)

def handle_call_answered(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    print('###')
    print(call_control_id)
    print('###')
    if call_control_id in call_handlers:
        call_handlers[call_control_id].handle_answered()
        print('TRUE!!!')

def handle_call_transcription(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    transcript = body.get("data").get("payload").get("transcription_data").get("transcript")
    if call_control_id in call_handlers:
        call_handlers[call_control_id].handle_transcription(transcript)

def handle_call_dtmf_received(body):
    event_id = body.get("data").get("id")
    if event_id in processed_event_ids:
        print(f"Event {event_id} already processed.")
        return
    processed_event_ids.add(event_id)

    call_control_id = body.get("data").get("payload").get("call_control_id")
    dtmf = body.get("data").get("payload").get("digit")
    if call_control_id in call_handlers:
        call_handlers[call_control_id].handle_dtmf(dtmf)

def handle_call_hangup(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    if call_control_id in call_handlers:
        del call_handlers[call_control_id]

if __name__ == '__main__':
    app.run(debug=True, port=5000)