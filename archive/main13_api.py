import json
import os
import telnyx
from flask import Flask, request
from openai import OpenAI
from dotenv import load_dotenv
import time
import re
import socket
import struct
import wave
import threading
import traceback
import audioop
from collections import OrderedDict
import sys

# Flask app setup
app = Flask(__name__)

# Load environment variables
load_dotenv()

# OpenAI Configuration
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
client = OpenAI()
assistant_id = 'asst_dLuE0A9zTpOYkz9m85NWz14x'  # Replace with your assistant ID

# Telnyx Configuration
TELNYX_API_KEY = os.getenv("TELNYX_API_KEY")
TELNYX_CONNECTION_ID = os.getenv("TELNYX_CONNECTION_ID")

if not TELNYX_API_KEY:
    raise ValueError("TELNYX_API_KEY is not set in the environment variables")
if not TELNYX_CONNECTION_ID:
    raise ValueError("TELNYX_CONNECTION_ID is not set in the environment variables")

telnyx.api_key = TELNYX_API_KEY

# RTP Configuration
UDP_IP = "0.0.0.0"
UDP_PORT = 5004
UDP_TRANSMIT = "*************"  # Replace with your public IP

# Dictionary to store call handlers
call_handlers = {}

# Set to track processed event IDs
processed_event_ids = set()

class CallHandler:
    def __init__(self, call_control_id):
        # OpenAI and Transcription attributes
        self.call_control_id = call_control_id
        self.thread = None
        self.transcript_buffer = ""

        # RTP and Audio Recording attributes
        self.call_leg_id = None
        self.wav_file = None
        self.rtp_thread = None
        self.packet_count = 0
        self.is_receiving = True
        self.sample_width = 2
        self.channels = 1
        self.sample_rate = 8000
        self.packet_buffer = OrderedDict()
        self.expected_seq = None
        print(f"CallHandler created for call_control_id: {call_control_id}")

    def handle_answered(self):
        try:
            # Initialize call object
            call = telnyx.Call(connection_id=TELNYX_CONNECTION_ID)
            call.call_control_id = self.call_control_id
            
            # Start transcription
            call.transcription_start(language="en", interim_results=True)
            
            # Create OpenAI thread
            self.thread = client.beta.threads.create()
            print(f"Thread created for call {self.call_control_id}: {self.thread.id}")
            
            # Start RTP fork
            print(f"Starting fork with target: udp:{UDP_TRANSMIT}:{UDP_PORT}")
            fork_response = call.fork_start(
                target=f"udp:{UDP_TRANSMIT}:{UDP_PORT}",
                rx="receive_stream",
                tx="transmit_stream"
            )
            print(f"Fork started for call {self.call_control_id}")
            print(f"Fork response: {fork_response}")
            
        except Exception as e:
            print(f"Error in handle_answered: {e}")
            traceback.print_exc()

    def handle_transcription(self, transcript):
        print(f"Transcribed Text for call {self.call_control_id}: {transcript}")
        self.transcript_buffer += " " + transcript

    def handle_dtmf(self, digit):
        if digit == "#":
            print(f'---------# PRESSED for call {self.call_control_id}--------------------')
            response = self.send_to_llm(self.transcript_buffer.strip() + " ")
            self.play_tts(response)
            self.transcript_buffer = ""
        elif digit == "*":
            self.hangup()

    def send_to_llm(self, transcript):
        print(f"Sending to LLM for call {self.call_control_id}, Thread ID: {self.thread.id}")

        message = client.beta.threads.messages.create(
            thread_id=self.thread.id,
            role="user",
            content=transcript
        )

        run = client.beta.threads.runs.create(
            thread_id=self.thread.id,
            assistant_id=assistant_id,
            tool_choice='required'
        )

        run = self.wait_on_run(run)
        openai_output_raw = self.pretty_str(self.get_response())
        last_assistant_text = openai_output_raw.split('assistant:')[-1].strip()
        last_assistant_text = re.sub(r'【[^】]*】', '', last_assistant_text)

        print(f"LLM response for call {self.call_control_id}: {last_assistant_text}")
        return last_assistant_text

    def wait_on_run(self, run):
        while run.status == "queued" or run.status == "in_progress":
            run = client.beta.threads.runs.retrieve(
                thread_id=self.thread.id,
                run_id=run.id
            )
            time.sleep(0.1)
        return run

    def pretty_str(self, messages):
        formatted_str = ""
        for m in messages:
            formatted_str += f"{m.role}: {m.content[0].text.value}\n"
        return formatted_str

    def get_response(self):
        return client.beta.threads.messages.list(thread_id=self.thread.id, order="asc")

    def play_tts(self, response_text):
        call = telnyx.Call(connection_id=TELNYX_CONNECTION_ID)
        call.call_control_id = self.call_control_id
        call.speak(
            payload=response_text,
            language="en-US",
            voice="Polly.Amy"
        )

    def start_rtp_listener(self, call_leg_id):
        self.call_leg_id = call_leg_id
        try:
            self.wav_file = wave.open(f'output_{call_leg_id}.wav', 'wb')
            self.wav_file.setnchannels(self.channels)
            self.wav_file.setsampwidth(self.sample_width)
            self.wav_file.setframerate(self.sample_rate)
            self.rtp_thread = threading.Thread(target=self.receive_rtp, daemon=True)
            self.rtp_thread.start()
            print(f"RTP listener started for call_leg_id: {call_leg_id}")
        except Exception as e:
            print(f"Error starting RTP listener: {e}")
            traceback.print_exc()

    def receive_rtp(self):
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.bind((UDP_IP, UDP_PORT))
        sock.settimeout(1.0)
        
        print(f"Listening for RTP on {UDP_IP}:{UDP_PORT}")
        
        start_time = time.time()
        while self.is_receiving:
            try:
                data, addr = sock.recvfrom(1500)
                strep_header, rtp_payload = self.parse_strep_packet(data)
                if rtp_payload:
                    self.process_rtp_packet(rtp_payload)
            except socket.timeout:
                elapsed_time = time.time() - start_time
                print(f"No data received for 1 second. Total packets: {self.packet_count}. Elapsed time: {elapsed_time:.2f}s")
            except Exception as e:
                print(f"Error receiving RTP: {e}")
                traceback.print_exc()
                break

    def parse_strep_packet(self, packet):
        if len(packet) < 24:
            print(f"Received packet is too short: {len(packet)} bytes")
            return None, b''

        header = struct.unpack('!HHLQQ', packet[:24])
        magic_version_flags = header[0]
        header_len = magic_version_flags & 0xFF
        version = (magic_version_flags >> 10) & 0xF
        leg = (magic_version_flags >> 5) & 0x1
        direction = (magic_version_flags >> 4) & 0x1
        call_leg_id = (header[3] << 64) | header[4]

        strep_header = {
            'version': version,
            'leg': leg,
            'direction': direction,
            'header_len': header_len,
            'call_leg_id': hex(call_leg_id)
        }

        return strep_header, packet[header_len:]

    def process_rtp_packet(self, rtp_payload):
        if len(rtp_payload) < 12:
            print(f"RTP payload too short: {len(rtp_payload)} bytes")
            return

        rtp_header = struct.unpack('!BBHII', rtp_payload[:12])
        payload_type = rtp_header[1] & 0x7F
        sequence_number = rtp_header[2]
        timestamp = rtp_header[3]
        audio_data = rtp_payload[12:]

        codec = 'PCMU' if payload_type == 0 else 'PCMA' if payload_type == 8 else None
        if not codec:
            print(f"Unsupported payload type: {payload_type}")
            return

        self.packet_buffer[sequence_number] = (codec, audio_data)
        self.process_buffered_packets()

    def process_buffered_packets(self):
        while self.packet_buffer:
            if self.expected_seq is None:
                self.expected_seq = min(self.packet_buffer.keys())

            if self.expected_seq not in self.packet_buffer:
                break

            codec, audio_data = self.packet_buffer.pop(self.expected_seq)
            
            pcm_data = (audioop.ulaw2lin(audio_data, self.sample_width) 
                       if codec == 'PCMU' 
                       else audioop.alaw2lin(audio_data, self.sample_width))
            
            if sys.byteorder == 'big':
                pcm_data = audioop.byteswap(pcm_data, self.sample_width)

            self.wav_file.writeframes(pcm_data)
            self.packet_count += 1

            if self.packet_count % 100 == 0:
                print(f"Processed {self.packet_count} packets for call {self.call_control_id}")

            self.expected_seq = (self.expected_seq + 1) & 0xFFFF

    def hangup(self):
        self.is_receiving = False
        if self.rtp_thread:
            self.rtp_thread.join(timeout=5)
        if self.wav_file:
            self.wav_file.close()
        
        call = telnyx.Call(connection_id=TELNYX_CONNECTION_ID)
        call.call_control_id = self.call_control_id
        call.hangup()
        
        if self.call_control_id in call_handlers:
            del call_handlers[self.call_control_id]
        
        print(f"Call {self.call_control_id} hung up. Total packets received: {self.packet_count}")

@app.route('/webhook', methods=['POST'])
def webhook():
    body = json.loads(request.data)
    event = body.get("data", {}).get("event_type")
    payload = body.get("data", {}).get("payload", {})
    call_control_id = payload.get("call_control_id")

    print(f"Received event: {event} for call_control_id: {call_control_id}")
    print(f"Full payload: {body}")

    try:
        if event == "call.initiated":
            if call_control_id:
                call_handlers[call_control_id] = CallHandler(call_control_id)
                call = telnyx.Call(connection_id=TELNYX_CONNECTION_ID)
                call.call_control_id = call_control_id
                call.answer()
                print(f"Answered call for call_control_id: {call_control_id}")
        
        elif event == "call.answered":
            if call_control_id in call_handlers:
                call_handlers[call_control_id].handle_answered()
        
        elif event == "call.transcription":
            if call_control_id in call_handlers:
                transcript = payload.get("transcription_data", {}).get("transcript")
                call_handlers[call_control_id].handle_transcription(transcript)
        
        elif event == "call.dtmf.received":
            event_id = body.get("data", {}).get("id")
            if event_id not in processed_event_ids:
                processed_event_ids.add(event_id)
                if call_control_id in call_handlers:
                    digit = payload.get("digit")
                    call_handlers[call_control_id].handle_dtmf(digit)
        
        elif event == "call.fork.started":
            call_leg_id = payload.get("call_leg_id")
            if call_control_id in call_handlers and call_leg_id:
                call_handlers[call_control_id].start_rtp_listener(call_leg_id)
        
        elif event == "call.hangup":
            if call_control_id in call_handlers:
                call_handlers[call_control_id].hangup()

    except Exception as e:
        print(f"Error in webhook: {e}")
        traceback.print_exc()
        return '', 500

    return '', 200

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)