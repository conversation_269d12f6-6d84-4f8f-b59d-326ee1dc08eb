import asyncio
import websockets
import torch
import numpy as np

# Load Silero VAD Model Correctly
model, utils = torch.hub.load(
    'snakers4/silero-vad',
    'silero_vad',
    trust_repo=True,  # Avoid warnings
    verbose=False
)

# Extract Correct Utility Functions from Tuple
get_speech_timestamps = utils[0]

# VAD Threshold for Speech Detection
VAD_THRESHOLD = 0.5  # Adjust sensitivity if needed

# WebSocket Audio Handler
async def handle_stream(websocket, path):
    print(f"WebSocket connected from {websocket.remote_address}")

    try:
        async for message in websocket:
            print(f"Received {len(message)} bytes of audio")

            # Convert Incoming Audio to PyTorch Tensor
            audio_tensor = torch.tensor(np.frombuffer(message, dtype=np.float32))

            # Run VAD for Speech Detection
            speech_timestamps = get_speech_timestamps(audio_tensor, model, threshold=VAD_THRESHOLD)

            if speech_timestamps:
                print(f"Speech detected! Timestamps: {speech_timestamps}")
            else:
                print("No speech detected.")

            # Echo Back Audio for Testing
            await websocket.send(message)

    except websockets.ConnectionClosed:
        print(f"WebSocket closed for {websocket.remote_address}")

# Start WebSocket Server
async def main():
    print("Starting WebSocket Server at ws://localhost:5000")
    async with websockets.serve(handle_stream, "0.0.0.0", 5000):
        await asyncio.Future()  # Keeps Server Running

# Run the WebSocket Server
if __name__ == "__main__":
    asyncio.run(main())
