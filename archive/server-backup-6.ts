/*******************************************************************
 * server.ts
 *
 * 1) Twilio => Node => local WAV + AssemblyAI real-time STT
 * 2) Also calls a Python-based VAD endpoint on port 8085
 * 3) Tracks speaker turns: multiple final transcripts accumulate until
 *    we detect ~1 second of silence -> finalise turn
 *******************************************************************/

import express from 'express'
import bodyParser from 'body-parser'
import { twiml as TwiML } from 'twilio'
import http from 'http'
import WebSocket from 'ws'
import { spawn, ChildProcessWithoutNullStreams } from 'child_process'
import axios from 'axios'
import { AssemblyAI, RealtimeTranscriber, RealtimeTranscript } from 'assemblyai'
import fs from 'fs'
import path from 'path'
import { OpenAI } from 'openai'
import dotenv from 'dotenv'
import { DefaultAzureCredential, getBearerTokenProvider } from '@azure/identity'
import * as sdk from 'microsoft-cognitiveservices-speech-sdk'
import { createClient } from '@supabase/supabase-js'
import { interpret } from 'xstate'
import { createOsceMachine } from './osceMachine'
import { CaseData } from './types'; // Corrected import path

// Load environment variables from parent directory
dotenv.config({ path: path.join(__dirname, '../../.env') })

/** 
 * We'll define a small function to split a Buffer into 20ms frames
 * at 8 kHz, 16-bit PCM => 320 bytes per frame.
 * If leftover data remains, we keep it in a global or callSid-based 
 * leftover buffer for the next chunk.
 */
function splitTo20msFrames(buffer: Buffer, leftover: Buffer): Buffer[] {
  const FRAME_SIZE = 320 // 20ms @ 8kHz, 16-bit mono
  
  // Combine leftover + new chunk
  const combined = Buffer.concat([leftover, buffer])

  const frames: Buffer[] = []
  let offset = 0

  // Only create frames of exactly FRAME_SIZE bytes
  while (offset + FRAME_SIZE <= combined.length) {
    const frame = combined.slice(offset, offset + FRAME_SIZE);
    // Sanity check to ensure we're sending valid frames
    if (frame.length === FRAME_SIZE) {
      frames.push(frame);
    } else {
      console.warn(`Created invalid frame size: ${frame.length} bytes, expected ${FRAME_SIZE}`);
    }
    offset += FRAME_SIZE;
  }

  // The leftover portion - data that's not a complete frame
  const newLeftover = combined.slice(offset);

  // Return frames + leftover as the last element
  return [...frames, newLeftover];
}

//-----------------------------------------------
// Configuration
//-----------------------------------------------
const PORT = process.env.PORT || 5000
const VAD_ENDPOINT = 'http://localhost:8085/vad' // Python VAD microservice
const RAG_SERVER_URL = 'http://localhost:8001/rag/converse'

// STT Provider Configuration
// Define STT Providers type
type SttProviderType = 'assemblyai' | 'azure';
// Select STT Provider (can be 'assemblyai' or 'azure')
const STT_PROVIDER: SttProviderType = process.env.STT_PROVIDER === 'azure' ? 'azure' : 'assemblyai';
console.log(`Using STT Provider: ${STT_PROVIDER}`);

// AssemblyAI configuration (only used if STT_PROVIDER is 'assemblyai')
const ASSEMBLYAI_API_KEY = '********************************' 

// Azure Speech configuration (used for TTS and optionally STT)
const AZURE_SPEECH_KEY = 'c539857332064fc38e4aa5075b652087';
const AZURE_SPEECH_REGION = 'swedencentral';
const AZURE_SPEECH_VOICE = 'en-US-AvaMultilingualNeural'; // High quality neural voice

// Constants for speech detection
// Increase the silence threshold to be more tolerant of brief pauses
const SILENCE_FRAMES_THRESHOLD = 20; // Reduced from 80 (~0.8 seconds of silence)
const LONG_PAUSE_THRESHOLD = 5; // Reduced from 200 (~2 seconds of silence)
const MIN_SPEECH_FRAMES = 5; // Reduced from 15 - require less speech to consider a turn valid

// Add these counters at the top of the file with other state variables
// Track media packet counts to reduce logging
const mediaPacketCounts: Record<string, number> = {};
const MEDIA_LOG_INTERVAL = 10000; // Set to a very high number to effectively disable

// Transcript logging settings
const TRANSCRIPT_LOG_ALL = false; // Don't log partial transcripts
const TRANSCRIPT_LOG_FINAL_ONLY = false; // Don't even log final transcripts
const VAD_LOG_INTERVAL = 10000; // Effectively disable VAD logging
const SILENCE_DETECTION_LOGS = false; // Disable silence detection logs

// Toggle for more verbose VAD logging without changing constants
const VERBOSE_VAD_LOGGING = true;

// Add this constant at the top of the file with other constants
const TRANSCRIPT_WAIT_TIMEOUT = 2000; // Wait up to 2 seconds for transcript after detecting speech

// Environment variables
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const SUPABASE_SERVICE_KEY = process.env.NEXT_SERVICE_SUPABASE_SERVICE_KEY || '';

// Initialize Supabase client with service key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Temporary storage for details received in /voice webhook before WS connection starts
const pendingCallDetails: Record<string, { from: string | null, to: string | null, userId?: string | null }> = {};

//-----------------------------------------------
// Setup Express
//-----------------------------------------------
const app = express()
app.use(bodyParser.urlencoded({ extended: false }))
app.use(bodyParser.json())

// AssemblyAI client (conditionally initialized)
let aaiClient: AssemblyAI | null = null;
if (STT_PROVIDER === 'assemblyai') {
    if (!ASSEMBLYAI_API_KEY) {
        console.error("Error: ASSEMBLYAI_API_KEY is required when STT_PROVIDER is 'assemblyai'");
        process.exit(1);
    }
    aaiClient = new AssemblyAI({ apiKey: ASSEMBLYAI_API_KEY });
}

// Define a common interface for STT providers
interface SttProvider {
    sendAudio(chunk: Buffer): void;
    close(): void;
    connect?(): Promise<void>; // Optional connect method if needed async
}

// Track call state
interface CallState {
  recordFfmpeg?: ChildProcessWithoutNullStreams
  decodeFfmpeg?: ChildProcessWithoutNullStreams
  leftover: Buffer
  sttProvider?: SttProvider // Use the generic interface
  currentTurnText: string
  consecutiveSilenceFrames: number
  conversation: Array<{ speaker: string; text: string }>
  isModelSpeaking: boolean
  currentModelResponse?: string
  audioResponsePath?: string // Path to the current audio response file
  chunkCount?: number
  streamSid?: string // Track the Twilio stream SID
  speechFramesInCurrentTurn: number
  currentFfmpegProcess?: ChildProcessWithoutNullStreams // For tracking current audio streaming ffmpeg process
  currentSpeakingTimeout?: NodeJS.Timeout // For tracking the timeout that automatically ends speaking mode
  pendingFinalizeTimeout?: NodeJS.Timeout // For tracking delayed finalization
  hasSpeechDetected: boolean // Flag to track if speech was detected even if no transcript is available
  finalizationInProgress: boolean // Flag to prevent duplicate finalization
  callStartTime: Date; // Add start time to calculate duration
  audioStream?: sdk.PushAudioInputStream; // Specific to Azure
  speechRecognizer?: sdk.SpeechRecognizer; // Specific to Azure
  // Add timing-related fields
  turnTimings: Array<{
    turnIndex: number;
    silenceDetectedAt?: number;
    transcriptReceivedAt?: number;
    ragRequestStartAt?: number;
    ragResponseReceivedAt?: number;
    audioGenerationStartAt?: number;
    audioGenerationEndAt?: number;
    audioStreamingStartAt?: number;
    audioStreamingEndAt?: number;
    ttsFilename?: string; // Basename of the TTS file generated for this turn
    totalTurnDuration?: number;
    interruptionTimestamp?: number; // Timestamp when an interruption occurred during this turn's TTS
  }>;
  currentTurnStartTime?: number; // Track when the current turn started
  currentTurnIndex: number; // Track the turn number
  recordingActualStartTime?: number; // Timestamp when the first decoded audio chunk is processed
  currentlyPlayingTurnIndex?: number; // Track which turn's audio is currently playing
  recordingStoragePath?: string; // Store just the storage path instead of full URL
}
const calls: Record<string, CallState> = {}

// Create audio directory if it doesn't exist
const AUDIO_DIR = path.join(__dirname, '../audio_responses')
if (!fs.existsSync(AUDIO_DIR)) {
  fs.mkdirSync(AUDIO_DIR, { recursive: true })
}

// Create conversations directory if it doesn't exist
const CONVERSATIONS_DIR = path.join(__dirname, '../conversations')
if (!fs.existsSync(CONVERSATIONS_DIR)) {
  fs.mkdirSync(CONVERSATIONS_DIR, { recursive: true })
}

//-----------------------------------------------
// 1) Twilio /voice -> Return <Start><Stream>
//-----------------------------------------------
app.post('/voice', async (req, res) => { // <-- Make this function async
  const twiml = new TwiML.VoiceResponse();
  const callSid = req.body.CallSid;
  const fromNumber = req.body.From || null;
  const toNumber = req.body.To || null;
  
  console.log(`Incoming call SID: ${callSid}, From: ${fromNumber}, To: ${toNumber}`);

  // Store details temporarily, associated with CallSid
  if (callSid) {
    // Look up user ID based on From number
    let userId: string | null = null;
    if (fromNumber) {
        try {
            userId = await findUserIdByPhoneNumber(fromNumber);
            if (userId) {
                console.log(`Found user ID ${userId} for phone number ${fromNumber}`);
            } else {
                console.log(`No user found for phone number ${fromNumber}`);
            }
        } catch (lookupError) {
            console.error(`Error looking up user ID for phone ${fromNumber}:`, lookupError);
        }
    }

    pendingCallDetails[callSid] = { from: fromNumber, to: toNumber, userId: userId }; // Store userId too
    console.log(`Stored initial details for call ${callSid} (userId: ${userId || 'N/A'})`);

    // Set a timeout to clean up pending details if WS never connects (e.g., 60 seconds)
    setTimeout(() => {
      if (pendingCallDetails[callSid]) {
        console.warn(`Cleaning up stale pending details for call ${callSid} (WS likely never connected)`);
        delete pendingCallDetails[callSid];
      }
    }, 60000); 
  }

  // First play a greeting
  twiml.say({
    voice: 'alice'
  }, 'Hello! How can I help you today?');
  
  // Then set up the bidirectional stream with <Connect><Stream>
  // per https://www.twilio.com/docs/voice/twiml/stream
  const connect = twiml.connect();
  connect.stream({
    url: `wss://${req.headers.host}/media`
    // No 'track' parameter - for bidirectional streams, only inbound_track is supported
  });
  
  console.log('Responding with TwiML:', twiml.toString());
  res.type('text/xml').send(twiml.toString());
});

//-----------------------------------------------
// 2) WebSocket for /media
//    Twilio => Node => ffmpeg => local WAV + STT (AAI or Azure)
//    Also do VAD checks => turn detection
//-----------------------------------------------
const server = http.createServer(app)
const wss = new WebSocket.Server({
  server,
  path: '/media'
});

// Log when the WebSocket server is created
console.log('WebSocket server created and listening on path: /media');

// Add connection attempt logging
wss.on('headers', (headers, req) => {
  console.log('WebSocket connection headers received:', headers.join(', '));
  console.log('Connection attempt from:', req.socket.remoteAddress);
});

wss.on('connection', (ws) => {
  console.log('Twilio connected to /media WebSocket at:', new Date().toISOString());

  let callSid: string | null = null

  // Add callSid property to the WebSocket object for tracking
  Object.defineProperty(ws, 'callSid', {
    get: function() { return callSid; },
    set: function(value) { callSid = value; }
  });

  // Log WebSocket close events
  ws.on('close', (code, reason) => {
    console.log(`WebSocket for call ${callSid || 'unknown'} closed with code ${code}, reason: ${reason || 'none'}`);
    // Perform cleanup when the connection is closed
    if (callSid) {
      console.log(`Cleaning up resources for call ${callSid} after WebSocket closed`);
      cleanupCall(callSid);
    }
  });

  // Log WebSocket errors
  ws.on('error', (error) => {
    console.error(`WebSocket error for call ${callSid || 'unknown'}:`, error);
  });

  // Add enhanced monitoring for all messages
  ws.on('message', (rawMessage) => {
    try {
      // Track clear-related events
      const message = rawMessage.toString();
      const data = JSON.parse(message);
      
      // Watch for specific events related to clear messages or marks
      if (data.event === 'mark') {
        console.log(`[WS-MONITOR] Received mark event: ${JSON.stringify(data)}`);
      } else if (data.event === 'clear') {
        console.log(`[WS-MONITOR] Received clear event: ${JSON.stringify(data)}`);
      } else if (data.event === 'error') {
        console.log(`[WS-MONITOR] Received error event: ${JSON.stringify(data)}`);
      }
    } catch (error) {
      // Ignore parsing errors in the monitor
    }
  });

  ws.on('message', async (message: string) => {
    // Safely parse the message
    const data = safeJSONParse(message);
    
    if (data.event === 'error') return; // Invalid JSON
    
    // Special debug logging for speech events
    if (data.type && data.type.includes('speech')) {
      console.log('SPEECH EVENT DETECTED:', data.type);
    }
    
    // Only log non-media events to reduce console spam
    if (data.event !== 'media') {
      console.log(`Received WS message event: ${data.event}`);
      
      // Add more detailed logging to debug the data structure
      if (data.event === 'start') {
        console.log('Start event data:', JSON.stringify(data, null, 2));
      }
    } else {
      // For media events, only log occasionally to show activity
      if (callSid) {
        if (!mediaPacketCounts[callSid]) {
          mediaPacketCounts[callSid] = 0;
        }
        
        mediaPacketCounts[callSid]++;
        
        // Skip logging completely by commenting this out
        /*
        if (mediaPacketCounts[callSid] % MEDIA_LOG_INTERVAL === 0) {
          console.log(`Received ${MEDIA_LOG_INTERVAL} media packets for call ${callSid} (total: ${mediaPacketCounts[callSid]})`);
        }
        */
      }
    }

    try {
    switch (data.event) {
      case 'connected':
          console.log('Twilio event: connected');
          break;

      case 'start':
          // Use the safer callSid extraction method
          const extractedCallSid = getCallData(data);
          if (!extractedCallSid) {
            console.error('Could not extract callSid from start event:', data);
            return;
          }
          
          callSid = extractedCallSid;
          // Store callSid with the WebSocket object
          (ws as any).callSid = callSid;
          console.log('Media start event for callSid:', callSid);

          // Extract streamSid if available
          let streamSid = data.streamSid || callSid; // Use streamSid if present, fallback to callSid
          console.log(`Using streamSid: ${streamSid}`);

          // --- BEGIN INSERT CALL HISTORY ---
          const callStartTime = new Date();
          // Retrieve stored From/To numbers and userId
          const initialDetails = pendingCallDetails[callSid] || { from: null, to: null, userId: null };
          const fromNumberFromWebhook = initialDetails.from;
          const toNumberFromWebhook = initialDetails.to;
          const userIdFromWebhook = initialDetails.userId; // Get the userId

          // Clean up temporary storage now that WS has started
          if (pendingCallDetails[callSid]) {
            delete pendingCallDetails[callSid];
            console.log(`Cleaned up pending details for call ${callSid}`);
          }

          // Insert using retrieved numbers and userId
          insertInitialCallRecord(callSid, callStartTime, fromNumberFromWebhook, toNumberFromWebhook, userIdFromWebhook).catch(err => { // Pass userId
              console.error(`[DB] Failed initial insert for call ${callSid}:`, err);
          });
          // --- END INSERT CALL HISTORY ---

          // Check for existing conversation history (from disk)
          const existingConversation = loadConversation(callSid);

        // Initialize call state
        calls[callSid] = {
          leftover: Buffer.alloc(0),
          currentTurnText: '',
          consecutiveSilenceFrames: 0,
          conversation: existingConversation.length > 0 ? existingConversation : [],
          isModelSpeaking: false,
          streamSid: streamSid,
          speechFramesInCurrentTurn: 0,
          hasSpeechDetected: false, // Initialize the new flag
          finalizationInProgress: false, // Initialize the lock flag
          callStartTime: callStartTime, // Store the start time
          turnTimings: [],
          currentTurnIndex: 0,
          recordingActualStartTime: Date.now(), // Timestamp when the first decoded audio chunk is processed
          };
          
          // Log if we loaded existing conversation
          if (existingConversation.length > 0) {
            console.log(`Loaded existing conversation history for call ${callSid} with ${existingConversation.length} messages`);
        }

        // 1) ffmpeg #1 => record WAV locally (remains the same)
        try {
          const outPath = `./recordings/${callSid}_caller.wav`
          const recordFfmpeg = spawn('ffmpeg', [
            '-f', 'mulaw',
            '-ar', '8000',
            '-i', 'pipe:0',
            '-ac', '1',
            '-ar', '8000',
            '-f', 'wav',
            '-acodec', 'pcm_mulaw', // Output µ-law encoded WAV
            outPath,
          ])
          recordFfmpeg.on('close', (code) => {
            console.log(`recordFfmpeg for ${callSid} exited with code ${code}`)
          })
          recordFfmpeg.on('error', (err) => {
            console.error(`recordFfmpeg error for ${callSid}:`, err)
          })

          calls[callSid].recordFfmpeg = recordFfmpeg
        } catch (err) {
          console.error('Error launching record ffmpeg:', err)
        }
 
        
        // 2) ffmpeg #2 => decode mulaw -> 8kHz PCM => STT Provider (AAI or Azure)
        try {
          const decodeFfmpeg = spawn('ffmpeg', [
            '-f', 'mulaw',
            '-ar', '8000',
            '-i', 'pipe:0',
            '-ac', '1',
            '-ar', '8000', // Output 8kHz PCM for STT
            '-f', 's16le', // Signed 16-bit Little Endian PCM
            'pipe:1',
          ])
          decodeFfmpeg.on('close', (code) => {
            console.log(`decodeFfmpeg for ${callSid} exited with code ${code}`)
          })
          decodeFfmpeg.on('error', (err) => {
            console.error(`decodeFfmpeg error for ${callSid}:`, err)
          })

          calls[callSid].decodeFfmpeg = decodeFfmpeg
        } catch (err) {
          console.error('Error launching decode ffmpeg:', err)
        }

        // 3) Initialize and connect the selected STT provider
        try {
          if (STT_PROVIDER === 'assemblyai') {
            if (!aaiClient) throw new Error("AssemblyAI client not initialized.");
          const aaiTranscriber = aaiClient.realtime.transcriber({
                sampleRate: 8000, // Matches ffmpeg output
            });

          aaiTranscriber.on('open', ({ sessionId }) => {
              console.log(`[AssemblyAI] Session opened: ${sessionId}`)
            });

          aaiTranscriber.on('transcript', (rt: RealtimeTranscript) => {
                if (!rt.text) return;
                handleTranscript(callSid!, { 
                    text: rt.text, 
                    isFinal: rt.message_type === 'FinalTranscript' 
                });
            });

            aaiTranscriber.on('error', (error: Error) => {
              console.error(`[AssemblyAI] Error for ${callSid}:`, error)
            });

            aaiTranscriber.on('close', (code: number, reason: string) => {
              console.log(`[AssemblyAI] Closed for ${callSid}: ${code} ${reason}`)
            });

            await aaiTranscriber.connect();
            
            // Wrap AssemblyAI transcriber in our common interface
            calls[callSid].sttProvider = {
                sendAudio: (chunk) => aaiTranscriber.sendAudio(chunk),
                close: () => aaiTranscriber.close(),
            };
            console.log(`[STT] Initialized AssemblyAI for call ${callSid}`);

          } else if (STT_PROVIDER === 'azure') {
              // Initialize Azure STT (Skeleton)
              calls[callSid].sttProvider = initializeAzureStt(callSid!);
              if (calls[callSid].sttProvider?.connect) {
                  await calls[callSid].sttProvider!.connect!(); // Connect if method exists
              }
              console.log(`[STT] Initialized Azure STT for call ${callSid}`);
          }

          // 4) read decodeFfmpeg stdout => feed STT provider + do VAD
          const decodeFfmpeg = calls[callSid].decodeFfmpeg
          if (decodeFfmpeg) {
            decodeFfmpeg.stdout.on('data', async (chunk: Buffer) => {
                // Record the actual start time when the first chunk arrives
                const state = calls[callSid!]; // Get state again inside handler
                if (state && !state.recordingActualStartTime) {
                    state.recordingActualStartTime = Date.now();
                    console.log(`[Recording Start] Set recordingActualStartTime for ${callSid} to ${state.recordingActualStartTime}`);
                }

                // Send chunk to the active STT provider
                const sttProvider = calls[callSid!]?.sttProvider;
                if (sttProvider) {
                    sttProvider.sendAudio(chunk);
                }
                
                // Also do VAD chunking (remains the same)
                await handleVadAndTurns(callSid!, chunk);
            });
                } else {
              console.error(`decodeFfmpeg not initialized for call ${callSid}`);
          }
        } catch (err) {
          console.error(`Error initializing STT provider for call ${callSid}:`, err)
        }
        break

      case 'media': {
        if (!callSid) break
        const audioBuffer = Buffer.from(data.media.payload, 'base64')

        // Write chunk to recordFfmpeg (remains the same)
        const recordFfmpeg = calls[callSid]?.recordFfmpeg
        if (recordFfmpeg) {
          recordFfmpeg.stdin.write(audioBuffer)
        }

        // Write chunk to decodeFfmpeg (remains the same)
        const decodeFfmpeg = calls[callSid]?.decodeFfmpeg
        if (decodeFfmpeg) {
          decodeFfmpeg.stdin.write(audioBuffer)
        }

          // Update streamSid if provided in media event and not already set
          if (data.streamSid && calls[callSid] && !calls[callSid].streamSid) {
            calls[callSid].streamSid = data.streamSid;
            console.log(`Updated streamSid for ${callSid} to ${data.streamSid}`);
        }
        break
      }

      case 'stop':
        console.log('Twilio stop event for callSid:', callSid)
        if (callSid) {
          cleanupCall(callSid)
        }
        break

      default:
        console.log('Unknown Twilio event:', data.event)
    }
    } catch (err) {
      console.error('Error handling WebSocket message:', err);
    }
  })
})

// Log WebSocket server errors
wss.on('error', (error) => {
  console.error('WebSocket server error:', error);
});

//-----------------------------------------------
// Helper: Unified Transcript Handling
//-----------------------------------------------
// Add makeRagRequest function before handleTranscript
async function makeRagRequest(userMessage: string, callSid: string, conversationHistory: Array<{ speaker: string; text: string }> = []): Promise<string> {
  try {
    const ragResponse = await axios.post(RAG_SERVER_URL, {
      callSid,
      userMessage,
      conversationHistory
    });
    return ragResponse.data.assistantReply || '';
  } catch (err) {
    console.error(`Error making RAG request for call ${callSid}:`, err);
    return 'I apologize, but I am having trouble processing your request at the moment.';
  }
}

// Modify handleTranscript to be async
async function handleTranscript(callSid: string, transcriptData: { text: string; isFinal: boolean }) {
    const state = calls[callSid];
    if (!state || !transcriptData.text) return; // Ignore empty transcripts

    const { text, isFinal } = transcriptData;

    if (isFinal) {
        // Log based on provider
        const providerTag = state.sttProvider ? `[${STT_PROVIDER.toUpperCase()} Final Transcript]` : '[Final Transcript]';
        console.log(`${providerTag} Call ${callSid} - "${text}"`);

        // Record transcript timing
        if (!state.currentTurnStartTime) {
            state.currentTurnStartTime = Date.now();
        }
        const currentTiming = state.turnTimings[state.currentTurnIndex] || {
            turnIndex: state.currentTurnIndex,
            silenceDetectedAt: state.currentTurnStartTime
        };
        currentTiming.transcriptReceivedAt = Date.now();
        state.turnTimings[state.currentTurnIndex] = currentTiming;

        // Append final transcript to the current turn's text
        if (state.currentTurnText) {
            state.currentTurnText += ' ' + text;
        } else {
            state.currentTurnText = text;
        }

        const currentTurn = state.currentTurnText.trim();
        if (currentTurn.length > 0) {
            console.log(`[Current Turn Text] Call ${callSid} - Total ${currentTurn.length} chars - "${currentTurn}"`);

            // Record RAG request start time
            const currentTiming = state.turnTimings[state.currentTurnIndex];
            currentTiming.ragRequestStartAt = Date.now();

            // Make RAG request
            const ragResponse = await makeRagRequest(currentTurn, callSid, state.conversation);
            
            // Record RAG response time
            currentTiming.ragResponseReceivedAt = Date.now();

            // Log RAG response
            console.log(`[RAG Response] Call ${callSid} - "${ragResponse}"`);

            // Mark speech detected and ensure minimum frames are met
            if (!state.hasSpeechDetected) {
                console.log(`[IMMEDIATE-PROCESS-DEBUG] 🔧 FIX: Final transcript received but hasSpeechDetected=false, forcing to true`);
                state.hasSpeechDetected = true;
            }
            if (state.speechFramesInCurrentTurn < MIN_SPEECH_FRAMES) {
                console.log(`[IMMEDIATE-PROCESS-DEBUG] 🔧 FIX: Final transcript received but speechFramesInCurrentTurn=${state.speechFramesInCurrentTurn}, forcing to ${MIN_SPEECH_FRAMES}`);
                state.speechFramesInCurrentTurn = MIN_SPEECH_FRAMES;
            }

            // Clear pending finalization timeout if transcript arrived
            if (state.pendingFinalizeTimeout) {
                console.log(`[IMMEDIATE-PROCESS-DEBUG] Clearing pending finalization timeout - final transcript arrived`);
                clearTimeout(state.pendingFinalizeTimeout);
                state.pendingFinalizeTimeout = undefined;
            }

            // Process immediately if no finalization is currently in progress
            if (!state.finalizationInProgress) {
                console.log(`[IMMEDIATE-PROCESS-DEBUG] ✅ INSTANT PROCESSING: Final transcript received - processing immediately`);
                console.log(`[FINALIZE-LOCK] Setting finalization lock for call ${callSid} - transcript handler`);
                
                state.finalizationInProgress = true; // Set lock
                
                finalizeTurn(callSid); // Process the completed turn
                
                // Reset turn state after processing
                state.consecutiveSilenceFrames = 0;
                state.speechFramesInCurrentTurn = 0;
                state.hasSpeechDetected = false; 
                
                state.finalizationInProgress = false; // Release lock
                console.log(`[FINALIZE-LOCK] Released finalization lock for call ${callSid} - transcript handler complete`);
            } else {
                console.log(`[IMMEDIATE-PROCESS-DEBUG] ⚠️ Finalization already in progress - transcript handler will skip processing`);
            }
        }
    } else if (TRANSCRIPT_LOG_ALL) {
        // Log partial transcripts if enabled
        const providerTag = state.sttProvider ? `[${STT_PROVIDER.toUpperCase()} Partial]` : '[Partial Transcript]';
        console.log(`${providerTag} Call ${callSid} => ${text}`);
    }
}


//-----------------------------------------------
// Helper: Initialize Azure STT (Skeleton)
//-----------------------------------------------
function initializeAzureStt(callSid: string): SttProvider {
    console.log(`[Azure STT] Initializing for call ${callSid}...`);
    const state = calls[callSid];
    if (!state) {
        throw new Error(`Call state not found for ${callSid} during Azure STT init.`);
    }

    if (!AZURE_SPEECH_KEY || !AZURE_SPEECH_REGION) {
        console.error("Azure Speech Key or Region not configured!");
        throw new Error("Azure Speech Key or Region not configured!");
    }

    // 1. Create PushAudioInputStream
    // Format matches the output of ffmpeg decode process
    const pushStream = sdk.AudioInputStream.createPushStream(
        sdk.AudioStreamFormat.getWaveFormatPCM(8000, 16, 1) // 8kHz, 16-bit, Mono
    );
    state.audioStream = pushStream; // Store stream in state

    // 2. Create SpeechConfig
    const speechConfig = sdk.SpeechConfig.fromSubscription(AZURE_SPEECH_KEY, AZURE_SPEECH_REGION);
    // Optional: Set language explicitly if needed
    // speechConfig.speechRecognitionLanguage = "en-US"; 
    // Optional: Set profanity filter
    speechConfig.setProfanity(sdk.ProfanityOption.Raw); 
    // Optional: Enable detailed output format if needed later
    // speechConfig.outputFormat = sdk.OutputFormat.Detailed;

    // 3. Create AudioConfig from the push stream
    const audioConfig = sdk.AudioConfig.fromStreamInput(pushStream);

    // 4. Create SpeechRecognizer
    const recognizer = new sdk.SpeechRecognizer(speechConfig, audioConfig);
    state.speechRecognizer = recognizer; // Store recognizer in state

    // 5. Setup Event Handlers (Skeleton)
    recognizer.recognizing = (s, e) => {
        // Handle partial results
        if (e.result.reason === sdk.ResultReason.RecognizingSpeech && e.result.text) {
            // console.log(`[Azure STT Recognizing] ${callSid}: ${e.result.text}`);
            handleTranscript(callSid, { text: e.result.text, isFinal: false });
        }
    };

    recognizer.recognized = (s, e) => {
        // Handle final results
        if (e.result.reason === sdk.ResultReason.RecognizedSpeech && e.result.text) {
            console.log(`[Azure STT Recognized] ${callSid}: ${e.result.text}`);
            handleTranscript(callSid, { text: e.result.text, isFinal: true });
        } else if (e.result.reason === sdk.ResultReason.NoMatch) {
            console.log(`[Azure STT NoMatch] ${callSid}: Speech could not be recognized.`);
            // Potentially handle no-match scenarios (e.g., trigger finalize if needed?)
            // If hasSpeechDetected is true but we get NoMatch, maybe finalize with placeholder
             if (state.hasSpeechDetected && state.speechFramesInCurrentTurn >= MIN_SPEECH_FRAMES) {
                 console.log(`[Azure STT NoMatch] Detected speech but no transcript, finalizing with placeholder.`);
                 state.currentTurnText = "[Speech detected but not transcribed]";
                 // Finalize immediately if VAD hasn't already
                 if (!state.finalizationInProgress) {
                    state.finalizationInProgress = true;
                    finalizeTurn(callSid);
                    state.consecutiveSilenceFrames = 0;
                    state.speechFramesInCurrentTurn = 0;
                    state.hasSpeechDetected = false;
                    state.finalizationInProgress = false;
                 }
             }
        }
    };

    recognizer.canceled = (s, e) => {
        console.error(`[Azure STT Canceled] ${callSid}: Reason=${e.reason}`);
        if (e.reason === sdk.CancellationReason.Error) {
            console.error(`[Azure STT Canceled] ${callSid}: ErrorCode=${e.errorCode}, Details=${e.errorDetails}`);
        }
        // Consider cleanup or error handling
        // Maybe attempt to stop recognition gracefully
        try {
            recognizer.stopContinuousRecognitionAsync(
                () => console.log(`[Azure STT] Stop recognition requested due to cancel.`),
                (err) => console.error(`[Azure STT] Error stopping recognition on cancel: ${err}`)
            );
        } catch(err) {
            console.error(`[Azure STT] Exception stopping recognition on cancel: ${err}`);
        }
    };

    recognizer.sessionStarted = (s, e) => {
        console.log(`[Azure STT SessionStarted] ${callSid}: SessionId=${e.sessionId}`);
    };

    recognizer.sessionStopped = (s, e) => {
        console.log(`[Azure STT SessionStopped] ${callSid}: SessionId=${e.sessionId}`);
        // Clean up resources related to this session if needed
        // Note: 'close' method handles main cleanup
    };

    // 6. Start Continuous Recognition
    recognizer.startContinuousRecognitionAsync(
        () => console.log(`[Azure STT] Continuous recognition started for ${callSid}`),
        (err) => console.error(`[Azure STT] Error starting recognition for ${callSid}: ${err}`)
    );

    // 7. Return the provider interface implementation
    return {
        sendAudio: (chunk: Buffer) => {
            // Push the 16-bit PCM chunk directly into the stream
             try {
                if (state.audioStream) {
                    state.audioStream.write(chunk);
                }
             } catch (error) {
                console.error(`[Azure STT] Error writing audio chunk for ${callSid}:`, error);
             }
        },
        close: () => {
            console.log(`[Azure STT] Closing resources for call ${callSid}...`);
             try {
                if (state.speechRecognizer) {
                    // Stop recognition
                    state.speechRecognizer.stopContinuousRecognitionAsync(
                        () => { 
                            console.log(`[Azure STT] Recognition stopped for ${callSid}`);
                            // Close recognizer AFTER stopping
                            state.speechRecognizer?.close(); 
                            state.speechRecognizer = undefined;
                        },
                        (err) => {
                            console.error(`[Azure STT] Error stopping recognition for ${callSid}: ${err}`);
                            // Still try to close recognizer
                            state.speechRecognizer?.close();
                            state.speechRecognizer = undefined;
                        }
                    );
                }
                // Close the audio stream
                if (state.audioStream) {
                    state.audioStream.close();
                    state.audioStream = undefined;
                }
             } catch (error) {
                 console.error(`[Azure STT] Error during close for ${callSid}:`, error);
                 // Ensure resources are cleared even if errors occur
                 if (state.speechRecognizer) {
                     try { state.speechRecognizer.close(); } catch {}
                     state.speechRecognizer = undefined;
                 }
                 if (state.audioStream) {
                     try { state.audioStream.close(); } catch {}
                     state.audioStream = undefined;
                 }
             }
        }
    };
}

//-----------------------------------------------
// Helper: End-of-call cleanup
//-----------------------------------------------
function cleanupCall(callSid: string) {
  const state = calls[callSid];
  if (!state) { 
    console.warn(`Cannot clean up call ${callSid} - state not found`);
    return;
  }

  console.log(`Initiating cleanup for call ${callSid}...`);

  // --- BEGIN UPDATE CALL HISTORY ON END ---
  const callEndTime = new Date();
  
  const callDurationSeconds = state.callStartTime ? 
    Math.floor((callEndTime.getTime() - state.callStartTime.getTime()) / 1000) : null;
  
  console.log(`Call ${callSid} ended. Duration: ${callDurationSeconds}s`);
  
  // Get recording storage path if available
  const recordingPath = state.recordingStoragePath || null;
  
  // Update the call record non-blockingly
  updateCallRecordOnEnd(callSid, callEndTime, callDurationSeconds, recordingPath).catch(err => {
    console.error(`[DB] Failed to update call record on end for ${callSid}:`, err);
  });
  // --- END UPDATE CALL HISTORY ON END ---

  // Close recordFfmpeg
  if (state.recordFfmpeg) {
    console.log(`[Cleanup ${callSid}] Closing recordFfmpeg...`);
    state.recordFfmpeg.stdin.end();

    // Wait for the ffmpeg process to close to ensure the file is written
    state.recordFfmpeg.on('close', async (code) => {
      console.log(`recordFfmpeg for ${callSid} exited with code ${code}`);

      let uploadSuccess = false;
      let fileToUploadPath: string | null = null;
      let targetFileName: string = `${callSid}.wav`; // Default to original if mixing fails or isn't needed

      if (code === 0) {
        const mainRecordingPath = path.join(__dirname, `../recordings/${callSid}_caller.wav`);
        const mixedOutputPath = path.join(__dirname, `../recordings/${callSid}.wav`);

        // --- Start Precise Mixing Logic ---
        const ttsInputs: { timing: typeof state.turnTimings[0]; path: string }[] = []; // Holds validated TTS info
 
        if (fs.existsSync(mainRecordingPath) && state.turnTimings && state.turnTimings.length > 0 && state.callStartTime) {
          // Use the more accurate recordingActualStartTime if available, otherwise fallback to callStartTime
          const recordingStartMs = state.recordingActualStartTime || state.callStartTime.getTime();
          console.log(`[Mixer ${callSid}] Effective recording start time used for mixing: ${recordingStartMs} (Actual: ${state.recordingActualStartTime}, CallStart: ${state.callStartTime.getTime()})`);
 
          for (const timing of state.turnTimings) {
            // Check if this turn has the necessary info for mixing
            if (timing.audioStreamingStartAt && timing.ttsFilename) {
              try {
                const ttsFilePath = path.join(AUDIO_DIR, timing.ttsFilename);
 
                if (fs.existsSync(ttsFilePath)) {
                  console.log(`[Mixer ${callSid}] Found TTS file ${timing.ttsFilename} for turn ${timing.turnIndex}.`);
                  ttsInputs.push({ timing, path: ttsFilePath });
                } else {
                  console.warn(`[Mixer ${callSid}] TTS file ${timing.ttsFilename} for turn ${timing.turnIndex} not found.`);
                }
              } catch (err) {
                console.error(`[Mixer ${callSid}] Error accessing TTS file for turn ${timing.turnIndex}:`, err);
              }
            } else {
              console.log(`[Mixer ${callSid}] Skipping turn ${timing.turnIndex} for mixing - missing audioStreamingStartAt or ttsFilename.`);
            }
          }
 
          // Proceed with mixing if we found TTS files to mix
          if (ttsInputs.length > 0) {
            const inputArgs: string[] = ['-i', mainRecordingPath];
            const filterComplexParts: string[] = [];
 
            ttsInputs.forEach((ttsInput, index) => {
              const streamIndex = index + 1; // 0 is main recording
              inputArgs.push('-i', ttsInput.path);
 
              // Calculate delay
              const delayMs = Math.max(0, ttsInput.timing.audioStreamingStartAt! - recordingStartMs);
 
              // Check for interruption and build filter chain
              let streamFilter = ''
              if (ttsInput.timing.interruptionTimestamp && ttsInput.timing.interruptionTimestamp > ttsInput.timing.audioStreamingStartAt!) {
                const playDurationMs = ttsInput.timing.interruptionTimestamp - ttsInput.timing.audioStreamingStartAt!;
                const playDurationSec = (playDurationMs / 1000).toFixed(6);
                
                // Debug logging for interruption details
                console.log(`[Mixer Debug ${callSid}] Turn ${ttsInput.timing.turnIndex} Interruption Details:`);
                console.log(`  audioStreamingStartAt: ${ttsInput.timing.audioStreamingStartAt}`);
                console.log(`  interruptionTimestamp: ${ttsInput.timing.interruptionTimestamp}`);
                console.log(`  Calculated playDurationMs: ${playDurationMs}`);
                console.log(`  Calculated playDurationSec: ${playDurationSec}`);
                
                console.log(`[Mixer ${callSid}] Turn ${ttsInput.timing.turnIndex} interrupted. Applying atrim=0:${playDurationSec}`);
                streamFilter = `[${streamIndex}:a]atrim=0:${playDurationSec},adelay=${delayMs}|${delayMs}[d${streamIndex}]`;
              } else {
                // No interruption, just apply delay
                streamFilter = `[${streamIndex}:a]adelay=${delayMs}|${delayMs}[d${streamIndex}]`;
              }
              filterComplexParts.push(streamFilter);
            });
 
            // Build the final amix part
            const mixInputs = ttsInputs.map((_, index) => `[d${index + 1}]`).join('');
            const amixFilter = `[0:a]${mixInputs}amix=inputs=${ttsInputs.length + 1}[a]`;
            filterComplexParts.push(amixFilter);
 
            const ffmpegArgs: string[] = [
              ...inputArgs,
              '-filter_complex', filterComplexParts.join(';'),
              '-map', '[a]', // Map the final mixed output
              '-ac', '1', // Force mono
              '-ar', '8000', // Ensure output sample rate is 8kHz
              '-acodec', 'pcm_mulaw', // Output µ-law encoded WAV
              mixedOutputPath
            ];

            console.log(`[Mixer ${callSid}] Starting precise ffmpeg mixing process...`);
            console.log(`[Mixer ${callSid}] ffmpeg command args: ${ffmpegArgs.join(' ')}`); // Log command for debug

            try {
              await new Promise<void>((resolve, reject) => {
                const mixFfmpeg = spawn('ffmpeg', ffmpegArgs);

                let ffmpegStderr = '';
                mixFfmpeg.stderr.on('data', (data) => { // Capture stderr
                   ffmpegStderr += data.toString();
                });

                mixFfmpeg.on('close', (mixCode) => {
                  // Log full FFmpeg stderr output
                  console.log(`[Mixer ${callSid}] FFmpeg process finished with code: ${mixCode}`);
                  if (ffmpegStderr.length > 0) {
                    console.log(`[Mixer ${callSid}] Full FFmpeg stderr output:\n>>>>>>>>>>\n${ffmpegStderr}\n<<<<<<<<<<`);
                  } else {
                    console.log(`[Mixer ${callSid}] No stderr output captured from FFmpeg.`);
                  }
                  
                  if (mixCode === 0) {
                    console.log(`[Mixer ${callSid}] Successfully created mixed recording: ${mixedOutputPath}`);
                    fileToUploadPath = mixedOutputPath;
                    targetFileName = `${callSid}.wav`; // Set target for upload
                    resolve();
                  } else {
                    console.error(`[Mixer ${callSid}] ffmpeg mixing process failed with code ${mixCode}. Uploading original.`);
                    console.error(`[Mixer ${callSid}] ffmpeg stderr: ${ffmpegStderr}`); // Log captured stderr on error
                    fileToUploadPath = mainRecordingPath; // Fallback to original
                    targetFileName = `${callSid}_caller.wav`;
                    resolve(); // Resolve even on failure to allow original upload
                  }
                });

                mixFfmpeg.on('error', (mixErr) => {
                  console.error(`[Mixer ${callSid}] Failed to start ffmpeg mixing process:`, mixErr);
                  fileToUploadPath = mainRecordingPath; // Fallback to original
                  targetFileName = `${callSid}_caller.wav`;
                  resolve(); // Resolve even on failure
                });
              });
            } catch (mixErr) {
              console.error(`[Mixer ${callSid}] Error during mixing process:`, mixErr);
              fileToUploadPath = mainRecordingPath; // Fallback to original
              targetFileName = `${callSid}_caller.wav`;
            }
          } else {
            console.warn(`[Mixer ${callSid}] Skipping mixing: Main recording not found, missing timing data, or necessary start timestamps.`);
            // Determine fallback upload path even if mixing is skipped
            fileToUploadPath = fs.existsSync(mainRecordingPath) ? mainRecordingPath : null;
            targetFileName = fs.existsSync(mainRecordingPath) ? `${callSid}_caller.wav` : `${callSid}.wav`;
          }
          // --- End Precise Mixing Logic ---

          // --- Start Upload Logic --- (Modified for Opus Transcoding)
          if (fileToUploadPath && fs.existsSync(fileToUploadPath)) {
            const opusOutputPath = fileToUploadPath.replace(/\.wav$/, '.opus');
            const targetOpusFileName = targetFileName.replace(/\.wav$/, '.opus');
            let transcodingSuccess = false;

            try {
              console.log(`[Opus Transcode] Transcoding ${fileToUploadPath} to ${opusOutputPath}...`);
              await new Promise<void>((resolve, reject) => {
                const transcodeFfmpeg = spawn('ffmpeg', [
                  '-i', fileToUploadPath,        // Input WAV file
                  '-acodec', 'libopus',        // Output Opus codec
                  '-b:a', '16k',             // Bitrate (e.g., 16kbps for good quality voice)
                  '-application', 'voip',     // Optimize for voice
                  '-f', 'opus',              // Output format
                  opusOutputPath
                ]);

                let ffmpegStderr = '';
                transcodeFfmpeg.stderr.on('data', (data) => { ffmpegStderr += data.toString(); });

                transcodeFfmpeg.on('close', (transcodeCode) => {
                  // Log full FFmpeg stderr output
                  console.log(`[Opus Transcode] FFmpeg process finished with code: ${transcodeCode}`);
                  if (ffmpegStderr.length > 0) {
                    console.log(`[Opus Transcode] Full FFmpeg stderr output:\n>>>>>>>>>>\n${ffmpegStderr}\n<<<<<<<<<<`);
                  } else {
                    console.log(`[Opus Transcode] No stderr output captured from FFmpeg.`);
                  }
                  
                  if (transcodeCode === 0) {
                    console.log(`[Opus Transcode] Successfully transcoded to ${opusOutputPath}`);
                    fileToUploadPath = opusOutputPath;
                    targetFileName = targetOpusFileName; // Set target for upload
                    transcodingSuccess = true;
                    resolve();
                  } else {
                    console.error(`[Opus Transcode] ffmpeg transcoding failed with code ${transcodeCode}. Uploading original.`);
                    console.error(`[Opus Transcode] ffmpeg stderr: ${ffmpegStderr}`); // Log captured stderr on error
                    fileToUploadPath = mainRecordingPath; // Fallback to original
                    targetFileName = `${callSid}_caller.wav`;
                    resolve(); // Resolve even on failure to allow original upload
                  }
                });

                transcodeFfmpeg.on('error', (transcodeErr) => {
                  console.error(`[Opus Transcode] Failed to start ffmpeg transcoding process:`, transcodeErr);
                  fileToUploadPath = mainRecordingPath; // Fallback to original
                  targetFileName = `${callSid}_caller.wav`;
                  resolve(); // Resolve even on failure
                });
              });
            } catch (err) {
              console.error(`[Opus Transcode/Upload] Error during process for ${callSid}:`, err);
            }
            // If transcoding was successful, proceed with upload
            if (transcodingSuccess && fs.existsSync(opusOutputPath)) {
              console.log(`[Supabase Storage] Attempting to upload ${opusOutputPath} as ${targetOpusFileName}...`);
              const fileContent = fs.readFileSync(opusOutputPath);
              const { data: uploadData, error: uploadError } = await supabase.storage
                .from('call_recordings')
                .upload(targetOpusFileName, fileContent, {
                  contentType: 'audio/opus', // Set correct content type
                  upsert: true
                });

              if (uploadError) {
                console.error(`[Supabase Storage] Error uploading ${targetOpusFileName}:`, uploadError);
              } else {
                console.log(`[Supabase Storage] Successfully uploaded ${targetOpusFileName} from ${opusOutputPath}`);
                uploadSuccess = true; // Mark overall success
                  
                // Store just the storage path for the Opus recording
                const storagePath = `call_recordings/${targetOpusFileName}`;
                state.recordingStoragePath = storagePath;
                console.log(`[Supabase Storage] Stored storage path: ${storagePath}`);
                  
                // Update the database with the recording path
                const updateTime = new Date();
                updateCallRecordingPath(callSid, storagePath, updateTime).catch(err => {
                  console.error(`[DB] Failed to update recording path for ${callSid}:`, err);
                });
              }
            } else {
               console.warn(`[Supabase Storage] Opus file not found or transcoding failed: ${opusOutputPath}`);
            }
          } else {
             console.warn(`[Supabase Storage] WAV file to transcode not found or not specified: ${fileToUploadPath}`);
          }
          // --- End Upload Logic ---

        } else {
          console.warn(`[Supabase Storage] Skipping mixing/upload for ${callSid} because recording ffmpeg exited with code ${code}`);
        }
        
        // --- Start Cleanup Logic ---
        // This cleanup should only happen if the initial ffmpeg process was successful (code === 0)
        // and the upload was also successful.
        if (uploadSuccess) { // Only cleanup local files if upload was successful
           cleanupLocalFiles(callSid, state.turnTimings);
        } else {
          // Log if cleanup is skipped due to upload failure, even if ffmpeg succeeded
          if (code === 0) { 
              console.warn(`[Cleanup ${callSid}] Skipping local file cleanup because Supabase upload failed or was skipped (ffmpeg code: ${code}).`);
          }
          // No cleanup needed if ffmpeg failed (code !== 0) as upload wouldn't have happened.
        }
        // --- End Cleanup Logic ---

      } else {
        console.warn(`[Supabase Storage] Skipping mixing/upload for ${callSid} because recording ffmpeg exited with code ${code}`);
      }
      
      // --- Start Cleanup Logic ---
      // This cleanup should only happen if the initial ffmpeg process was successful (code === 0)
      // and the upload was also successful.
      if (uploadSuccess) { // Only cleanup local files if upload was successful
         cleanupLocalFiles(callSid, state.turnTimings);
      } else {
        // Log if cleanup is skipped due to upload failure, even if ffmpeg succeeded
        if (code === 0) { 
            console.warn(`[Cleanup ${callSid}] Skipping local file cleanup because Supabase upload failed or was skipped (ffmpeg code: ${code}).`);
        }
        // No cleanup needed if ffmpeg failed (code !== 0) as upload wouldn't have happened.
      }
      // --- End Cleanup Logic ---

    });

    state.recordFfmpeg = undefined;
  }
  // Close decodeFfmpeg
  if (state.decodeFfmpeg) {
    console.log(`[Cleanup ${callSid}] Closing decodeFfmpeg...`);
    state.decodeFfmpeg.stdin.end()
    state.decodeFfmpeg = undefined
  }
  // Close the active STT provider
  if (state.sttProvider) {
    console.log(`[Cleanup ${callSid}] Closing STT provider (${STT_PROVIDER})...`);
    try {
      state.sttProvider.close();
    } catch (err) {
      console.error(`[Cleanup ${callSid}] Error closing STT provider:`, err);
    }
    state.sttProvider = undefined;
  } else {
    console.log(`[Cleanup ${callSid}] No active STT provider found to close.`);
  }
  
  // Kill any current audio streaming process (TTS playback)
  if (state.currentFfmpegProcess) {
    console.log(`[Cleanup ${callSid}] Killing current audio streaming process...`);
    try {
      state.currentFfmpegProcess.kill();
    } catch (err) {
      console.error(`[Cleanup ${callSid}] Error killing TTS ffmpeg process: ${err}`);
    }
    state.currentFfmpegProcess = undefined;
  }
  
  // Clear any audio duration timeout
  if (state.currentSpeakingTimeout) {
    console.log(`[Cleanup ${callSid}] Clearing audio duration timeout...`);
    clearTimeout(state.currentSpeakingTimeout);
    state.currentSpeakingTimeout = undefined;
  }

  // Clear any pending finalization timeout
  if (state.pendingFinalizeTimeout) {
    console.log(`[Cleanup ${callSid}] Clearing pending finalization timeout...`);
    clearTimeout(state.pendingFinalizeTimeout);
    state.pendingFinalizeTimeout = undefined;
  }

  // If there's any partial turn not ended, finalize it
  const { currentTurnText, conversation } = state
  if (currentTurnText.trim()) {
    console.log(`[Cleanup ${callSid}] Saving final partial turn: "${currentTurnText.trim()}"`);
    conversation.push({ speaker: 'caller', text: currentTurnText.trim() })
    // Save the final conversation state
    saveConversation(callSid, conversation);
  }

  // Log all turn timings for the completed call
  console.log(`
=============== CALL TIMING SUMMARY (${callSid}) ===============`);
  if (state.turnTimings && state.turnTimings.length > 0) {
    const calcDuration = (t1?: number, t2?: number) => (
      t1 && t2 ? `${((t2 - t1) / 1000).toFixed(2)}s` : 'N/A'
    );

    state.turnTimings.forEach(timings => {
      console.log(`
--- Turn ${timings.turnIndex} ---`);
      console.log(`  Silence -> Transcript: ${calcDuration(timings.silenceDetectedAt, timings.transcriptReceivedAt)}`);
      console.log(`  Transcript -> RAG Req: ${calcDuration(timings.transcriptReceivedAt, timings.ragRequestStartAt)}`);
      console.log(`  RAG Req -> RAG Res:  ${calcDuration(timings.ragRequestStartAt, timings.ragResponseReceivedAt)}`);
      console.log(`  RAG Res -> TTS Start: ${calcDuration(timings.ragResponseReceivedAt, timings.audioGenerationStartAt)}`);
      console.log(`  TTS Start -> TTS End:  ${calcDuration(timings.audioGenerationStartAt, timings.audioGenerationEndAt)}`);
      console.log(`  TTS End -> Stream Start: ${calcDuration(timings.audioGenerationEndAt, timings.audioStreamingStartAt)}`);
      console.log(`  Stream Start -> End: ${calcDuration(timings.audioStreamingStartAt, timings.audioStreamingEndAt)}`);
    });
  } else {
    console.log("No turn timing data recorded for this call.");
  }
  console.log(`=================== END CALL SUMMARY (${callSid}) ===================\n`);

  // Reset media packet counter
  if (mediaPacketCounts[callSid]) {
    delete mediaPacketCounts[callSid];
  }

  // Note: Audio and conversation file cleanup is now handled within the recordFfmpeg close handler
  // to ensure mixing and uploading complete first.

  // Delete the call state
  delete calls[callSid];
  console.log(`Cleanup complete for call ${callSid}. State deleted.`);
}

// Function to clean up audio files (TTS responses + recordings) for a specific call
// Should be called AFTER mixing/uploading is done.
function cleanupLocalFiles(callSid: string, completedTurnTimings: typeof calls[string]['turnTimings']) {
  console.log(`[Cleanup ${callSid}] Cleaning up local audio and conversation files...`);
  // Clean specific TTS response files used in the call (these are WAV)
  if (completedTurnTimings && completedTurnTimings.length > 0) {
    completedTurnTimings.forEach(timing => {
      if (timing.ttsFilename) {
        const filePath = path.join(AUDIO_DIR, timing.ttsFilename);
        try {
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log(`[Cleanup ${callSid}] Deleted used TTS file: ${filePath}`);
          }
        } catch (err) {
          console.error(`[Cleanup ${callSid}] Failed to delete TTS file ${filePath}:`, err);
        }
      }
    });
  }

  // Clean recording files (original WAV, mixed WAV, and final Opus)
  const recordingsDir = path.join(__dirname, '../recordings');
  if (fs.existsSync(recordingsDir)) {
    const recordingFiles = fs.readdirSync(recordingsDir);
    for (const file of recordingFiles) {
      if (file.startsWith(callSid) && (file.endsWith('.wav') || file.endsWith('.opus'))) { 
        const filePath = path.join(recordingsDir, file);
        try {
          fs.unlinkSync(filePath);
          console.log(`[Cleanup ${callSid}] Deleted recording file: ${filePath}`);
        } catch (err) {
          console.error(`[Cleanup ${callSid}] Failed to delete recording file ${filePath}:`, err);
        }
      }
    }
  }

  // Clean up conversation files
  if (!fs.existsSync(CONVERSATIONS_DIR)) return;
  try {
    const filePath = path.join(CONVERSATIONS_DIR, `${callSid}.json`);
    if (fs.existsSync(filePath)) {
      const completedDir = path.join(CONVERSATIONS_DIR, 'completed');
      if (!fs.existsSync(completedDir)) {
        fs.mkdirSync(completedDir, { recursive: true });
      }
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const newPath = path.join(completedDir, `${callSid}_${timestamp}.json`);
      fs.renameSync(filePath, newPath);
      console.log(`[Cleanup ${callSid}] Moved conversation file to completed directory`);
    }
  } catch (err) {
    console.error(`[Cleanup ${callSid}] Failed to cleanup conversation file:`, err);
  }
}

// Set up periodic audio file cleanup in case of abrupt server termination
function setupPeriodicCleanup() {
  // Clean up audio files older than 1 hour every 15 minutes
  setInterval(() => {
    try {
      if (!fs.existsSync(AUDIO_DIR)) return;
      
      const files = fs.readdirSync(AUDIO_DIR);
      const now = Date.now();
      const ONE_HOUR = 60 * 60 * 1000;
      
      for (const file of files) {
        const filePath = path.join(AUDIO_DIR, file);
        const stats = fs.statSync(filePath);
        
        // If file is older than 1 hour, delete it
        if (now - stats.mtimeMs > ONE_HOUR) {
          try {
            fs.unlinkSync(filePath);
            console.log(`Deleted old audio file: ${filePath}`);
          } catch (err) {
            console.error(`Failed to delete old audio file ${filePath}:`, err);
          }
        }
      }
    } catch (err) {
      console.error('Error in periodic cleanup:', err);
    }
  }, 15 * 60 * 1000); // Run every 15 minutes
}

// Handle process termination signals
process.on('SIGINT', () => {
  console.log('Server shutting down, cleaning up audio files...');
  cleanupAllAudioFiles();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Server shutting down, cleaning up audio files...');
  cleanupAllAudioFiles();
  process.exit(0);
});

// Function to clean up all audio files
function cleanupAllAudioFiles() {
  if (!fs.existsSync(AUDIO_DIR)) return;
  
  try {
    const files = fs.readdirSync(AUDIO_DIR);
    for (const file of files) {
      const filePath = path.join(AUDIO_DIR, file);
      try {
        fs.unlinkSync(filePath);
      } catch (e) {
        console.error(`Failed to delete ${filePath}:`, e);
      }
    }
    console.log('All audio files cleaned up');
  } catch (err) {
    console.error('Error cleaning up audio files:', err);
  }
}

//-----------------------------------------------
// 3) handleVadAndTurns => splits chunk, calls VAD (No changes needed here for STT provider switch)
//-----------------------------------------------

async function handleVadAndTurns(callSid: string, chunk: Buffer) {
  const state = calls[callSid];
  if (!state) return;

  // Properly calculate frames with consistent byte size
  let leftover = state.leftover;
  const framesPlusLeftover = splitTo20msFrames(chunk, leftover);
  leftover = framesPlusLeftover.pop()!;
  const frames = framesPlusLeftover;
  state.leftover = leftover;

  // Initialize speech frames counter if it doesn't exist
  if (state.speechFramesInCurrentTurn === undefined) {
    state.speechFramesInCurrentTurn = 0;
  }

  // Track VAD results to reduce logging
  let speechFrames = 0;
  let silenceFrames = 0;
  let energyDetectionCount = 0; // Count energy-based detections vs VAD

  // Check for user interruption - process frames even while model is speaking
  // to detect if user is interrupting
  let userInterruptDetected = false;
  // Make interruption detection super aggressive - detect with just 1 frame of speech
  const INTERRUPT_SPEECH_THRESHOLD = 1;
  const interruptDetectionFrames = [];
  
  // Track consecutive speech frames - only reset when we get several silence frames
  const RESET_INTERRUPT_SILENCE_THRESHOLD = 2; // Reduced from 3 - Reset interruption detection after fewer silence frames
  let consecutiveSilenceFrames = 0;

  for (const frame of frames) {
    try {
      // Verify frame is the correct size (320 bytes for 20ms of 8kHz, 16-bit mono audio)
      // If not, log and skip to prevent sending malformed data to VAD
      if (frame.length !== 320 && frame !== leftover) {
        console.warn(`[VAD] Skipping abnormal frame size: ${frame.length} bytes`);
        continue;
      }

      // Add timeout to prevent hanging on VAD requests
      // Include callSid in the URL query parameters
      const vadUrl = `${VAD_ENDPOINT}?callSid=${encodeURIComponent(callSid)}`;
      const response = await axios.post(vadUrl, frame, {
        headers: { 'Content-Type': 'application/octet-stream' },
        timeout: 1000, // Reduced timeout for faster failure detection
        validateStatus: (status) => status === 200, // Only accept 200 responses
      });
      
      // Check for valid response format
      if (!response.data) {
        console.warn(`[VAD] Empty response from VAD server`);
        continue;
      }
      
      // Enhanced response validation
      const data = response.data;

      // Check if we're getting info messages (collecting short frames)
      if (data.info && data.info.includes("Collecting short frames")) {
        // This is not a real speech detection response, just a status update
        continue;
      }
      
      // Track energy detection vs VAD - used for logging
      if (data.method === 'energy' || data.method === 'energy_fallback') {
        energyDetectionCount++;
      }
      
      // Check for errors in the response
      if (data.error) {
        console.warn(`[VAD] Server reported error: ${data.error}`);
      }
      
      // Validate the speech field exists
      if (typeof data.speech !== 'boolean') {
        console.warn(`[VAD] Invalid response format from VAD server: ${JSON.stringify(data)}`);
        continue;
      }

      // Track if we detect consistent speech during model's speaking turn
      if (state.isModelSpeaking) {
      if (data.speech) {
          interruptDetectionFrames.push(true);
          consecutiveSilenceFrames = 0; // Reset silence counter
          
          // Enhanced logging for interrupt detection
          if (interruptDetectionFrames.length === 1) {
            console.log(`[INTERRUPT-DEBUG] Call ${callSid} - First speech frame detected while model speaking (method: ${data.method || 'unknown'})`);
          }
          
          // Log progress toward interruption detection
          if (VERBOSE_VAD_LOGGING) {
            console.log(`[VAD] Call ${callSid} - Potential interruption - Speech detected during model speaking (frame ${interruptDetectionFrames.length})`);
          }
          
          // If we've detected enough consecutive speech frames while model is speaking,
          // treat it as an interruption - now just 1 frame!
          if (interruptDetectionFrames.length >= INTERRUPT_SPEECH_THRESHOLD) {
            userInterruptDetected = true;
            console.log(`[INTERRUPT-DEBUG] Call ${callSid} - INTERRUPTION DETECTED - Threshold met: ${interruptDetectionFrames.length}/${INTERRUPT_SPEECH_THRESHOLD} frames`);
            console.log(`[VAD] Call ${callSid} - USER INTERRUPTION DETECTED - Stopping assistant's turn after ${interruptDetectionFrames.length} speech frames`);
            
            // Handle the interruption immediately
            console.log(`[INTERRUPT-DEBUG] Handling interruption IMMEDIATELY to prevent delays`);
            handleInterruption(callSid);
            
            // Since we've handled the interruption, reset model speaking state immediately
            state.isModelSpeaking = false;
            console.log(`[INTERRUPT-DEBUG] Force reset isModelSpeaking to false`);
            
            // Reset detection frames
            interruptDetectionFrames.length = 0;
          }
        } else {
          // Count consecutive silence frames
          consecutiveSilenceFrames++;
          
          // Only reset detection frames if we've had enough consecutive silence
          if (consecutiveSilenceFrames >= RESET_INTERRUPT_SILENCE_THRESHOLD) {
            if (interruptDetectionFrames.length > 0 && VERBOSE_VAD_LOGGING) {
              console.log(`[VAD] Call ${callSid} - Resetting interruption detection after ${consecutiveSilenceFrames} silence frames`);
            }
            interruptDetectionFrames.length = 0;
            consecutiveSilenceFrames = 0;
          }
        }
      }

      // Normal turn processing (don't process if model is still speaking and no interruption)
      if (!state.isModelSpeaking || userInterruptDetected) {
        if (data.speech) {
          speechFrames++;
          // Reset silence counter when speech is detected
        state.consecutiveSilenceFrames = 0;
          // Increment speech counter to track meaningful speech
          // Add a larger increment to reach MIN_SPEECH_FRAMES faster
          // This ensures the first detected speech frame immediately enables transcript processing
          state.speechFramesInCurrentTurn += 5; // Increment by 5 to reach threshold immediately
          
          // Mark that we've detected speech for this turn, even if no transcript yet
          state.hasSpeechDetected = true;
          
          // Log first detected speech in a turn
          if (state.speechFramesInCurrentTurn <= 5 && VERBOSE_VAD_LOGGING) {
            console.log(`[VAD] Call ${callSid} - SPEECH DETECTED (method: ${data.method || 'unknown'}) - Starting new turn`);
            console.log(`[VAD] Call ${callSid} - Ensuring MIN_SPEECH_FRAMES threshold met immediately - set to ${state.speechFramesInCurrentTurn} frames`);
          }
          
          // Log when significant speech is detected
          if (state.speechFramesInCurrentTurn >= MIN_SPEECH_FRAMES && state.speechFramesInCurrentTurn <= MIN_SPEECH_FRAMES + 5 && VERBOSE_VAD_LOGGING) {
            console.log(`[VAD] Call ${callSid} - SIGNIFICANT SPEECH DETECTED - ${state.speechFramesInCurrentTurn} frames of speech (threshold: ${MIN_SPEECH_FRAMES})`);
          }
      } else {
          silenceFrames++;
        state.consecutiveSilenceFrames++;
          
          // Log silence detection progress
          if (VERBOSE_VAD_LOGGING && state.speechFramesInCurrentTurn >= MIN_SPEECH_FRAMES) {
            // Only log at certain thresholds to avoid console spam
            const silenceThresholds = [20, 40, 60, 80, 100, 120, 140, 160, 180, SILENCE_FRAMES_THRESHOLD];
            if (silenceThresholds.includes(state.consecutiveSilenceFrames)) {
              console.log(`[VAD] Call ${callSid} - SILENCE DETECTED - ${state.consecutiveSilenceFrames} frames of silence (threshold: ${SILENCE_FRAMES_THRESHOLD})`);
            }
          }
          
          // Only consider finalizing turn if we've detected enough meaningful speech first
          if (state.speechFramesInCurrentTurn >= MIN_SPEECH_FRAMES) {
            // If we've detected a significant amount of speech followed by silence
        if (state.consecutiveSilenceFrames >= SILENCE_FRAMES_THRESHOLD) {
              // Record silence detection time
              const currentTiming = state.turnTimings[state.currentTurnIndex] || { turnIndex: state.currentTurnIndex };
              if (!currentTiming.silenceDetectedAt) { // Only set once per turn
                currentTiming.silenceDetectedAt = Date.now();
                state.turnTimings[state.currentTurnIndex] = currentTiming;
              }
              
              // Check if this is a long pause or medium pause
              if (state.consecutiveSilenceFrames >= LONG_PAUSE_THRESHOLD) {
                // Long pause - definitely end the turn
                console.log(`[VAD] Call ${callSid} - LONG PAUSE DETECTED (${state.consecutiveSilenceFrames} frames) - Finalizing turn`);
                
                // CRITICAL FIX: Set lock flag to prevent duplicate finalization
                if (!state.finalizationInProgress) {
                  state.finalizationInProgress = true;
                  console.log(`[FINALIZE-LOCK] Setting finalization lock for call ${callSid} - VAD long pause`);
                  
                  // Allow more time for transcript to arrive before finalizing
                  setTimeout(async () => {
                    // Only finalize if no transcript has processed it already
                    if (state.finalizationInProgress) {
          await finalizeTurn(callSid);
          state.consecutiveSilenceFrames = 0;
                      state.speechFramesInCurrentTurn = 0;
                      state.hasSpeechDetected = false; // Reset speech detected flag
                      state.finalizationInProgress = false; // Release the lock
                      console.log(`[FINALIZE-LOCK] Released finalization lock for call ${callSid} - VAD turn complete`);
                    } else {
                      console.log(`[FINALIZE-LOCK] Turn already finalized for call ${callSid} - VAD skipping`);
                    }
                  }, 1); // Wait extra 500ms for transcript to arrive
                } else {
                  console.log(`[FINALIZE-LOCK] Finalization already in progress for call ${callSid} - VAD skipping`);
                }
              } else {
                // Medium pause - end the turn if there's enough content
                if (state.currentTurnText.trim().length > 0) {
                  console.log(`[VAD] Call ${callSid} - MEDIUM PAUSE WITH CONTENT - Finalizing turn with "${state.currentTurnText.trim()}"`);
                  
                  // CRITICAL FIX: Set lock flag to prevent duplicate finalization
                  if (!state.finalizationInProgress) {
                    state.finalizationInProgress = true;
                    console.log(`[FINALIZE-LOCK] Setting finalization lock for call ${callSid} - VAD medium pause`);
                    
                    // Allow more time for transcript to arrive before finalizing
                    setTimeout(async () => {
                      // Only finalize if no transcript has processed it already
                      if (state.finalizationInProgress) {
                        await finalizeTurn(callSid);
                        state.consecutiveSilenceFrames = 0;
                        state.speechFramesInCurrentTurn = 0;
                        state.hasSpeechDetected = false; // Reset speech detected flag
                        state.finalizationInProgress = false; // Release the lock
                        console.log(`[FINALIZE-LOCK] Released finalization lock for call ${callSid} - VAD turn complete`);
                      } else {
                        console.log(`[FINALIZE-LOCK] Turn already finalized for call ${callSid} - VAD skipping`);
                      }
                    }, 500); // Wait extra 500ms for transcript to arrive
                  } else {
                    console.log(`[FINALIZE-LOCK] Finalization already in progress for call ${callSid} - VAD skipping`);
                  }
                } else if (state.hasSpeechDetected) {
                  // We detected speech but no transcript yet
                  console.log(`[VAD] Call ${callSid} - MEDIUM PAUSE WITH DETECTED SPEECH BUT NO TRANSCRIPT YET - Setting delayed finalization`);
                  
                  // Clear any existing timeout
                  if (state.pendingFinalizeTimeout) {
                    clearTimeout(state.pendingFinalizeTimeout);
                    state.pendingFinalizeTimeout = undefined;
                  }
                  
                  // Set a timeout to finalize this turn even if no transcript arrives
                  state.pendingFinalizeTimeout = setTimeout(async () => {
                    // Only proceed if we still have no transcript but detected speech
                    if (state.hasSpeechDetected && state.currentTurnText.trim().length === 0) {
                      console.log(`[VAD] Call ${callSid} - TRANSCRIPT WAIT TIMEOUT - Finalizing turn with empty text`);
                      
                      // Respect finalization lock
                      if (!state.finalizationInProgress) {
                        // Set the lock to prevent VAD or transcript handler from also finalizing
                        state.finalizationInProgress = true;
                        console.log(`[FINALIZE-LOCK] Setting finalization lock for call ${callSid} - transcript wait timeout`);
                        
                        // Create a placeholder utterance since we know speech occurred
                        state.currentTurnText = "[Speech detected but not transcribed]";
                        await finalizeTurn(callSid);
                        state.consecutiveSilenceFrames = 0;
                        state.speechFramesInCurrentTurn = 0;
                        state.hasSpeechDetected = false;
                        
                        // Release the lock
                        state.finalizationInProgress = false;
                        console.log(`[FINALIZE-LOCK] Released finalization lock for call ${callSid} - transcript wait timeout complete`);
                      } else {
                        console.log(`[FINALIZE-LOCK] Finalization already in progress for call ${callSid} - transcript wait timeout skipping`);
                      }
                    }
                    state.pendingFinalizeTimeout = undefined;
                  }, TRANSCRIPT_WAIT_TIMEOUT);
                } else {
                  // Not enough content yet, keep waiting despite the silence
                  if (VERBOSE_VAD_LOGGING && state.consecutiveSilenceFrames % 20 === 0) {
                    console.log(`[VAD] Call ${callSid} - MEDIUM PAUSE BUT NO CONTENT - Continuing turn`);
                  }
                }
              }
            }
          }
        }
      }
    } catch (err) {
      // More detailed error logging with error type
      if (axios.isAxiosError(err)) {
        const status = err.response?.status;
        const message = err.response?.data || err.message;
        console.error(`VAD call failed (HTTP ${status}) for callSid=${callSid}:`, message);
      } else {
      console.error(`VAD call failed for callSid=${callSid}:`, err);
      }
      
      // Despite VAD failure, still count as silence to prevent hanging
      silenceFrames++;
      // Don't increment state.consecutiveSilenceFrames to prevent false turn endings
    }
  }

  // Only log a summary if there were frames processed
  if (frames.length > 0) {
    // Log only occasionally (e.g., every 50 chunks)
    if (!state.chunkCount) state.chunkCount = 0;
    state.chunkCount++;
    
    // Skip logging completely by checking against a very high interval
    if (state.chunkCount % VAD_LOG_INTERVAL === 0) {
      console.log(`VAD processed ${frames.length} frames for ${callSid}, speech: ${speechFrames}, silence: ${silenceFrames}, energy detections: ${energyDetectionCount}, silence streak: ${state.consecutiveSilenceFrames}, speech frames in turn: ${state.speechFramesInCurrentTurn}`);
    }
  }
}

// Add interruption handler (No changes needed here for STT provider switch)
async function handleInterruption(callSid: string) {
  const state = calls[callSid];
  if (!state) return;
  
  console.log(`[INTERRUPT-DEBUG] Handling user interruption for call ${callSid}`);
  console.log(`[INTERRUPT-DEBUG] Current model speaking state: ${state.isModelSpeaking}`);
  
  // Stop audio playback if in progress
  if (state.isModelSpeaking) {
    try {
      // CRITICAL: Clear any duration-based timeout that would automatically end speaking
      if (state.currentSpeakingTimeout) {
        console.log(`[INTERRUPT-DEBUG] Clearing duration-based speaking timeout`);
        clearTimeout(state.currentSpeakingTimeout);
        state.currentSpeakingTimeout = undefined;
      }
      
      // CRITICAL FIX: Make sure we properly kill the ffmpeg process FIRST before anything else
      if (state.currentFfmpegProcess) {
        const pid = state.currentFfmpegProcess.pid;
        console.log(`[INTERRUPT-DEBUG] KILLING ffmpeg process with PID: ${pid}`);
        
        // Properly kill the process - first close all streams to prevent hanging
        try {
          if (state.currentFfmpegProcess.stdin) {
            state.currentFfmpegProcess.stdin.end();
            state.currentFfmpegProcess.stdin.destroy();
          }
          if (state.currentFfmpegProcess.stdout) {
            state.currentFfmpegProcess.stdout.destroy();
          }
          if (state.currentFfmpegProcess.stderr) {
            state.currentFfmpegProcess.stderr.destroy();
          }
          
          // Kill with SIGKILL immediately - don't wait
          state.currentFfmpegProcess.kill('SIGKILL');
          
          // Also try the system kill command as a backup
          try {
            console.log(`[INTERRUPT-DEBUG] Also using system kill for PID: ${pid}`);
            // Use process.kill as a direct way to send the signal
            if(pid) process.kill(pid, 'SIGKILL');
          } catch (killErr) {
            // Ignore errors from system kill - the process might already be gone
          }
        } catch (err) {
          console.error(`[INTERRUPT-DEBUG] Error killing ffmpeg:`, err);
        }
        
        // Immediately clear the reference
        state.currentFfmpegProcess = undefined;
        console.log(`[INTERRUPT-DEBUG] Cleared ffmpeg process reference`);
      } else {
        console.log(`[INTERRUPT-DEBUG] No ffmpeg process found to kill`);
      }
      
      // Find the WebSocket connection for this call
      const wsConnection = Array.from(wss.clients).find((client: any) => {
        return client.callSid === callSid && client.readyState === WebSocket.OPEN;
      }) as WebSocket & { callSid?: string };
      
      if (wsConnection) {
        console.log(`[INTERRUPT-DEBUG] Found active WebSocket connection for call ${callSid}`);
        console.log(`[INTERRUPT-DEBUG] WebSocket readyState: ${wsConnection.readyState} (1=OPEN)`);
        
        // Get the streamSid from call state
        const streamSid = state.streamSid || callSid;
        console.log(`[INTERRUPT-DEBUG] Using streamSid: ${streamSid}`);
        
        console.log(`[INTERRUPT-CLEAR] Attempting to clear audio buffer for call ${callSid}, stream ${streamSid}`);
        
        // CRITICAL: Try the simple format first - this is the most reliable
        const clearMessage1 = JSON.stringify({
          event: 'clear',
          streamSid: streamSid
        });
        
        console.log(`[INTERRUPT-CLEAR] Sending clear command: ${clearMessage1}`);
        wsConnection.send(clearMessage1);
        
        // REMOVED: Mark event after clear was likely unnecessary and potentially problematic.
      } else {
        console.error(`[INTERRUPT-DEBUG] No active WebSocket connection found for call ${callSid}`);
      }
    } catch (err) {
      console.error(`[INTERRUPT-DEBUG] Error stopping audio playback for call ${callSid}:`, err);
    }
    
    // CRITICAL FIX: Force reset all streaming flags
    setModelSpeaking(callSid, false, "User interruption handled - forced reset");
    
    // Record interruption time in the current turn's timings
    try {
      if (state.turnTimings && state.currentlyPlayingTurnIndex !== undefined && state.currentlyPlayingTurnIndex >= 0) {
        const playingTurnIndex = state.currentlyPlayingTurnIndex;
        const currentTiming = state.turnTimings[playingTurnIndex] || { turnIndex: playingTurnIndex };
        const interruptTime = Date.now();
        const audioStartTime = currentTiming.audioStreamingStartAt;
        currentTiming.interruptionTimestamp = Date.now();
        state.turnTimings[playingTurnIndex] = currentTiming;
        console.log(`[INTERRUPT-DEBUG] ****************************************`);
        console.log(`[INTERRUPT-DEBUG] Interruption for PLAYING turn ${playingTurnIndex} (next turn is ${state.currentTurnIndex}):`);
        console.log(`[INTERRUPT-DEBUG]   Audio start time: ${audioStartTime}`);
        console.log(`[INTERRUPT-DEBUG]   Interruption time: ${interruptTime}`);
        console.log(`[INTERRUPT-DEBUG]   Duration before interrupt: ${audioStartTime ? interruptTime - audioStartTime : 'N/A'} ms`);
        console.log(`[INTERRUPT-DEBUG]   TTS file: ${currentTiming.ttsFilename || 'N/A'}`);
        console.log(`[INTERRUPT-DEBUG] ****************************************`);
      } else {
        console.warn(`[INTERRUPT-DEBUG] Could not record interruption timestamp - missing currentlyPlayingTurnIndex (${state.currentlyPlayingTurnIndex}), currentTurnIndex: ${state.currentTurnIndex}`);
      }
    } catch (err) {
      console.error(`[INTERRUPT-DEBUG] Error recording interruption timestamp:`, err);
    }
    
    // Clear any path to the current audio file to prevent re-use
    state.audioResponsePath = undefined;
  
  // If there was a partial model response, save it to conversation history
  if (state.currentModelResponse) {
      console.log(`[INTERRUPT-DEBUG] Saving interrupted response to conversation history`);
    state.conversation.push({ 
      speaker: 'assistant', 
      text: state.currentModelResponse + ' [interrupted]' 
    });
    state.currentModelResponse = undefined;
      
      // Save conversation after interruption
      saveConversation(callSid, state.conversation);
    }
    
    // Reset user speech counts to prepare for new turn
    state.speechFramesInCurrentTurn = 0;
    state.consecutiveSilenceFrames = 0;
    state.currentTurnText = '';
    
    console.log(`[INTERRUPT-DEBUG] Interruption handling complete for call ${callSid}`);
  } else {
    console.log(`[INTERRUPT-DEBUG] Model not speaking for call ${callSid}, no interruption needed`);
  }
}

// Function to generate audio from text using Azure Speech SDK (TTS)
// Updated to include turnIndex in filename
async function generateAudioResponse(text: string, callSid: string, turnIndex: number): Promise<string | null> {
  try {
    // Log for debugging
    console.log(`Generating audio response for callSid=${callSid}, turn=${turnIndex}`);
    
    // Generate a unique filename based on timestamp, callSid, and turnIndex
    const timestamp = Date.now();
    const audioFilename = `${callSid}_turn${turnIndex}_${timestamp}.wav`;
    const audioPath = path.join(AUDIO_DIR, audioFilename);
    
    // Create Speech SDK config for TTS
    const speechConfig = sdk.SpeechConfig.fromSubscription(AZURE_SPEECH_KEY, AZURE_SPEECH_REGION);
    speechConfig.speechSynthesisVoiceName = AZURE_SPEECH_VOICE;
    
    // Create audio config for file output
    const audioConfig = sdk.AudioConfig.fromAudioFileOutput(audioPath);
    
    // Create the speech synthesizer
    const synthesizer = new sdk.SpeechSynthesizer(speechConfig, audioConfig);
    
    // Return a promise that resolves with the audio file path when synthesis is complete
    return new Promise((resolve, reject) => {
      // Start speech synthesis
      synthesizer.speakTextAsync(
        text,
        // Success callback
        (result) => {
          if (result.reason === sdk.ResultReason.SynthesizingAudioCompleted) {
            console.log(`Audio response saved to ${audioPath}`);
            synthesizer.close();
            resolve(audioPath);
          } else {
            console.error(`Speech synthesis failed: ${result.errorDetails}`);
            synthesizer.close();
            reject(new Error(result.errorDetails));
          }
        },
        // Error callback
        (error) => {
          console.error(`Speech synthesis error: ${error}`);
          synthesizer.close();
          reject(error);
        }
      );
    });
  } catch (err) {
    console.error(`Error in audio generation for ${callSid}:`, err);
    return null;
  }
}

// Function to track when model speaking state changes (remains the same)
function setModelSpeaking(callSid: string, value: boolean, reason: string) {
  const state = calls[callSid]
  if (!state) return;
  
  // Store previous value before changing it
  const wasModelSpeaking = state.isModelSpeaking;
  
  console.log(`[MODEL-SPEAKING] Setting isModelSpeaking=${value} for ${callSid} - Reason: ${reason}`);
  state.isModelSpeaking = value;
  
  // When transitioning from not-speaking to speaking, record which turn is playing
  if (!wasModelSpeaking && value === true) {
    state.currentlyPlayingTurnIndex = state.currentTurnIndex;
    console.log(`[MODEL-SPEAKING] Now playing turn ${state.currentlyPlayingTurnIndex}`);
  }
}

// Update the streamAudioToCall function to use our tracking function (TTS playback - remains the same)
async function streamAudioToCall(audioPath: string, callSid: string) {
  const state = calls[callSid];
  if (!state) {
    console.error(`No call state found for ${callSid} when attempting to stream audio.`);
    return false;
  }
  
  // Backup mechanism: Ensure currentlyPlayingTurnIndex is set
  if (state.currentlyPlayingTurnIndex === undefined) {
    state.currentlyPlayingTurnIndex = state.currentTurnIndex;
    console.log(`[STREAM-DEBUG] Setting missing currentlyPlayingTurnIndex to ${state.currentlyPlayingTurnIndex}`);
  }
  
  // Check for streamSid
  const streamSid = state.streamSid;
  if (!streamSid) {
    console.error(`No streamSid found for call ${callSid}`);
    return false;
  }

  // Check if audio file exists
  if (!fs.existsSync(audioPath)) {
    console.error(`Audio file not found: ${audioPath}`);
    setModelSpeaking(callSid, false, "Audio file not found");
    return false;
  }

  try {
    // Use ffprobe to get the duration of the audio file
    let audioDurationSec = 0;
    try {
      const ffprobeProcess = spawn('ffprobe', [
        '-v', 'error',
        '-show_entries', 'format=duration',
        '-of', 'default=noprint_wrappers=1:nokey=1',
        audioPath
      ]);

      let ffprobeOutput = '';
      ffprobeProcess.stdout.on('data', (data) => {
        ffprobeOutput += data.toString();
      });

      await new Promise<void>((resolve, reject) => {
        ffprobeProcess.on('close', (code) => {
          if (code === 0) {
            try {
              audioDurationSec = parseFloat(ffprobeOutput.trim());
              console.log(`[STREAM-DEBUG] Audio duration for ${path.basename(audioPath)}: ${audioDurationSec} seconds`);
              resolve();
            } catch (e) {
              console.error(`[STREAM-ERROR] Failed to parse ffprobe duration output for ${callSid}:`, e);
              audioDurationSec = 0; // Default to 0 on error
              resolve();
            }
          } else {
            console.error(`[STREAM-ERROR] ffprobe process exited with code ${code} for ${callSid}`);
            audioDurationSec = 0; // Default to 0 on error
            resolve(); // Resolve anyway to continue with default duration
          }
        });

        ffprobeProcess.on('error', (err) => {
          console.error(`[STREAM-ERROR] Failed to start ffprobe process for ${callSid}:`, err);
          audioDurationSec = 0; // Default to 0 on error
          resolve(); // Resolve anyway to continue with default duration
        });
      });
    } catch (err) {
      console.error(`[STREAM-ERROR] Error getting audio duration for ${callSid}:`, err);
      audioDurationSec = 0; // Default to 0 on error
    }

    // Limit the duration to a maximum value to prevent very long files
    const maxDurationSec = 60; // 60 seconds maximum
    const effectiveDurationSec = Math.min(audioDurationSec || 30, maxDurationSec);
    const timeoutDuration = (effectiveDurationSec * 1000) + 2000; // Add 2 seconds buffer

    // Mark that model is speaking
    setModelSpeaking(callSid, true, "Starting to stream audio");
    
    // Set a timeout to end speaking mode after the audio duration plus buffer
    // This is a safety mechanism in case the ffmpeg process doesn't end properly
    if (state.currentSpeakingTimeout) {
      clearTimeout(state.currentSpeakingTimeout);
    }
    state.currentSpeakingTimeout = setTimeout(() => {
      console.log(`[STREAM-DEBUG] Speaking timeout of ${timeoutDuration}ms elapsed for call ${callSid} - ending speaking mode`);
      setModelSpeaking(callSid, false, "Speaking timeout elapsed");
    }, timeoutDuration);

    // Create WebSocket for this call
    const wsConnections = [...wss.clients].filter(client => (client as any).callSid === callSid);
    if (wsConnections.length === 0) {
      console.error(`[STREAM-ERROR] No WebSocket connection found for call ${callSid}`);
      setModelSpeaking(callSid, false, "No WebSocket connection");
      return false;
    }
    const wsConnection = wsConnections[0];

    // Kill any existing ffmpeg process for this call
    if (state.currentFfmpegProcess) {
      try {
        console.log(`[STREAM-DEBUG] Killing existing ffmpeg process for call ${callSid}`);
        state.currentFfmpegProcess.kill('SIGKILL');
      } catch (e) {
        console.error(`[STREAM-ERROR] Error killing existing ffmpeg process:`, e);
      }
      state.currentFfmpegProcess = undefined;
    }

    // Set up ffmpeg command to stream audio to Twilio
    console.log(`[STREAM-DEBUG] Streaming audio ${audioPath} to call ${callSid}`);
    const ffmpegArgs = [
      '-re', // Read at native frame rate (real-time)
      '-i', audioPath,
      '-acodec', 'pcm_mulaw',
      '-ar', '8000',
      '-ac', '1',
      '-f', 'mulaw',
      '-'
    ];

    const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);
    state.currentFfmpegProcess = ffmpegProcess;

    // Send chunks of audio data to Twilio
    ffmpegProcess.stdout.on('data', (chunk) => {
      try {
        if (wsConnection.readyState === WebSocket.OPEN && state.isModelSpeaking) {
          const msg = JSON.stringify({
            event: 'media',
            streamSid,
            media: {
              payload: chunk.toString('base64')
            }
          });
          wsConnection.send(msg);
        } else if (!state.isModelSpeaking) {
          console.log(`[STREAM-DEBUG] Stopping data send to Twilio for call ${callSid} - model no longer speaking`);
          ffmpegProcess.kill('SIGKILL');
        }
      } catch (err) {
        console.error(`[STREAM-ERROR] Failed to send audio chunk to Twilio for call ${callSid}:`, err);
      }
    });

    // Log errors from ffmpeg
    ffmpegProcess.stderr.on('data', (data) => {
      console.error(`[STREAM-DEBUG] ffmpeg stderr for ${callSid}: ${data.toString().substr(0, 150)}...`);
    });

    // Return a promise that resolves when ffmpeg finishes
    return new Promise<boolean>((resolve) => {
      ffmpegProcess.on('close', (code) => {
        console.log(`[STREAM-DEBUG] ffmpeg process for call ${callSid} closed with code ${code}`);
        
        // Clear the speaking timeout
        if (state.currentSpeakingTimeout) {
          clearTimeout(state.currentSpeakingTimeout);
          state.currentSpeakingTimeout = undefined;
        }
        
        setModelSpeaking(callSid, false, "Audio streaming completed");
        state.currentFfmpegProcess = undefined;
        resolve(code === 0); // Resolve promise with success (true) if code is 0
      });

      // Set a timeout for the maximum duration just in case
      setTimeout(() => {
        console.log(`[STREAM-DEBUG] Setting timeout for ${timeoutDuration}ms for audio playback for call ${callSid}`);
      }, 15000); // 15 second timeout (reduced from 30)
    });
  } catch (err) {
    console.error(`[STREAM-ERROR] Failed to stream audio to call ${callSid}:`, err);
    // Make sure to reset speaking flag if any exception occurs
    const state = calls[callSid];
    if (state) {
      console.log(`[STREAM-ERROR] Setting isModelSpeaking=false for call ${callSid} after stream exception`);
      
      // Clear the speaking timeout
      if (state.currentSpeakingTimeout) {
        clearTimeout(state.currentSpeakingTimeout);
        state.currentSpeakingTimeout = undefined;
      }
      
      setModelSpeaking(callSid, false, "Stream exception occurred");
      
      // Clear ffmpeg reference
      if (state.currentFfmpegProcess) {
        try {
          state.currentFfmpegProcess.kill('SIGKILL');
        } catch (e) {
          console.error(`[STREAM-ERROR] Error killing process during exception:`, e);
        }
        state.currentFfmpegProcess = undefined;
      }
    }
    return false;
  }
}

// Modify finalizeTurn to log the transcript content being sent to the LLM (remains mostly the same)
async function finalizeTurn(callSid: string) {
  const state = calls[callSid]
  if (!state) return

  const text = state.currentTurnText.trim()
  if (text) {
    // Add the utterance to conversation history - no duplicate checking
    state.conversation.push({ speaker: 'caller', text })
    
    // Save conversation after adding caller's message
    saveConversation(callSid, state.conversation);
    
    // Add detailed logging of the actual transcript content
    console.log(`
------------------------------------------------------
TRANSCRIPT SENT TO LLM for call ${callSid}:
------------------------------------------------------
${text}
------------------------------------------------------
`)

    try {
      // Mark that model is processing/speaking using our tracking function
      setModelSpeaking(callSid, true, "Processing user turn");
      state.currentModelResponse = '';

      console.log(`Sending conversation history to RAG server at ${RAG_SERVER_URL}...`);
      
      // Log the complete conversation history
      console.log(`
------------------------------------------------------
CONVERSATION HISTORY for call ${callSid}:
------------------------------------------------------
${state.conversation.map(msg => `${msg.speaker}: ${msg.text}`).join('\n')}
------------------------------------------------------
`);
      
      // Send the entire conversation history to the RAG server
      const ragResponse = await axios.post(RAG_SERVER_URL, {
        callSid,
        userMessage: text,
        conversationHistory: state.conversation
      });
      
      const assistantReply = ragResponse.data.assistantReply;
      if (assistantReply) {
        // Only add to conversation if not interrupted
        if (state.isModelSpeaking) {
          state.conversation.push({ speaker: 'assistant', text: assistantReply });
          
          // Save conversation after adding assistant's response
          saveConversation(callSid, state.conversation);
          
          // Log the LLM response in detail
          console.log(`
------------------------------------------------------
LLM RESPONSE for call ${callSid}:
------------------------------------------------------
${assistantReply}
------------------------------------------------------
`)
          
          // Track current model response for potential interruption
          state.currentModelResponse = assistantReply;
          
          // Generate audio from the text response (TTS)
          const ttsStartTime = Date.now();
          let currentTiming = state.turnTimings[state.currentTurnIndex] || { turnIndex: state.currentTurnIndex };
          currentTiming.audioGenerationStartAt = ttsStartTime;
          state.turnTimings[state.currentTurnIndex] = currentTiming; // Ensure update
          
          const audioPath = await generateAudioResponse(assistantReply, callSid, state.currentTurnIndex);
          
          // Record TTS end time
          const ttsEndTime = Date.now();
          currentTiming = state.turnTimings[state.currentTurnIndex]; // Re-fetch potentially updated timing obj
          currentTiming.audioGenerationEndAt = ttsEndTime;
          state.turnTimings[state.currentTurnIndex] = currentTiming; // Ensure update
          
          // Store the exact filename for this turn
          if (audioPath) {
            currentTiming.ttsFilename = path.basename(audioPath);
            state.turnTimings[state.currentTurnIndex] = currentTiming; // Ensure update
            console.log(`[TTS-DEBUG] Turn ${state.currentTurnIndex} TTS file generated: ${path.basename(audioPath)}`);
          }
          
          if (audioPath) {
            state.audioResponsePath = audioPath;
            
            // Record streaming start time
            const streamStartTime = Date.now();
            currentTiming = state.turnTimings[state.currentTurnIndex];
            currentTiming.audioStreamingStartAt = streamStartTime;
            state.turnTimings[state.currentTurnIndex] = currentTiming;
            console.log(`[TTS-DEBUG] Turn ${state.currentTurnIndex} streaming about to start at ${streamStartTime}`);
            
            // Note: streamAudioToCall will set isModelSpeaking=true again
            // Stream the audio to the call
            const streamResult = await streamAudioToCall(audioPath, callSid);
            
            // Record streaming end time
            const streamEndTime = Date.now();
            currentTiming = state.turnTimings[state.currentTurnIndex];
            currentTiming.audioStreamingEndAt = streamEndTime;
            state.turnTimings[state.currentTurnIndex] = currentTiming;
            
            console.log(`[TURN-DEBUG] Audio streaming result: ${streamResult ? 'success' : 'failed'}`);
          } else {
            // If audio generation failed, we're not speaking
            setModelSpeaking(callSid, false, "Audio generation failed");
          }
        } else {
          console.log(`[TURN-DEBUG] Not playing audio response - model speaking was interrupted`);
        }
      } else {
        console.log(`[TURN-DEBUG] No assistant reply received from RAG server`);
        setModelSpeaking(callSid, false, "No response from RAG server");
      }
    } catch (err) {
      console.error(`Failed to get RAG response for ${callSid}:`, err);
      setModelSpeaking(callSid, false, "Error in RAG server communication");
    } finally {
      // Make sure currentModelResponse is cleared
      state.currentModelResponse = undefined;
    }

    state.currentTurnText = '' // Reset current turn text after processing
    state.currentTurnIndex++; // Increment turn index *BEFORE* logging and releasing lock
    state.currentTurnStartTime = undefined; // Reset turn start time

    // Log turn timings for the turn that just completed
    const completedTurnIndex = state.currentTurnIndex - 1; // Use index of the turn we just finished
    if (completedTurnIndex >= 0 && state.turnTimings[completedTurnIndex]) {
      const timings = state.turnTimings[completedTurnIndex];
      const start = timings.silenceDetectedAt || timings.transcriptReceivedAt || 0; // Use first available timestamp as start
      
      const calcDuration = (t1?: number, t2?: number) => (
          t1 && t2 ? `${((t2 - t1) / 1000).toFixed(2)}s` : 'N/A'
      );

      console.log(`
---------------- TURN TIMINGS (Turn ${timings.turnIndex}) ------------------
  Silence Detected -> Transcript: ${calcDuration(timings.silenceDetectedAt, timings.transcriptReceivedAt)}
  Transcript -> RAG Request:    ${calcDuration(timings.transcriptReceivedAt, timings.ragRequestStartAt)}
  RAG Request -> RAG Response:  ${calcDuration(timings.ragRequestStartAt, timings.ragResponseReceivedAt)}
  RAG Response -> TTS Start:    ${calcDuration(timings.ragResponseReceivedAt, timings.audioGenerationStartAt)}
  TTS Start -> TTS End:         ${calcDuration(timings.audioGenerationStartAt, timings.audioGenerationEndAt)}
  TTS End -> Streaming Start: ${calcDuration(timings.audioGenerationEndAt, timings.audioStreamingStartAt)}
  Streaming Start -> End:     ${calcDuration(timings.audioStreamingStartAt, timings.audioStreamingEndAt)}
  ------------------------------------------------------
      `);
    }

    state.finalizationInProgress = false; // Release lock
    console.log(`[FINALIZE-LOCK] Released finalization lock for call ${callSid} - finalizeTurn complete`);

  } else {
      console.log(`[FinalizeTurn] Skipping finalize for call ${callSid} as currentTurnText is empty.`);
      state.finalizationInProgress = false; // Ensure lock is released even if skipped
      console.log(`[FINALIZE-LOCK] Released finalization lock for call ${callSid} - finalizeTurn skipped (empty text)`);
  }
}

// Global error handlers to prevent crashes (remains the same)
process.on('uncaughtException', (err) => {
  console.error('UNCAUGHT EXCEPTION - server will continue running:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('UNHANDLED REJECTION - server will continue running:', reason);
});

// For WebSocket error handling (remains the same)
const safeJSONParse = (message: string) => {
  try {
    return JSON.parse(message);
  } catch (err) {
    console.error('Failed to parse WS message:', err);
    return { event: 'error', error: 'Invalid JSON' };
  }
};

// Function to safely get call data (remains the same)
const getCallData = (data: any) => {
  if (!data) return null;
  
  // Try various formats of where the callSid might be
  if (data.start && typeof data.start === 'object' && data.start.callSid) {
    return data.start.callSid;
  }
  
  if (data.start && typeof data.start === 'string') {
    return data.start;
  }
  
  if (data.callSid) {
    return data.callSid;
  }
  
  return null;
};

// Function to save conversation to disk (remains the same)
function saveConversation(callSid: string, conversation: Array<{ speaker: string; text: string }>) {
  try {
    const filePath = path.join(CONVERSATIONS_DIR, `${callSid}.json`);
    fs.writeFileSync(filePath, JSON.stringify({
      callSid,
      timestamp: new Date().toISOString(),
      conversation
    }, null, 2));
    console.log(`Saved conversation for call ${callSid} to disk`);
    
    // Also save to Supabase
    saveConversationToDb(callSid, conversation).catch(err => {
      console.error(`[DB] Failed to save conversation for call ${callSid} to database:`, err);
    });
  } catch (err) {
    console.error(`Failed to save conversation for call ${callSid}:`, err);
  }
}

// Function to save conversation to Supabase database (remains the same)
async function saveConversationToDb(callSid: string, conversation: Array<{ speaker: string; text: string }>) {
  try {
    // Update the call_history table with the conversation
    const { error } = await supabase
      .from('call_history')
      .update({ 
        conversation_history: conversation,
        // Include a timestamp of when this was updated
        updated_at: new Date().toISOString()
      })
      .eq('call_sid', callSid);
    
    if (error) {
      console.error(`[DB] Supabase error updating conversation for ${callSid}:`, error);
    } else {
      console.log(`[DB] Successfully updated conversation history for call ${callSid}`);
    }
  } catch (err) {
    console.error(`[DB] Error in database operation for ${callSid}:`, err);
  }
}

// Function to load conversation from disk (remains the same)
function loadConversation(callSid: string): Array<{ speaker: string; text: string }> {
  try {
    const filePath = path.join(CONVERSATIONS_DIR, `${callSid}.json`);
    if (fs.existsSync(filePath)) {
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      console.log(`Loaded conversation for call ${callSid} from disk`);
      return data.conversation;
    }
  } catch (err) {
    console.error(`Failed to load conversation for call ${callSid}:`, err);
  }
  return [];
}

// --- BEGIN DATABASE HELPER FUNCTIONS --- (remain the same)

// Lookup User ID from auth.users table based on phone number
async function findUserIdByPhoneNumber(phoneNumber: string): Promise<string | null> {
    // Format the phone number: Remove leading '+' if present
    const formattedPhoneNumber = phoneNumber.startsWith('+') ? phoneNumber.slice(1) : phoneNumber;

    console.log(`[DB-RPC] Looking up user ID for formatted phone: ${formattedPhoneNumber} (Original: ${phoneNumber})`);
    try {
        // Call the PostgreSQL function with the formatted number
        const { data, error } = await supabase.rpc('get_user_id_by_phone', {
            p_phone_number: formattedPhoneNumber // Use the formatted number here
        });

        if (error) {
            console.error(`[DB-RPC] Error calling get_user_id_by_phone for ${formattedPhoneNumber}:`, error);
            return null;
        }

        if (data) {
             console.log(`[DB-RPC] Found user ID: ${data} for phone ${formattedPhoneNumber}`);
        } else {
             console.log(`[DB-RPC] No user found for phone ${formattedPhoneNumber}`);
        }
        return data; // data will be the UUID string or null

    } catch (err) {
        console.error(`[DB-RPC] Exception during RPC call for phone ${formattedPhoneNumber}:`, err);
        return null;
    }
}

// Insert initial record when a call starts
async function insertInitialCallRecord(callSid: string, startTime: Date, fromNumber: string | null, toNumber: string | null, userId: string | null) { // Add userId parameter
  try {
    const { error } = await supabase
      .from('call_history')
      .insert({
        call_sid: callSid,
        call_start_time: startTime.toISOString(),
        from_number: fromNumber, // May be null if not available
        to_number: toNumber,     // May be null if not available
        call_status: 'connected', // Initial status
        session_id: null,       // Explicitly null initially
        user_id: userId           // Use the passed userId (could be null)
        // created_at is handled by Supabase default/trigger
        // updated_at will be set later
      });

    if (error) {
      console.error(`[DB] Supabase error inserting initial call record for ${callSid}:`, error);
    } else {
      console.log(`[DB] Successfully inserted initial call record for ${callSid}`);
    }
  } catch (err) {
    console.error(`[DB] Error inserting initial call record for ${callSid}:`, err);
  }
}

// Update record when a call ends
async function updateCallRecordOnEnd(callSid: string, endTime: Date, durationSeconds: number | null, recordingPath?: string) {
  try {
    const updateData: any = {
      call_end_time: endTime.toISOString(),
      call_duration: durationSeconds,
      call_status: 'completed', // Assuming normal completion
      updated_at: endTime.toISOString() // Also set updated_at
    };
    
    // We'll update the recording path separately when it's available
    
    const { error } = await supabase
      .from('call_history')
      .update(updateData)
      .eq('call_sid', callSid);

    if (error) {
      console.error(`[DB] Supabase error updating call record on end for ${callSid}:`, error);
    } else {
      console.log(`[DB] Successfully updated call record on end for ${callSid}`);
    }
  } catch (err) {
    console.error(`[DB] Error updating call record on end for ${callSid}:`, err);
  }
}

// Add new function for updating just the recording path
async function updateCallRecordingPath(callSid: string, recordingPath: string, updateTime: Date) {
  try {
    const { error } = await supabase
      .from('call_history')
      .update({
        call_recording_path: recordingPath,
        updated_at: updateTime.toISOString()
      })
      .eq('call_sid', callSid);

    if (error) {
      console.error(`[DB] Supabase error updating recording path for ${callSid}:`, error);
    } else {
      console.log(`[DB] Successfully updated recording path for ${callSid}: ${recordingPath}`);
    }
  } catch (err) {
    console.error(`[DB] Error updating recording path for ${callSid}:`, err);
  }
}

// --- END DATABASE HELPER FUNCTIONS ---

//-----------------------------------------------
// Start the Server
//-----------------------------------------------
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`)
  // Set up periodic cleanup
  setupPeriodicCleanup();
})
