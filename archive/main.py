import telnyx
import os
import json
from dotenv import load_dotenv
from flask import Flask, render_template, request

# Load environment variables from .env file
load_dotenv()

# Initialize Flask app and set Telnyx API Key
app = Flask(__name__)
telnyx.api_key = os.getenv("TELNYX_API_KEY")

# Homepage that allows user to enter a number to call
@app.route("/")
def home():
    return render_template("messageform.html")

# Endpoint to handle outbound calls
@app.route("/outbound", methods=["POST"])
def outbound():
    number = request.form["to_number"]
    try:
        telnyx.Call.create(
            connection_id=os.getenv("TELNYX_CONNECTION_ID"),
            to=number,
            from_=os.getenv("TELNYX_NUMBER")
        )
        return render_template("messagesuccess.html")
    except Exception as e:
        print(f"An error occurred: {e}")
        return render_template("messagefailure.html")

# Endpoint to handle inbound call control
@app.route("/webhook", methods=["POST"])
def inbound():
    body = json.loads(request.data)
    event = body.get("data", {}).get("event_type")
    print(body)

    try:
        if event == "call.initiated":
            call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
            call.call_control_id = body["data"]["payload"]["call_control_id"]
            call.answer()
        elif event == "call.answered":
            call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
            call.call_control_id = body["data"]["payload"]["call_control_id"]
            call.speak(
                payload="Hello, Telnyx user! Welcome to this call control demonstration",
                language="en-US",
                voice="female"
            )
        elif event == "call.speak.ended":
            call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
            call.call_control_id = body["data"]["payload"]["call_control_id"]
            call.hangup()
    except Exception as e:
        print(f"An error occurred: {e}")
        return json.dumps({"success": False}), 500, {"ContentType": "application/json"}

    return json.dumps({"success": True}), 200, {"ContentType": "application/json"}

# Main program execution
def main():
    app.run(port=int(os.getenv("TELNYX_APP_PORT", 5000)), debug=True)

if __name__ == "__main__":
    main()