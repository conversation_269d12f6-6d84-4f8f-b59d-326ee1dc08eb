import { vi, describe, it, expect, beforeEach } from 'vitest';
import { createOsceMachine } from './osceMachine';
import { interpret } from 'xstate';
import { getUserPreferences, getClinicalScenarios, getUserSessionHistory } from './database';
import type { UserPreferences, OsceEvent } from './types';

// Mock database functions
vi.mock('./database', () => ({
  getUserPreferences: vi.fn(),
  getClinicalScenarios: vi.fn(),
  getUserSessionHistory: vi.fn()
}));

describe('OSCE Machine - Case Selection', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('fetchCaseForUserActor', () => {
    it('should select a case successfully when user has preferences and uncompleted cases', async () => {
      // Mock getUserPreferences
      (getUserPreferences as any).mockResolvedValue({
        user_id: 'test-user',
        preferred_categories: ['Cardiology:Chest Pain'],
        category_mode: 'selected' as const
      });

      // Mock getClinicalScenarios
      (getClinicalScenarios as any).mockResolvedValue([{
        id: 'case-1',
        title: 'Chest Pain Case',
        specialty: 'Cardiology',
        category: 'Chest Pain',
        difficulty: 'medium',
        scenario: 'Patient presents with chest pain',
        symptoms: 'Sharp chest pain radiating to left arm',
        history: 'No significant medical history',
        initial_question: 'What brings you to the clinic today?',
        questions: ['When did the pain start?'],
        patient_agent: {
          initial_briefing: 'You are a 45-year-old patient with chest pain.'
        }
      }]);

      // Mock getUserSessionHistory
      (getUserSessionHistory as any).mockResolvedValue([]);

      // Create machine with initial context
      const machine = createOsceMachine();
      const service = interpret(
        machine
          .provide({
            actors: {
              // Add any additional actor implementations needed
            }
          })
      ).start();

      // Send events to trigger case selection
      service.send({ type: 'START', callSid: 'test-call-1' });

      // Wait for case selection to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify the state and context
      const state = service.getSnapshot();
      expect(state.context.caseData).toBeTruthy();
      expect(state.context.selectedCaseId).toBe('case-1');
      expect(state.context.caseLoadingError).toBeNull();

      // Clean up
      service.stop();
    });

    it('should handle errors when no cases are available', async () => {
      // Mock getUserPreferences
      (getUserPreferences as any).mockResolvedValue({
        user_id: 'test-user',
        preferred_categories: ['Cardiology:Chest Pain'],
        category_mode: 'selected'
      });

      // Mock getClinicalScenarios to return empty array
      (getClinicalScenarios as any).mockResolvedValue([]);

      // Mock getUserSessionHistory
      (getUserSessionHistory as any).mockResolvedValue([]);

      // Create and start the machine
      const machine = createOsceMachine();
      const service = interpret(machine.provide({
        actors: {
          // Add any additional actor implementations needed
        }
      })).start();

      // Send events to trigger case selection
      service.send({ type: 'START', callSid: 'test-call-1' });

      // Wait for case selection to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify the state and context
      const state = service.getSnapshot();
      expect(state.context.caseData).toBeNull();
      expect(state.context.selectedCaseId).toBeNull();
      expect(state.context.caseLoadingError).toBe('No scenarios available matching your preferences');

      // Clean up
      service.stop();
    });

    it('should handle missing userId', async () => {
      // Create and start the machine
      const machine = createOsceMachine();
      const service = interpret(machine.provide({
        actors: {
          // Add any additional actor implementations needed
        }
      })).start();

      // Send events to trigger case selection without userId
      service.send({ type: 'START', callSid: 'test-call-1' });

      // Wait for case selection to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify the state and context
      const state = service.getSnapshot();
      expect(state.context.caseData).toBeNull();
      expect(state.context.selectedCaseId).toBeNull();
      expect(state.context.caseLoadingError).toBe('No userId provided');

      // Clean up
      service.stop();
    });

    it('should prioritize uncompleted cases', async () => {
      // Mock getUserPreferences
      (getUserPreferences as any).mockResolvedValue({
        user_id: 'test-user',
        preferred_categories: ['Cardiology:Chest Pain'],
        category_mode: 'selected'
      });

      // Mock getClinicalScenarios with multiple cases
      (getClinicalScenarios as any).mockResolvedValue([
        {
          id: 'case-1',
          title: 'Chest Pain Case 1',
          specialty: 'Cardiology',
          category: 'Chest Pain',
          difficulty: 'medium',
          questions: []
        },
        {
          id: 'case-2',
          title: 'Chest Pain Case 2',
          specialty: 'Cardiology',
          category: 'Chest Pain',
          difficulty: 'medium',
          questions: []
        }
      ]);

      // Mock getUserSessionHistory with one completed case
      (getUserSessionHistory as any).mockResolvedValue(['case-1']);

      // Create and start the machine
      const machine = createOsceMachine();
      const service = interpret(machine.provide({
        actors: {
          // Add any additional actor implementations needed
        }
      })).start();

      // Send events to trigger case selection
      service.send({ type: 'START', callSid: 'test-call-1' });

      // Wait for case selection to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify the state and context
      const state = service.getSnapshot();
      expect(state.context.caseData).toBeTruthy();
      expect(state.context.selectedCaseId).toBe('case-2'); // Should select the uncompleted case
      expect(state.context.caseLoadingError).toBeNull();

      // Clean up
      service.stop();
    });
  });
});
