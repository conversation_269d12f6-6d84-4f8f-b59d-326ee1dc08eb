/*******************************************************************
 * server.ts
 *
 * 1) Twilio => Node => local WAV + AssemblyAI real-time STT
 * 2) Also calls a Python-based VAD endpoint on port 8085
 * 3) Tracks speaker turns: multiple final transcripts accumulate until
 *    we detect ~1 second of silence -> finalise turn
 *******************************************************************/

import express from 'express'
import bodyParser from 'body-parser'
import { twiml as TwiML } from 'twilio'
import http from 'http'
import WebSocket from 'ws'
import { spawn, ChildProcessWithoutNullStreams } from 'child_process'
import axios from 'axios'
import { AssemblyAI, RealtimeTranscriber, RealtimeTranscript } from 'assemblyai'
import fs from 'fs'
import path from 'path'
import { OpenAI } from 'openai'
import dotenv from 'dotenv'
import { DefaultAzureCredential, getBearerTokenProvider } from '@azure/identity'
import * as sdk from 'microsoft-cognitiveservices-speech-sdk'
import { createClient } from '@supabase/supabase-js'

// Load environment variables from parent directory
dotenv.config({ path: path.join(__dirname, '../../.env') })

// Define interfaces for transcription providers
interface TranscriptionResult {
  text: string;
  message_type: 'PartialTranscript' | 'FinalTranscript';
}

interface TranscriptionProvider {
  connect(): Promise<void>;
  sendAudio(chunk: Buffer): void;
  close(): void;
  on(event: 'transcript', handler: (result: TranscriptionResult) => void): void;
  on(event: 'error', handler: (error: Error) => void): void;
  on(event: 'open', handler: (data: any) => void): void;
  on(event: 'close', handler: (code: number, reason: string) => void): void;
}

/** 
 * We'll define a small function to split a Buffer into 20ms frames
 * at 8 kHz, 16-bit PCM => 320 bytes per frame.
 * If leftover data remains, we keep it in a global or callSid-based 
 * leftover buffer for the next chunk.
 */
function splitTo20msFrames(buffer: Buffer, leftover: Buffer): Buffer[] {
  const FRAME_SIZE = 320 // 20ms @ 8kHz, 16-bit mono
  
  // Combine leftover + new chunk
  const combined = Buffer.concat([leftover, buffer])

  const frames: Buffer[] = []
  let offset = 0

  // Only create frames of exactly FRAME_SIZE bytes
  while (offset + FRAME_SIZE <= combined.length) {
    const frame = combined.slice(offset, offset + FRAME_SIZE);
    // Sanity check to ensure we're sending valid frames
    if (frame.length === FRAME_SIZE) {
      frames.push(frame);
    } else {
      console.warn(`Created invalid frame size: ${frame.length} bytes, expected ${FRAME_SIZE}`);
    }
    offset += FRAME_SIZE;
  }

  // The leftover portion - data that's not a complete frame
  const newLeftover = combined.slice(offset);

  // Return frames + leftover as the last element
  return [...frames, newLeftover];
}

//-----------------------------------------------
// Configuration
//-----------------------------------------------
const PORT = process.env.PORT || 5000
const ASSEMBLYAI_API_KEY = '********************************'
const VAD_ENDPOINT = 'http://localhost:8085/vad' // Python VAD microservice
const RAG_SERVER_URL = 'http://localhost:8001/rag/converse'

// Azure Speech configuration
const AZURE_SPEECH_KEY = 'c539857332064fc38e4aa5075b652087';
const AZURE_SPEECH_REGION = 'swedencentral';
const AZURE_SPEECH_VOICE = 'en-US-AvaMultilingualNeural'; // High quality neural voice

// Speech-to-Text provider configuration
type STTProviderType = 'assemblyai' | 'azure';
const STT_PROVIDER: STTProviderType = 'assemblyai'; // Options: 'assemblyai' or 'azure'

// Constants for speech detection
// Increase the silence threshold to be more tolerant of brief pauses
const SILENCE_FRAMES_THRESHOLD = 40; // Reduced from 80 (~0.8 seconds of silence)
const LONG_PAUSE_THRESHOLD = 100; // Reduced from 200 (~2 seconds of silence)
const MIN_SPEECH_FRAMES = 5; // Reduced from 15 - require less speech to consider a turn valid

// Add these counters at the top of the file with other state variables
// Track media packet counts to reduce logging
const mediaPacketCounts: Record<string, number> = {};
const MEDIA_LOG_INTERVAL = 10000; // Set to a very high number to effectively disable

// Transcript logging settings
const TRANSCRIPT_LOG_ALL = false; // Don't log partial transcripts
const TRANSCRIPT_LOG_FINAL_ONLY = false; // Don't even log final transcripts
const VAD_LOG_INTERVAL = 10000; // Effectively disable VAD logging
const SILENCE_DETECTION_LOGS = false; // Disable silence detection logs

// Toggle for more verbose VAD logging without changing constants
const VERBOSE_VAD_LOGGING = true;

// Add this constant at the top of the file with other constants
const TRANSCRIPT_WAIT_TIMEOUT = 2000; // Wait up to 2 seconds for transcript after detecting speech

// Environment variables
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const SUPABASE_SERVICE_KEY = process.env.NEXT_SERVICE_SUPABASE_SERVICE_KEY || '';

// Initialize Supabase client with service key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Temporary storage for details received in /voice webhook before WS connection starts
const pendingCallDetails: Record<string, { from: string | null, to: string | null, userId?: string | null }> = {};

//-----------------------------------------------
// Setup Express
//-----------------------------------------------
const app = express()
app.use(bodyParser.urlencoded({ extended: false }))
app.use(bodyParser.json())

// AssemblyAI client
const aaiClient = new AssemblyAI({ apiKey: ASSEMBLYAI_API_KEY })

// Track call state
interface CallState {
  recordFfmpeg?: ChildProcessWithoutNullStreams
  decodeFfmpeg?: ChildProcessWithoutNullStreams
  leftover: Buffer
  transcriptionProvider?: TranscriptionProvider
  aaiTranscriber?: RealtimeTranscriber // For legacy support
  currentTurnText: string
  consecutiveSilenceFrames: number
  conversation: Array<{ speaker: string; text: string }>
  isModelSpeaking: boolean
  currentModelResponse?: string
  audioResponsePath?: string // Path to the current audio response file
  chunkCount?: number
  streamSid?: string // Track the Twilio stream SID
  speechFramesInCurrentTurn: number
  currentFfmpegProcess?: ChildProcessWithoutNullStreams // For tracking current audio streaming ffmpeg process
  currentSpeakingTimeout?: NodeJS.Timeout // For tracking the timeout that automatically ends speaking mode
  pendingFinalizeTimeout?: NodeJS.Timeout // For tracking delayed finalization
  hasSpeechDetected: boolean // Flag to track if speech was detected even if no transcript is available
  finalizationInProgress: boolean // Flag to prevent duplicate finalization
  callStartTime: Date; // Add start time to calculate duration
}
const calls: Record<string, CallState> = {}

// Create audio directory if it doesn't exist
const AUDIO_DIR = path.join(__dirname, '../audio_responses')
if (!fs.existsSync(AUDIO_DIR)) {
  fs.mkdirSync(AUDIO_DIR, { recursive: true })
}

// Create conversations directory if it doesn't exist
const CONVERSATIONS_DIR = path.join(__dirname, '../conversations')
if (!fs.existsSync(CONVERSATIONS_DIR)) {
  fs.mkdirSync(CONVERSATIONS_DIR, { recursive: true })
}

//-----------------------------------------------
// 1) Twilio /voice -> Return <Start><Stream>
//-----------------------------------------------
app.post('/voice', async (req, res) => { // <-- Make this function async
  const twiml = new TwiML.VoiceResponse();
  const callSid = req.body.CallSid;
  const fromNumber = req.body.From || null;
  const toNumber = req.body.To || null;
  
  console.log(`Incoming call SID: ${callSid}, From: ${fromNumber}, To: ${toNumber}`);

  // Store details temporarily, associated with CallSid
  if (callSid) {
    // Look up user ID based on From number
    let userId: string | null = null;
    if (fromNumber) {
        try {
            userId = await findUserIdByPhoneNumber(fromNumber);
            if (userId) {
                console.log(`Found user ID ${userId} for phone number ${fromNumber}`);
            } else {
                console.log(`No user found for phone number ${fromNumber}`);
            }
        } catch (lookupError) {
            console.error(`Error looking up user ID for phone ${fromNumber}:`, lookupError);
        }
    }

    pendingCallDetails[callSid] = { from: fromNumber, to: toNumber, userId: userId }; // Store userId too
    console.log(`Stored initial details for call ${callSid} (userId: ${userId || 'N/A'})`);

    // Set a timeout to clean up pending details if WS never connects (e.g., 60 seconds)
    setTimeout(() => {
      if (pendingCallDetails[callSid]) {
        console.warn(`Cleaning up stale pending details for call ${callSid} (WS likely never connected)`);
        delete pendingCallDetails[callSid];
      }
    }, 60000); 
  }

  // First play a greeting
  twiml.say({
    voice: 'alice'
  }, 'Hello! How can I help you today?');
  
  // Then set up the bidirectional stream with <Connect><Stream>
  // per https://www.twilio.com/docs/voice/twiml/stream
  const connect = twiml.connect();
  connect.stream({
    url: `wss://${req.headers.host}/media`
    // No 'track' parameter - for bidirectional streams, only inbound_track is supported
  });
  
  console.log('Responding with TwiML:', twiml.toString());
  res.type('text/xml').send(twiml.toString());
});

//-----------------------------------------------
// 2) WebSocket for /media
//    Twilio => Node => ffmpeg => local WAV + AAI
//    Also do VAD checks => turn detection
//-----------------------------------------------
const server = http.createServer(app)
const wss = new WebSocket.Server({
  server,
  path: '/media'
});

// Log when the WebSocket server is created
console.log('WebSocket server created and listening on path: /media');

// Add connection attempt logging
wss.on('headers', (headers, req) => {
  console.log('WebSocket connection headers received:', headers.join(', '));
  console.log('Connection attempt from:', req.socket.remoteAddress);
});

wss.on('connection', (ws) => {
  console.log('Twilio connected to /media WebSocket at:', new Date().toISOString());

  let callSid: string | null = null

  // Add callSid property to the WebSocket object for tracking
  Object.defineProperty(ws, 'callSid', {
    get: function() { return callSid; },
    set: function(value) { callSid = value; }
  });

  // Log WebSocket close events
  ws.on('close', (code, reason) => {
    console.log(`WebSocket for call ${callSid || 'unknown'} closed with code ${code}, reason: ${reason || 'none'}`);
    // Perform cleanup when the connection is closed
    if (callSid) {
      console.log(`Cleaning up resources for call ${callSid} after WebSocket closed`);
      cleanupCall(callSid);
    }
  });

  // Log WebSocket errors
  ws.on('error', (error) => {
    console.error(`WebSocket error for call ${callSid || 'unknown'}:`, error);
  });

  // Add enhanced monitoring for all messages
  ws.on('message', (rawMessage) => {
    try {
      // Track clear-related events
      const message = rawMessage.toString();
      const data = JSON.parse(message);
      
      // Watch for specific events related to clear messages or marks
      if (data.event === 'mark') {
        console.log(`[WS-MONITOR] Received mark event: ${JSON.stringify(data)}`);
      } else if (data.event === 'clear') {
        console.log(`[WS-MONITOR] Received clear event: ${JSON.stringify(data)}`);
      } else if (data.event === 'error') {
        console.log(`[WS-MONITOR] Received error event: ${JSON.stringify(data)}`);
      }
    } catch (error) {
      // Ignore parsing errors in the monitor
    }
  });

  ws.on('message', async (message: string) => {
    // Safely parse the message
    const data = safeJSONParse(message);
    
    if (data.event === 'error') return; // Invalid JSON
    
    // Special debug logging for speech events
    if (data.type && data.type.includes('speech')) {
      console.log('SPEECH EVENT DETECTED:', data.type);
    }
    
    // Only log non-media events to reduce console spam
    if (data.event !== 'media') {
      console.log(`Received WS message event: ${data.event}`);
      
      // Add more detailed logging to debug the data structure
      if (data.event === 'start') {
        console.log('Start event data:', JSON.stringify(data, null, 2));
      }
    } else {
      // For media events, only log occasionally to show activity
      if (callSid) {
        if (!mediaPacketCounts[callSid]) {
          mediaPacketCounts[callSid] = 0;
        }
        
        mediaPacketCounts[callSid]++;
        
        // Skip logging completely by commenting this out
        /*
        if (mediaPacketCounts[callSid] % MEDIA_LOG_INTERVAL === 0) {
          console.log(`Received ${MEDIA_LOG_INTERVAL} media packets for call ${callSid} (total: ${mediaPacketCounts[callSid]})`);
        }
        */
      }
    }

    try {
    switch (data.event) {
      case 'connected':
          console.log('Twilio event: connected');
          break;

      case 'start':
          // Use the safer callSid extraction method
          const extractedCallSid = getCallData(data);
          if (!extractedCallSid) {
            console.error('Could not extract callSid from start event:', data);
            return;
          }
          
          callSid = extractedCallSid;
          // Store callSid with the WebSocket object
          (ws as any).callSid = callSid;
          console.log('Media start event for callSid:', callSid);

          // Extract streamSid if available
          let streamSid = data.streamSid || callSid; // Use streamSid if present, fallback to callSid
          console.log(`Using streamSid: ${streamSid}`);

          // --- BEGIN INSERT CALL HISTORY ---
          const callStartTime = new Date();
          // Retrieve stored From/To numbers and userId
          const initialDetails = pendingCallDetails[callSid] || { from: null, to: null, userId: null };
          const fromNumberFromWebhook = initialDetails.from;
          const toNumberFromWebhook = initialDetails.to;
          const userIdFromWebhook = initialDetails.userId; // Get the userId

          // Clean up temporary storage now that WS has started
          if (pendingCallDetails[callSid]) {
            delete pendingCallDetails[callSid];
            console.log(`Cleaned up pending details for call ${callSid}`);
          }

          // Insert using retrieved numbers and userId
          insertInitialCallRecord(callSid, callStartTime, fromNumberFromWebhook, toNumberFromWebhook, userIdFromWebhook).catch(err => { // Pass userId
              console.error(`[DB] Failed initial insert for call ${callSid}:`, err);
          });
          // --- END INSERT CALL HISTORY ---

          // Check for existing conversation history (from disk)
          const existingConversation = loadConversation(callSid);

        // Initialize call state
        calls[callSid] = {
          leftover: Buffer.alloc(0),
          currentTurnText: '',
          consecutiveSilenceFrames: 0,
          conversation: existingConversation.length > 0 ? existingConversation : [],
          isModelSpeaking: false,
          streamSid: streamSid,
          speechFramesInCurrentTurn: 0,
          hasSpeechDetected: false, // Initialize the new flag
          finalizationInProgress: false, // Initialize the lock flag
          callStartTime: callStartTime // Store the start time
          };
          
          // Log if we loaded existing conversation
          if (existingConversation.length > 0) {
            console.log(`Loaded existing conversation history for call ${callSid} with ${existingConversation.length} messages`);
        }

        // 1) ffmpeg #1 => record WAV locally
        try {
          const outPath = `./recordings/${callSid}.wav`
          const recordFfmpeg = spawn('ffmpeg', [
            '-f', 'mulaw',
            '-ar', '8000',
            '-i', 'pipe:0',
            '-ac', '1',
            '-ar', '8000',
            '-f', 'wav',
            outPath,
          ])
          recordFfmpeg.on('close', (code) => {
            console.log(`recordFfmpeg for ${callSid} exited with code ${code}`)
          })
          recordFfmpeg.on('error', (err) => {
            console.error(`recordFfmpeg error for ${callSid}:`, err)
          })

          calls[callSid].recordFfmpeg = recordFfmpeg
        } catch (err) {
          console.error('Error launching record ffmpeg:', err)
        }
 
        
        // 2) ffmpeg #2 => decode mulaw -> 8kHz PCM => AssemblyAI
        try {
          const decodeFfmpeg = spawn('ffmpeg', [
            '-f', 'mulaw',
            '-ar', '8000',
            '-i', 'pipe:0',
            '-ac', '1',
            '-ar', '8000',
            '-f', 's16le',
            'pipe:1',
          ])
          decodeFfmpeg.on('close', (code) => {
            console.log(`decodeFfmpeg for ${callSid} exited with code ${code}`)
          })
          decodeFfmpeg.on('error', (err) => {
            console.error(`decodeFfmpeg error for ${callSid}:`, err)
          })

          calls[callSid].decodeFfmpeg = decodeFfmpeg
        } catch (err) {
          console.error('Error launching decode ffmpeg:', err)
        }

        // 3) Create transcription provider
        try {
          // Create transcription provider based on configuration
          const transcriptionProvider = createTranscriptionProvider();
          
          transcriptionProvider.on('open', ({ sessionId }) => {
            console.log(`Transcription session opened: ${sessionId}`);
          });

          transcriptionProvider.on('transcript', (result: TranscriptionResult) => {
            if (!result.text) return;
              
            // Merge multiple final transcripts into current turn
            if (result.message_type === 'FinalTranscript') {
              // Make sure we have a state for this call
              if (!calls[callSid!]) {
                console.error(`No call state found for ${callSid} in transcript handler`);
                return;
              }
              
              // Add transcript to current turn, with a space if there's already content
              if (calls[callSid!].currentTurnText) {
                calls[callSid!].currentTurnText += ' ' + result.text;
            } else {
                calls[callSid!].currentTurnText = result.text;
              }
              
              // Always log final transcripts regardless of TRANSCRIPT_LOG_FINAL_ONLY setting
              console.log(`[Final Transcript] Call ${callSid} - "${result.text}"`);
              
              // Show current accumulated turn text
              const currentTurn = calls[callSid!].currentTurnText.trim();
              if (currentTurn.length > 0) {
                console.log(`[Current Turn Text] Call ${callSid} - Total ${currentTurn.length} chars - "${currentTurn}"`);
                
                // Enhanced logging for better debugging of immediate processing
                console.log(`[IMMEDIATE-PROCESS-DEBUG] Speech detected: ${calls[callSid!].hasSpeechDetected}, Speech frames: ${calls[callSid!].speechFramesInCurrentTurn}, Min frames needed: ${MIN_SPEECH_FRAMES}, Finalization lock: ${calls[callSid!].finalizationInProgress}`);
                
                // CRITICAL FIX: Always detect speech when we have a final transcript!
                // If we have a transcript, we definitely had speech
                if (!calls[callSid!].hasSpeechDetected) {
                  console.log(`[IMMEDIATE-PROCESS-DEBUG] 🔧 FIX: Transcript received but hasSpeechDetected=false, forcing to true`);
                  calls[callSid!].hasSpeechDetected = true;
                }
                
                // Same for speech frames - ensure we have enough to process
                if (calls[callSid!].speechFramesInCurrentTurn < MIN_SPEECH_FRAMES) {
                  console.log(`[IMMEDIATE-PROCESS-DEBUG] 🔧 FIX: Transcript received but speechFramesInCurrentTurn=${calls[callSid!].speechFramesInCurrentTurn}, forcing to ${MIN_SPEECH_FRAMES}`);
                  calls[callSid!].speechFramesInCurrentTurn = MIN_SPEECH_FRAMES;
                }
                
                // Clear any pending finalization timeout since we now have transcript
                if (calls[callSid!].pendingFinalizeTimeout) {
                  console.log(`[IMMEDIATE-PROCESS-DEBUG] Clearing pending finalization timeout - transcript arrived before timeout fired`);
                  clearTimeout(calls[callSid!].pendingFinalizeTimeout);
                  calls[callSid!].pendingFinalizeTimeout = undefined;
                }
                
                // INSTANT PROCESSING: Process immediately, but respect finalization lock
                if (!calls[callSid!].finalizationInProgress) {
                  // If no finalization is in progress, we can take the lead
                  console.log(`[IMMEDIATE-PROCESS-DEBUG] ✅ INSTANT PROCESSING: Final transcript received - processing immediately`);
                  console.log(`[FINALIZE-LOCK] Setting finalization lock for call ${callSid} - transcript handler`);
                  
                  // Set the lock to prevent VAD from also finalizing
                  calls[callSid!].finalizationInProgress = true;
                  
                  // Process the turn
                  finalizeTurn(callSid!);
                  calls[callSid!].consecutiveSilenceFrames = 0;
                  calls[callSid!].speechFramesInCurrentTurn = 0;
                  calls[callSid!].hasSpeechDetected = false;
                  
                  // Release the lock
                  calls[callSid!].finalizationInProgress = false;
                  console.log(`[FINALIZE-LOCK] Released finalization lock for call ${callSid} - transcript handler complete`);
                } else {
                  // If finalization is already in progress, just log
                  console.log(`[IMMEDIATE-PROCESS-DEBUG] ⚠️ Finalization already in progress - transcript handler will skip processing`);
                }
              }
            } else if (TRANSCRIPT_LOG_ALL) {
              // Only log partials if explicitly enabled
              console.log(`[PARTIAL for ${callSid}] => ${result.text}`)
            }
          });

          transcriptionProvider.on('error', (error: Error) => {
            console.error(`Transcription error for ${callSid}:`, error)
          });

          transcriptionProvider.on('close', (code: number, reason: string) => {
            console.log(`Transcription closed for ${callSid}: ${code} ${reason}`)
          });

          await transcriptionProvider.connect();
          calls[callSid].transcriptionProvider = transcriptionProvider;

          // read decodeFfmpeg stdout => feed transcription provider + do VAD
          const decodeFfmpeg = calls[callSid].decodeFfmpeg;
          if (decodeFfmpeg) {
            decodeFfmpeg.stdout.on('data', async (chunk: Buffer) => {
              // Send to transcription provider
              transcriptionProvider.sendAudio(chunk);
              // Also do VAD chunking
              await handleVadAndTurns(callSid!, chunk);
            });
          }
        } catch (err) {
          console.error('Error creating transcription provider:', err);
        }
        break

      case 'media': {
        if (!callSid) break
        const audioBuffer = Buffer.from(data.media.payload, 'base64')

        // Write chunk to recordFfmpeg
        const recordFfmpeg = calls[callSid].recordFfmpeg
        if (recordFfmpeg) {
          recordFfmpeg.stdin.write(audioBuffer)
        }

        // Write chunk to decodeFfmpeg
        const decodeFfmpeg = calls[callSid].decodeFfmpeg
        if (decodeFfmpeg) {
          decodeFfmpeg.stdin.write(audioBuffer)
        }

          // Update streamSid if provided in media event and not already set
          if (data.streamSid && calls[callSid] && !calls[callSid].streamSid) {
            calls[callSid].streamSid = data.streamSid;
            console.log(`Updated streamSid for ${callSid} to ${data.streamSid}`);
        }
        break
      }

      case 'stop':
        console.log('Twilio stop event for callSid:', callSid)
        if (callSid) {
          cleanupCall(callSid)
        }
        break

      default:
        console.log('Unknown Twilio event:', data.event)
    }
    } catch (err) {
      console.error('Error handling WebSocket message:', err);
    }
  })
})

// Log WebSocket server errors
wss.on('error', (error) => {
  console.error('WebSocket server error:', error);
});

//-----------------------------------------------
// Helper: End-of-call cleanup
//-----------------------------------------------
function cleanupCall(callSid: string) {
  const state = calls[callSid]
  if (!state) return

  // --- BEGIN UPDATE CALL HISTORY ON END ---
  const callEndTime = new Date();
  let callDurationSeconds = null;
  if (state.callStartTime) {
      callDurationSeconds = Math.round((callEndTime.getTime() - state.callStartTime.getTime()) / 1000);
  }
  updateCallRecordOnEnd(callSid, callEndTime, callDurationSeconds).catch(err => {
      console.error(`[DB] Failed final update for call ${callSid}:`, err);
  });
  // --- END UPDATE CALL HISTORY ON END ---

  // Close recordFfmpeg
  if (state.recordFfmpeg) {
    state.recordFfmpeg.stdin.end()
    state.recordFfmpeg = undefined
  }
  // Close decodeFfmpeg
  if (state.decodeFfmpeg) {
    state.decodeFfmpeg.stdin.end()
    state.decodeFfmpeg = undefined
  }
  
  // Close transcription provider
  if (state.transcriptionProvider) {
    console.log(`Closing transcription provider for ${callSid}...`)
    state.transcriptionProvider.close()
    state.transcriptionProvider = undefined
  }
  
  // Close AAI transcriber (legacy support)
  if (state.aaiTranscriber) {
    console.log(`Closing AAI transcriber for ${callSid}...`)
    state.aaiTranscriber.close()
    state.aaiTranscriber = undefined
  }
  
  // Kill any current audio streaming process
  if (state.currentFfmpegProcess) {
    console.log(`Killing current audio streaming process for ${callSid}...`);
    try {
      state.currentFfmpegProcess.kill();
    } catch (err) {
      console.error(`Error killing ffmpeg process: ${err}`);
    }
    state.currentFfmpegProcess = undefined;
  }
  
  // Clear any audio duration timeout
  if (state.currentSpeakingTimeout) {
    console.log(`Clearing audio duration timeout for call ${callSid}`);
    clearTimeout(state.currentSpeakingTimeout);
    state.currentSpeakingTimeout = undefined;
  }

  // Clear any pending finalization timeout
  if (state.pendingFinalizeTimeout) {
    console.log(`Clearing pending finalization timeout for call ${callSid}`);
    clearTimeout(state.pendingFinalizeTimeout);
    state.pendingFinalizeTimeout = undefined;
  }

  // If there's any partial turn not ended, finalize it
  const { currentTurnText, conversation } = state
  if (currentTurnText.trim()) {
    conversation.push({ speaker: 'caller', text: currentTurnText.trim() })
    // Save the final conversation state
    saveConversation(callSid, conversation);
  }

  // Reset media packet counter
  if (mediaPacketCounts[callSid]) {
    delete mediaPacketCounts[callSid];
  }

  // Clean up audio response files for this call
  cleanupCallAudioFiles(callSid);
  
  // Clean up conversation files for this call
  cleanupConversationFiles(callSid);

  delete calls[callSid]
}

// Function to clean up audio files for a specific call
function cleanupCallAudioFiles(callSid: string) {
  // Get all files in the audio directory
  if (!fs.existsSync(AUDIO_DIR)) return;
  
  const files = fs.readdirSync(AUDIO_DIR);
  
  // Find and delete all files for this callSid
  for (const file of files) {
    if (file.startsWith(callSid + '_')) {
      const filePath = path.join(AUDIO_DIR, file);
      try {
        fs.unlinkSync(filePath);
        console.log(`Deleted audio file: ${filePath}`);
      } catch (err) {
        console.error(`Failed to delete audio file ${filePath}:`, err);
      }
    }
  }
}

// Function to clean up conversation files for a specific call
function cleanupConversationFiles(callSid: string) {
  // Get all files in the conversations directory
  if (!fs.existsSync(CONVERSATIONS_DIR)) return;
  
  try {
    const filePath = path.join(CONVERSATIONS_DIR, `${callSid}.json`);
    if (fs.existsSync(filePath)) {
      // Move to a completed conversations directory instead of deleting
      const completedDir = path.join(CONVERSATIONS_DIR, 'completed');
      if (!fs.existsSync(completedDir)) {
        fs.mkdirSync(completedDir, { recursive: true });
      }
      
      // Add timestamp to filename to avoid collisions
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const newPath = path.join(completedDir, `${callSid}_${timestamp}.json`);
      
      fs.renameSync(filePath, newPath);
      console.log(`Moved conversation file for ${callSid} to completed directory`);
    }
  } catch (err) {
    console.error(`Failed to cleanup conversation file for ${callSid}:`, err);
  }
}

// Set up periodic audio file cleanup in case of abrupt server termination
function setupPeriodicCleanup() {
  // Clean up audio files older than 1 hour every 15 minutes
  setInterval(() => {
    try {
      if (!fs.existsSync(AUDIO_DIR)) return;
      
      const files = fs.readdirSync(AUDIO_DIR);
      const now = Date.now();
      const ONE_HOUR = 60 * 60 * 1000;
      
      for (const file of files) {
        const filePath = path.join(AUDIO_DIR, file);
        const stats = fs.statSync(filePath);
        
        // If file is older than 1 hour, delete it
        if (now - stats.mtimeMs > ONE_HOUR) {
          try {
            fs.unlinkSync(filePath);
            console.log(`Deleted old audio file: ${filePath}`);
          } catch (err) {
            console.error(`Failed to delete old audio file ${filePath}:`, err);
          }
        }
      }
    } catch (err) {
      console.error('Error in periodic cleanup:', err);
    }
  }, 15 * 60 * 1000); // Run every 15 minutes
}

// Handle process termination signals
process.on('SIGINT', () => {
  console.log('Server shutting down, cleaning up audio files...');
  cleanupAllAudioFiles();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Server shutting down, cleaning up audio files...');
  cleanupAllAudioFiles();
  process.exit(0);
});

// Function to clean up all audio files
function cleanupAllAudioFiles() {
  if (!fs.existsSync(AUDIO_DIR)) return;
  
  try {
    const files = fs.readdirSync(AUDIO_DIR);
    for (const file of files) {
      const filePath = path.join(AUDIO_DIR, file);
      try {
        fs.unlinkSync(filePath);
      } catch (e) {
        console.error(`Failed to delete ${filePath}:`, e);
      }
    }
    console.log('All audio files cleaned up');
  } catch (err) {
    console.error('Error cleaning up audio files:', err);
  }
}

//-----------------------------------------------
// 3) handleVadAndTurns => splits chunk, calls VAD
//-----------------------------------------------

async function handleVadAndTurns(callSid: string, chunk: Buffer) {
  const state = calls[callSid];
  if (!state) return;

  // Properly calculate frames with consistent byte size
  let leftover = state.leftover;
  const framesPlusLeftover = splitTo20msFrames(chunk, leftover);
  leftover = framesPlusLeftover.pop()!;
  const frames = framesPlusLeftover;
  state.leftover = leftover;

  // Initialize speech frames counter if it doesn't exist
  if (state.speechFramesInCurrentTurn === undefined) {
    state.speechFramesInCurrentTurn = 0;
  }

  // Track VAD results to reduce logging
  let speechFrames = 0;
  let silenceFrames = 0;
  let energyDetectionCount = 0; // Count energy-based detections vs VAD

  // Check for user interruption - process frames even while model is speaking
  // to detect if user is interrupting
  let userInterruptDetected = false;
  // Make interruption detection super aggressive - detect with just 1 frame of speech
  const INTERRUPT_SPEECH_THRESHOLD = 1;
  const interruptDetectionFrames = [];
  
  // Track consecutive speech frames - only reset when we get several silence frames
  const RESET_INTERRUPT_SILENCE_THRESHOLD = 2; // Reduced from 3 - Reset interruption detection after fewer silence frames
  let consecutiveSilenceFrames = 0;

  for (const frame of frames) {
    try {
      // Verify frame is the correct size (320 bytes for 20ms of 8kHz, 16-bit mono audio)
      // If not, log and skip to prevent sending malformed data to VAD
      if (frame.length !== 320 && frame !== leftover) {
        console.warn(`[VAD] Skipping abnormal frame size: ${frame.length} bytes`);
        continue;
      }

      // Add timeout to prevent hanging on VAD requests
      // Include callSid in the URL query parameters
      const vadUrl = `${VAD_ENDPOINT}?callSid=${encodeURIComponent(callSid)}`;
      const response = await axios.post(vadUrl, frame, {
        headers: { 'Content-Type': 'application/octet-stream' },
        timeout: 1000, // Reduced timeout for faster failure detection
        validateStatus: (status) => status === 200, // Only accept 200 responses
      });
      
      // Check for valid response format
      if (!response.data) {
        console.warn(`[VAD] Empty response from VAD server`);
        continue;
      }
      
      // Enhanced response validation
      const data = response.data;

      // Check if we're getting info messages (collecting short frames)
      if (data.info && data.info.includes("Collecting short frames")) {
        // This is not a real speech detection response, just a status update
        continue;
      }
      
      // Track energy detection vs VAD - used for logging
      if (data.method === 'energy' || data.method === 'energy_fallback') {
        energyDetectionCount++;
      }
      
      // Check for errors in the response
      if (data.error) {
        console.warn(`[VAD] Server reported error: ${data.error}`);
      }
      
      // Validate the speech field exists
      if (typeof data.speech !== 'boolean') {
        console.warn(`[VAD] Invalid response format from VAD server: ${JSON.stringify(data)}`);
        continue;
      }

      // Track if we detect consistent speech during model's speaking turn
      if (state.isModelSpeaking) {
      if (data.speech) {
          interruptDetectionFrames.push(true);
          consecutiveSilenceFrames = 0; // Reset silence counter
          
          // Enhanced logging for interrupt detection
          if (interruptDetectionFrames.length === 1) {
            console.log(`[INTERRUPT-DEBUG] Call ${callSid} - First speech frame detected while model speaking (method: ${data.method || 'unknown'})`);
          }
          
          // Log progress toward interruption detection
          if (VERBOSE_VAD_LOGGING) {
            console.log(`[VAD] Call ${callSid} - Potential interruption - Speech detected during model speaking (frame ${interruptDetectionFrames.length})`);
          }
          
          // If we've detected enough consecutive speech frames while model is speaking,
          // treat it as an interruption - now just 1 frame!
          if (interruptDetectionFrames.length >= INTERRUPT_SPEECH_THRESHOLD) {
            userInterruptDetected = true;
            console.log(`[INTERRUPT-DEBUG] Call ${callSid} - INTERRUPTION DETECTED - Threshold met: ${interruptDetectionFrames.length}/${INTERRUPT_SPEECH_THRESHOLD} frames`);
            console.log(`[VAD] Call ${callSid} - USER INTERRUPTION DETECTED - Stopping assistant's turn after ${interruptDetectionFrames.length} speech frames`);
            
            // Handle the interruption immediately
            console.log(`[INTERRUPT-DEBUG] Handling interruption IMMEDIATELY to prevent delays`);
            handleInterruption(callSid);
            
            // Since we've handled the interruption, reset model speaking state immediately
            state.isModelSpeaking = false;
            console.log(`[INTERRUPT-DEBUG] Force reset isModelSpeaking to false`);
            
            // Reset detection frames
            interruptDetectionFrames.length = 0;
          }
        } else {
          // Count consecutive silence frames
          consecutiveSilenceFrames++;
          
          // Only reset detection frames if we've had enough consecutive silence
          if (consecutiveSilenceFrames >= RESET_INTERRUPT_SILENCE_THRESHOLD) {
            if (interruptDetectionFrames.length > 0 && VERBOSE_VAD_LOGGING) {
              console.log(`[VAD] Call ${callSid} - Resetting interruption detection after ${consecutiveSilenceFrames} silence frames`);
            }
            interruptDetectionFrames.length = 0;
            consecutiveSilenceFrames = 0;
          }
        }
      }

      // Normal turn processing (don't process if model is still speaking and no interruption)
      if (!state.isModelSpeaking || userInterruptDetected) {
        if (data.speech) {
          speechFrames++;
          // Reset silence counter when speech is detected
        state.consecutiveSilenceFrames = 0;
          // Increment speech counter to track meaningful speech
          // Add a larger increment to reach MIN_SPEECH_FRAMES faster
          // This ensures the first detected speech frame immediately enables transcript processing
          state.speechFramesInCurrentTurn += 5; // Increment by 5 to reach threshold immediately
          
          // Mark that we've detected speech for this turn, even if no transcript yet
          state.hasSpeechDetected = true;
          
          // Log first detected speech in a turn
          if (state.speechFramesInCurrentTurn <= 5 && VERBOSE_VAD_LOGGING) {
            console.log(`[VAD] Call ${callSid} - SPEECH DETECTED (method: ${data.method || 'unknown'}) - Starting new turn`);
            console.log(`[VAD] Call ${callSid} - Ensuring MIN_SPEECH_FRAMES threshold met immediately - set to ${state.speechFramesInCurrentTurn} frames`);
          }
          
          // Log when significant speech is detected
          if (state.speechFramesInCurrentTurn >= MIN_SPEECH_FRAMES && state.speechFramesInCurrentTurn <= MIN_SPEECH_FRAMES + 5 && VERBOSE_VAD_LOGGING) {
            console.log(`[VAD] Call ${callSid} - SIGNIFICANT SPEECH DETECTED - ${state.speechFramesInCurrentTurn} frames of speech (threshold: ${MIN_SPEECH_FRAMES})`);
          }
      } else {
          silenceFrames++;
        state.consecutiveSilenceFrames++;
          
          // Log silence detection progress
          if (VERBOSE_VAD_LOGGING && state.speechFramesInCurrentTurn >= MIN_SPEECH_FRAMES) {
            // Only log at certain thresholds to avoid console spam
            const silenceThresholds = [20, 40, 60, 80, 100, 120, 140, 160, 180, SILENCE_FRAMES_THRESHOLD];
            if (silenceThresholds.includes(state.consecutiveSilenceFrames)) {
              console.log(`[VAD] Call ${callSid} - SILENCE DETECTED - ${state.consecutiveSilenceFrames} frames of silence (threshold: ${SILENCE_FRAMES_THRESHOLD})`);
            }
          }
          
          // Only consider finalizing turn if we've detected enough meaningful speech first
          if (state.speechFramesInCurrentTurn >= MIN_SPEECH_FRAMES) {
            // If we've detected a significant amount of speech followed by silence
        if (state.consecutiveSilenceFrames >= SILENCE_FRAMES_THRESHOLD) {
              // Check if this is a long pause or medium pause
              if (state.consecutiveSilenceFrames >= LONG_PAUSE_THRESHOLD) {
                // Long pause - definitely end the turn
                console.log(`[VAD] Call ${callSid} - LONG PAUSE DETECTED (${state.consecutiveSilenceFrames} frames) - Finalizing turn`);
                
                // CRITICAL FIX: Set lock flag to prevent duplicate finalization
                if (!state.finalizationInProgress) {
                  state.finalizationInProgress = true;
                  console.log(`[FINALIZE-LOCK] Setting finalization lock for call ${callSid} - VAD long pause`);
                  
                  // Allow more time for transcript to arrive before finalizing
                  setTimeout(async () => {
                    // Only finalize if no transcript has processed it already
                    if (state.finalizationInProgress) {
          await finalizeTurn(callSid);
          state.consecutiveSilenceFrames = 0;
                      state.speechFramesInCurrentTurn = 0;
                      state.hasSpeechDetected = false; // Reset speech detected flag
                      state.finalizationInProgress = false; // Release the lock
                      console.log(`[FINALIZE-LOCK] Released finalization lock for call ${callSid} - VAD turn complete`);
                    } else {
                      console.log(`[FINALIZE-LOCK] Turn already finalized for call ${callSid} - VAD skipping`);
                    }
                  }, 500); // Wait extra 500ms for transcript to arrive
                } else {
                  console.log(`[FINALIZE-LOCK] Finalization already in progress for call ${callSid} - VAD skipping`);
                }
              } else {
                // Medium pause - end the turn if there's enough content
                if (state.currentTurnText.trim().length > 0) {
                  console.log(`[VAD] Call ${callSid} - MEDIUM PAUSE WITH CONTENT - Finalizing turn with "${state.currentTurnText.trim()}"`);
                  
                  // CRITICAL FIX: Set lock flag to prevent duplicate finalization
                  if (!state.finalizationInProgress) {
                    state.finalizationInProgress = true;
                    console.log(`[FINALIZE-LOCK] Setting finalization lock for call ${callSid} - VAD medium pause`);
                    
                    // Allow more time for transcript to arrive before finalizing
                    setTimeout(async () => {
                      // Only finalize if no transcript has processed it already
                      if (state.finalizationInProgress) {
                        await finalizeTurn(callSid);
                        state.consecutiveSilenceFrames = 0;
                        state.speechFramesInCurrentTurn = 0;
                        state.hasSpeechDetected = false; // Reset speech detected flag
                        state.finalizationInProgress = false; // Release the lock
                        console.log(`[FINALIZE-LOCK] Released finalization lock for call ${callSid} - VAD turn complete`);
                      } else {
                        console.log(`[FINALIZE-LOCK] Turn already finalized for call ${callSid} - VAD skipping`);
                      }
                    }, 500); // Wait extra 500ms for transcript to arrive
                  } else {
                    console.log(`[FINALIZE-LOCK] Finalization already in progress for call ${callSid} - VAD skipping`);
                  }
                } else if (state.hasSpeechDetected) {
                  // We detected speech but no transcript yet
                  console.log(`[VAD] Call ${callSid} - MEDIUM PAUSE WITH DETECTED SPEECH BUT NO TRANSCRIPT YET - Setting delayed finalization`);
                  
                  // Clear any existing timeout
                  if (state.pendingFinalizeTimeout) {
                    clearTimeout(state.pendingFinalizeTimeout);
                    state.pendingFinalizeTimeout = undefined;
                  }
                  
                  // Set a timeout to finalize this turn even if no transcript arrives
                  state.pendingFinalizeTimeout = setTimeout(async () => {
                    // Only proceed if we still have no transcript but detected speech
                    if (state.hasSpeechDetected && state.currentTurnText.trim().length === 0) {
                      console.log(`[VAD] Call ${callSid} - TRANSCRIPT WAIT TIMEOUT - Finalizing turn with empty text`);
                      
                      // Respect finalization lock
                      if (!state.finalizationInProgress) {
                        // Set the lock to prevent VAD or transcript handler from also finalizing
                        state.finalizationInProgress = true;
                        console.log(`[FINALIZE-LOCK] Setting finalization lock for call ${callSid} - transcript wait timeout`);
                        
                        // Create a placeholder utterance since we know speech occurred
                        state.currentTurnText = "[Speech detected but not transcribed]";
                        await finalizeTurn(callSid);
                        state.consecutiveSilenceFrames = 0;
                        state.speechFramesInCurrentTurn = 0;
                        state.hasSpeechDetected = false;
                        
                        // Release the lock
                        state.finalizationInProgress = false;
                        console.log(`[FINALIZE-LOCK] Released finalization lock for call ${callSid} - transcript wait timeout complete`);
                      } else {
                        console.log(`[FINALIZE-LOCK] Finalization already in progress for call ${callSid} - transcript wait timeout skipping`);
                      }
                    }
                    state.pendingFinalizeTimeout = undefined;
                  }, TRANSCRIPT_WAIT_TIMEOUT);
                } else {
                  // Not enough content yet, keep waiting despite the silence
                  if (VERBOSE_VAD_LOGGING && state.consecutiveSilenceFrames % 20 === 0) {
                    console.log(`[VAD] Call ${callSid} - MEDIUM PAUSE BUT NO CONTENT - Continuing turn`);
                  }
                }
              }
            }
          }
        }
      }
    } catch (err) {
      // More detailed error logging with error type
      if (axios.isAxiosError(err)) {
        const status = err.response?.status;
        const message = err.response?.data || err.message;
        console.error(`VAD call failed (HTTP ${status}) for callSid=${callSid}:`, message);
      } else {
      console.error(`VAD call failed for callSid=${callSid}:`, err);
      }
      
      // Despite VAD failure, still count as silence to prevent hanging
      silenceFrames++;
      // Don't increment state.consecutiveSilenceFrames to prevent false turn endings
    }
  }

  // Only log a summary if there were frames processed
  if (frames.length > 0) {
    // Log only occasionally (e.g., every 50 chunks)
    if (!state.chunkCount) state.chunkCount = 0;
    state.chunkCount++;
    
    // Skip logging completely by checking against a very high interval
    if (state.chunkCount % VAD_LOG_INTERVAL === 0) {
      console.log(`VAD processed ${frames.length} frames for ${callSid}, speech: ${speechFrames}, silence: ${silenceFrames}, energy detections: ${energyDetectionCount}, silence streak: ${state.consecutiveSilenceFrames}, speech frames in turn: ${state.speechFramesInCurrentTurn}`);
    }
  }
}

// Add interruption handler
async function handleInterruption(callSid: string) {
  const state = calls[callSid];
  if (!state) return;
  
  console.log(`[INTERRUPT-DEBUG] Handling user interruption for call ${callSid}`);
  console.log(`[INTERRUPT-DEBUG] Current model speaking state: ${state.isModelSpeaking}`);
  
  // Stop audio playback if in progress
  if (state.isModelSpeaking) {
    try {
      // CRITICAL: Clear any duration-based timeout that would automatically end speaking
      if (state.currentSpeakingTimeout) {
        console.log(`[INTERRUPT-DEBUG] Clearing duration-based speaking timeout`);
        clearTimeout(state.currentSpeakingTimeout);
        state.currentSpeakingTimeout = undefined;
      }
      
      // CRITICAL FIX: Make sure we properly kill the ffmpeg process FIRST before anything else
      if (state.currentFfmpegProcess) {
        const pid = state.currentFfmpegProcess.pid;
        console.log(`[INTERRUPT-DEBUG] KILLING ffmpeg process with PID: ${pid}`);
        
        // Properly kill the process - first close all streams to prevent hanging
        try {
          if (state.currentFfmpegProcess.stdin) {
            state.currentFfmpegProcess.stdin.end();
            state.currentFfmpegProcess.stdin.destroy();
          }
          if (state.currentFfmpegProcess.stdout) {
            state.currentFfmpegProcess.stdout.destroy();
          }
          if (state.currentFfmpegProcess.stderr) {
            state.currentFfmpegProcess.stderr.destroy();
          }
          
          // Kill with SIGKILL immediately - don't wait
          state.currentFfmpegProcess.kill('SIGKILL');
          
          // Also try the system kill command as a backup
          try {
            console.log(`[INTERRUPT-DEBUG] Also using system kill for PID: ${pid}`);
            // Use process.kill as a direct way to send the signal
            process.kill(pid, 'SIGKILL');
          } catch (killErr) {
            // Ignore errors from system kill - the process might already be gone
          }
        } catch (err) {
          console.error(`[INTERRUPT-DEBUG] Error killing ffmpeg:`, err);
        }
        
        // Immediately clear the reference
        state.currentFfmpegProcess = undefined;
        console.log(`[INTERRUPT-DEBUG] Cleared ffmpeg process reference`);
      } else {
        console.log(`[INTERRUPT-DEBUG] No ffmpeg process found to kill`);
      }
      
      // Find the WebSocket connection for this call
      const wsConnection = Array.from(wss.clients).find((client: any) => {
        return client.callSid === callSid && client.readyState === WebSocket.OPEN;
      }) as WebSocket & { callSid?: string };
      
      if (wsConnection) {
        console.log(`[INTERRUPT-DEBUG] Found active WebSocket connection for call ${callSid}`);
        console.log(`[INTERRUPT-DEBUG] WebSocket readyState: ${wsConnection.readyState} (1=OPEN)`);
        
        // Get the streamSid from call state
        const streamSid = state.streamSid || callSid;
        console.log(`[INTERRUPT-DEBUG] Using streamSid: ${streamSid}`);
        
        // Create timestamp for logging
        const timestamp = Date.now();
        console.log(`\n===== CLEAR COMMAND DEBUG - START ${new Date().toISOString()} =====`);
        console.log(`[TWILIO-CLEAR-DEBUG] Starting to monitor Twilio responses after clear command`);
        
        // Set up a temporary message handler with enhanced debugging
        const originalOnMessage = wsConnection.onmessage;
        wsConnection.onmessage = function(event) {
          try {
            // Parse and log any messages received after our clear command
            const data = JSON.parse(event.data.toString());
            const elapsed = Date.now() - timestamp;
            
            // Create a visually distinct log entry for Twilio responses
            console.log(`\n>>>>> TWILIO RESPONSE @ T+${elapsed}ms <<<<<`);
            console.log(JSON.stringify(data, null, 2));
            console.log(`>>>>> END TWILIO RESPONSE <<<<<\n`);
            
            // Specific logging for different event types
            if (data.event) {
              console.log(`[TWILIO-CLEAR-DEBUG] Received event type: "${data.event}" after clear command`);
              
              // Special handling for specific events
              if (data.event === 'connected' || data.event === 'start') {
                console.log(`[TWILIO-CLEAR-DEBUG] ⚠️ Twilio sent "${data.event}" event - this is a RECONNECTION, not a response to clear`);
              } else if (data.event === 'mark') {
                console.log(`[TWILIO-CLEAR-DEBUG] ✓ Twilio acknowledged our mark event`);
              } else if (data.event === 'media') {
                console.log(`[TWILIO-CLEAR-DEBUG] Twilio sent media packet after clear command`);
              } else if (data.event === 'clear') {
                console.log(`[TWILIO-CLEAR-DEBUG] ✓ Twilio acknowledged our clear command!`);
              } else if (data.event === 'stop') {
                console.log(`[TWILIO-CLEAR-DEBUG] ⚠️ Twilio sent stop event - connection may be closing`);
              } else {
                console.log(`[TWILIO-CLEAR-DEBUG] Received other event type: ${data.event}`);
              }
            } else {
              console.log(`[TWILIO-CLEAR-DEBUG] Received message with no event property`);
            }
            
            // Further examine for error or status information
            if (data.error) {
              console.error(`[TWILIO-CLEAR-DEBUG] ❌ ERROR in Twilio response: ${data.error}`);
            }
            if (data.status) {
              console.log(`[TWILIO-CLEAR-DEBUG] Status in Twilio response: ${data.status}`);
            }
            
            // Still call original handler if it exists
            if (originalOnMessage) {
              originalOnMessage.call(wsConnection, event);
            }
          } catch (err) {
            console.error(`[TWILIO-CLEAR-DEBUG] Error parsing Twilio response:`, err);
            console.error(`[TWILIO-CLEAR-DEBUG] Raw message:`, event.data.toString());
          }
        };
        
        // Restore original handler after 1 second to capture responses (reduced from 3)
        setTimeout(() => {
          console.log(`[TWILIO-CLEAR-DEBUG] Restoring original message handler`);
          wsConnection.onmessage = originalOnMessage;
          console.log(`===== CLEAR COMMAND DEBUG - END ${new Date().toISOString()} =====\n`);
        }, 1000);
        
        // CRITICAL: Try the simple format first - this is the most reliable
        const clearMessage1 = JSON.stringify({
          event: 'clear',
          streamSid: streamSid
        });
        
        console.log(`[TWILIO-CLEAR-DEBUG] 📣 SENDING CLEAR COMMAND: ${clearMessage1}`);
        wsConnection.send(clearMessage1);
        
        // Small delay before mark event - reduced to 25ms
        setTimeout(() => {
          try {
            if (wsConnection.readyState === WebSocket.OPEN) {
              // Send a mark message as a final attempt to clear the buffer
              const markMessage = JSON.stringify({
                event: 'mark',
                streamSid: streamSid,
                mark: {
                  name: 'interrupt-marker-' + timestamp
                }
              });
              
              console.log(`[TWILIO-CLEAR-DEBUG] 📣 SENDING MARK EVENT: ${markMessage}`);
              wsConnection.send(markMessage);
            }
          } catch (err) {
            console.error(`[TWILIO-CLEAR-DEBUG] Error sending mark message:`, err);
          }
        }, 25);
      } else {
        console.error(`[INTERRUPT-DEBUG] No active WebSocket connection found for call ${callSid}`);
      }
    } catch (err) {
      console.error(`[INTERRUPT-DEBUG] Error stopping audio playback for call ${callSid}:`, err);
    }
    
    // CRITICAL FIX: Force reset all streaming flags
    setModelSpeaking(callSid, false, "User interruption handled - forced reset");
    
    // Clear any path to the current audio file to prevent re-use
    state.audioResponsePath = undefined;
  
  // If there was a partial model response, save it to conversation history
  if (state.currentModelResponse) {
      console.log(`[INTERRUPT-DEBUG] Saving interrupted response to conversation history`);
    state.conversation.push({ 
      speaker: 'assistant', 
      text: state.currentModelResponse + ' [interrupted]' 
    });
    state.currentModelResponse = undefined;
      
      // Save conversation after interruption
      saveConversation(callSid, state.conversation);
    }
    
    // Reset user speech counts to prepare for new turn
    state.speechFramesInCurrentTurn = 0;
    state.consecutiveSilenceFrames = 0;
    state.currentTurnText = '';
    
    console.log(`[INTERRUPT-DEBUG] Interruption handling complete for call ${callSid}`);
  } else {
    console.log(`[INTERRUPT-DEBUG] Model not speaking for call ${callSid}, no interruption needed`);
  }
}

// Function to generate audio from text using Azure Speech SDK
async function generateAudioResponse(text: string, callSid: string): Promise<string | null> {
  try {
    // Log for debugging
    console.log(`Generating audio response for callSid=${callSid}`);
    
    // Generate a unique filename based on timestamp and callSid
    const timestamp = Date.now();
    const audioFilename = `${callSid}_${timestamp}.wav`;
    const audioPath = path.join(AUDIO_DIR, audioFilename);
    
    // Create Speech SDK config for TTS
    const speechConfig = sdk.SpeechConfig.fromSubscription(AZURE_SPEECH_KEY, AZURE_SPEECH_REGION);
    speechConfig.speechSynthesisVoiceName = AZURE_SPEECH_VOICE;
    
    // Create audio config for file output
    const audioConfig = sdk.AudioConfig.fromAudioFileOutput(audioPath);
    
    // Create the speech synthesizer
    const synthesizer = new sdk.SpeechSynthesizer(speechConfig, audioConfig);
    
    // Return a promise that resolves with the audio file path when synthesis is complete
    return new Promise((resolve, reject) => {
      // Start speech synthesis
      synthesizer.speakTextAsync(
        text,
        // Success callback
        (result) => {
          if (result.reason === sdk.ResultReason.SynthesizingAudioCompleted) {
            console.log(`Audio response saved to ${audioPath}`);
            synthesizer.close();
            resolve(audioPath);
          } else {
            console.error(`Speech synthesis failed: ${result.errorDetails}`);
            synthesizer.close();
            reject(new Error(result.errorDetails));
          }
        },
        // Error callback
        (error) => {
          console.error(`Speech synthesis error: ${error}`);
          synthesizer.close();
          reject(error);
        }
      );
    });
  } catch (err) {
    console.error(`Error in audio generation for ${callSid}:`, err);
    return null;
  }
}

// Function to track when model speaking state changes
function setModelSpeaking(callSid: string, value: boolean, reason: string) {
  const state = calls[callSid];
  if (!state) return;
  
  // Only log if the value is actually changing
  if (state.isModelSpeaking !== value) {
    console.log(`[MODEL-SPEAKING] Call ${callSid}: ${state.isModelSpeaking} → ${value} (${reason})`);
    state.isModelSpeaking = value;
  }
}

// AssemblyAI transcription provider adapter
class AssemblyAIProvider implements TranscriptionProvider {
  private transcriber: RealtimeTranscriber;
  private client: AssemblyAI;
  private eventHandlers: {
    transcript: Array<(result: TranscriptionResult) => void>;
    error: Array<(error: Error) => void>;
    open: Array<(data: any) => void>;
    close: Array<(code: number, reason: string) => void>;
  };

  constructor(apiKey: string, sampleRate: number = 8000) {
    this.client = new AssemblyAI({ apiKey });
    this.eventHandlers = {
      transcript: [],
      error: [],
      open: [],
      close: []
    };
  }

  async connect(): Promise<void> {
    this.transcriber = this.client.realtime.transcriber({
      sampleRate: 8000,
    });

    // Set up event handlers
    this.transcriber.on('open', (data) => {
      this.eventHandlers.open.forEach(handler => handler(data));
    });

    this.transcriber.on('transcript', (transcript: RealtimeTranscript) => {
      const result: TranscriptionResult = {
        text: transcript.text || '',
        message_type: transcript.message_type as 'PartialTranscript' | 'FinalTranscript'
      };
      this.eventHandlers.transcript.forEach(handler => handler(result));
    });

    this.transcriber.on('error', (error) => {
      this.eventHandlers.error.forEach(handler => handler(error));
    });

    this.transcriber.on('close', (code, reason) => {
      this.eventHandlers.close.forEach(handler => handler(code, reason));
    });

    // Connect but don't return the SessionBeginsEventData, just wait for completion
    await this.transcriber.connect();
    return;
  }

  sendAudio(chunk: Buffer): void {
    if (this.transcriber) {
      this.transcriber.sendAudio(chunk);
    }
  }

  close(): void {
    if (this.transcriber) {
      this.transcriber.close();
    }
  }

  on(event: 'transcript' | 'error' | 'open' | 'close', handler: any): void {
    if (this.eventHandlers[event]) {
      this.eventHandlers[event].push(handler);
    }
  }
}

// Azure transcription provider adapter (skeleton)
class AzureSpeechProvider implements TranscriptionProvider {
  private recognizer: sdk.SpeechRecognizer | null = null;
  private audioFormat: sdk.AudioStreamFormat;
  private pushStream: sdk.PushAudioInputStream;
  private eventHandlers: {
    transcript: Array<(result: TranscriptionResult) => void>;
    error: Array<(error: Error) => void>;
    open: Array<(data: any) => void>;
    close: Array<(code: number, reason: string) => void>;
  };
  private isConnected: boolean = false;

  constructor(subscriptionKey: string, region: string) {
    // Initialize event handlers
    this.eventHandlers = {
      transcript: [],
      error: [],
      open: [],
      close: []
    };

    // Create format for 8kHz 16-bit mono PCM
    this.audioFormat = sdk.AudioStreamFormat.getWaveFormatPCM(8000, 16, 1);
    
    // Create the push stream for audio input
    this.pushStream = sdk.AudioInputStream.createPushStream(this.audioFormat);
  }

  async connect(): Promise<void> {
    try {
      // Create speech config
      const speechConfig = sdk.SpeechConfig.fromSubscription(
        AZURE_SPEECH_KEY, 
        AZURE_SPEECH_REGION
      );
      speechConfig.speechRecognitionLanguage = 'en-US';
      
      // Use continuous recognition mode
      speechConfig.enableDictation();
      
      // Create audio config from push stream
      const audioConfig = sdk.AudioConfig.fromStreamInput(this.pushStream);
      
      // Create the recognizer
      this.recognizer = new sdk.SpeechRecognizer(speechConfig, audioConfig);
      
      // Set up event handlers for Azure Speech recognition events
      this.recognizer.recognizing = (s, e) => {
        if (e.result.text) {
          const result: TranscriptionResult = {
            text: e.result.text,
            message_type: 'PartialTranscript'
          };
          this.eventHandlers.transcript.forEach(handler => handler(result));
        }
      };
      
      this.recognizer.recognized = (s, e) => {
        if (e.result.text) {
          const result: TranscriptionResult = {
            text: e.result.text,
            message_type: 'FinalTranscript'
          };
          this.eventHandlers.transcript.forEach(handler => handler(result));
        }
      };
      
      this.recognizer.canceled = (s, e) => {
        const error = new Error(`Recognition canceled: ${e.errorCode} ${e.errorDetails}`);
        this.eventHandlers.error.forEach(handler => handler(error));
      };
      
      this.recognizer.sessionStarted = (s, e) => {
        console.log(`Azure Speech session started: ${e.sessionId}`);
        this.isConnected = true;
        const sessionData = { sessionId: e.sessionId };
        this.eventHandlers.open.forEach(handler => handler(sessionData));
      };
      
      this.recognizer.sessionStopped = (s, e) => {
        console.log(`Azure Speech session stopped: ${e.sessionId}`);
        this.isConnected = false;
        this.eventHandlers.close.forEach(handler => handler(0, 'Session ended'));
      };
      
      // Start continuous recognition
      await this.recognizer.startContinuousRecognitionAsync();
      
      // Trigger open event if not triggered by sessionStarted
      if (!this.isConnected) {
        this.isConnected = true;
        const sessionData = { sessionId: 'unknown' };
        this.eventHandlers.open.forEach(handler => handler(sessionData));
      }
    } catch (error: any) {
      console.error('Azure Speech recognizer error:', error);
      const errorObj = new Error(String(error?.message || error));
      this.eventHandlers.error.forEach(handler => handler(errorObj));
      throw error;
    }
  }

  sendAudio(chunk: Buffer): void {
    if (this.pushStream && this.isConnected) {
      this.pushStream.write(chunk);
    }
  }

  close(): void {
    if (this.recognizer) {
      this.recognizer.stopContinuousRecognitionAsync(
        () => {
          console.log('Azure Speech recognition stopped');
          this.isConnected = false;
          if (this.recognizer) {
            this.recognizer.close();
            this.recognizer = null;
          }
        },
        (error: any) => {
          console.error('Error stopping Azure Speech recognition:', error);
          const errorObj = new Error(String(error?.message || error));
          this.eventHandlers.error.forEach(handler => handler(errorObj));
        }
      );
    }
  }

  on(event: 'transcript' | 'error' | 'open' | 'close', handler: any): void {
    if (this.eventHandlers[event]) {
      this.eventHandlers[event].push(handler);
    }
  }
}

// Factory function to create the appropriate transcription provider
function createTranscriptionProvider(): TranscriptionProvider {
  if (STT_PROVIDER === 'azure') {
    return new AzureSpeechProvider(AZURE_SPEECH_KEY, AZURE_SPEECH_REGION);
  } else {
    // Default to AssemblyAI
    return new AssemblyAIProvider(ASSEMBLYAI_API_KEY);
  }
}

// Safely parse JSON from WebSocket messages
function safeJSONParse(message: string) {
  try {
    return JSON.parse(message);
  } catch (err) {
    console.error('Failed to parse WS message:', err);
    return { event: 'error', error: 'Invalid JSON' };
  }
}

// Function to safely get call data
function getCallData(data: any) {
  if (!data) return null;
  
  // Try various formats of where the callSid might be
  if (data.start && typeof data.start === 'object' && data.start.callSid) {
    return data.start.callSid;
  }
  
  if (data.start && typeof data.start === 'string') {
    return data.start;
  }
  
  if (data.callSid) {
    return data.callSid;
  }
  
  return null;
}

// These functions are required but implemented elsewhere in the file
// Adding placeholders to fix linter errors
async function findUserIdByPhoneNumber(phoneNumber: string): Promise<string | null> {
  // Implementation exists elsewhere in the file
            return null;
        }

async function insertInitialCallRecord(callSid: string, startTime: Date, fromNumber: string | null, toNumber: string | null, userId: string | null) {
  // Implementation exists elsewhere in the file
}

function loadConversation(callSid: string): Array<{ speaker: string; text: string }> {
  // Implementation exists elsewhere in the file
  return [];
}

function saveConversation(callSid: string, conversation: Array<{ speaker: string; text: string }>) {
  // Implementation exists elsewhere in the file
}

async function updateCallRecordOnEnd(callSid: string, endTime: Date, durationSeconds: number | null) {
  // Implementation exists elsewhere in the file
}

async function finalizeTurn(callSid: string) {
  // Implementation exists elsewhere in the file
}

//-----------------------------------------------
// Start the Server
//-----------------------------------------------
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  // Set up periodic cleanup
  setupPeriodicCleanup();
});
