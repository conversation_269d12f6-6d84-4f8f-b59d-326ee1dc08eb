/*******************************************************************
 Working with twilio call answer & Transcription
 
/*******************************************************************
 * src/server.ts
 *
 * 1. Twilio -> Node.js: /voice => returns <Start> <Stream> TwiML
 * 2. Node.js receives audio (8 kHz μ-law) via WebSocket at /media
 * 3. We spawn two ffmpeg processes:
 *    (A) To record a WAV locally in ./recordings/<callSid>.wav
 *    (B) To decode μ-law -> linear PCM (s16le) for AssemblyAI
 * 4. We feed the decoded PCM into AssemblyAI's real-time transcriber
 * 5. Print partial/final transcripts and store references for cleanup
 *******************************************************************/

import express from 'express'
import bodyParser from 'body-parser'
import { twiml as TwiML } from 'twilio'
import http from 'http'
import WebSocket from 'ws'
import { spawn, ChildProcessWithoutNullStreams } from 'child_process'

// AssemblyAI Realtime
import { AssemblyAI, RealtimeTranscriber, RealtimeTranscript } from 'assemblyai'

const ASSEMBLYAI_API_KEY = '********************************'
const client = new AssemblyAI({ apiKey: ASSEMBLYAI_API_KEY })

//  Ensure you have a ./recordings folder in your project root
//  mkdir recordings

const app = express()
app.use(bodyParser.urlencoded({ extended: false }))
app.use(bodyParser.json())

// Data structures to track ffmpeg processes and AssemblyAI transcribers by callSid
const ffmpegRecorders: Record<string, ChildProcessWithoutNullStreams> = {}
const ffmpegDecoders: Record<string, ChildProcessWithoutNullStreams> = {}
const assemblyTranscribers: Record<string, RealtimeTranscriber> = {}

// -----------------------------------------------------
// 1) Twilio incoming call -> /voice
// -----------------------------------------------------
app.post('/voice', (req, res) => {
  const twiml = new TwiML.VoiceResponse()

  // <Start> a Media Stream so Twilio sends audio to wss://<host>/media
  twiml
    .start({ action: '/voiceStatusCallback' })
    .stream({
      url: `wss://${req.headers.host}/media`,
      track: 'inbound_track',
    })

  // Greeting
  twiml.say('Hello! We are streaming your audio to AssemblyAI and also recording a WAV file.')

  // Keep call open with a long <Pause>, else Twilio ends after <Say>
  twiml.pause({ length: 3600 })

  res.type('text/xml').send(twiml.toString())
})

// Optional callback after <Start>
app.post('/voiceStatusCallback', (req, res) => {
  console.log('Media Stream started. Call SID:', req.body.CallSid)
  res.sendStatus(200)
})

// -----------------------------------------------------
// 2) WebSocket /media: 8 kHz μ-law -> local WAV + AssemblyAI
// -----------------------------------------------------
const server = http.createServer(app)
const wss = new WebSocket.Server({ server, path: '/media' })

wss.on('connection', (ws) => {
  console.log('Twilio connected to /media WebSocket.')

  let callSid: string | null = null

  ws.on('message', async (msg: string) => {
    let data
    try {
      data = JSON.parse(msg)
    } catch (err) {
      console.error('Failed to parse WebSocket message:', err)
      return
    }

    switch (data.event) {
      case 'connected':
        console.log('Twilio event: connected')
        break

      case 'start':
        callSid = data.start.callSid
        console.log('Media start event. callSid:', callSid)

        // -------- 1) FFmpeg #1: Record WAV Locally --------
        try {
          const outPath = `./recordings/${callSid}.wav`
          const recordFfmpeg = spawn('ffmpeg', [
            '-f', 'mulaw',
            '-ar', '8000',
            '-i', 'pipe:0',
            '-ac', '1',
            '-ar', '8000',
            '-f', 'wav',
            outPath,
          ])

          recordFfmpeg.on('close', (code) => {
            console.log(`Recorder ffmpeg for ${callSid} exited with code ${code}`)
          })
          recordFfmpeg.on('error', (err) => {
            console.error(`Recorder ffmpeg error for ${callSid}:`, err)
          })

          ffmpegRecorders[callSid] = recordFfmpeg
        } catch (err) {
          console.error('Error launching recorder ffmpeg:', err)
        }

        // -------- 2) FFmpeg #2: Decode to s16le for AssemblyAI --------
        let decodeFfmpeg: ChildProcessWithoutNullStreams | null = null
        try {
          decodeFfmpeg = spawn('ffmpeg', [
            '-f', 'mulaw',
            '-ar', '8000',
            '-i', 'pipe:0',
            '-ac', '1',
            '-ar', '8000',
            '-f', 's16le', // raw 16-bit PCM
            'pipe:1',
          ])

          decodeFfmpeg.on('close', (code) => {
            console.log(`Decoder ffmpeg for ${callSid} exited with code ${code}`)
          })
          decodeFfmpeg.on('error', (err) => {
            console.error(`Decoder ffmpeg error for ${callSid}:`, err)
          })

          ffmpegDecoders[callSid] = decodeFfmpeg
        } catch (err) {
          console.error('Error launching decoder ffmpeg:', err)
        }

        // -------- 3) Create an AssemblyAI Realtime Transcriber --------
        try {
          const transcriber = client.realtime.transcriber({
            sampleRate: 8000, // since we're decoding to 8k PCM
          })

          transcriber.on('open', ({ sessionId }) => {
            console.log(`AssemblyAI session opened: ${sessionId}`)
          })

          transcriber.on('transcript', (rt: RealtimeTranscript) => {
            if (!rt.text) return
            if (rt.message_type === 'FinalTranscript') {
              console.log(`[AAI FINAL] ${rt.text}`)
            } else {
              console.log(`[AAI PARTIAL] ${rt.text}`)
            }
          })

          transcriber.on('error', (error: Error) => {
            console.error('AssemblyAI error:', error)
          })

          transcriber.on('close', (code: number, reason: string) => {
            console.log(`AssemblyAI closed for ${callSid}:`, code, reason)
          })

          // Connect to AssemblyAI
          await transcriber.connect()

          // Pipe decodeFfmpeg stdout -> AssemblyAI
          if (decodeFfmpeg) {
            decodeFfmpeg.stdout.on('data', (chunk: Buffer) => {
              transcriber.sendAudio(chunk)
            })
          }

          assemblyTranscribers[callSid] = transcriber
        } catch (err) {
          console.error('Error creating AssemblyAI transcriber:', err)
        }
        break

      case 'media': {
        // Twilio "media" -> base64-encoded raw mu-law
        if (!callSid) break
        const audioBuffer = Buffer.from(data.media.payload, 'base64')

        // 1) Send chunk to local WAV recorder
        const recordFfmpeg = ffmpegRecorders[callSid]
        if (recordFfmpeg) {
          recordFfmpeg.stdin.write(audioBuffer)
        }

        // 2) Send chunk to decode ffmpeg for AssemblyAI
        const decodeFfmpeg = ffmpegDecoders[callSid]
        if (decodeFfmpeg) {
          decodeFfmpeg.stdin.write(audioBuffer)
        }
        break
      }

      case 'stop':
        console.log('Twilio stop event for callSid:', callSid)
        if (callSid) {
          // Close recorder ffmpeg
          const recordFfmpeg = ffmpegRecorders[callSid]
          if (recordFfmpeg) {
            recordFfmpeg.stdin.end()
            delete ffmpegRecorders[callSid]
          }

          // Close decoder ffmpeg
          const decodeFfmpeg = ffmpegDecoders[callSid]
          if (decodeFfmpeg) {
            decodeFfmpeg.stdin.end()
            delete ffmpegDecoders[callSid]
          }

          // Close AssemblyAI transcriber
          const transcriber = assemblyTranscribers[callSid]
          if (transcriber) {
            console.log(`Closing AssemblyAI transcriber for ${callSid}...`)
            await transcriber.close()
            delete assemblyTranscribers[callSid]
          }
        }
        break

      default:
        console.log('Unknown Twilio event:', data.event)
    }
  })

  // If the user hangs up unexpectedly => WebSocket close
  ws.on('close', async () => {
    console.log('Twilio WebSocket disconnected.')
    if (callSid) {
      // End the local WAV recorder
      const recordFfmpeg = ffmpegRecorders[callSid]
      if (recordFfmpeg) {
        recordFfmpeg.stdin.end()
        delete ffmpegRecorders[callSid]
      }

      // End the decode ffmpeg
      const decodeFfmpeg = ffmpegDecoders[callSid]
      if (decodeFfmpeg) {
        decodeFfmpeg.stdin.end()
        delete ffmpegDecoders[callSid]
      }

      // Close AssemblyAI transcriber
      const transcriber = assemblyTranscribers[callSid]
      if (transcriber) {
        console.log(`Closing AssemblyAI transcriber for ${callSid}...`)
        await transcriber.close()
        delete assemblyTranscribers[callSid]
      }
    }
  })
})

// -----------------------------------------------------
// 3) Start Server on port 5000
// -----------------------------------------------------
const PORT = process.env.PORT || 5000
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`)
})
