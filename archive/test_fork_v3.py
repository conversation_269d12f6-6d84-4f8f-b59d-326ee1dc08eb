import json
import os
import telnyx
from flask import Flask, request
from openai import OpenAI
from dotenv import load_dotenv
import time
import re
import sys
import socket
import struct
import wave
import threading
import traceback
import audioop
from collections import OrderedDict
from multiprocessing import Process, Value, Queue

RECORDINGS_DIR = "recordings"
os.makedirs(RECORDINGS_DIR, exist_ok=True)

app = Flask(__name__)

# Define the IP and port you want to listen on for RTP
UDP_PORT = 5004
UDP_IP = "0.0.0.0"  # Listen on all available interfaces
UDP_TRANSMIT = "*************"  # Replace with your public IP or appropriate address

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

client = OpenAI()
assistant_id = 'asst_dLuE0A9zTpOYkz9m85NWz14x'

# Dictionary to store call handlers
call_handlers = {}

# Set to track processed event IDs
processed_event_ids = set()

class CallHandler:
    def __init__(self, call_control_id):
        self.call_control_id = call_control_id
        self.thread = None
        self.transcript_buffer = ""
        self.call_leg_id = None
        self.wav_filename = None
        self.rtp_process = None
        self.packet_count = 0
        self.is_receiving = Value('b', True)  # Shared boolean between processes
        self.sample_width = 2  # 16-bit audio
        self.channels = 1      # Mono
        self.sample_rate = 8000  # G.711 sample rate
        self.lock = threading.Lock()
        print(f"CallHandler created for call_control_id: {call_control_id}")

    def handle_answered(self):
        print('handle_answered func triggered')
        try:
            print('trying handle_answered')
            # Debugging: Log the current call control ID
            print(f"Handling answered call for call_control_id: {self.call_control_id}")

            # Create the Telnyx call object
            call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
            call.call_control_id = self.call_control_id

            # Debugging: Log before starting transcription
            print("Starting transcription for call.")
            call.transcription_start(language="en", interim_results=True)

            # Assign a unique UDP port for this call
            self.udp_port = UDP_PORT
            #self.get_unique_udp_port()
            print(f"Assigned UDP port {self.udp_port} for call {self.call_control_id}")

            call.fork_start(
                target=f"udp:{UDP_TRANSMIT}:{self.udp_port}",
                rx=f"udp:{UDP_TRANSMIT}:{self.udp_port}",
                tx=f"udp:{UDP_TRANSMIT}:{self.udp_port+1}",
            )

            # Debugging: Log before thread creation
            print("Creating thread for call.")
            self.thread = client.beta.threads.create()
            print(f"Thread creation response: {self.thread}")

            # Debugging: Check and log thread creation
            if not self.thread:
                raise Exception("Thread creation failed. No thread object returned.")

            # Debugging: Log thread ID
            print(f"Thread created successfully for call {self.call_control_id}: {self.thread.id}")
        except Exception as e:
            # Debugging: Log any errors encountered during execution
            print(f"Error in handle_answered for call {self.call_control_id}: {e}")

    def get_unique_udp_port(self):
        import random
        return random.randint(10000, 20000)

    def handle_transcription(self, transcript):
        with self.lock:
            print(f"Transcribed Text for call {self.call_control_id}: {transcript}")
            self.transcript_buffer += " " + transcript

    def handle_dtmf(self, digit):
        if digit == "#":
            print(f'---------# PRESSED for call {self.call_control_id}--------------------')
            # Start a new thread to handle the LLM and TTS
            threading.Thread(target=self.process_dtmf_pound).start()
        elif digit == "*":
            self.hangup()

    def process_dtmf_pound(self):
        try:
            with self.lock:
                transcript = self.transcript_buffer.strip() + " "
                self.transcript_buffer = ""
            response = self.send_to_llm(transcript)
            self.play_tts(response)
        except Exception as e:
            print(f"Error in process_dtmf_pound for call {self.call_control_id}: {e}")
            traceback.print_exc()

    def send_to_llm(self, transcript):
        print(f"Sending to LLM for call {self.call_control_id}, Thread ID: {self.thread.id}")

        message = client.beta.threads.messages.create(
            thread_id=self.thread.id,
            role="user",
            content=transcript,
        )

        run = client.beta.threads.runs.create(
            thread_id=self.thread.id,
            assistant_id=assistant_id,
            tool_choice='required',
        )

        run = self.wait_on_run(run)

        openai_output_raw = self.pretty_str(self.get_response())

        last_assistant_text = openai_output_raw.split('assistant:')[-1].strip()
        last_assistant_text = re.sub(r'【[^】]*】', '', last_assistant_text)

        print(f"LLM response for call {self.call_control_id}: {last_assistant_text}")
        return last_assistant_text

    def wait_on_run(self, run):
        while run.status == "queued" or run.status == "in_progress":
            run = client.beta.threads.runs.retrieve(
                thread_id=self.thread.id,
                run_id=run.id,
            )
            time.sleep(0.1)
        return run

    def pretty_str(self, messages):
        formatted_str = ""
        for m in messages:
            formatted_str += f"{m.role}: {m.content[0].text.value}\n"
        return formatted_str

    def get_response(self):
        return client.beta.threads.messages.list(thread_id=self.thread.id, order="asc")

    def play_tts(self, response_text):
        call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
        call.call_control_id = self.call_control_id
        # Uncomment the following lines if you want to use TTS
        call.speak(
            payload=response_text,
            language="en-US",
            voice="Polly.Amy"
        )

    def hangup(self):
        # Call hangup on the Telnyx call
        call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
        call.call_control_id = self.call_control_id
        call.hangup()
        # Stop the RTP listener and clean up
        self.stop_rtp_listener()
        # Remove this call handler from the dictionary after ensuring processes are done
        if self.call_control_id in call_handlers:
            del call_handlers[self.call_control_id]

    def start_rtp_listener(self, call_leg_id):
        self.call_leg_id = call_leg_id
        try:
            # Create filename with full path
            self.wav_filename = os.path.join(RECORDINGS_DIR, f'output_{call_leg_id}.wav')
            print(f"Attempting to create WAV file: {self.wav_filename}")

            # Start RTP listener in a separate process
            self.rtp_process = Process(target=receive_rtp_process, args=(
                UDP_IP,
                self.udp_port,
                self.wav_filename,
                self.is_receiving,
                self.sample_width,
                self.channels,
                self.sample_rate,
                self.call_control_id
            ))
            self.rtp_process.start()

            print(f"RTP listener process started for call_leg_id: {call_leg_id}")
        except Exception as e:
            print(f"Error starting RTP listener: {e}")
            traceback.print_exc()

    def stop_rtp_listener(self):
        self.is_receiving.value = False
        if self.rtp_process and self.rtp_process.is_alive():
            self.rtp_process.join()
        print(f"RTP listener process stopped for call_leg_id: {self.call_leg_id}")

# Function to receive RTP in a separate process
def receive_rtp_process(
    udp_ip,
    udp_port,
    wav_filename,
    is_receiving,
    sample_width,
    channels,
    sample_rate,
    call_control_id
):
    packet_buffer = OrderedDict()
    expected_seq = None
    packet_count = 0

    try:
        # Open WAV file within the process
        with open(wav_filename, 'wb') as file_obj:
            wav_file = wave.open(file_obj, 'wb')
            wav_file.setnchannels(channels)
            wav_file.setsampwidth(sample_width)
            wav_file.setframerate(sample_rate)

            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.bind((udp_ip, udp_port))
            sock.settimeout(1.0)  # Set a timeout for the socket

            print(f"Listening for RTP on {udp_ip}:{udp_port}")

            while is_receiving.value:
                try:
                    data, addr = sock.recvfrom(1500)
                    #strep_header, rtp_payload = parse_strep_packet(data)
                    rtp_payload = data
                    if rtp_payload:
                        process_rtp_packet(
                            rtp_payload,
                            packet_buffer,
                            expected_seq,
                            wav_file,
                            sample_width,
                            call_control_id
                        )
                        packet_count += 1
                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"Error receiving RTP for call {call_control_id}: {e}")
                    traceback.print_exc()
            sock.close()
            wav_file.close()
    except Exception as e:
        print(f"Error in receive_rtp_process for call {call_control_id}: {e}")
        traceback.print_exc()

def parse_strep_packet(packet):
    if len(packet) < 24:
        print(f"Received packet is too short: {len(packet)} bytes")
        return None, b''

    # Parse STREP header
    header = struct.unpack('!HHLQQ', packet[:24])
    magic_version_flags = header[0]
    header_len = magic_version_flags & 0xFF
    version = (magic_version_flags >> 10) & 0xF
    leg = (magic_version_flags >> 5) & 0x1
    direction = (magic_version_flags >> 4) & 0x1
    call_leg_id = (header[3] << 64) | header[4]

    strep_header = {
        'version': version,
        'leg': leg,
        'direction': direction,
        'header_len': header_len,
        'call_leg_id': hex(call_leg_id)
    }

    # Extract RTP payload
    rtp_payload = packet[header_len:]

    return strep_header, rtp_payload

def process_rtp_packet(
    rtp_payload,
    packet_buffer,
    expected_seq,
    wav_file,
    sample_width,
    call_control_id
):
    # Parse RTP header
    if len(rtp_payload) < 12:
        print(f"RTP payload too short: {len(rtp_payload)} bytes")
        return

    rtp_header = struct.unpack('!BBHII', rtp_payload[:12])
    payload_type = rtp_header[1] & 0x7F
    sequence_number = rtp_header[2]
    timestamp = rtp_header[3]

    # Extract audio data
    audio_data = rtp_payload[12:]
    
    

    # Determine codec
    if payload_type == 0:
        codec = 'PCMU'
    elif payload_type == 8:
        codec = 'PCMA'
    elif payload_type == 101:
        print(f"Payload type {payload_type} is telephone-event; skipping packet.")
        return expected_seq  # Skip processing this packet
    else:
        print(f"Unsupported payload type: {payload_type}")
        return expected_seq

    # Buffer the packet
    packet_buffer[sequence_number] = (codec, audio_data)

    # Process buffered packets
    process_buffered_packets(packet_buffer, expected_seq, wav_file, sample_width, call_control_id)

def process_buffered_packets(
    packet_buffer,
    expected_seq,
    wav_file,
    sample_width,
    call_control_id
):
    while packet_buffer:
        if expected_seq is None:
            expected_seq = min(packet_buffer.keys())

        if expected_seq not in packet_buffer:
            break

        codec, audio_data = packet_buffer.pop(expected_seq)

        pcm_data = (audioop.ulaw2lin(audio_data, sample_width)
                    if codec == 'PCMU'
                    else audioop.alaw2lin(audio_data, sample_width))

        if sys.byteorder == 'big':
            pcm_data = audioop.byteswap(pcm_data, sample_width)

        try:
            if wav_file:
                wav_file.writeframes(pcm_data)
            else:
                print("Warning: wav_file is None")
        except Exception as e:
            print(f"Error writing to WAV file for call {call_control_id}: {e}")
            traceback.print_exc()

        expected_seq = (expected_seq + 1) & 0xFFFF

@app.route('/webhook', methods=['POST'])
def inbound():
    body = json.loads(request.data)
    event = body.get("data").get("event_type")
    call_control_id = body.get("data").get("payload").get("call_control_id")

    print(f"Received event: {event} for call_control_id: {call_control_id}")

    try:
        if event == "call.initiated":
            handle_call_initiated(body)
        elif event == "call.answered":
            handle_call_answered(body)
        elif event == "call.transcription":
            handle_call_transcription(body)
        elif event == 'call.dtmf.received':
            handle_call_dtmf_received(body)
        elif event == 'call.hangup':
            handle_call_hangup(body)
        elif event == 'call.fork.started':
            handle_call_fork_started(body)
        elif event == 'call.fork.stopped':
            handle_call_fork_stopped(body)
    except Exception as e:
        print(f"Error: {e}")
        return json.dumps({"success": False}), 500, {"ContentType": "application/json"}
    return json.dumps({"success": True}), 200, {"ContentType": "application/json"}

def handle_call_initiated(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
    call.call_control_id = call_control_id
    call.answer()
    print('call answered')

def handle_call_answered(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    print('###')
    print(call_control_id)
    print('###')
    # Ensure the CallHandler exists
    if call_control_id not in call_handlers:
        print(f'Creating CallHandler for call_control_id: {call_control_id}')
        call_handlers[call_control_id] = CallHandler(call_control_id)
    call_handlers[call_control_id].handle_answered()
    print('CallHandler.handle_answered() called successfully')

def handle_call_transcription(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    transcript = body.get("data").get("payload").get("transcription_data").get("transcript")
    if call_control_id in call_handlers:
        call_handlers[call_control_id].handle_transcription(transcript)

def handle_call_dtmf_received(body):
    event_id = body.get("data").get("id")
    if event_id in processed_event_ids:
        print(f"Event {event_id} already processed.")
        return
    processed_event_ids.add(event_id)
    call_control_id = body.get("data").get("payload").get("call_control_id")
    dtmf = body.get("data").get("payload").get("digit")
    if call_control_id in call_handlers:
        call_handlers[call_control_id].handle_dtmf(dtmf)

def handle_call_hangup(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    if call_control_id in call_handlers:
        handler = call_handlers[call_control_id]
        handler.stop_rtp_listener()
        del call_handlers[call_control_id]

def handle_call_fork_started(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    call_leg_id = body.get("data").get("payload").get('call_leg_id')
    print(f"Fork started event received. call_control_id: {call_control_id}, call_leg_id: {call_leg_id}")
    if call_control_id in call_handlers and call_leg_id:
        call_handlers[call_control_id].start_rtp_listener(call_leg_id)
    else:
        print(f"Error: CallHandler not found or call_leg_id missing for call_control_id: {call_control_id}")

def handle_call_fork_stopped(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    print(f"Fork stopped event received. call_control_id: {call_control_id}")
    # Handle fork stopped event if necessary

if __name__ == '__main__':
    app.run(debug=True, port=5000)