import json
import os
import telnyx
from flask import Flask, request
from openai import OpenAI
from dotenv import load_dotenv

app = Flask(__name__)

load_dotenv()

# Set your OpenAI API key
#OpenAI.api_key = os.getenv("OPENAI_API_KEY")
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

client = OpenAI()
#client = OpenAI()
  #organization='org-jzHx0Gzd7dY46sBE3jgOddAF'
  #project=os.getenv("OPENAI_API_KEY")
  #api_key = os.getenv("OPENAI_API_KEY")
#)
# Endpoint for receiving webhook events
@app.route('/webhook', methods=['POST'])
def inbound():
    body = json.loads(request.data)
    event = body.get("data").get("event_type")

    try:
        if event == "call.initiated":
            handle_call_initiated(body)
        elif event == "call.answered":
            handle_call_answered(body)
        elif event == "call.transcription":
            handle_call_transcription(body)
        elif event == 'call.dtmf.received':
            handle_call_dtmf_received(body)
    except Exception as e:
        print(f"Error: {e}")
        return json.dumps({"success":False}), 500, {"ContentType":"application/json"}
    return json.dumps({"success":True}), 200, {"ContentType":"application/json"}

def handle_call_initiated(body):
    call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
    call.call_control_id = body.get("data").get("payload").get("call_control_id")
    call.answer()

def handle_call_answered(body):
    call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
    call.call_control_id = body.get("data").get("payload").get("call_control_id")
    call.transcription_start(language="en", interim_results=True)

transcript_buffer = ""

def handle_call_transcription(body):
    global transcript_buffer
    transcription_data = body.get("data").get("payload").get("transcription_data")
    transcript = transcription_data.get("transcript")
    print(f"Transcribed Text: {transcript}")
    transcript_buffer += " " + transcript

def handle_call_dtmf_received(body):
    global transcript_buffer
    dtmf = body.get("data").get("payload").get("digits")

    if dtmf == "#":  # Assuming '#' is the signal for end of speech
        response = send_to_llm(transcript_buffer.strip())
        play_tts(response)
        transcript_buffer = ""
    elif dtmf == "*":  # Assuming '*' is the signal to hang up
        call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
        call.call_control_id = body.get("data").get("payload").get("call_control_id")
        call.hangup()

def send_to_llm(transcript):
    response = openai.Completion.create(
        engine="davinci",
        prompt=transcript,
        max_tokens=150
    )
    return response.choices[0].text.strip()

def play_tts(response_text):
    tts_audio = convert_text_to_speech(response_text)
    call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
    call.playback_start(media_url=tts_audio)

def convert_text_to_speech(text):
    # Implement conversion of text to speech using a TTS service
    # This could be a call to a TTS API like Google TTS or Amazon Polly
    # For example, you could save the audio file and return its URL
    pass

if __name__ == '__main__':
    app.run(debug=True, port=5000)
