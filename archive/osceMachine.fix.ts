// @ts-nocheck
import { createMachine, assign, send, forwardTo, ActorRefFrom } from 'xstate';
import axios from 'axios';
import { OsceEvents, TransitionReasons } from './osceEvents';
import { OsceContext, OsceEvent, DtmfInputEvent, CaseData, OsceMachineSchema, Utterance } from './types';
import { getCallSid } from './server';

// Cache for cross-call context data
const contextBackup = {
  lastCallSid: undefined,
  contexts: {} as Record<string, any>
};

// Call context backups to prevent the model losing context
export function backupContext(callSid: string, context: OsceContext) {
  console.log(`[XState] Backing up context for callSid ${callSid}`);
  contextBackup.lastCallSid = callSid;
  contextBackup.contexts[callSid] = context;
}

export function getBackupContext(callSid: string): OsceContext | undefined {
  return contextBackup.contexts[callSid];
}

// Initialize context for new calls
function initializeContextForCall(callSid: string): OsceContext {
  console.log(`[XState] Initializing context for callSid ${callSid}`);
  
  const initialContext = {
    // Transition state handling
    currentState: 'idle',
    lastTransitionTimestamp: Date.now(),
    
    // Call tracking
    callSid: callSid,
    
    // Turn tracking  
    lastUtterance: undefined,
    waitingForUserInput: false,
    
    // Conversation
    conversation: [],
    
    // Patient status
    caseData: undefined,
    patient: undefined,
    
    // Speech timing
    speechFramesInCurrentTurn: 0,
    consecutiveSilenceFrames: 0,
    
    // Generated response
    responseText: '',
    
    // DTMF
    lastDtmfDigit: undefined,
    transitionReason: undefined,
    
    // Turn handling
    turnNumber: 0
  };
  
  // Store the new context in our backup
  contextBackup.contexts[callSid] = initialContext;
  
  return initialContext;
}

export function createOsceMachine() {
  return createMachine<OsceContext, OsceEvents, OsceMachineSchema>({
    id: 'osce',
    initial: 'idle',
    predictableActionArguments: true,
    context: {
      currentState: 'idle',
      lastTransitionTimestamp: Date.now(),
      callSid: undefined,
      lastUtterance: undefined,
      waitingForUserInput: false,
      conversation: [],
      caseData: undefined,
      patient: undefined,
      speechFramesInCurrentTurn: 0,
      consecutiveSilenceFrames: 0,
      responseText: '',
      
      turnNumber: 0,
      transitionReason: undefined,
      lastDtmfDigit: undefined
    },
    
    // Global event handlers
    on: {
      DTMF_INPUT: [
        {
          // First version with guard for transition state
          actions: ['logDtmfEvent', 'logGuardSuccess'],
          guard: 'isInTransitionState',
          cond: (context, event) => {
            // This should be used to add conditions based on event properties
            return true;
          }
        },
        // Log that guard failed but continue to other handlers
        {
          actions: ['logDtmfEvent', 'logGuardFailure']
        }
      ],
      
      START: {
        actions: assign((context, event) => {
          // Initialize context for the call
          const callSid = event?.callSid || 'unknown';
          const existingContext = getBackupContext(callSid);
          
          if (existingContext) {
            console.log(`[XState] Using existing context for callSid ${callSid}`);
            return existingContext;
          } else {
            return initializeContextForCall(callSid);
          }
        }),
        target: 'idle'
      },

      TTS_FINISHED: {
        actions: (context, event) => {
          console.log(`[XState] TTS finished, lastDtmfDigit: ${event?.lastDtmfDigit}`);
          if (event?.lastDtmfDigit) {
            console.log(`[XState] Transitioning due to DTMF: ${event.lastDtmfDigit}`);
            event.transitionReason = `dtmf_${event.lastDtmfDigit}`;
          }
        }
      },

      INTERNAL_SET_CASE: {
        actions: assign({
          caseData: (context, event) => {
            console.log(`[XState] Setting caseData to ${event?.data?.scenario || 'undefined'}`);
            return event?.data;
          },
          currentTurnIndex: 0, // Reset turn index when setting new case
          turnStartTime: Date.now() // Mark the start time
        })
      },
      
      // Transition to the target state and pass along data
      INTERNAL_NEXT_STATE: [
        {
          actions: (context, event) => {
            console.log(`[XState] Handling INTERNAL_NEXT_STATE to ${event?.targetState}`, event);
            
            // Update the timestamp of the last transition
            context.lastTransitionTimestamp = Date.now();
          }
        }
      ],
      
      RESTART: {
        actions: assign((_, event) => {
          // When restarting, treat it like a new call
          const callSid = event?.callSid || 'unknown';
          return initializeContextForCall(callSid);
        }),
        target: 'idle'  
      }
    },
  
    states: {
      idle: {
        // Handles any case setup and logic
        on: {
          // Transition to case_selection on start
          INTERNAL_NEXT_STATE: [
            {
              target: 'case_selection',
              actions: assign((context, event) => {
                context.responseText = event?.transitionMessage || 'Welcome to the medical examination simulation.';
                return context;
              }),
              cond: (context, event) => {
                return event?.targetState === 'case_selection';
              }
            },
            // For all other targetStates, use this rule
            {
              target: (context, event) => {
                return event?.targetState || 'case_selection';
              },
              actions: assign((context, event) => {
                context.responseText = event?.transitionMessage || 'Moving to the next stage.';
                return context;
              })
            }
          ]
        },
        // Initial entry actions
        entry: ['logStateEntry']
      },
      
      transition: { // New Generic Transition State
        on: {
          // Handles transitioning to the next state automatically
          TRANSITION_TIMER: [
            {
              // Transition to history_taking from case_selection (if that's where we came from)
              target: (context) => {
                // Set a default target based on the transitionReason
                let target = 'history_taking'; // Default
                
                // Only use transitionReason if current state is transition
                if (context.currentState === 'transition') {
                  if (context.transitionReason === TransitionReasons.EXAMINATION) {
                    target = 'examination';
                  } else if (context.transitionReason === TransitionReasons.DIAGNOSIS) {
                    target = 'diagnosis';
                  } else if (context.transitionReason === TransitionReasons.CLOSING) {
                    target = 'closing';
                  }
                }
                
                console.log(`[XState] Transition determining target: ${target} (reason=${context.transitionReason}, currentState=${context.currentState})`);
                return target;
              },
              actions: assign((context, event) => {
                // Reset dtmf and transition reason
                context.transitionReason = undefined;
                context.lastDtmfDigit = undefined;
                return context;
              })
            }
          ],
          
          DTMF_INPUT: [
            {
              actions: [
                (context, event) => {
                  const callSid = event?.callSid || context.callSid || 'unknown';
                  console.log(`[XState] Received DTMF in transition state: ${event?.digit} (callSid: ${callSid})`);
                  backupContext(callSid, context);
                },
                assign((context, event) => {
                  // Set DTMF for use in next state
                  context.lastDtmfDigit = event?.digit;
                  
                  // Set transitionReason based on digit
                  if (event?.digit === '1') {
                    context.transitionReason = TransitionReasons.EXAMINATION;
                    context.responseText = "Transitioning to physical examination.";
                  } 
                  else if (event?.digit === '2') {
                    context.transitionReason = TransitionReasons.DIAGNOSIS;
                    context.responseText = "Let's discuss the diagnosis.";
                  }
                  else if (event?.digit === '3') {
                    context.transitionReason = TransitionReasons.CLOSING;
                    context.responseText = "Let's wrap up this encounter.";
                  }
                  
                  console.log(`[XState] Set transitionReason to ${context.transitionReason} based on DTMF ${event?.digit}`);
                  return context;
                })
              ]
            }
          ]
        },
        
        // Wait for a little bit, then transition
        after: {
          3000: { actions: send('TRANSITION_TIMER') }
        },
        
        entry: [
          assign({ currentState: 'transition' }),
          'logStateEntry'
        ]
      },
      
      case_selection: {
        entry: [
          assign({ currentState: 'case_selection' }),
          'logStateEntry',
          'selectCaseAutomatically'
        ],
        
        on: {
          // Handle transitioning to history_taking after case selection
          INTERNAL_NEXT_STATE: {
            target: 'transition',
            cond: (context, event) => {
              return event?.targetState === 'history_taking';
            },
            actions: [
              assign((context, event) => {
                context.responseText = event?.transitionMessage || 'Let\'s begin the history taking phase.';
                return context;
              })
            ]
          }
        }
      },
      
      history_taking: {
        entry: [
          assign({ currentState: 'history_taking' }),
          'logStateEntry',
          // Provide welcome message
          assign({
            responseText: 'We are now in the history taking phase. Ask the patient questions about their symptoms, or press 1 for examination, 2 for diagnosis, or 3 to close the encounter.'
          })
        ],
        
        initial: 'waitingInput',
        
        states: {
          waitingInput: {
            on: {
              TRANSCRIPTION: {
                actions: [
                  assign((context, event) => {
                    if (event && typeof event.text === 'string') {
                      context.lastUtterance = event.text || '';
                    } else {
                      console.error('[XState] Invalid event.text in TRANSCRIPTION event:', event);
                      context.lastUtterance = '';
                    }
                    return context;
                  })
                ],
                target: 'processing'
              },
              
              DTMF_INPUT: [
                {
                  actions: [
                    (context, event) => {
                      // Log the DTMF input
                      console.log(`[XState] Received DTMF in history_taking: ${event?.digit}`);
                    },
                    assign((context, event) => {
                      // Store the digit
                      context.lastDtmfDigit = event?.digit;
                      
                      // Set transitionReason based on digit
                      if (event?.digit === '1') {
                        context.transitionReason = TransitionReasons.EXAMINATION;
                        context.responseText = "Transitioning to physical examination.";
                      } 
                      else if (event?.digit === '2') {
                        context.transitionReason = TransitionReasons.DIAGNOSIS;
                        context.responseText = "Let's discuss the diagnosis.";
                      }
                      else if (event?.digit === '3') {
                        context.transitionReason = TransitionReasons.CLOSING;
                        context.responseText = "Let's wrap up this encounter.";
                      }
                      
                      return context;
                    })
                  ],
                  // If transition reason was set, go to transition state
                  target: (context) => {
                    return context.transitionReason ? 'transition' : 'waitingInput';
                  }
                }
              ]
            }
          },
          
          processing: {
            invoke: {
              src: 'fetchPatient',
              onDone: {
                actions: [
                  assign({
                    patient: (context, event) => event.data 
                  }),
                  (context, event) => {
                    console.log(`[XState] Patient data received: ${JSON.stringify(event.data)}`);
                    const callSid = context.callSid;
                    if (callSid) {
                      backupContext(callSid, context);
                    } else {
                      console.error(`[XState] No callSid found in context when saving patient data`);
                    }
                  }
                ],
                target: 'respondingToPatient'
              },
              onError: {
                actions: [
                  (context, ev) => console.error(`[XState] Error fetching patient data: ${ev?.data}`, ev),
                  assign({
                    patient: undefined
                  })
                ],
                target: 'waitingInput'
              }
            }
          },
          
          respondingToPatient: {
            entry: [
              (context) => {
                console.log(`[XState] Responding to patient with scenario: ${context.caseData?.scenario}`);
                const callSid = context.callSid;
                if (callSid) {
                  backupContext(callSid, context);
                }
              },
              assign({
                lastUtterance: (context) => {
                  return context.lastUtterance || '';
                },
                responseText: (context) => {
                  if (context.caseData?.scenario === 'Chest Pain') {
                    // For chest pain scenario, provide appropriate responses
                    const utterance = context.lastUtterance?.toLowerCase() || '';
                    
                    if (utterance.includes('pain') || utterance.includes('chest')) {
                      return "I've been having this sharp pain in my chest for about 2 hours now. It feels like pressure and it's mostly on the left side.";
                    } else if (utterance.includes('breath') || utterance.includes('breathing')) {
                      return "I'm having some trouble catching my breath, especially when the pain gets worse.";
                    } else if (utterance.includes('medication') || utterance.includes('medicine')) {
                      return "I take atorvastatin for my cholesterol and lisinopril for high blood pressure.";
                    } else {
                      return "I'm not sure I understand. Could you rephrase your question?";
                    }
                  } else {
                    // Generic response for other scenarios
                    return "I'm sorry, I don't have specific information about this case scenario.";
                  }
                }
              })
            ],
            // After responding, go back to waiting for input
            always: {
              target: 'waitingInput'
            }
          },
          
          transition: {
            entry: assign({ currentState: 'transition' }),
            always: { target: '#osce.transition' }
          }
        }
      },
      
      examination: {
        initial: 'waitingInput',
        entry: assign({ currentState: 'examination' }),
        states: {
          waitingInput: {}
        }
      },
      
      diagnosis: {
        initial: 'waitingInput',
        entry: assign({ currentState: 'diagnosis' }),
        states: {
          waitingInput: {}
        }
      },
      
      closing: {
        initial: 'waitingInput',
        entry: assign({ currentState: 'closing' }),
        states: {
          waitingInput: {}
        }
      },
      
      ended: {
        type: 'final',
        entry: assign({ currentState: 'ended' })
      }
    },
    
    // Global actions
    actions: {
      incrementSpeechFrames: assign({
        speechFramesInCurrentTurn: (ctx) => ctx.speechFramesInCurrentTurn + 1,
        consecutiveSilenceFrames: 0
      }),
      
      incrementSilenceFrames: assign({
        consecutiveSilenceFrames: (ctx) => ctx.consecutiveSilenceFrames + 1
      }),
      
      logTransitionReason: (context, event) => {
        console.log(`[XState] Transition reason: ${event?.reason}`);
      },
      
      logStateEntry: (context) => {
        console.log(`[XState] Entering state: ${context.currentState}`);
      },
      
      logDtmfEvent: (context, event) => {
        console.log(`[XState] DTMF event received: ${event?.digit} in state: ${context.currentState}`);
      },
      
      setLlmStartTime: assign({
        llmInvokeStartTime: (context) => {
          const now = Date.now();
          console.log(`[XState] LLM start time: ${now}`);
          return now;
        }
      }),
      
      setTtsStartTime: assign({
        ttsStartTime: (context) => {
          const now = Date.now();
          console.log(`[XState] TTS start time: ${now}`);
          return now;
        }
      }),
      
      selectCaseAutomatically: (context) => {
        // Simulate selecting a case
        const caseData = {
          scenario: 'Chest Pain',
          patientName: 'John Smith',
          patientAge: 55,
          patientGender: 'male',
          vitalSigns: {
            bloodPressure: '140/90',
            heartRate: 95,
            respiratoryRate: 18,
            temperature: 37.1,
            oxygenSaturation: 96
          }
        };
        
        // Send event to set the case
        context.caseData = caseData;
        return send({ type: 'INTERNAL_SET_CASE', data: caseData });
      },
      
      logGuardSuccess: (context, event) => {
        console.log(`[XState Guard] SUCCESS: Guard passed for DTMF '${event?.digit}' in state '${context.currentState}'`);
      },
      
      logGuardFailure: (context, event) => {
        console.log(`[XState Guard] FAILURE: Guard failed for DTMF '${event?.digit}' in state '${context.currentState}'`);  
      }
    },
    
    // Guards
    guards: {
      isInTransitionState: (context: OsceContext) => {
        const result = context.currentState === 'transition';
        console.log(`[XState Guard] isInTransitionState evaluated to: ${result} (currentState=${context.currentState})`);
        return result;
      }
    }
  });
}
