from sqlalchemy import Column, DateTime, PrimaryKeyConstraint, String, UniqueConstraint, Uuid, text
from sqlalchemy.orm import Mapped, declarative_base, mapped_column
from sqlalchemy.orm.base import Mapped

Base = declarative_base()


class Users(Base):
    __tablename__ = 'users'
    __table_args__ = (
        PrimaryKeyConstraint('user_id', name='users_pkey'),
        UniqueConstraint('phone_number', 'pin_code', name='unique_phone_pin')
    )

    user_id = mapped_column(Uuid)
    first_name = mapped_column(String(50))
    last_name = mapped_column(String(50))
    phone_number = mapped_column(String(15))
    pin_code = mapped_column(String(4))
    nickname = mapped_column(String(50))
    created_at = mapped_column(DateTime, server_default=text('now()'))
    updated_at = mapped_column(DateTime, server_default=text('now()'))
