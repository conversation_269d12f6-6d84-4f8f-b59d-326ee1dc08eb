import logging
import json
import base64

from flask import Flask, request, Response
from flask_sockets import Sockets

# Twilio helper library for generating TwiML
from twilio.twiml.voice_response import VoiceResponse, Start

app = Flask(__name__)
sockets = Sockets(app)

logging.basicConfig(level=logging.DEBUG)


@app.route('/voice', methods=['GET', 'POST'])
def voice():
    """
    <PERSON><PERSON><PERSON> will POST here at the start of a call.
    We'll return TwiML that instructs <PERSON><PERSON><PERSON> to start
    sending media over WebSocket, and keep the call open.
    """
    logging.info("Incoming call to /voice with data: %s", request.values)

    response = VoiceResponse()

    # 1. Tell <PERSON>wi<PERSON> to Start a Media Stream to our WebSocket endpoint.
    start = Start()
    # Replace 'my-ngrok-subdomain' with your actual subdomain:
    start.stream(
        url='wss://my-ngrok-subdomain.ngrok.io/stream',  
        name='MyAudioStream'
    )
    response.append(start)

    # 2. Keep the call active so the media stream doesn't end immediately.
    #    For example, say something and pause for 60 seconds:
    response.say("Hello! How can I help?", voice='woman')
    response.pause(length=5)

    # 3. Return TwiML as XML to Twilio
    return Response(str(response), mimetype='application/xml')


@sockets.route('/stream')
def media_stream(ws):
    """
    This WebSocket endpoint receives real-time audio from Twilio.
    We'll log and optionally process the audio here.
    """
    logging.info("WebSocket connection established to /stream.")

    while not ws.closed:
        message = ws.receive()
        if message is None:
            continue

        try:
            data = json.loads(message)
        except Exception as e:
            logging.error("Failed to parse WebSocket message: %s", e)
            continue

        event = data.get("event")
        logging.debug("Received event: %s", event)

        if event == "connected":
            logging.info("Stream connected: %s", data)
        elif event == "start":
            logging.info("Stream start: %s", data)
        elif event == "media":
            # The base64-encoded audio bytes are in data["media"]["payload"]
            payload = data["media"]["payload"]
            audio_data = base64.b64decode(payload)

            logging.info("Received %d bytes of audio data", len(audio_data))
            # Optionally process or store `audio_data` here
        elif event == "closed":
            logging.info("Stream closed: %s", data)
            break

    logging.info("WebSocket connection closed.")
    return ""


# Run the server with gevent for WebSocket support
if __name__ == "__main__":
    from gevent import pywsgi
    from geventwebsocket.handler import WebSocketHandler

    logging.info("Starting server on port 5000...")
    server = pywsgi.WSGIServer(("0.0.0.0", 5000), app, handler_class=WebSocketHandler)
    server.serve_forever()