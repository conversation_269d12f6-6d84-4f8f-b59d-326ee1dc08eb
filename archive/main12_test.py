import json
import os
import telnyx
from flask import Flask, request
from openai import OpenAI
from dotenv import load_dotenv
import time
import re

app = Flask(__name__)
load_dotenv()

def verify_environment():
    required_vars = [
        "TELNYX_CONNECTION_ID",
        "OPENAI_API_KEY",
        "TELNYX_API_KEY"
        #"WEBHOOK_URL"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        raise EnvironmentError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    try:
        client.models.list()
        print("OpenAI API key verified successfully")
    except Exception as e:
        raise EnvironmentError(f"OpenAI API key verification failed: {str(e)}")
    
    
def get_ngrok_url():
    try:
        # Try to get the ngrok tunnel URL
        response = requests.get('http://localhost:4040/api/tunnels')
        tunnels = response.json()['tunnels']
        for tunnel in tunnels:
            if tunnel['proto'] == 'https':
                return tunnel['public_url']
    except:
        return None


def verify_telnyx_config():
    try:
        telnyx.api_key = os.getenv("TELNYX_API_KEY")
        connection_id = os.getenv("TELNYX_CONNECTION_ID")
        
        # Verify the connection exists
        connections = telnyx.Connection.list()
        connection_ids = [conn.id for conn in connections]
        
        if connection_id not in connection_ids:
            raise ValueError(f"Connection ID {connection_id} not found in Telnyx account")
            
        print("Telnyx configuration verified successfully")
        return True
    except Exception as e:
        print(f"Error verifying Telnyx configuration: {e}")
        return False

os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
client = OpenAI()
assistant_id = 'asst_dLuE0A9zTpOYkz9m85NWz14x'

try:
    assistant = client.beta.assistants.retrieve(assistant_id)
    print(f"Successfully verified assistant ID: {assistant_id}")
except Exception as e:
    print(f"Error verifying assistant ID: {str(e)}")
    raise

call_handlers = {}
processed_event_ids = set()

class CallHandler:
    def __init__(self, call_control_id):
        self.call_control_id = call_control_id
        self.thread = None
        self.transcript_buffer = []
        self.transcription_started = False
        self.last_transcription_time = None
        
        try:
            self.thread = client.beta.threads.create()
            print(f"Thread created during initialization: {self.thread.id}")
        except Exception as e:
            print(f"Error creating thread during initialization: {str(e)}")

    def handle_answered(self):
        try:
            print(f"Handling answered call for call_control_id: {self.call_control_id}")
            
            call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
            call.call_control_id = self.call_control_id
            
            # Get webhook URL
            webhook_url = os.getenv("WEBHOOK_URL") or get_ngrok_url()
            if webhook_url:
                webhook_url = f"{webhook_url}/webhook"
            else:
                webhook_url = "https://your-default-webhook-url.com/webhook"  # Replace with your default webhook URL
            
            print(f"Using webhook URL: {webhook_url}")
            
            # Start transcription with specific parameters
            transcription_response = call.transcription_start(
                language="en-US",
                interim_results=True,
                client_state="transcribing",
                webhook_url=webhook_url,
                record_audio=False,
                minimum_silence_length=1
            )
            print(f"Transcription start response: {transcription_response}")
            self.transcription_started = True
            
            # Initial greeting
            time.sleep(1)
            welcome_message = "Hello! I'm ready to assist you. Please speak and press hash when you're done."
            self.play_tts(welcome_message)
            
        except Exception as e:
            print(f"Error in handle_answered: {str(e)}")
            raise

    def handle_transcription(self, transcript):
        try:
            current_time = time.time()
            self.last_transcription_time = current_time
            
            print(f"Handling transcription for call {self.call_control_id}")
            print(f"Received transcript: '{transcript}'")
            
            if transcript and transcript.strip():
                self.transcript_buffer.append(transcript.strip())
                print(f"Updated transcript buffer: {self.transcript_buffer}")
                
        except Exception as e:
            print(f"Error in handle_transcription: {str(e)}")

    def get_full_transcript(self):
        return " ".join(self.transcript_buffer)

    def handle_dtmf(self, digit):
        try:
            print(f"Received DTMF digit: {digit}")
            if digit == "#":
                print(f'---------# PRESSED for call {self.call_control_id}--------------------')
                
                full_transcript = self.get_full_transcript()
                print(f"Full transcript: '{full_transcript}'")
                
                if not full_transcript:
                    response = "I haven't heard anything yet. Please speak and then press hash again."
                else:
                    response = self.send_to_llm(full_transcript)
                
                self.play_tts(response)
                self.transcript_buffer = []  # Clear buffer after processing
                
            elif digit == "*":
                self.hangup()
                
        except Exception as e:
            print(f"Error in handle_dtmf: {str(e)}")
            raise

    def send_to_llm(self, transcript):
        try:
            print(f"Sending to LLM for call {self.call_control_id}, Thread ID: {self.thread.id}")

            message = client.beta.threads.messages.create(
                thread_id=self.thread.id,
                role="user",
                content=transcript
            )

            run = client.beta.threads.runs.create(
                thread_id=self.thread.id,
                assistant_id=assistant_id,
                tool_choice='required',
            )
                    
            run = self.wait_on_run(run)
            openai_output_raw = self.pretty_str(self.get_response())
            last_assistant_text = openai_output_raw.split('assistant:')[-1].strip()
            last_assistant_text = re.sub(r'【[^】]*】', '', last_assistant_text)

            print(f"LLM response for call {self.call_control_id}: {last_assistant_text}")
            return last_assistant_text
        except Exception as e:
            print(f"Error in send_to_llm: {str(e)}")
            return "I apologize, but I encountered an error processing your request. Please try again."

    def wait_on_run(self, run):
        while run.status == "queued" or run.status == "in_progress":
            run = client.beta.threads.runs.retrieve(
                thread_id=self.thread.id,
                run_id=run.id,
            )
            time.sleep(0.1)
        return run

    def pretty_str(self, messages):
        formatted_str = ""
        for m in messages:
            formatted_str += f"{m.role}: {m.content[0].text.value}\n"
        return formatted_str

    def get_response(self):
        return client.beta.threads.messages.list(thread_id=self.thread.id, order="asc")

    def play_tts(self, response_text):
        try:
            call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
            call.call_control_id = self.call_control_id
            call.speak(
                payload=response_text,
                language="en-US",
                voice="Polly.Amy")
        except Exception as e:
            print(f"Error in play_tts: {str(e)}")

    def hangup(self):
        try:
            call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
            call.call_control_id = self.call_control_id
            call.hangup()
            self.cleanup()
            del call_handlers[self.call_control_id]
        except Exception as e:
            print(f"Error in hangup: {str(e)}")

    def cleanup(self):
        try:
            if self.thread:
                print(f"Cleaning up thread {self.thread.id} for call {self.call_control_id}")
            self.transcript_buffer = []
            self.transcription_started = False
        except Exception as e:
            print(f"Error in cleanup: {str(e)}")

@app.route('/debug/call/<call_id>', methods=['GET'])
def debug_call(call_id):
    if call_id in call_handlers:
        handler = call_handlers[call_id]
        return {
            'call_id': call_id,
            'transcription_started': handler.transcription_started,
            'transcript_buffer': handler.transcript_buffer,
            'has_thread': bool(handler.thread),
            'thread_id': handler.thread.id if handler.thread else None
        }
    return {'error': 'Call not found'}, 404

@app.route('/webhook', methods=['POST'])
def inbound():
    body = json.loads(request.data)
    print(f"Received webhook: {json.dumps(body, indent=2)}")
    
    event = body.get("data", {}).get("event_type")
    call_control_id = body.get("data", {}).get("payload", {}).get("call_control_id")
    
    print(f"Event type: {event}")
    print(f"Call control ID: {call_control_id}")

    try:
        if event == "call.initiated":
            handle_call_initiated(body)
        elif event == "call.answered":
            handle_call_answered(body)
        elif event == "call.transcription":
            handle_call_transcription(body)
        elif event == 'call.dtmf.received':
            handle_call_dtmf_received(body)
        elif event == 'call.hangup':
            handle_call_hangup(body)
    except Exception as e:
        print(f"Error in webhook handler: {e}")
        return json.dumps({"success": False}), 500, {"ContentType": "application/json"}
    return json.dumps({"success": True}), 200, {"ContentType": "application/json"}

def handle_call_initiated(body):
    try:
        call_control_id = body.get("data").get("payload").get("call_control_id")
        print(f"Initiating call with control ID: {call_control_id}")
        
        connection_id = os.getenv("TELNYX_CONNECTION_ID")
        print(f"Using connection ID: {connection_id}")
        
        call = telnyx.Call(connection_id=connection_id)
        call.call_control_id = call_control_id
        call.answer()
        
        handler = CallHandler(call_control_id)
        call_handlers[call_control_id] = handler
        
        print(f"Call handler created successfully for {call_control_id}")
    except Exception as e:
        print(f"Error in handle_call_initiated: {str(e)}")
        raise

def handle_call_answered(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    if call_control_id in call_handlers:
        call_handlers[call_control_id].handle_answered()

def handle_call_transcription(body):
    try:
        call_control_id = body.get("data", {}).get("payload", {}).get("call_control_id")
        payload = body.get("data", {}).get("payload", {})
        
        print(f"Full transcription payload: {json.dumps(payload, indent=2)}")
        
        transcription_data = payload.get("transcription_data", {})
        transcript = transcription_data.get("transcript", "")
        
        print(f"Transcription received for call {call_control_id}:")
        print(f"Raw transcript: {transcript}")
        
        if call_control_id in call_handlers and transcript:
            call_handlers[call_control_id].handle_transcription(transcript)
        else:
            print(f"Warning: Either call_control_id not found or empty transcript")
            print(f"call_control_id in handlers: {call_control_id in call_handlers}")
            print(f"transcript present: {bool(transcript)}")
            
    except Exception as e:
        print(f"Error in handle_call_transcription: {str(e)}")
        print(f"Full body: {json.dumps(body, indent=2)}")

def handle_call_dtmf_received(body):
    event_id = body.get("data").get("id")
    if event_id in processed_event_ids:
        print(f"Event {event_id} already processed.")
        return
    processed_event_ids.add(event_id)

    call_control_id = body.get("data").get("payload").get("call_control_id")
    dtmf = body.get("data").get("payload").get("digit")
    if call_control_id in call_handlers:
        call_handlers[call_control_id].handle_dtmf(dtmf)

def handle_call_hangup(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    if call_control_id in call_handlers:
        call_handlers[call_control_id].cleanup()
        del call_handlers[call_control_id]

if __name__ == '__main__':
    verify_environment()
    verify_telnyx_config()
    app.run(debug=True, port=5000)