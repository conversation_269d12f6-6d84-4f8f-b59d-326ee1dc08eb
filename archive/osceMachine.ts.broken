// @ts-nocheck
import { createMachine, assign } from 'xstate';
import axios from 'axios';
import { OsceEvents, TransitionReasons } from './osceEvents';
import { OsceContext, CaseData, OsceMachineSchema } from './types';

// Constants for speech processing
const MIN_SPEECH_FRAMES = 5; // Minimum frames to consider speech
const SILENCE_FRAMES_THRESHOLD = 20; // Frames of silence to end turn

// Types are now imported from types.ts

// -------------------- Helpers -----------------------------
const RAG_URL = process.env.RAG_SERVER_URL || 'http://localhost:8001/rag';

const timer = (sec: number, cb: () => void): NodeJS.Timeout => {
  return setTimeout(cb, sec * 1000);
};

// Track valid transitions between states
const validTransitions = {
  'idle': ['intro'],
  'intro': ['case_selection'],
  'case_selection': ['history_taking'],
  'history_taking': ['transition_agent'],
  'examination': ['transition_agent'],
  'diagnosis': ['transition_agent'],
  'feedback': ['transition_agent'],
  'transition_agent': ['history_taking', 'examination', 'diagnosis', 'feedback', 'ended'],
  'ended': []
};

// Check if a transition is valid
function isValidTransition(fromState: string, toState: string): boolean {
  return validTransitions[fromState]?.includes(toState) || false;
}

// -------------------- Context Recovery ------------------
// Create a reusable context recovery action that can be used in any state entry
const recoverContext = assign((ctx) => {
  // Get a safe callSid for logging
  const safeCallSid = ctx?.callSid || 'UNKNOWN_CALL';
  console.log(`[XState:${safeCallSid}] Attempting context recovery check`);
  
  // Create a new context object starting with existing context
  const recoveredContext = { ...ctx };
  
  // Check if essential context is missing
  const contextIsDamaged = !ctx.callSid || !ctx.caseData;
  
  if (!contextIsDamaged) {
    console.log(`[XState:${safeCallSid}] Context is intact, no recovery needed`);
    return ctx; // Return unchanged context if it's not damaged
  }
  
  console.log(`[XState:${safeCallSid}] Context is damaged, attempting recovery`);
  
  // Try to recover callSid if missing
  if (!ctx.callSid && safeCallSid !== 'UNKNOWN_CALL') {
    console.log(`[XState:${safeCallSid}] Recovered callSid: ${safeCallSid}`);
    recoveredContext.callSid = safeCallSid;
  }
  
  // Try to recover caseData from global cache if available
  if (!ctx.caseData && global.caseDataCache && recoveredContext.callSid) {
    const cachedData = global.caseDataCache[recoveredContext.callSid];
    if (cachedData) {
      console.log(`[XState:${safeCallSid}] Recovered caseData from global cache`);
      recoveredContext.caseData = cachedData;
    } else {
      console.log(`[XState:${safeCallSid}] ERROR: Could not recover caseData, not in global cache`);
    }
  }
  
  // Ensure conversation is always an array
  if (!Array.isArray(recoveredContext.conversation)) {
    console.log(`[XState:${safeCallSid}] Initialized missing conversation array`);
    recoveredContext.conversation = [];
  }
  
  // Log recovered context
  console.log(`[XState:${safeCallSid}] Recovery result:`);
  console.log(`[XState:${safeCallSid}] - callSid: ${recoveredContext.callSid || 'FAILED TO RECOVER'}`);
  console.log(`[XState:${safeCallSid}] - caseData: ${recoveredContext.caseData?.id || 'FAILED TO RECOVER'}`);
  console.log(`[XState:${safeCallSid}] - conversation: ${recoveredContext.conversation.length} items`);
  
  return recoveredContext;
});

// -------------------- Services ----------------------------
const fetchPatient = (ctx: OsceContext, event: any) => {
  // CRITICAL FIX: Use only the mapped 'input' from the event object.
  // The 'ctx' argument here represents the context *at the time of invocation*, 
  // while 'event.input' holds the data mapped specifically for this service run.
  const inputData = event.input || {}; // Get the mapped input object
  
  const callSid = inputData.callSid || 'MISSING_CALLSID_IN_INPUT';
  const caseData = inputData.caseData || null;
  
  // Ensure conversation array is always valid, taken from the input mapping
  const conversation = Array.isArray(inputData.conversation) ? inputData.conversation : [];
  const userMessage = inputData.text || '';

  console.log(`[XState:${callSid}] fetchPatient invoked with input validation:`);
  console.log(`[XState:${callSid}] - CallSid source: ${inputData.callSid ? 'input' : 'missing'}`);
  console.log(`[XState:${callSid}] - Case data source: ${inputData.caseData ? 'input' : 'missing'}`);
  console.log(`[XState:${callSid}] - Case ID: ${caseData?.id || 'missing'}`);
  console.log(`[XState:${callSid}] - Text: ${userMessage || 'missing'}`);
  console.log(`[XState:${callSid}] - Conversation items from input: ${conversation.length}`);
  
  // Check the machine context ('ctx') just for comparison/debugging, but don't use it for the request
  console.log(`[XState:${callSid}] - Context state at invocation (for debug): callSid=${ctx?.callSid || 'missing'}, caseData=${ctx?.caseData ? 'present' : 'missing'}, conversation=${Array.isArray(ctx?.conversation) ? ctx.conversation.length : 'not-array'}`);
  
  return axios
    .post(`${RAG_URL}/patient`, {
      callSid,
      userMessage: userMessage,
      conversationHistory: conversation, // Use conversation from input
      caseData, // Use caseData from input
    })
    .then((r) => {
      // Return the structured output expected by onDone
      return { output: r.data.assistantReply as string }; 
    })
    .catch(error => {
      // Log the error and rethrow or return a specific error structure
      console.error(`[XState:${callSid}] fetchPatient Axios Error:`, error.message);
      // Rethrow to trigger onError, ensuring it includes error details
      throw new Error(error.response?.data?.detail || error.message || 'Unknown Axios error in fetchPatient'); 
    });
};

const fetchExaminer = (ctx: OsceContext) =>
  axios
    .post(`${RAG_URL}/examiner`, {
      callSid: ctx.callSid,
      userMessage: '', // examiner just needs history
      conversationHistory: ctx.conversation,
      caseData: ctx.caseData,
    })
    .then((r) => r.data.assistantReply as string);

const fetchMarking = (ctx: OsceContext) =>
  axios
    .post(`${RAG_URL}/mark`, {
      callSid: ctx.callSid,
      userMessage: '',
      conversationHistory: ctx.conversation,
      caseData: ctx.caseData,
    })
    .then((r) => r.data.assistantReply as string);

// New service for transition prompts
const fetchTransitionPrompt = (ctx: OsceContext) =>
  axios
    .post(`${RAG_URL}/transition`, {
      callSid: ctx.callSid,
      fromState: ctx.fromState,
      toState: ctx.toState,
      reason: ctx.transitionReason,
      conversationHistory: ctx.conversation,
    })
    .then((r) => r.data.assistantReply as string);

// -------------------- State Machine Reset VAD Actions ------------------------
const resetVadCounters = {
  assignment: {
    consecutiveSilenceFrames: 0,
    speechFramesInCurrentTurn: 0,
    hasSpeechDetected: false,
  }
};

// -------------------- Machine -----------------------------
export const createOsceMachine = () =>
  createMachine({
    /**
     * IMPORTANT: Using schema-based typing for better TypeScript integration.
     * This follows XState's recommended approach for type safety while also
     * maintaining compatibility with the existing codebase.
     */
    /** @xstate-layout N/A */
    schema: {
      context: {} as OsceContext,
      events: {} as OsceEvents,
      input: {} as { callSid: string }
    },
    // -----------------------------------------------------------------
    // Machine config ---------------------------------------------------
    // -----------------------------------------------------------------
    id: 'osce',
    context: ({ input }) => ({
      callSid: input.callSid,
      caseData: null,
      conversation: [],
      currentState: 'idle',
      currentQuestionIdx: 0,
      consecutiveSilenceFrames: 0,
      speechFramesInCurrentTurn: 0,
      hasSpeechDetected: false,
      lastUtterance: '',
      rejectionReason: null,
      fromState: undefined,
      toState: undefined,
      transitionReason: undefined,
      responseText: undefined,
      turnTimings: [],
      turnStartTime: undefined,
      currentTurnIndex: 0,
      currentlyPlayingTurnIndex: undefined,
      llmInvokeStartTime: undefined,
      llmInvokeEndTime: undefined,
      llmLastTurnDuration: 0,
      ttsTriggerTime: undefined,
      ttsEndTime: undefined,
      ttsLastTurnDuration: 0,
      turnNumber: 0,
    }),
    initial: 'idle',
    states: {
      // -------- Idle State - Starting Point --------
      idle: {
        on: {
          START: {
            target: 'intro',
            actions: assign({
              currentState: 'intro',
              responseText: 'Welcome to the medical examination practice session. I will guide you through this experience.',
              turnNumber: 0,
              currentTurnIndex: 0,
              llmLastTurnDuration: 0,
              ttsLastTurnDuration: 0,
              turnTimings: [],
              conversation: [], // Initialize conversation array
            })
          }
        }
      },

      // -------- Introduction --------
      intro: {
        entry: ['recordTurnStart', 'recordTtsTrigger'], 
        on: {
          TTS_FINISHED: {
            target: 'case_selection',
            actions: [
              (context, event) => { // Log full context on entry
                // First, check if we have a nested context structure
                const extractCallSid = (ctx) => {
                  if (ctx.callSid) return ctx.callSid;
                  if (ctx.context && typeof ctx.context === 'object') return extractCallSid(ctx.context);
                  return 'CONTEXT_ENTRY_MISSING_SID';
                };
                
                const safeCallSid = extractCallSid(context);
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 0: Starting TTS_FINISHED transition. Extracting callSid from context...`);
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 0: Found callSid=${safeCallSid}`);
              },
              (context) => { // Log before any action
                const safeCallSid = context.callSid || 'BEFORE_TTS_ASSIGN';
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 1: Before any actions. callSid=${context.callSid}`);
              },
              // HARDCODED ASSIGN: Using property-specific assign for TTS_FINISHED
              assign({
                callSid: (context) => {
                  // Extract callSid from various places
                  let extractedCallSid = context.callSid;
                  
                  // Check one level of nesting if needed
                  if (!extractedCallSid && context.context && typeof context.context === 'object') {
                    extractedCallSid = context.context.callSid;
                  }
                  
                  // Check for XState v5 input
                  if (!extractedCallSid && context._input && typeof context._input === 'object') {
                    extractedCallSid = context._input.callSid;
                  }
                  
                  // Final fallback
                  if (!extractedCallSid) {
                    console.error('[XState:CRITICAL] No callSid found in TTS_FINISHED handler');
                    extractedCallSid = 'FALLBACK_TTS_SID';
                  }
                  
                  console.log(`[XState:${extractedCallSid}] TTS_FINISHED DIRECT ASSIGN callSid=${extractedCallSid}`);
                  return extractedCallSid;
                },
                context: undefined, // Remove nested context property
                currentState: 'case_selection',
                responseText: null, // Clear response text after TTS finishes
              }),
              'recordTtsEnd',
              (context) => { // Log after recordTtsEnd
                const safeCallSid = context.callSid || 'AFTER_RECORD_TTS_END';
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 5: After recordTtsEnd. callSid=${context.callSid}`);
              },
            ]
          },
          USER_UTTERANCE: { 
            actions: assign((ctx, ev) => ({
              ...ctx, // Preserve existing context
              conversation: [
                ...(Array.isArray(ctx.conversation) ? ctx.conversation : []), // Ensure conversation is an array
                { speaker: 'caller', text: ev.text },
              ],
            })),
          },
        },
        after: {
          5000: { 
            target: 'case_selection',
            // Add actions to properly handle context during automatic transition
            actions: [
              (context) => {
                // First, check if we have a nested context structure
                const extractCallSid = (ctx) => {
                  if (ctx.callSid) return ctx.callSid;
                  if (ctx.context && typeof ctx.context === 'object') return extractCallSid(ctx.context);
                  return 'AUTO_TRANSITION_MISSING_SID';
                };
                
                const safeCallSid = extractCallSid(context);
                console.log(`[XState:${safeCallSid}] AUTO TRANSITION (5s): Starting auto-transition from intro to case_selection`);
                console.log(`[XState:${safeCallSid}] AUTO TRANSITION (5s): Found callSid=${safeCallSid}`);
              },
              // HARDCODED ASSIGN: Using property-specific assign for auto-transition
              assign({
                callSid: (context) => {
                  // Extract callSid from various places
                  let extractedCallSid = context.callSid;
                  
                  // Check one level of nesting if needed
                  if (!extractedCallSid && context.context && typeof context.context === 'object') {
                    extractedCallSid = context.context.callSid;
                  }
                  
                  // Check for XState v5 input 
                  if (!extractedCallSid && context._input && typeof context._input === 'object') {
                    extractedCallSid = context._input.callSid;
                  }
                  
                  // Final fallback
                  if (!extractedCallSid) {
                    console.error('[XState:CRITICAL] No callSid found in auto-transition');
                    extractedCallSid = 'FALLBACK_AUTO_SID';
                  }
                  
                  console.log(`[XState:${extractedCallSid}] AUTO TRANSITION DIRECT ASSIGN callSid=${extractedCallSid}`);
                  return extractedCallSid;
                },
                // Explicitly set other required properties
                context: undefined, // Remove nested context property
                currentState: 'case_selection',
                responseText: null // Clear response text
              })
            ]
          }
        }
      },

      // -------- Case Selection --------
      case_selection: {
        entry: [
          // DEBUG: Add detailed diagnostics for case_selection entry
          (context) => {
            // Extract callSid from potentially nested context
            const extractCallSid = (ctx) => {
              if (ctx.callSid) return ctx.callSid;
              if (ctx.context && typeof ctx.context === 'object') return extractCallSid(ctx.context);
              return 'MISSING_CALLSID';
            };
            
            // Always log the full context at state entry
            const callSid = extractCallSid(context);
            console.log(`[XState:${callSid}] ENTRY to case_selection - CONTEXT DIAGNOSTICS:`);
            console.log(`[XState:${callSid}] - callSid: ${context.callSid}`);
            console.log(`[XState:${callSid}] - currentState: ${context.currentState}`);
            console.log(`[XState:${callSid}] - extracted callSid: ${callSid}`);
          },
          // HARDCODED ASSIGN: Using forceful approach to prevent context loss
          assign({
            callSid: (context) => {
              // Try to extract callSid from various places
              let extractedCallSid = context.callSid;
              
              // Check one level of nesting if needed
              if (!extractedCallSid && context.context && typeof context.context === 'object') {
                extractedCallSid = context.context.callSid;
              }
              
              // Check for XState v5 input
              if (!extractedCallSid && context._input && typeof context._input === 'object') {
                extractedCallSid = context._input.callSid;
              }
              
              // Final fallback
              if (!extractedCallSid) {
                console.error('[XState:CRITICAL] No callSid found in context');
                extractedCallSid = 'FALLBACK_SID';
              }
              
              console.log(`[XState:${extractedCallSid}] DIRECT ASSIGN callSid=${extractedCallSid}`);
              return extractedCallSid;
            },
            // Flatten any nested context
            currentState: 'case_selection',
            selectedCase: (context) => {
              const cases = [
                {
                  case_id: 'case1',
                  scenario: 'Headache', 
                  initial_question: 'Okay, please tell me about this headache. When did it start?',
                  questions: [],
                  data: {}
                },
                {
                  case_id: 'case2',
                  scenario: 'Chest Pain', 
                  initial_question: 'Got it. Can you describe the chest pain for me?',
                  questions: [],
                  data: {}
                }
              ];
              
              const selected = cases[Math.floor(Math.random() * cases.length)];
              console.log(`[XState:CASE] Selected case: ${selected.scenario}`);
              return selected;
            }
          }),
          // Verify context after assignment
          (context) => {
            const callSid = context.callSid || 'MISSING_CALLSID';
            console.log(`[XState:${callSid}] AFTER assign - context.callSid: ${context.callSid}`);
            console.log(`[XState:${callSid}] AFTER assign - selectedCase: ${context.selectedCase?.scenario}`);
          }
        ],
        // Simpler transition to avoid syntax issues
        after: {
          100: {
            target: 'history_taking',
            actions: [
              // Simple action to preserve callSid
              (context) => {
                const callSid = context.callSid || context.context?.callSid || 'UNKNOWN_CALLSID';
                console.log(`[XState:${callSid}] Transitioning from case_selection to history_taking`);
                
                // Create global backup to ensure context preservation
                if (typeof global.contextBackup === 'undefined') global.contextBackup = {};
                global.contextBackup.lastCallSid = callSid;
                global.contextBackup.lastSelectedCase = context.selectedCase || null;
              },
              // Assign critical context values explicitly
              assign({
                callSid: (context) => {
                  return context.callSid || context.context?.callSid || global.contextBackup?.lastCallSid || 'MISSING_CALLSID';
                },
                caseData: (context) => {
                  return context.selectedCase || null;
                },
                currentState: 'history_taking',
                turnNumber: 1,
                turnStartTime: Date.now()
              })
            ]
          }
        },
        },
        on: {
          // Existing SELECT_CASE (keep for now, might remove later)
          SELECT_CASE: {
            target: 'history_taking',
            actions: assign((context, event) => ({
              // Preserve all existing context
              ...context,
              // Update case-specific fields
              caseData: event.caseData,
              responseText: event.caseData?.initial_question || 'Please tell me about your symptoms.',
              // Start the first turn timing here
              turnStartTime: Date.now(),
              turnNumber: 1,
              // Set the current state and explicitly preserve callSid
              currentState: 'history_taking'
            }))
          },
          // Handle the internally set case data with enhanced context preservation
          INTERNAL_SET_CASE: {
            target: 'history_taking', 
            actions: [
              (context, event) => {
                const callSid = context.callSid || 'UNKNOWN_CALL';
                console.log(`[XState:${callSid}] Handling INTERNAL_SET_CASE transition to history_taking`);
                console.log(`[XState:${callSid}] Selected case: ${event.caseData?.scenario}, callSid: ${callSid}`);
              },
              // Use function form of assign to preserve all existing context
              assign((context, event) => ({
                // Preserve all existing context
                ...context,
                // Update with new case data
                caseData: event.caseData,
                // Set initial response text for history taking
                responseText: event.caseData?.initial_question || 'Okay, let\'s begin. Tell me about your concerns.',
                // Explicitly preserve callSid
                callSid: context.callSid,
                // Start the first turn timing here
                turnStartTime: Date.now(),
                turnNumber: 1
              })),
              (context, event) => console.log(`[XState Transition][${context.callSid}] After assign in INTERNAL_SET_CASE. New context case: ${context.caseData?.scenario}`)
            ]
          },
          // *************************************************
        },
      },

      // -------- History Taking --------
      history_taking: {
        entry: [
          // ENHANCED RECOVERY: Robust context recovery implementation
          assign((ctx) => {
            // First try to get callSid from various possible locations
            let recoveredCallSid = ctx.callSid;
            
            // Check for nested context structure
            if (!recoveredCallSid && ctx.context && typeof ctx.context === 'object') {
              recoveredCallSid = ctx.context.callSid;
              console.log(`[XState:Recovery] Found callSid in nested context: ${recoveredCallSid}`);
            }
            
            // Try to get from global backup if available
            if (!recoveredCallSid && global.contextBackup && global.contextBackup.lastCallSid) {
              recoveredCallSid = global.contextBackup.lastCallSid;
              console.log(`[XState:Recovery] Using callSid from global backup: ${recoveredCallSid}`);
            }
            
            // Final fallback
            if (!recoveredCallSid) {
              recoveredCallSid = 'RECOVERED_FALLBACK_SID';
              console.log(`[XState:Recovery] Using fallback callSid: ${recoveredCallSid}`);
            }
            
            console.log(`[XState:${recoveredCallSid}] Running enhanced context recovery`);
            
            // Get selectedCase from anywhere it might be stored
            const selectedCase = ctx.selectedCase || 
                               (ctx.context && ctx.context.selectedCase) || 
                               (global.contextBackup && global.contextBackup.lastSelectedCase) || 
                               {
                                 case_id: 'fallback_case',
                                 scenario: 'Unknown Scenario',
                                 initial_question: 'Please tell me about your symptoms.',
                                 questions: [],
                                 data: {}
                               };
            
            // Create a fresh context with all critical properties explicitly set
            const recoveredContext = {
              ...ctx,  // Keep any existing properties
              callSid: recoveredCallSid,  // Use recovered callSid
              currentState: 'history_taking',
              caseData: selectedCase,  // Use recovered case data
              responseText: selectedCase.initial_question || 'Please tell me about your symptoms.',
              conversation: Array.isArray(ctx.conversation) ? ctx.conversation : [],
              // Remove any problematic nested context
              context: undefined
            };
            
            console.log(`[XState:${recoveredCallSid}] Enhanced recovery complete - callSid=${recoveredCallSid}`);
            return recoveredContext;
          }),
            
            // Try to recover from global cache
            if (!ctx.caseData && global.caseDataCache && recoveredContext.callSid) {
              const cachedData = global.caseDataCache[recoveredContext.callSid];
              if (cachedData) {
                console.log(`[XState:${safeCallSid}] Recovered caseData from global cache`);
                recoveredContext.caseData = cachedData;
              } else {
                console.log(`[XState:${safeCallSid}] Failed to recover caseData, not in global cache`);
              }
            }
            
            // Always set the current state 
            recoveredContext.currentState = 'history_taking';
            
            // Ensure conversation is always an array
            if (!Array.isArray(recoveredContext.conversation)) {
              recoveredContext.conversation = [];
            }
            
            // Log recovery results
            console.log(`[XState:${safeCallSid}] Context recovery complete:`);
            console.log(`[XState:${safeCallSid}] - callSid: ${recoveredContext.callSid || 'FAILED TO RECOVER'}`);
            console.log(`[XState:${safeCallSid}] - caseData present: ${!!recoveredContext.caseData}`);
            console.log(`[XState:${safeCallSid}] - conversation items: ${recoveredContext.conversation.length}`);
            
            return recoveredContext;
          }),
          // Log additional entry info
          (ctx) => {
            console.log(`[XState:${ctx.callSid}] Ready to process user input in history_taking state`);
          }
        ],
        initial: 'waitingInput',
        states: {
          waitingInput: {
            on: {
              SPEECH_FRAME: { 
                actions: (ctx) => ctx.speechFramesInCurrentTurn === 0 ? 
                  ['recordTurnStart', 'incrementSpeechFrames'] : 'incrementSpeechFrames'
              },
              SILENCE_FRAME: {
                actions: 'incrementSilenceFrames',
                target: 'answering',
                cond: (ctx) => {
                  const shouldTransition = 
                    ctx.hasSpeechDetected &&
                    ctx.speechFramesInCurrentTurn >= MIN_SPEECH_FRAMES &&
                    ctx.consecutiveSilenceFrames >= SILENCE_FRAMES_THRESHOLD;
                  
                  // Log the condition check details
                  console.log(`[XState:${ctx.callSid}] SILENCE_FRAME Condition Check:`);
                  console.log(`  - hasSpeechDetected: ${ctx.hasSpeechDetected}`);
                  console.log(`  - speechFramesInCurrentTurn: ${ctx.speechFramesInCurrentTurn} (>= ${MIN_SPEECH_FRAMES}?)`);
                  console.log(`  - consecutiveSilenceFrames: ${ctx.consecutiveSilenceFrames} (>= ${SILENCE_FRAMES_THRESHOLD}?)`);
                  console.log(`  - ==> Should Transition: ${shouldTransition}`);
                  
                  return shouldTransition;
                },
              },
              USER_UTTERANCE: { 
                target: 'answering', 
                actions: assign((ctx, event) => { // Add action to store utterance
                  const safeConversation = Array.isArray(ctx.conversation) ? ctx.conversation : [];
                  console.log(`[XState:${ctx.callSid || 'USER_UTTERANCE_ASSIGN'}] Adding USER_UTTERANCE to conversation: "${event.text.substring(0, 30)}..."`);
                  return {
                    ...ctx, // Preserve existing context
                    lastUtterance: event.text,
                    conversation: [
                      ...safeConversation,
                      { speaker: 'caller', text: event.text },
                    ]
                  };
                })
              },
              TTS_FINISHED: { 
                actions: [
                  (context) => { // Log before any action
                    const safeCallSid = context.callSid || 'BEFORE_TTS_ASSIGN';
                    console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 1: Before any actions. callSid=${context.callSid}`);
                  },
                  assign((context) => {
                    const safeCallSid = context.callSid || 'IN_TTS_ASSIGN';
                    console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 2: Inside assign. callSid=${context.callSid}`);
                    return {
                      ...context,
                      responseText: null
                    };
                  }),
                  (context) => { // Log after assign
                    const safeCallSid = context.callSid || 'AFTER_TTS_ASSIGN';
                    console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 3: After assign. callSid=${context.callSid}`);
                  },
                  'recordTtsEnd', // Keep this action
                  (context) => { // Log after recordTtsEnd
                    const safeCallSid = context.callSid || 'AFTER_RECORD_TTS_END';
                    console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 5: After recordTtsEnd. callSid=${context.callSid}`);
                  }
                ]
              },
              // Add transition request handler
              REQUEST_TRANSITION: {
                target: '#osce.transition_agent',
                cond: (ctx, event) => 
                  event.targetState === 'examination' || 
                  event.targetState === 'diagnosis',
                actions: assign({
                  fromState: 'history_taking',
                  toState: (_, event) => event.targetState,
                  transitionReason: (_, event) => event.reason
                })
              }
            },
            // Add time-based transition trigger (5 minutes)
            after: {
              300000: {
                target: '#osce.transition_agent',
                actions: assign({
                  fromState: 'history_taking',
                  toState: 'examination',
                  transitionReason: TransitionReasons.TIME_THRESHOLD
                })
              }
            }
          },
          answering: {
            entry: [
              'recordLlmInvokeStart',
              // Add entry action to log detailed context at entry to answering state
              (ctx) => {
                console.log(`[XState:${ctx.callSid}] Entering answering state, caseData: ${ctx.caseData ? 'present' : 'missing'}`);
              },
            ],
            invoke: {
              id: 'fetchPatientHistoryTaking', // Give unique ID
              src: fetchPatient,
              input: (ctx) => ({ // Pass context needed by the service
                  text: ctx.lastUtterance, // Assuming lastUtterance holds the user input
                  callSid: ctx.callSid,
                  caseData: ctx.caseData,
                  conversation: ctx.conversation, // Pass conversation history
              }),
              onDone: {
                target: 'waitingInput',
                actions: [
                  assign({
                    responseText: (_ctx, ev) => ev.output,
                    conversation: (ctx, ev) => [
                      ...ctx.conversation,
                      { speaker: 'assistant', text: ev.output },
                    ],
                  }),
                  'recordLlmInvokeEnd',
                  'recordTtsTrigger'
                ],
              },
              onError: {
                // CRITICAL FIX: Handle errors gracefully instead of transitioning to error state
                target: 'waitingInput',
                actions: [
                  // Log detailed error information
                  (ctx, ev) => {
                    const callSid = ctx?.callSid || 'MISSING_CALLSID';
                    console.log(`[XState:${callSid}] Error in fetchPatient service:`);
                    console.log(`[XState:${callSid}] - Error:`, ev ? JSON.stringify(ev).substring(0, 200) : 'no error data');
                    console.log(`[XState:${callSid}] - Context check: callSid=${callSid}, caseData=${ctx?.caseData ? 'present' : 'missing'}, conversation=${Array.isArray(ctx?.conversation) ? ctx.conversation.length : 'not-array'}`);
                  },
                  // Set a recovery response
                  assign({
                    responseText: "I'm sorry, I'm having trouble understanding. Could you please repeat that?",
                    conversation: (ctx) => {
                      // Safely initialize conversation if needed
                      const safeConversation = Array.isArray(ctx?.conversation) ? ctx.conversation : [];
                      return [
                        ...safeConversation,
                        { 
                          speaker: 'assistant', 
                          text: "I'm sorry, I'm having trouble understanding. Could you please repeat that?" 
                        },
                      ];
                    },
                  }),
                  'recordLlmInvokeEnd',
                  'recordTtsTrigger'
                ]
              },
            },
            exit: assign({
              // Reset VAD counters after processing user input
              ...resetVadCounters.assignment,
            }),
          },
        },
      },

      // -------- Examination State --------
      examination: {
        entry: assign({ currentState: 'examination' }),
        initial: 'waitingInput',
        states: {
          waitingInput: {
            on: {
              SPEECH_FRAME: { 
                actions: (ctx) => ctx.speechFramesInCurrentTurn === 0 ? 
                  ['recordTurnStart', 'incrementSpeechFrames'] : 'incrementSpeechFrames'
              },
              SILENCE_FRAME: {
                actions: 'incrementSilenceFrames',
                target: 'answering',
                cond: (ctx) =>
                   ctx.hasSpeechDetected &&
                   ctx.speechFramesInCurrentTurn >= MIN_SPEECH_FRAMES &&
                   ctx.consecutiveSilenceFrames >= SILENCE_FRAMES_THRESHOLD,
              },
              USER_UTTERANCE: { target: 'answering' },
              TTS_FINISHED: { 
                actions: [
                  (context) => { // Log before any action
                    const safeCallSid = context.callSid || 'BEFORE_TTS_ASSIGN';
                    console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 1: Before any actions. callSid=${context.callSid}`);
                  },
                  assign((context) => {
                    const safeCallSid = context.callSid || 'IN_TTS_ASSIGN';
                    console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 2: Inside assign. callSid=${context.callSid}`);
                    return {
                      ...context,
                      responseText: null
                    };
                  }),
                  (context) => { // Log after assign
                    const safeCallSid = context.callSid || 'AFTER_TTS_ASSIGN';
                    console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 3: After assign. callSid=${context.callSid}`);
                  },
                  'recordTtsEnd', // Keep this action
                  (context) => { // Log after recordTtsEnd
                    const safeCallSid = context.callSid || 'AFTER_RECORD_TTS_END';
                    console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 5: After recordTtsEnd. callSid=${context.callSid}`);
                  }
                ]
              },
              // Add transition request handler
              REQUEST_TRANSITION: {
                target: '#osce.transition_agent',
                cond: (ctx, event) => 
                  event.targetState === 'diagnosis' || 
                  event.targetState === 'history_taking',
                actions: assign({
                  fromState: 'examination',
                  toState: (_, event) => event.targetState,
                  transitionReason: (_, event) => event.reason
                })
              }
            },
            // Add time-based transition trigger (5 minutes)
            after: {
              300000: {
                target: '#osce.transition_agent',
                actions: assign({
                  fromState: 'examination',
                  toState: 'diagnosis',
                  transitionReason: TransitionReasons.TIME_THRESHOLD
                })
              }
            }
          },
          answering: {
            entry: 'recordLlmInvokeStart',
            invoke: {
              src: fetchPatient, // Using patient service for now
              input: (ctx, e: any) => ({ text: e.text }),
              onDone: {
                target: 'waitingInput',
                actions: [
                  assign({
                    responseText: (_ctx, ev) => ev.output,
                    conversation: (ctx, ev) => [
                      ...ctx.conversation,
                      { speaker: 'assistant', text: ev.output },
                    ],
                  }),
                  'recordLlmInvokeEnd',
                  'recordTtsTrigger'
                ],
              },
              onError: {
                // CRITICAL FIX: Handle errors gracefully instead of transitioning to error state
                target: 'waitingInput',
                actions: [
                  // Log detailed error information
                  (ctx, ev) => {
                    const callSid = ctx?.callSid || 'MISSING_CALLSID';
                    console.log(`[XState:${callSid}] Error in fetchPatient service:`);
                    console.log(`[XState:${callSid}] - Error:`, ev ? JSON.stringify(ev).substring(0, 200) : 'no error data');
                    console.log(`[XState:${callSid}] - Context check: callSid=${callSid}, caseData=${ctx?.caseData ? 'present' : 'missing'}, conversation=${Array.isArray(ctx?.conversation) ? ctx.conversation.length : 'not-array'}`);
                  },
                  // Set a recovery response
                  assign({
                    responseText: "I'm sorry, I'm having trouble understanding. Could you please repeat that?",
                    conversation: (ctx) => {
                      // Safely initialize conversation if needed
                      const safeConversation = Array.isArray(ctx?.conversation) ? ctx.conversation : [];
                      return [
                        ...safeConversation,
                        { 
                          speaker: 'assistant', 
                          text: "I'm sorry, I'm having trouble understanding. Could you please repeat that?" 
                        },
                      ];
                    },
                  }),
                  'recordLlmInvokeEnd',
                  'recordTtsTrigger'
                ]
              },
            },
            exit: assign({
              // Reset VAD counters after processing user input
              ...resetVadCounters.assignment,
            }),
          },
        }
      },

      // -------- Diagnosis State --------
      diagnosis: {
        entry: assign({ currentState: 'diagnosis' }),
        initial: 'waitingInput',
        states: {
          waitingInput: {
            on: {
              SPEECH_FRAME: { 
                actions: (ctx) => ctx.speechFramesInCurrentTurn === 0 ? 
                  ['recordTurnStart', 'incrementSpeechFrames'] : 'incrementSpeechFrames'
              },
              SILENCE_FRAME: {
                actions: 'incrementSilenceFrames',
                target: 'answering',
                cond: (ctx) =>
                   ctx.hasSpeechDetected &&
                   ctx.speechFramesInCurrentTurn >= MIN_SPEECH_FRAMES &&
                   ctx.consecutiveSilenceFrames >= SILENCE_FRAMES_THRESHOLD,
              },
              USER_UTTERANCE: { target: 'answering' },
              TTS_FINISHED: { 
                actions: [
                  (context) => { // Log before any action
                    const safeCallSid = context.callSid || 'BEFORE_TTS_ASSIGN';
                    console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 1: Before any actions. callSid=${context.callSid}`);
                  },
                  assign((context) => {
                    const safeCallSid = context.callSid || 'IN_TTS_ASSIGN';
                    console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 2: Inside assign. callSid=${context.callSid}`);
                    return {
                      ...context,
                      responseText: null
                    };
                  }),
                  (context) => { // Log after assign
                    const safeCallSid = context.callSid || 'AFTER_TTS_ASSIGN';
                    console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 3: After assign. callSid=${context.callSid}`);
                  },
                  'recordTtsEnd', // Keep this action
                  (context) => { // Log after recordTtsEnd
                    const safeCallSid = context.callSid || 'AFTER_RECORD_TTS_END';
                    console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 5: After recordTtsEnd. callSid=${context.callSid}`);
                  }
                ]
              },
              // Add transition request handler
              REQUEST_TRANSITION: {
                target: '#osce.transition_agent',
                cond: (ctx, event) => 
                  event.targetState === 'feedback',
                actions: assign({
                  fromState: 'diagnosis',
                  toState: (_, event) => event.targetState,
                  transitionReason: (_, event) => event.reason
                })
              }
            },
            // Add time-based transition trigger (5 minutes)
            after: {
              300000: {
                target: '#osce.transition_agent',
                actions: assign({
                  fromState: 'diagnosis',
                  toState: 'feedback',
                  transitionReason: TransitionReasons.TIME_THRESHOLD
                })
              }
            }
          },
          answering: {
            entry: 'recordLlmInvokeStart',
            invoke: {
              src: fetchPatient, // Using patient service for now
              input: (ctx, e: any) => ({ text: e.text }),
              onDone: {
                target: 'waitingInput',
                actions: [
                  assign({
                    responseText: (_ctx, ev) => ev.output,
                    conversation: (ctx, ev) => [
                      ...ctx.conversation,
                      { speaker: 'assistant', text: ev.output },
                    ],
                  }),
                  'recordLlmInvokeEnd',
                  'recordTtsTrigger'
                ],
              },
              onError: {
                // CRITICAL FIX: Handle errors gracefully instead of transitioning to error state
                target: 'waitingInput',
                actions: [
                  // Log detailed error information
                  (ctx, ev) => {
                    const callSid = ctx?.callSid || 'MISSING_CALLSID';
                    console.log(`[XState:${callSid}] Error in fetchPatient service:`);
                    console.log(`[XState:${callSid}] - Error:`, ev ? JSON.stringify(ev).substring(0, 200) : 'no error data');
                    console.log(`[XState:${callSid}] - Context check: callSid=${callSid}, caseData=${ctx?.caseData ? 'present' : 'missing'}, conversation=${Array.isArray(ctx?.conversation) ? ctx.conversation.length : 'not-array'}`);
                  },
                  // Set a recovery response
                  assign({
                    responseText: "I'm sorry, I'm having trouble understanding. Could you please repeat that?",
                    conversation: (ctx) => {
                      // Safely initialize conversation if needed
                      const safeConversation = Array.isArray(ctx?.conversation) ? ctx.conversation : [];
                      return [
                        ...safeConversation,
                        { 
                          speaker: 'assistant', 
                          text: "I'm sorry, I'm having trouble understanding. Could you please repeat that?" 
                        },
                      ];
                    },
                  }),
                  'recordLlmInvokeEnd',
                  'recordTtsTrigger'
                ]
              },
            },
            exit: assign({
              // Reset VAD counters after processing user input
              ...resetVadCounters.assignment,
            }),
          },
        }
      },

      // -------- Feedback State --------
      feedback: {
        entry: assign({ currentState: 'feedback' }),
        on: {
          TTS_FINISHED: {
            actions: [
              (context) => { // Log before any action
                const safeCallSid = context.callSid || 'BEFORE_TTS_ASSIGN';
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 1: Before any actions. callSid=${context.callSid}`);
              },
              assign((context) => {
                const safeCallSid = context.callSid || 'IN_TTS_ASSIGN';
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 2: Inside assign. callSid=${context.callSid}`);
                return {
                  ...context,
                  responseText: null
                };
              }),
              (context) => { // Log after assign
                const safeCallSid = context.callSid || 'AFTER_TTS_ASSIGN';
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 3: After assign. callSid=${context.callSid}`);
              },
              'recordTtsEnd', // Keep this action
              (context) => { // Log after recordTtsEnd
                const safeCallSid = context.callSid || 'AFTER_RECORD_TTS_END';
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 5: After recordTtsEnd. callSid=${context.callSid}`);
              }
            ]
          },
          USER_UTTERANCE: {
            actions: assign({
              conversation: (ctx, ev) => [
                ...ctx.conversation,
                { speaker: 'caller', text: ev.text },
              ],
            }),
          },
          // Add transition request handler
          REQUEST_TRANSITION: {
            target: '#osce.transition_agent',
            cond: (ctx, event) => 
              event.targetState === 'ended',
            actions: assign({
              fromState: 'feedback',
              toState: (_, event) => event.targetState,
              transitionReason: (_, event) => event.reason
            })
          }
        },
        // Add time-based trigger to end after 10 minutes
        after: {
          600000: {
            target: 'transition_agent',
            actions: assign({
              fromState: 'feedback',
              toState: 'ended',
              transitionReason: TransitionReasons.TIME_THRESHOLD
            })
          }
        }
      },

      // -------- Transition Agent --------
      transition_agent: {
        entry: [
          assign({ currentState: 'transition_agent' }),
          'recordLlmInvokeStart'
        ],
        invoke: {
          src: fetchTransitionPrompt,
          onDone: {
            actions: [
              assign({
                responseText: (_ctx, ev) => ev.output,
                conversation: (ctx, ev) => [
                  ...ctx.conversation,
                  { speaker: 'assistant', text: ev.output },
                ],
              }),
              'recordLlmInvokeEnd',
              'recordTtsTrigger'
            ]
          },
          onError: {
            target: 'error',
            actions: (ctx, ev) => console.error(`Error in transition agent for call ${ctx.callSid}:`, ev.data)
          }
        },
        on: {
          TTS_FINISHED: {
            actions: [
              (context) => { // Log before any action
                const safeCallSid = context.callSid || 'BEFORE_TTS_ASSIGN';
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 1: Before any actions. callSid=${context.callSid}`);
              },
              assign((context) => {
                const safeCallSid = context.callSid || 'IN_TTS_ASSIGN';
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 2: Inside assign. callSid=${context.callSid}`);
                return {
                  ...context,
                  responseText: null
                };
              }),
              (context) => { // Log after assign
                const safeCallSid = context.callSid || 'AFTER_TTS_ASSIGN';
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 3: After assign. callSid=${context.callSid}`);
              },
              'recordTtsEnd', // Keep this action
              (context) => { // Log after recordTtsEnd
                const safeCallSid = context.callSid || 'AFTER_RECORD_TTS_END';
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 5: After recordTtsEnd. callSid=${context.callSid}`);
              }
            ]
          },
          ACCEPT_TRANSITION: {
            // Dynamically target the state from context
            target: (ctx) => `#osce.${ctx.toState}`,
            actions: (ctx) => console.log(`[Machine ${ctx.callSid}] Transition accepted: ${ctx.fromState} -> ${ctx.toState}`)
          },
          REJECT_TRANSITION: {
            // Return to the original state
            target: (ctx) => `#osce.${ctx.fromState}`,
            actions: [
              (ctx) => console.log(`[Machine ${ctx.callSid}] Transition rejected: staying in ${ctx.fromState}`),
              assign({
                // Set a prompt for the return to original state
                responseText: (ctx) => `Let's continue with the ${ctx.fromState.replace('_', ' ')}.`
              }),
              'recordTtsTrigger'
            ]
          },
          USER_UTTERANCE: {
            // Handle user response but stay in transition state
            // The LLM will need to determine if this is accept/reject
            actions: assign({
              conversation: (ctx, ev) => [
                ...ctx.conversation,
                { speaker: 'caller', text: ev.text },
              ],
            })
          }
        },
        exit: assign({
          // Clear transition state on exit
          fromState: undefined,
          toState: undefined,
          transitionReason: undefined
        })
      },

      // -------- Ended State --------
      ended: {
        entry: [
          assign({ currentState: 'ended' }),
          assign({ 
            responseText: 'Thank you for completing this medical examination session. Goodbye!'
          }),
          'recordTtsTrigger'
        ],
        on: {
          TTS_FINISHED: {
            actions: [
              (context) => { // Log before any action
                const safeCallSid = context.callSid || 'BEFORE_TTS_ASSIGN';
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 1: Before any actions. callSid=${context.callSid}`);
              },
              assign((context) => {
                const safeCallSid = context.callSid || 'IN_TTS_ASSIGN';
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 2: Inside assign. callSid=${context.callSid}`);
                return {
                  ...context,
                  responseText: null
                };
              }),
              (context) => { // Log after assign
                const safeCallSid = context.callSid || 'AFTER_TTS_ASSIGN';
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 3: After assign. callSid=${context.callSid}`);
              },
              'recordTtsEnd', // Keep this action
              (context) => { // Log after recordTtsEnd
                const safeCallSid = context.callSid || 'AFTER_RECORD_TTS_END';
                console.log(`[XState:${safeCallSid}] TTS_FINISHED Action 5: After recordTtsEnd. callSid=${context.callSid}`);
              }
            ]
          },
        },
        type: 'final'
      },

      // -------- Error --------
      error: {
        entry: (ctx) => console.error(`Machine encountered an unrecoverable error for call ${ctx.callSid}`),
        type: 'final',
      },
    },
    // Global transitions
    on: {
      SET_CONVERSATION: {
        actions: assign({
          conversation: (_, event) => event.data
        })
      },
      INTERRUPT: {
        actions: (ctx) => console.log(`[Machine ${ctx.callSid}] Received interrupt event`)
      }
    },
    actions: {
      logTimeout: (ctx) => console.log(`[Machine ${ctx.callSid}] Examiner question timed out.`),
      // VAD Actions
      incrementSpeechFrames: assign({
        speechFramesInCurrentTurn: (ctx) => ctx.speechFramesInCurrentTurn + 1,
        hasSpeechDetected: true,
        consecutiveSilenceFrames: 0, // Reset silence on speech
      }),
      incrementSilenceFrames: assign({
        consecutiveSilenceFrames: (ctx) => ctx.consecutiveSilenceFrames + 1,
      }),
      recordRejectionReason: assign({
        rejectionReason: (_ctx, event: any) => {
          // Ensure event and event.reason exist and are strings
          if (event && typeof event.reason === 'string') {
            return event.reason;
          }
          // Provide a default or fallback reason
          return 'Unknown error occurred'; 
        },
      }),
      recordTurnStart: assign({
        turnStartTime: (_ctx) => Date.now(),
        speechFramesInCurrentTurn: 1, // Start counting frames for this turn
        consecutiveSilenceFrames: 0, // Reset silence at turn start
        hasSpeechDetected: true, // Mark speech detected for this turn
        turnNumber: (ctx) => ctx.turnNumber + 1, // Increment turn number
      }),
      recordLlmInvokeStart: assign({
        llmInvokeStartTime: (_ctx) => Date.now(),
      }),
      recordLlmInvokeEnd: assign({
        llmInvokeEndTime: (_ctx) => Date.now(),
        llmLastTurnDuration: (ctx) => ctx.llmInvokeStartTime ? Date.now() - ctx.llmInvokeStartTime : 0,
      }),
      recordTtsTrigger: assign((_ctx) => ({
        ..._ctx, // Preserve existing context
        ttsTriggerTime: Date.now(),
      })),
      recordTtsEnd: assign({
        ttsEndTime: (_ctx) => Date.now(),
        ttsLastTurnDuration: (ctx) => ctx.ttsTriggerTime ? Date.now() - ctx.ttsTriggerTime : 0,
      }),
      // New action that selects a case and sends an event - doesn't need self parameter
      selectCaseForMachine: (context, _event, meta) => {
        const callSid = context.callSid || 'UNKNOWN_CALL';
        console.log(`[XState:${callSid}] Selecting case for machine - Current callSid: ${callSid}`);
        
        // Hard-coded case data for simplicity
        const cases = [
          {
            case_id: 'case1',
            scenario: 'Headache', 
            initial_question: 'Okay, please tell me about this headache. When did it start?',
            questions: [],
            data: {}
          },
          {
            case_id: 'case2',
            scenario: 'Chest Pain', 
            initial_question: 'Got it. Can you describe the chest pain for me?',
            questions: [],
            data: {}
          }
        ];
        
        // Randomly select a case
        const caseData = cases[Math.floor(Math.random() * cases.length)];
        console.log(`[XState:${callSid}] Selected case: ${caseData.scenario}`);
        
        // Use the state from meta to send the event - this doesn't require self
        if (meta.state && meta.state.actions) {
          console.log(`[XState:${callSid}] Setting up INTERNAL_SET_CASE event with case: ${caseData.scenario}`);
          meta.state._event.origin.send({ type: 'INTERNAL_SET_CASE', caseData });
        } else {
          console.error(`[XState:${callSid}] Cannot send event - meta.state not available`);
        }
      },
      selectCaseAutomatically: (context, _event, { self }) => {
        // Get callSid safely, with fallback to avoid undefined errors
        const callSid = context.callSid || 'UNKNOWN_CALL';
        
        console.log(`[XState:${callSid}] Running selectCaseAutomatically action`);
        
        // Extra debug logging to troubleshoot context issues
        console.log(`[XState:${callSid}] Context dump:`, { 
          callSid: context.callSid,
          currentState: context.currentState,
          hasConversation: Array.isArray(context.conversation)
        });
        
        if (!context.callSid) {
          console.error(`[XState:${callSid}] ERROR: Missing callSid in context during case selection`);
          // Continue anyway - we'll use the fallback callSid
        }
        
        console.log(`[XState:${callSid}] Automatically selecting a case...`);
        
        // Use hardcoded case data to avoid circular dependency
        // This is the same data structure used in fetchRandomCaseData
        const cases = [
          {
            case_id: 'case1',
            scenario: 'Headache', 
            initial_question: 'Okay, please tell me about this headache. When did it start?',
            questions: [],
            data: {}
          },
          {
            case_id: 'case2',
            scenario: 'Chest Pain', 
            initial_question: 'Got it. Can you describe the chest pain for me?',
            questions: [],
            data: {}
          }
        ];
        
        // Randomly select a case
        const caseData = cases[Math.floor(Math.random() * cases.length)];
        console.log(`[XState:${callSid}] Selected case: ${caseData.scenario}`);
        
        // Send the INTERNAL_SET_CASE event with the case data
        console.log(`[XState:${callSid}] Sending INTERNAL_SET_CASE event with case: ${caseData.scenario}`);
        self.send({ type: 'INTERNAL_SET_CASE', caseData });
        console.log(`[XState Action][${context.callSid}] --- Finished selectCaseAutomatically ---`); // <<< ADDED LOGGING
      },
      assignCurrentStateCaseSelection: assign({ currentState: 'case_selection' })
    }
  }) as unknown as ReturnType<typeof createMachine<OsceContext, OsceEvents>>; 