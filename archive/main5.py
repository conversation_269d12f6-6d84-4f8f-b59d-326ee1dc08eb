from flask import Flask, request, jsonify

app = Flask(__name__)

# Middleware to log every request
@app.before_request
def log_request_info():
    print(f'{request.method} {request.url}')
    print('Headers:', request.headers)
    print('Body:', request.get_json())

# Main webhook endpoint
@app.route('/webhook', methods=['POST'])
def webhook():
    data = request.get_json()
    event_type = data.get('event_type')
    
    if event_type == 'call.initiated':
        print(f"Call initiated from {data.get('from')}")
    elif event_type == 'call.answered':
        print(f"Call answered from {data.get('from')}")
    elif event_type == 'call.completed':
        print(f"Call completed from {data.get('from')}")
    else:
        print(f"Unhandled event type: {event_type}")
    
    return 'Received', 200

# Handle other routes
@app.errorhandler(404)
def not_found(e):
    return 'Not Found', 404

# Error handling middleware
@app.errorhandler(Exception)
def handle_error(e):
    print(e)
    return 'Something broke!', 500

if __name__ == '__main__':
    port = 5000
    app.run(port=port, debug=True)
