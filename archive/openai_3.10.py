import os
import time
from dotenv import load_dotenv
from langchain_groq import ChatGroq
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import FAISS
from langchain_community.document_loaders import DirectoryLoader
from langchain.text_splitter import CharacterTextSplitter
from langchain_core.messages import HumanMessage, AIMessage
from langchain_community.chat_message_histories import ChatMessageHistory

load_dotenv()

os.environ["GROQ_API_KEY"] = os.getenv("GROQ_API_KEY")
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

chat = ChatGroq(temperature=0, model_name="llama-3.1-8b-instant")

def prepare_documents():
    if not os.path.exists("faiss_index"):
        loader = DirectoryLoader('RAG', glob="**/*.txt")
        documents = loader.load()
        text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)
        texts = text_splitter.split_documents(documents)
        embeddings = OpenAIEmbeddings()
        vectorstore = FAISS.from_documents(texts, embeddings)
        vectorstore.save_local("faiss_index")
        print("Vector store created and saved.")
    else:
        print("Vector store already exists.")

prepare_documents()

embeddings = OpenAIEmbeddings()
vectorstore = FAISS.load_local("faiss_index", embeddings, allow_dangerous_deserialization=True)

def get_rag_response(query):
    docs = vectorstore.similarity_search(query, k=3)
    context = "\n".join([doc.page_content for doc in docs])
    return context

system_prompt = """You are a customer service agent for EnergyPlus Utilities, a gas and electricity provider. Respond to customer queries professionally and helpfully. Use British English and format responses for text-to-speech delivery. Only provide information that is explicitly stated in the given context. If the information is not in the context, say "I don't have that information available." Do not make up or infer any information not directly provided in the context."""

chat_history = ChatMessageHistory()

def process_query(query):
    start_time = time.perf_counter()
    
    chat_history.add_user_message(query)
    
    context = get_rag_response(query)
    
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"Query: {query}\n\nContext: {context}\n\nPlease answer the query based only on the provided context. If the context doesn't contain relevant information, say 'I don't have that information available.'"}
    ]

    response = chat.invoke(messages)
    chat_history.add_ai_message(response.content)
    
    end_time = time.perf_counter()
    print(f"Query processing time: {end_time - start_time:.4f} seconds")
    return response.content.strip()

# Example usage
queries = [
    "Hi, I'm interested in signing up for your electricity service. Can you tell me about your rates?",
    "What discounts do you offer?",
    "Tell me about your company's history and values."
]

for query in queries:
    print(f"Query: {query}")
    response = process_query(query)
    print(f"Response: {response}")
    print('-------------------------')