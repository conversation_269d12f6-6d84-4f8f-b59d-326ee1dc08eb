import json
import os
import telnyx
from flask import Flask, request
from dotenv import load_dotenv
import time
import socket
import struct
import wave
import threading
import traceback
import audioop
from collections import OrderedDict
import sys
import base64
# Flask app setup
app = Flask(__name__)

# Load environment variables
load_dotenv()

# Set up Telnyx API key and Connection ID
TELNYX_API_KEY = os.getenv("TELNYX_API_KEY")
TELNYX_CONNECTION_ID = os.getenv("TELNYX_CONNECTION_ID")

if not TELNYX_API_KEY:
    raise ValueError("TELNYX_API_KEY is not set in the environment variables")
if not TELNYX_CONNECTION_ID:
    raise ValueError("TELNYX_CONNECTION_ID is not set in the environment variables")

telnyx.api_key = TELNYX_API_KEY

# Dictionary to store call handlers
call_handlers = {}

# Define the IP and port you want to listen on for RTP
UDP_IP = "0.0.0.0"  # Listen on all available interfaces
UDP_PORT = 5004
UDP_TRANSMIT = "*************"

class CallHandler:
    def __init__(self, call_control_id):
        self.call_control_id = call_control_id
        self.call_leg_id = None
        self.wav_file = None
        self.rtp_thread = None
        self.is_receiving = True
        self.sample_width = 2  # 16-bit audio
        self.channels = 1      # Mono
        self.sample_rate = 8000  # G.711 sample rate
        self.packet_buffer = OrderedDict()
        self.expected_seq = None
        print(f"CallHandler created for call_control_id: {call_control_id}")

    def handle_answered(self):
        try:
            call = telnyx.Call(connection_id=TELNYX_CONNECTION_ID)
            call.call_control_id = self.call_control_id

            # Start the fork (RTP listening)
            fork_response = call.fork_start(target=f"udp:{UDP_TRANSMIT}:{UDP_PORT}")
            print(f"Fork started for call {self.call_control_id}")
            print(f"Fork response: {fork_response}")

            # Start speaking concurrently
            speak_thread = threading.Thread(target=self.speak_on_call, daemon=True)
            speak_thread.start()

        except Exception as e:
            print(f"Error in handle_answered for call {self.call_control_id}: {e}")
            traceback.print_exc()

    # def speak_on_call(self):
    #     try:
    #         call = telnyx.Call(connection_id=TELNYX_CONNECTION_ID)
    #         call.call_control_id = self.call_control_id

    #         # Trigger speaking on the call
    #         speak_response = call.speak(
    #             payload="Say this on the call. Streamz helps you build pipelines to manage continuous streams of data. It is simple to use in simple cases, but also supports complex pipelines that involve branching, joining, flow control, feedback, back pressure, and so on.",
    #             language="en-US",
    #             voice="female"
    #         )

    #         print(f"Speak response: {speak_response}")
    #     except Exception as e:
    #         print(f"Error in speak_on_call for call {self.call_control_id}: {e}")
    #         traceback.print_exc()

    def speak_on_call(self):
        try:
            call = telnyx.Call(connection_id=TELNYX_CONNECTION_ID)
            call.call_control_id = self.call_control_id

            # Convert the WAV file to Base64
            base64_audio = convert_wav_to_base64('OSR_us_000_0010_8k.wav')

            # Trigger playback of the Base64 audio file
            playback_response = call.playback_start(
                playback_content=base64_audio,  # Base64 encoded audio file
                audio_type="wav",               # Specify it's a WAV file
                target_legs="self",             # Play audio on self leg
                cache_audio=True                # Cache the audio
            )

            print(f"Playback response: {playback_response}")
            
            # Start a new thread to stop the playback after 5 seconds
            threading.Thread(target=self.stop_playback_after_delay, args=(call, 5)).start()

        except Exception as e:
            print(f"Error in speak_on_call for call {self.call_control_id}: {e}")
            traceback.print_exc()

    def stop_playback_after_delay(self, call, delay):
        """Stops the playback after a delay without blocking the main flow."""
        try:
            # Wait for the specified delay (e.g., 5 seconds)
            time.sleep(delay)

            # Stop the playback
            stop_response = call.playback_stop()
            print(f"Playback stopped after {delay} seconds: {stop_response}")
        except Exception as e:
            print(f"Error stopping playback after {delay} seconds: {e}")
            traceback.print_exc()
    def start_rtp_listener(self, call_leg_id):
        self.call_leg_id = call_leg_id
        try:
            self.wav_file = wave.open(f'output_{call_leg_id}.wav', 'wb')
            self.wav_file.setnchannels(self.channels)
            self.wav_file.setsampwidth(self.sample_width)
            self.wav_file.setframerate(self.sample_rate)
            self.rtp_thread = threading.Thread(target=self.receive_rtp, daemon=True)
            self.rtp_thread.start()
            print(f"RTP listener started for call_leg_id: {call_leg_id}")
        except Exception as e:
            print(f"Error starting RTP listener for call {self.call_control_id}: {e}")
            traceback.print_exc()

    def receive_rtp(self):
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.bind((UDP_IP, UDP_PORT))
        sock.settimeout(1.0)  # Set a timeout for the socket
        
        print(f"Listening for RTP on {UDP_IP}:{UDP_PORT}")
        
        start_time = time.time()
        while self.is_receiving:
            try:
                data, addr = sock.recvfrom(1500)
                strep_header, rtp_payload = self.parse_strep_packet(data)
                if rtp_payload:
                    self.process_rtp_packet(rtp_payload)
            except socket.timeout:
                elapsed_time = time.time() - start_time
                print(f"No data received for 1 second. Total packets: {self.packet_count}. Elapsed time: {elapsed_time:.2f}s")
            except Exception as e:
                print(f"Error receiving RTP for call {self.call_control_id}: {e}")
                traceback.print_exc()
                break

    def parse_strep_packet(self, packet):
        if len(packet) < 24:
            print(f"Received packet is too short: {len(packet)} bytes")
            return None, b''

        # Parse STREP header
        header = struct.unpack('!HHLQQ', packet[:24])
        magic_version_flags = header[0]
        header_len = magic_version_flags & 0xFF
        version = (magic_version_flags >> 10) & 0xF
        leg = (magic_version_flags >> 5) & 0x1
        direction = (magic_version_flags >> 4) & 0x1
        call_leg_id = (header[3] << 64) | header[4]

        strep_header = {
            'version': version,
            'leg': leg,
            'direction': direction,
            'header_len': header_len,
            'call_leg_id': hex(call_leg_id)
        }

        # Extract RTP payload
        rtp_payload = packet[header_len:]

        return strep_header, rtp_payload

    def process_rtp_packet(self, rtp_payload):
        # Parse RTP header
        if len(rtp_payload) < 12:
            print(f"RTP payload too short: {len(rtp_payload)} bytes")
            return

        rtp_header = struct.unpack('!BBHII', rtp_payload[:12])
        payload_type = rtp_header[1] & 0x7F
        sequence_number = rtp_header[2]
        timestamp = rtp_header[3]

        # Extract audio data
        audio_data = rtp_payload[12:]

        # Determine codec
        if payload_type == 0:
            codec = 'PCMU'
        elif payload_type == 8:
            codec = 'PCMA'
        else:
            print(f"Unsupported payload type: {payload_type}")
            return

        # Buffer the packet
        self.packet_buffer[sequence_number] = (codec, audio_data)

        # Process buffered packets
        self.process_buffered_packets()

    def process_buffered_packets(self):
        while self.packet_buffer:
            if self.expected_seq is None:
                self.expected_seq = min(self.packet_buffer.keys())

            if self.expected_seq not in self.packet_buffer:
                break

            codec, audio_data = self.packet_buffer.pop(self.expected_seq)
            
            # Convert to PCM
            if codec == 'PCMU':
                pcm_data = audioop.ulaw2lin(audio_data, self.sample_width)
            elif codec == 'PCMA':
                pcm_data = audioop.alaw2lin(audio_data, self.sample_width)
            
            # Ensure correct endianness
            if sys.byteorder == 'big':
                pcm_data = audioop.byteswap(pcm_data, self.sample_width)

            self.wav_file.writeframes(pcm_data)
            self.packet_count += 1

            if self.packet_count % 100 == 0:
                print(f"Processed {self.packet_count} packets for call {self.call_control_id}")
                print(f"Codec: {codec}")
                print(f"Audio data length: {len(audio_data)}")
                print(f"PCM data length: {len(pcm_data)}")

            self.expected_seq = (self.expected_seq + 1) & 0xFFFF  # 16-bit sequence number

    def hangup(self):
        self.is_receiving = False
        if self.rtp_thread:
            self.rtp_thread.join(timeout=5)  # Wait for the thread to finish
        if self.wav_file:
            self.wav_file.close()
        print(f"Call {self.call_control_id} hung up. Total packets received: {self.packet_count}")

def convert_wav_to_base64(file_path):
    with open(file_path, "rb") as wav_file:
        # Read the WAV file and encode it as Base64
        encoded_string = base64.b64encode(wav_file.read()).decode('utf-8')
    return encoded_string

# Convert the WAV file
# base64_audio = convert_wav_to_base64('OSR_us_000_0010_8k.wav')
# print(base64_audio)


@app.route('/webhook', methods=['POST'])
def webhook():
    data = request.json
    event_type = data['data']['event_type']
    payload = data['data']['payload']
    call_control_id = payload.get('call_control_id')

    print(f"Received event: {event_type} for call_control_id: {call_control_id}")
    print(f"Full payload: {data}")

    try:
        if event_type == 'call.initiated':
            if call_control_id:
                call_handlers[call_control_id] = CallHandler(call_control_id)
                call = telnyx.Call(connection_id=TELNYX_CONNECTION_ID)
                call.call_control_id = call_control_id
                answer_response = call.answer()
                print(f"Answered call for call_control_id: {call_control_id}")
                print(f"Answer response: {answer_response}")
            else:
                print("Error: call_control_id not found in call.initiated event")
        
        elif event_type == 'call.answered':
            if call_control_id in call_handlers:
                print(f"Handling answered event for call_control_id: {call_control_id}")
                call_handlers[call_control_id].handle_answered()
            else:
                print(f"Error: CallHandler not found for call_control_id: {call_control_id}")
        
        elif event_type == 'call.fork.started':
            call_leg_id = payload.get('call_leg_id')
            print(f"Fork started event received. call_control_id: {call_control_id}, call_leg_id: {call_leg_id}")
            if call_control_id in call_handlers and call_leg_id:
                call_handlers[call_control_id].start_rtp_listener(call_leg_id)
            else:
                print(f"Error: CallHandler not found or call_leg_id missing for call_control_id: {call_control_id}")
        
        elif event_type == 'call.hangup':
            if call_control_id in call_handlers:
                print(f"Handling hangup for call_control_id: {call_control_id}")
                call_handlers[call_control_id].hangup()
                del call_handlers[call_control_id]
            else:
                print(f"Error: CallHandler not found for call_control_id: {call_control_id}")
        
        else:
            print(f"Unhandled event type: {event_type}")

        print(f"Current call_handlers: {list(call_handlers.keys())}")

    except Exception as e:
        print(f"Error processing webhook: {e}")
        traceback.print_exc()

    return '', 200

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)