import os
import json
from flask import Flask, request, jsonify
import telnyx
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Set Telnyx API Key and Public Key
telnyx.api_key = os.getenv("TELNYX_API_KEY")
public_key = os.getenv("TELNYX_PUBLIC_KEY")

app = Flask(__name__)

@app.route("/webhook", methods=["POST"])
def webhooks():
    body = request.data.decode("utf-8")
    signature = request.headers.get("Telnyx-Signature-Ed25519", None)
    timestamp = request.headers.get("Telnyx-Timestamp", None)
    
    print("Received webhook request")
    print("Headers:", request.headers)
    print("Body:", body)
    print(f"Signature: {signature}")
    print(f"Timestamp: {timestamp}")

    try:
        if not signature or not timestamp:
            raise ValueError("Missing required headers")

        # Check if timestamp can be converted to float (Unix time format)
        try:
            float(timestamp)
        except ValueError:
            raise ValueError("Invalid timestamp format")

        # Ensure timestamp is treated as a string
        event = telnyx.Webhook.construct_event(body, signature, str(timestamp), public_key)
        print(f"Received event: id={event.id}, type={event.type}")
        handle_event(event)
        return "", 200
    except ValueError as e:
        print("Error while decoding event:", e)
        return "Bad payload", 400
    except telnyx.error.SignatureVerificationError as e:
        print("Invalid signature:", e)
        return "Bad signature", 400
    except Exception as e:
        print("Internal server error:", e)
        return "Internal server error", 500

def handle_event(event):
    try:
        if event.type == "call.initiated":
            print(f"Handling call.initiated for call_control_id={event.data.payload['call_control_id']}")
            call = telnyx.Call.retrieve(event.data.payload["call_control_id"])
            call.answer()
            print("Call answered")
        elif event.type == "call.answered":
            print(f"Handling call.answered for call_control_id={event.data.payload['call_control_id']}")
            call = telnyx.Call.retrieve(event.data.payload["call_control_id"])
            call.speak(
                payload="Hello, Telnyx user! Welcome to this call control demonstration",
                language="en-US",
                voice="female"
            )
            print("Speaking message")
        elif event.type == "call.speak.ended":
            print(f"Handling call.speak.ended for call_control_id={event.data.payload['call_control_id']}")
            call = telnyx.Call.retrieve(event.data.payload["call_control_id"])
            call.hangup()
            print("Call hung up")
    except Exception as e:
        print(f"An error occurred while handling event: {e}")

if __name__ == "__main__":
    app.run(port=int(os.getenv("TELNYX_APP_PORT", 5000)), debug=True)
