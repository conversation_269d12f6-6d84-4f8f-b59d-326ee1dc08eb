from flask import Flask, Response, request
from flask_sockets import Sockets
import json
import base64
import logging
from datetime import datetime
import socket
import sys

# Use DEBUG or INFO as needed
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

HTTP_SERVER_PORT = 5000
app = Flask(__name__)
sockets = Sockets(app)

# Store active calls in memory
active_calls = {}

@app.route('/voice', methods=['POST'])
def voice():
    """
    Initial TwiML response that starts the stream.
    <PERSON><PERSON><PERSON> will hit this endpoint first, where we instruct <PERSON><PERSON><PERSON>
    to open a secure websocket (wss://) to /stream.
    """
    call_sid = request.values.get('CallSid')
    logger.info(f"Initial voice request for call {call_sid}")

    active_calls[call_sid] = {
        'start_time': datetime.now(),
        'status': 'starting'
    }

    # Log all request details for debugging
    logger.debug(f"Request values: {request.values}")
    logger.debug(f"Request headers: {dict(request.headers)}")

    # Always use wss:// for Twilio Media Streams
    twiml = f"""<?xml version="1.0" encoding="UTF-8"?>
    <Response>
        <Start>
            <Stream url="wss://{request.host}/stream"/>
        </Start>
        <Redirect method="POST">/continue-call</Redirect>
    </Response>
    """
    logger.info(f"Sending TwiML response: {twiml}")
    return Response(twiml, mimetype='text/xml')


@app.route('/continue-call', methods=['POST'])
def continue_call():
    """
    Keeps the call alive by continuously redirecting.
    Twilio will POST here repeatedly. If we've flagged the call as finished,
    send a <Hangup/>; otherwise, reissue a Gather or redirect so the call stays up.
    """
    call_sid = request.values.get('CallSid')
    logger.info(f"Continuing call {call_sid}")

    if call_sid in active_calls:
        if active_calls[call_sid].get('status') == 'finished':
            twiml = """<?xml version="1.0" encoding="UTF-8"?>
            <Response>
                <Hangup/>
            </Response>
            """
        else:
            twiml = """<?xml version="1.0" encoding="UTF-8"?>
            <Response>
                <Gather timeout="10" action="/continue-call" method="POST"/>
                <Redirect method="POST">/continue-call</Redirect>
            </Response>
            """
    else:
        # If we don't recognise the CallSid, end the call
        twiml = """<?xml version="1.0" encoding="UTF-8"?>
        <Response>
            <Hangup/>
        </Response>
        """

    return Response(twiml, mimetype='text/xml')


# -------------------------------------------------------------------
# 1) WebSocket route for receiving audio from Twilio
# -------------------------------------------------------------------
@sockets.route('/stream')
def stream_socket(ws):
    """
    Actual WebSocket handler for incoming Twilio Media Stream audio frames.
    We expect a GET + "Upgrade: websocket" request here.
    """
    logger.info("WebSocket connection accepted")
    count = 0
    has_seen_media = False
    call_sid = None

    while not ws.closed:
        message = ws.receive()
        if message is None:
            logger.warning("No message received (possible WebSocket close).")
            continue

        try:
            data = json.loads(message)
            event_type = data.get('event')

            if event_type == "connected":
                logger.info("Connected event received: %s", message)

            elif event_type == "start":
                logger.info("Start event received: %s", message)
                # Extract the call SID from the stream SID
                call_sid = data.get('streamSid', '').split('.')[0]
                if call_sid in active_calls:
                    active_calls[call_sid]['status'] = 'streaming'
                    # Send an acknowledgement back to the WebSocket
                    ws.send(json.dumps({
                        "event": "start_ack",
                        "call_sid": call_sid
                    }))

            elif event_type == "media":
                if not has_seen_media:
                    logger.info("Received first media frame")
                    has_seen_media = True
                media_data = data.get('media', {}).get('payload')
                if media_data:
                    # Decode base64 audio data here if needed
                    try:
                        audio_bytes = base64.b64decode(media_data)
                        logger.debug(f"Received audio chunk: {len(audio_bytes)} bytes")
                        # You could process or store audio_bytes here
                    except Exception as e:
                        logger.error(f"Error decoding audio data: {e}")

            elif event_type == "closed":
                logger.info("Closed event received: %s", message)
                if call_sid in active_calls:
                    active_calls[call_sid]['status'] = 'finished'
                break

            count += 1

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON from message: {e}")
        except Exception as e:
            logger.error(f"General error processing message: {e}")

    logger.info("WebSocket closed. Received %d messages total.", count)
    if call_sid in active_calls:
        active_calls[call_sid]['status'] = 'finished'


# -------------------------------------------------------------------
# 2) Fallback HTTP route on the same path to avoid WebsocketMismatch
# -------------------------------------------------------------------
@app.route('/stream', methods=['POST', 'GET'])
def fallback_stream():
    """
    Fallback route for any non-WS requests (e.g. POST or normal GET) to /stream.
    Twilio occasionally may send a request that isn't a WebSocket handshake.
    This avoids 400 errors and logs a simple 200 OK response.
    """
    logger.debug("Received a non-WebSocket request at /stream; returning 200 OK.")
    return "OK", 200


@app.route('/status')
def get_status():
    """
    Simple endpoint to show the current active calls and durations.
    """
    return {
        'active_calls': len(active_calls),
        'calls': {
            sid: {
                'status': data['status'],
                'duration': str(datetime.now() - data['start_time'])
            }
            for sid, data in active_calls.items()
        }
    }


def find_available_port(start_port, max_attempts=10):
    """
    Find an available port starting from start_port and returning the first
    free one. This avoids conflicts if the default port is in use.
    """
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    return None


if __name__ == '__main__':
    from gevent import pywsgi
    from geventwebsocket.handler import WebSocketHandler

    # Attempt to find an open port for the server
    port = find_available_port(HTTP_SERVER_PORT)
    if port is None:
        logger.error("Could not find an available port. Exiting.")
        sys.exit(1)

    logger.info(f"Initialising server on port {port}")

    try:
        # Use gevent’s pywsgi server with WebSocket support
        server = pywsgi.WSGIServer(
            ('', port),
            app,
            handler_class=WebSocketHandler
        )
        server.serve_forever()
    except OSError as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Server stopped by user.")
        sys.exit(0)