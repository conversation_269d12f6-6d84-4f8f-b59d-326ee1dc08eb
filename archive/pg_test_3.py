import psycopg2
from psycopg2 import sql

# Connect to PostgreSQL database
def connect_to_db():
    try:
        conn = psycopg2.connect(
            host="**************",
            database="agent",   # Update with your database name
            user="haran",             # Update with your database user
            password="Haran2804"      # Update with your database password
        )
        return conn
    except Exception as e:
        print(f"Error connecting to the database: {e}")
        return None

# Get user details by phone number and optional pin
def get_user_by_phone_and_pin(phone_number, pin_code=None):
    conn = connect_to_db()
    
    if conn is None:
        return None

    try:
        cursor = conn.cursor()

        # If pin_code is provided, we will add it to the query
        if pin_code:
            query = sql.SQL("""
                SELECT user_id, COALESCE(NULLIF(nickname, ''), first_name) AS name, pin_code
                FROM users
                WHERE phone_number = %s AND pin_code = %s
            """)
            cursor.execute(query, (phone_number, pin_code))
        else:
            query = sql.SQL("""
                SELECT user_id, COALESCE(NULLIF(nickname, ''), first_name) AS name, pin_code
                FROM users
                WHERE phone_number = %s
            """)
            cursor.execute(query, (phone_number,))
        
        # Fetch all matching records
        results = cursor.fetchall()
        
        # Check for duplicates (more than one row with the same phone number)
        duplicates = len(results) > 1
        
        # If results are found, return them with the duplicates flag
        if results:
            user_data = []
            for row in results:
                user_data.append({
                    'user_id': row[0],
                    'name': row[1],
                    'pin_code': row[2]
                })
            return {
                'user_data': user_data,
                'duplicates': duplicates
            }
        else:
            return {
                'user_data': None,
                'duplicates': False
            }
    
    except Exception as e:
        print(f"Error retrieving user data: {e}")
        return None

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Example usage:
# phone_number = "+447931868798"
# pin_code = "1234"  # Optional, can be None

# result = get_user_by_phone_and_pin(phone_number)#, pin_code)

# if result:
#     print("Duplicates:", result['duplicates'])
#     if result['user_data']:
#         for user in result['user_data']:
#             print(f"User ID: {user['user_id']}, Name: {user['name']}, PIN: {user['pin_code']}")
#     else:
#         print("No users found.")
# else:
#     print("An error occurred.")

def handle_user_lookup(phone_number, pin_code=None):
    result = get_user_by_phone_and_pin(phone_number, pin_code)

    if result:
        if result['duplicates']:
            print("Duplicates found.")
            if not pin_code:
                # Ask for the PIN code if not already provided
                pin_code = input("Please enter the 4-digit PIN code: ")
                return get_user_by_phone_and_pin(phone_number, pin_code)
        else:
            return result
    else:
        print("An error occurred.")
        return None

# Example usage:
phone_number = "+447931868798"
pin_code = None  # Optional at first, could be None

# Call the function and handle duplicates
final_result = handle_user_lookup(phone_number, pin_code)

# Output result
if final_result:
    print("Duplicates:", final_result['duplicates'])
    if final_result['user_data']:
        for user in final_result['user_data']:
            print(f"User ID: {user['user_id']}, Name: {user['name']}, PIN: {user['pin_code']}")
    else:
        print("No users found.")
else:
    print("No results returned.")