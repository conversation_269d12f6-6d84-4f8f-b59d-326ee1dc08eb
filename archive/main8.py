import json
import os
import telnyx
from flask import Flask, request
from openai import OpenAI
from dotenv import load_dotenv
import re
app = Flask(__name__)
import time

load_dotenv()

# Set your OpenAI API key
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

client = OpenAI()
assistant_id = 'asst_7xt8bTWIh6GZHDCi74l492iv'
current_thread = None

def wait_on_run(run, thread):
    while run.status == "queued" or run.status == "in_progress":
        run = client.beta.threads.runs.retrieve(
            thread_id=thread.id,
            run_id=run.id,
        )
        time.sleep(0.1)
    return run

def pretty_str(messages):
    formatted_str = ""
    for m in messages:
        formatted_str += f"{m.role}: {m.content[0].text.value}\n"
    return formatted_str

def get_response(thread):
    return client.beta.threads.messages.list(thread_id=thread.id, order="asc")


# Endpoint for receiving webhook events
@app.route('/webhook', methods=['POST'])
def inbound():
    body = json.loads(request.data)
    event = body.get("data").get("event_type")

    try:
        if event == "call.initiated":
            handle_call_initiated(body)
        elif event == "call.answered":
            handle_call_answered(body)
        elif event == "call.transcription":
            handle_call_transcription(body)
        elif event == 'call.dtmf.received':
            handle_call_dtmf_received(body)
    except Exception as e:
        print(f"Error: {e}")
        return json.dumps({"success":False}), 500, {"ContentType":"application/json"}
    return json.dumps({"success":True}), 200, {"ContentType":"application/json"}

def handle_call_initiated(body):
    call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
    call.call_control_id = body.get("data").get("payload").get("call_control_id")
    call.answer()

def handle_call_answered(body):
    global current_thread  
    call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
    call.call_control_id = body.get("data").get("payload").get("call_control_id")
    call.transcription_start(language="en", interim_results=True)
    current_thread = client.beta.threads.create()
    print(current_thread.id)
transcript_buffer = ""

def handle_call_transcription(body):
    global transcript_buffer
    transcription_data = body.get("data").get("payload").get("transcription_data")
    transcript = transcription_data.get("transcript")
    print(f"Transcribed Text: {transcript}")
    transcript_buffer += " " + transcript

def handle_call_dtmf_received(body):
    global transcript_buffer
    print(body)
    dtmf = body.get("data").get("payload").get("digit")

    if dtmf == "#":  # Assuming '#' is the signal for end of speech
        print('---------# PRESSED--------------------')
        response = send_to_llm(transcript_buffer.strip())
        print
        play_tts(body, response)
        transcript_buffer = ""
    elif dtmf == "*":  # Assuming '*' is the signal to hang up
        call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
        call.call_control_id = body.get("data").get("payload").get("call_control_id")
        call.hangup()

def send_to_llm(transcript):
    global current_thread
    # response = client.completions.create(
    #     engine="davinci",
    #     prompt=transcript,
    #     max_tokens=150
    # )
    # print(response.choices[0].text.strip())
    print("CURR_Thread_ID: ", current_thread.id)
    # run = client.beta.threads.runs.create_and_poll(
    #     thread_id=thread_id,
    #     assistant_id=assistant_id,
    #     instructions=transcript  # "Please address the user as Jane Doe. The user has a premium account."
    # )
    message = client.beta.threads.messages.create(
                    thread_id=current_thread.id,
                    role="user",
                    content=transcript,
                )

    run = client.beta.threads.runs.create(
                    thread_id=current_thread.id,
                    assistant_id=assistant_id,
                )
                
    run = wait_on_run(run, current_thread)

    openai_output_raw = pretty_str(get_response(current_thread))

    last_assistant_text = openai_output_raw.split('assistant:')[-1].strip()


    print(last_assistant_text)  
    return last_assistant_text
    
    # if run.status == 'completed':
    #     messages = client.beta.threads.messages.list(
    #         thread_id=current_thread.id
    #     )
    #     if messages.data:
    #         latest_message = messages.data[-1]  # Extract the latest message
    #         latest_message_content = latest_message.content[0].text.value  # Extract the value of the latest message content
            
    #         # Remove anything within 【】 along with the symbols
    #         cleaned_message_content = re.sub(r'【[^】]*】', '', latest_message_content)
            
    #         print(cleaned_message_content)
    #     else:
    #         print("No messages found in the thread.")
    # else:
    #     print(run.status)
    
    # return cleaned_message_content

def play_tts(body, response_text):
    tts_audio = convert_text_to_speech(response_text)
    print('TTS Successful')
    call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
    call.call_control_id = body.get("data").get("payload").get("call_control_id")
    call.playback_start(playback_content="output.mp3")
    #url_audio = 'https://www.sample-videos.com/audio/mp3/india-national-anthem.mp3'
    #url_audio = "https://archive.org/download/JFK_Inaugural_Address_19610120/JFK_Inaugural_Address_19610120.mp3"
    #call.playback_start(audio_url=url_audio)
def convert_text_to_speech(text):
    response = client.audio.speech.create(
    model="tts-1",
    voice="alloy",
    input=text,
)
    response.stream_to_file("output.mp3")
    return "output.mp3"

if __name__ == '__main__':
    app.run(debug=True, port=5000)
