from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError
from datetime import datetime
from uuid import uuid4
from models import Users  # Assuming your model is saved in models.py

# Create an engine and bind it to the PostgreSQL database
engine = create_engine('************************************************/agent')

# Create a configured session class
Session = sessionmaker(bind=engine)

# Create a session object
session = Session()


# Insert a new user
# Insert a new user into the users table with unique constraint error handling
def insert_new_user(first_name, last_name, phone_number, pin_code, nickname):
    try:
        # Generate current timestamp for created_at and updated_at
        current_time = datetime.now()

        # Create a new user instance
        new_user = Users(
            user_id=uuid4(),
            first_name=first_name,
            last_name=last_name,
            phone_number=phone_number,
            pin_code=pin_code,
            nickname=nickname,
            created_at=current_time,
            updated_at=current_time
        )

        # Add and commit the new user
        session.add(new_user)
        session.commit()
        print(f"New user {first_name} {last_name} added successfully.")
        return {"duplicate": False}

    except IntegrityError as e:
        # Rollback the session in case of an error
        session.rollback()

        # Check if the error is due to the unique constraint on (phone_number, pin_code)
        if 'duplicate key value violates unique constraint' in str(e):
            print(f"Duplicate entry found for phone number {phone_number} and pin code {pin_code}.")
            return {"duplicate": True}
        else:
            # Raise other exceptions
            print(f"An unexpected error occurred: {e}")
            raise

    finally:
        session.close()  # Close the session

# Example usage:
first_name = "Kheerthiharan"
last_name = "Saravanan"
phone_number = "+447931868798"
pin_code = "2804"
nickname = "Haran"

# Insert new user using SQLAlchemy with error handling
result = insert_new_user(first_name, last_name, phone_number, pin_code, nickname)
print(result)