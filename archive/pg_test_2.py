import psycopg2
from psycopg2 import sql, errors
import uuid
from datetime import datetime

# Connect to PostgreSQL database
def connect_to_db():
    try:
        conn = psycopg2.connect(
            host="**************",
            database="agent",   # Update with your database name
            user="haran",             # Update with your database user
            password="Haran2804"      # Update with your database password
        )
        return conn
    except Exception as e:
        print(f"Error connecting to the database: {e}")
        return None

# Insert a new user into the users table
def insert_new_user(first_name, last_name, phone_number, pin_code, nickname):
    conn = connect_to_db()
    
    if conn is None:
        return

    try:
        cursor = conn.cursor()

        # Generate UUID for the user
        user_id = uuid.uuid4()

        # Get current timestamp for created_at and updated_at
        current_time = datetime.now()

        # SQL query to insert a new user
        insert_query = sql.SQL("""
            INSERT INTO users (user_id, first_name, last_name, phone_number, pin_code, nickname, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s);
        """)

        # Values to insert
        values = (str(user_id), first_name, last_name, phone_number, pin_code, nickname, current_time, current_time)

        # Execute the insert query
        cursor.execute(insert_query, values)

        # Commit the transaction
        conn.commit()

        print(f"New user {first_name} {last_name} added successfully with ID: {user_id}")

    except errors.UniqueViolation:
        # Handle unique constraint violation for duplicate phone number + pin combinations
        print(f"Error: Phone number {phone_number} with PIN {pin_code} already exists.")
        if conn is not None:
            conn.rollback()

    except Exception as e:
        print(f"Error inserting new user: {e}")
        if conn is not None:
            conn.rollback()

    finally:
        # Close the cursor and connection
        if cursor is not None:
            cursor.close()
        if conn is not None:
            conn.close()

# Example usage:
first_name = "Kheerthiharan"
last_name = "Saravanan"
phone_number = "+447931868798"
pin_code = "2804"  # 4-digit PIN
nickname = "Haran"

# Insert new user
insert_new_user(first_name, last_name, phone_number, pin_code, nickname)