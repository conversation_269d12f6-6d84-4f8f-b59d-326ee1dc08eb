/*******************************************************************
 * server.ts
 *
 * 1) Twilio => Node => local WAV + AssemblyAI real-time STT
 * 2) Also calls a Python-based VAD endpoint on port 8085
 * 3) Tracks speaker turns: multiple final transcripts accumulate until
 *    we detect ~1 second of silence -> finalise turn
 *******************************************************************/

import express from 'express'
import bodyParser from 'body-parser'
import { twiml as TwiML } from 'twilio'
import http from 'http'
import WebSocket from 'ws'
import { spawn, ChildProcessWithoutNullStreams } from 'child_process'
import axios from 'axios'
import { AssemblyAI, RealtimeTranscriber, RealtimeTranscript } from 'assemblyai'

/** 
 * We'll define a small function to split a Buffer into 20ms frames
 * at 8 kHz, 16-bit PCM => 320 bytes per frame.
 * If leftover data remains, we keep it in a global or callSid-based 
 * leftover buffer for the next chunk.
 */
function splitTo20msFrames(buffer: Buffer, leftover: Buffer): Buffer[] {
  const FRAME_SIZE = 320 // 20ms @ 8kHz, 16-bit mono
  // Combine leftover + new chunk
  const combined = Buffer.concat([leftover, buffer])

  const frames: Buffer[] = []
  let offset = 0

  while (offset + FRAME_SIZE <= combined.length) {
    frames.push(combined.slice(offset, offset + FRAME_SIZE))
    offset += FRAME_SIZE
  }

  // The leftover portion
  const newLeftover = combined.slice(offset)

  // Return frames + leftover
  leftover = newLeftover
  return [ ...frames, leftover ] // We'll treat the last element as "updated leftover"
}

//-----------------------------------------------
// Configuration
//-----------------------------------------------
const PORT = process.env.PORT || 5000
const ASSEMBLYAI_API_KEY = '********************************'
const VAD_ENDPOINT = 'http://localhost:8085/vad' // Python VAD microservice
const RAG_SERVER_URL = 'http://localhost:8001/rag/converse'

// We'll define how many consecutive "no-speech" frames = end of turn
// For 20ms frames, 50 frames ~ 1 second of silence
const SILENCE_FRAMES_THRESHOLD = 75

//-----------------------------------------------
// Setup Express
//-----------------------------------------------
const app = express()
app.use(bodyParser.urlencoded({ extended: false }))
app.use(bodyParser.json())

// AssemblyAI client
const aaiClient = new AssemblyAI({ apiKey: ASSEMBLYAI_API_KEY })

// Track call state
interface CallState {
  recordFfmpeg?: ChildProcessWithoutNullStreams
  decodeFfmpeg?: ChildProcessWithoutNullStreams
  leftover: Buffer
  aaiTranscriber?: RealtimeTranscriber
  currentTurnText: string
  consecutiveSilenceFrames: number
  conversation: Array<{ speaker: string; text: string }>
  isModelSpeaking: boolean
  currentModelResponse?: string
}
const calls: Record<string, CallState> = {}

//-----------------------------------------------
// 1) Twilio /voice -> Return <Start><Stream>
//-----------------------------------------------
app.post('/voice', (req, res) => {
  const twiml = new TwiML.VoiceResponse()

  twiml
    .start({ action: '/voiceStatusCallback' })
    .stream({
      url: `wss://${req.headers.host}/media`,
      track: 'inbound_track', // only inbound audio
    })

  twiml.say(
    'Hello! '
  )
  twiml.pause({ length: 3600 }) // keep call open
  res.type('text/xml').send(twiml.toString())
})

// Optional callback once <Start> is done
app.post('/voiceStatusCallback', (req, res) => {
  console.log('Media stream started. Call SID:', req.body.CallSid)
  res.sendStatus(200)
})

//-----------------------------------------------
// 2) WebSocket for /media
//    Twilio => Node => ffmpeg => local WAV + AAI
//    Also do VAD checks => turn detection
//-----------------------------------------------
const server = http.createServer(app)
const wss = new WebSocket.Server({ server, path: '/media' })

wss.on('connection', (ws) => {
  console.log('Twilio connected to /media WebSocket.')

  let callSid: string | null = null

  ws.on('message', async (message: string) => {
    let data
    try {
      data = JSON.parse(message)
    } catch (err) {
      console.error('Failed to parse WS message:', err)
      return
    }

    switch (data.event) {
      case 'connected':
        console.log('Twilio event: connected')
        break

      case 'start':
        callSid = data.start.callSid
        console.log('Media start event for callSid:', callSid)

        // Initialize call state
        calls[callSid] = {
          leftover: Buffer.alloc(0),
          currentTurnText: '',
          consecutiveSilenceFrames: 0,
          conversation: [],
          isModelSpeaking: false,
        }

        // 1) ffmpeg #1 => record WAV locally
        try {
          const outPath = `./recordings/${callSid}.wav`
          const recordFfmpeg = spawn('ffmpeg', [
            '-f', 'mulaw',
            '-ar', '8000',
            '-i', 'pipe:0',
            '-ac', '1',
            '-ar', '8000',
            '-f', 'wav',
            outPath,
          ])
          recordFfmpeg.on('close', (code) => {
            console.log(`recordFfmpeg for ${callSid} exited with code ${code}`)
          })
          recordFfmpeg.on('error', (err) => {
            console.error(`recordFfmpeg error for ${callSid}:`, err)
          })

          calls[callSid].recordFfmpeg = recordFfmpeg
        } catch (err) {
          console.error('Error launching record ffmpeg:', err)
        }
 
        
        // 2) ffmpeg #2 => decode mulaw -> 8kHz PCM => AssemblyAI
        try {
          const decodeFfmpeg = spawn('ffmpeg', [
            '-f', 'mulaw',
            '-ar', '8000',
            '-i', 'pipe:0',
            '-ac', '1',
            '-ar', '8000',
            '-f', 's16le',
            'pipe:1',
          ])
          decodeFfmpeg.on('close', (code) => {
            console.log(`decodeFfmpeg for ${callSid} exited with code ${code}`)
          })
          decodeFfmpeg.on('error', (err) => {
            console.error(`decodeFfmpeg error for ${callSid}:`, err)
          })

          calls[callSid].decodeFfmpeg = decodeFfmpeg
        } catch (err) {
          console.error('Error launching decode ffmpeg:', err)
        }

        // 3) Create AssemblyAI transcriber
        try {
          const aaiTranscriber = aaiClient.realtime.transcriber({
            sampleRate: 8000,
          })

          aaiTranscriber.on('open', ({ sessionId }) => {
            console.log(`AssemblyAI session opened: ${sessionId}`)
          })

          aaiTranscriber.on('transcript', (rt: RealtimeTranscript) => {
            if (!rt.text) return
            // Merge multiple final transcripts into current turn
            if (rt.message_type === 'FinalTranscript') {
              calls[callSid!].currentTurnText += rt.text + ' '
              console.log(`[AAI FINAL for ${callSid}] => ${rt.text}`)
            } else {
              // partial, we won't store permanently
              console.log(`[AAI PARTIAL for ${callSid}] => ${rt.text}`)
            }
          })

          aaiTranscriber.on('error', (error: Error) => {
            console.error(`AssemblyAI error for ${callSid}:`, error)
          })

          aaiTranscriber.on('close', (code: number, reason: string) => {
            console.log(`AssemblyAI closed for ${callSid}: ${code} ${reason}`)
          })

          await aaiTranscriber.connect()
          calls[callSid].aaiTranscriber = aaiTranscriber

          // read decodeFfmpeg stdout => feed aai + do VAD
          const decodeFfmpeg = calls[callSid].decodeFfmpeg
          if (decodeFfmpeg) {
            decodeFfmpeg.stdout.on('data', async (chunk: Buffer) => {
              // Send to AAI
              aaiTranscriber.sendAudio(chunk)
              // Also do VAD chunking
              await handleVadAndTurns(callSid!, chunk)
            })
          }
        } catch (err) {
          console.error('Error creating AAI transcriber:', err)
        }
        break

      case 'media': {
        if (!callSid) break
        const audioBuffer = Buffer.from(data.media.payload, 'base64')

        // Write chunk to recordFfmpeg
        const recordFfmpeg = calls[callSid].recordFfmpeg
        if (recordFfmpeg) {
          recordFfmpeg.stdin.write(audioBuffer)
        }

        // Write chunk to decodeFfmpeg
        const decodeFfmpeg = calls[callSid].decodeFfmpeg
        if (decodeFfmpeg) {
          decodeFfmpeg.stdin.write(audioBuffer)
        }
        break
      }

      case 'stop':
        console.log('Twilio stop event for callSid:', callSid)
        if (callSid) {
          cleanupCall(callSid)
        }
        break

      default:
        console.log('Unknown Twilio event:', data.event)
    }
  })

  ws.on('close', () => {
    console.log('Twilio WebSocket disconnected.')
    if (callSid) {
      cleanupCall(callSid)
    }
  })
})

//-----------------------------------------------
// Helper: End-of-call cleanup
//-----------------------------------------------
function cleanupCall(callSid: string) {
  const state = calls[callSid]
  if (!state) return

  // Close recordFfmpeg
  if (state.recordFfmpeg) {
    state.recordFfmpeg.stdin.end()
    state.recordFfmpeg = undefined
  }
  // Close decodeFfmpeg
  if (state.decodeFfmpeg) {
    state.decodeFfmpeg.stdin.end()
    state.decodeFfmpeg = undefined
  }
  // Close AAI transcriber
  if (state.aaiTranscriber) {
    console.log(`Closing AAI transcriber for ${callSid}...`)
    state.aaiTranscriber.close()
    state.aaiTranscriber = undefined
  }

  // If there's any partial turn not ended, finalize it
  const { currentTurnText, conversation } = state
  if (currentTurnText.trim()) {
    conversation.push({ speaker: 'caller', text: currentTurnText.trim() })
  }

  // Optionally log or store the final conversation array
  console.log(`Conversation for callSid=${callSid}:`, conversation)

  delete calls[callSid]
}

//-----------------------------------------------
// 3) handleVadAndTurns => splits chunk, calls VAD
//-----------------------------------------------

async function handleVadAndTurns(callSid: string, chunk: Buffer) {
  const state = calls[callSid];
  if (!state) return;

  let leftover = state.leftover;
  const framesPlusLeftover = splitTo20msFrames(chunk, leftover);
  leftover = framesPlusLeftover.pop()!;
  const frames = framesPlusLeftover;
  state.leftover = leftover;

  for (const frame of frames) {
    try {
      const response = await axios.post(VAD_ENDPOINT, frame, {
        headers: { 'Content-Type': 'application/octet-stream' },
        timeout: 3000,
      });
      const data = response.data;

      if (data.speech) {
        state.consecutiveSilenceFrames = 0;
      } else {
        state.consecutiveSilenceFrames++;
        if (state.consecutiveSilenceFrames >= SILENCE_FRAMES_THRESHOLD) {
          await finalizeTurn(callSid);
          state.consecutiveSilenceFrames = 0;
        }
      }
    } catch (err) {
      console.error(`VAD call failed for callSid=${callSid}:`, err);
    }
  }
}

// Add interruption handler
async function handleInterruption(callSid: string) {
  const state = calls[callSid];
  if (!state || !state.isModelSpeaking) return;

  // Mark that model stopped speaking
  state.isModelSpeaking = false;
  
  // If there was a partial model response, save it to conversation history
  if (state.currentModelResponse) {
    state.conversation.push({ 
      speaker: 'assistant', 
      text: state.currentModelResponse + ' [interrupted]' 
    });
    state.currentModelResponse = undefined;
  }

  console.log(`Model interrupted for callSid=${callSid}`);
}

// Modify finalizeTurn to track model speaking state
async function finalizeTurn(callSid: string) {
  const state = calls[callSid]
  if (!state) return

  const text = state.currentTurnText.trim()
  if (text) {
    state.conversation.push({ speaker: 'caller', text })
    console.log(`>>> Turn end for callSid=${callSid}, text="${text}"`)

    try {
      // Mark that model is processing/speaking
      state.isModelSpeaking = true;
      state.currentModelResponse = '';

      const ragResponse = await axios.post(RAG_SERVER_URL, {
        callSid,
        userMessage: text
      });
      
      const assistantReply = ragResponse.data.assistantReply;
      if (assistantReply) {
        // Only add to conversation if not interrupted
        if (state.isModelSpeaking) {
          state.conversation.push({ speaker: 'assistant', text: assistantReply });
          console.log(`>>> RAG response for ${callSid}: "${assistantReply}"`);
        }
      }
    } catch (err) {
      console.error(`Failed to get RAG response for ${callSid}:`, err);
    } finally {
      state.isModelSpeaking = false;
      state.currentModelResponse = undefined;
    }

    state.currentTurnText = ''
  }
}

//-----------------------------------------------
// Start the Server
//-----------------------------------------------
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`)
})
