import json
import os
import telnyx
from flask import Flask, request
from openai import OpenAI
from dotenv import load_dotenv
import asyncio
import re
import time
load_dotenv()
def wait_on_run(run, thread):
    while run.status == "queued" or run.status == "in_progress":
        run = client.beta.threads.runs.retrieve(
            thread_id=thread.id,
            run_id=run.id,
        )
        time.sleep(0.1)
    return run

def pretty_str(messages):
    formatted_str = ""
    for m in messages:
        formatted_str += f"{m.role}: {m.content[0].text.value}\n"
    return formatted_str


# Set your OpenAI API key
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

client = OpenAI()
assistant_id = 'asst_7xt8bTWIh6GZHDCi74l492iv'


thread = client.beta.threads.create()
thread_id = thread.id
def get_response(thread):
    return client.beta.threads.messages.list(thread_id=thread.id, order="asc")
print(thread_id)


transcript_buffer="Hello Good morning, tell me about the opening hours"
transcript_buffer2="How does AI work? Explain it in simple terms."

# thread_message = client.beta.threads.messages.create(
#   thread_id = thread_id,
#   role="user",
#   content=transcript_buffer
# )

message = client.beta.threads.messages.create(
                thread_id=thread.id,
                role="user",
                content=transcript_buffer,
            )

run = client.beta.threads.runs.create(
                thread_id=thread.id,
                assistant_id=assistant_id,
            )
            
run = wait_on_run(run, thread)

openai_output_raw = pretty_str(get_response(thread))

last_assistant_text = openai_output_raw.split('assistant:')[-1].strip()


print(last_assistant_text)





# run = client.beta.threads.runs.create_and_poll(
#   thread_id=thread_id,
#   assistant_id=assistant_id
  #instructions=transcript_buffer#"Please address the user as Jane Doe. The user has a premium account."
    #  additional_messages=[
    #         {
    #             "role": "user",
    #             "content": "How does AI work? Explain it in simple terms."
    #         }
    #     ]
    #)



# if run.status == 'completed':
#   messages = client.beta.threads.messages.list(
#     thread_id=thread_id
#   )
#   if messages.data:
#     latest_message = messages.data[-1]  # Extract the latest message
#     latest_message_content = latest_message.content[0].text.value  # Extract the value of the latest message content
#     #print(latest_message_content)
#     # Remove anything within 【】 along with the symbols
#     cleaned_message_content = re.sub(r'【[^】]*】', '', latest_message_content)
    
#     print(cleaned_message_content)
#   else:
#     print("No messages found in the thread.")
# else:
#   print(run.status)