#import anthropic
import os
from dotenv import load_dotenv
import re
from llama_index.core import VectorStoreIndex, SimpleDirectoryReader, StorageContext
from llama_index.vector_stores.faiss import FaissVectorStore

from llama_index.embeddings.openai import OpenAIEmbedding

from llama_index.llms.anthropic import Anthropic #as anthropic
from llama_index.core import Settings

load_dotenv()

os.environ["ANTHROPIC_API_KEY"] = os.getenv("ANTHROPIC_API_KEY")
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")  # Make sure to add this to your .env file

client = Anthropic(
    api_key=os.environ.get("ANTHROPIC_API_KEY")
)

# Set up RAG system
documents = SimpleDirectoryReader('RAG').load_data()
embed_model = OpenAIEmbedding()
vector_store = FaissVectorStore(dim=1536)

# Create or load index
index_path = 'RAG/DB'
if os.path.exists(index_path):
    storage_context = StorageContext.from_defaults(persist_dir=index_path)
    index = VectorStoreIndex.load_from_storage(storage_context)
else:
    index = VectorStoreIndex.from_documents(
        documents,
        embed_model=embed_model,
        vector_store=vector_store
    )
    index.storage_context.persist(index_path)

# Set up query engine
llm = LlamaAnthropicLLM(model="claude-3-haiku-20240307")
query_engine = index.as_query_engine(llm=llm)

def get_rag_response(query):
    response = query_engine.query(query)
    return response.response

# ... (rest of the code remains the same)