from sqlalchemy import create_engine, or_
from sqlalchemy.orm import sessionmaker
from models import Users  # Assuming your SQLAlchemy model is defined in models.py
from sqlalchemy.exc import SQLAlchemyError

# Database connection setup
engine = create_engine('************************************************/agent')
Session = sessionmaker(bind=engine)
session = Session()

# Get user details by phone number and optional pin
def get_user_by_phone_and_pin(phone_number, pin_code=None):
    try:
        query = session.query(
            Users.user_id,
            Users.nickname,
            Users.first_name,
            Users.pin_code
        ).filter(Users.phone_number == phone_number)

        # If pin_code is provided, filter by pin_code
        if pin_code:
            query = query.filter(Users.pin_code == pin_code)

        # Execute the query and fetch results
        results = query.all()

        # Check for duplicates (more than one row with the same phone number)
        duplicates = len(results) > 1

        # If results are found, process them
        if results:
            user_data = []
            for row in results:
                user_data.append({
                    'user_id': row.user_id,
                    'name': row.nickname if row.nickname else row.first_name,
                    'pin_code': row.pin_code
                })
            return {
                'user_data': user_data,
                'duplicates': duplicates
            }
        else:
            return {
                'user_data': None,
                'duplicates': False
            }
    
    except SQLAlchemyError as e:
        print(f"Error retrieving user data: {e}")
        return None
    finally:
        session.close()

# Handle user lookup and potential duplicates
def handle_user_lookup(phone_number, pin_code=None):
    result = get_user_by_phone_and_pin(phone_number, pin_code)

    if result:
        if result['duplicates']:
            print("Duplicates found.")
            if not pin_code:
                # Ask for the PIN code if not already provided
                pin_code = input("Please enter the 4-digit PIN code: ")
                return get_user_by_phone_and_pin(phone_number, pin_code)
        else:
            return result
    else:
        print("An error occurred.")
        return None

# Example usage:
phone_number = "+447931868798"
pin_code = None  # Optional at first, could be None

# Call the function and handle duplicates
final_result = handle_user_lookup(phone_number, pin_code)

# Output result
if final_result:
    print("Duplicates:", final_result['duplicates'])
    if final_result['user_data']:
        for user in final_result['user_data']:
            print(f"User ID: {user['user_id']}, Name: {user['name']}, PIN: {user['pin_code']}")
    else:
        print("No users found.")
else:
    print("No results returned.")
