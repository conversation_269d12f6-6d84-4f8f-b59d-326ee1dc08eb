import json
import os
import telnyx
from flask import Flask, request
from openai import OpenAI
from dotenv import load_dotenv
import asyncio

app = Flask(__name__)

load_dotenv()

# Set your OpenAI API key
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

client = OpenAI()
assistant_id = 'asst_7xt8bTWIh6GZHDCi74l492iv'

# Endpoint for receiving webhook events
@app.route('/webhook', methods=['POST'])
def inbound():
    body = json.loads(request.data)
    event = body.get("data").get("event_type")

    try:
        if event == "call.initiated":
            handle_call_initiated(body)
        elif event == "call.answered":
            handle_call_answered(body)
        elif event == "call.transcription":
            handle_call_transcription(body)
        elif event == 'call.dtmf.received':
            handle_call_dtmf_received(body)
    except Exception as e:
        print(f"Error: {e}")
        return json.dumps({"success":False}), 500, {"ContentType":"application/json"}
    return json.dumps({"success":True}), 200, {"ContentType":"application/json"}

def handle_call_initiated(body):
    call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
    call.call_control_id = body.get("data").get("payload").get("call_control_id")
    call.answer()

def handle_call_answered(body):
    call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
    call.call_control_id = body.get("data").get("payload").get("call_control_id")
    call.transcription_start(language="en", interim_results=True)
    thread = client.beta.threads.create()
    thread_id = thread.id
    print(thread_id)
    return thread.id
transcript_buffer = ""
    

def handle_call_transcription(body):
    global transcript_buffer
    transcription_data = body.get("data").get("payload").get("transcription_data")
    transcript = transcription_data.get("transcript")
    print(f"Transcribed Text: {transcript}")
    transcript_buffer += " " + transcript

def handle_call_dtmf_received(body):
    global transcript_buffer
    print(body)
    dtmf = body.get("data").get("payload").get("digit")

    if dtmf == "#":  # Assuming '#' is the signal for end of speech
        print('---------# PRESSED--------------------')
        response = asyncio.run(send_to_llm(transcript_buffer.strip()))
        play_tts(response)
        transcript_buffer = ""
    elif dtmf == "*":  # Assuming '*' is the signal to hang up
        call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
        call.call_control_id = body.get("data").get("payload").get("call_control_id")
        call.hangup()



async def handle_call_dtmf_received(body):
    global transcript_buffer
    print(body)
    dtmf = body.get("data").get("payload").get("digit")

    if dtmf == "#":  # Assuming '#' is the signal for end of speech
        print('---------# PRESSED--------------------')
        response = await send_to_llm(transcript_buffer.strip())
        play_tts(response)
        transcript_buffer = ""
    elif dtmf == "*":  # Assuming '*' is the signal to hang up
        call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
        call.call_control_id = body.get("data").get("payload").get("call_control_id")
        call.hangup()

async def submit_message(assistant_id, thread_id, user_message):
    response_message = ""
    thread = None
    run = None  # Variable to store the run ID

    try:
        if thread_id is None or thread_id == "":
            thread = await client.beta.threads.create()
            thread_id = thread.id

        # Creates the message
        await client.beta.threads.messages.create(thread_id=thread_id,
                                                  role="user",
                                                  content=user_message)

        run = await client.beta.threads.runs.create_and_poll(
            thread_id=thread_id, assistant_id=assistant_id, poll_interval_ms=2000)

        # RUN STATUS: REQUIRES ACTION
        if run.status == 'requires_action':
            # do stuff
            run = await client.beta.threads.runs.submit_tool_outputs_and_poll(
                thread_id=run.thread_id,
                run_id=run.id,
                tool_outputs=[
                    {
                        "tool_call_id": run.tool_call_id,  # Ensure correct tool_call_id is used
                        "output": response_message
                    }
                ],
            )

        # RUN STATUS: COMPLETED
        if run.status == "completed":
            response_message = await get_response(run.thread_id)
            return response_message

        # RUN STATUS: EXPIRED | FAILED | CANCELLED | INCOMPLETE
        if run.status in ['expired', 'failed', 'cancelled', 'incomplete']:
            # do stuff
            pass

    except Exception as e:
        print(f"Exception: {e}")

async def get_response(thread_id):
    messages = await client.beta.threads.messages.list(thread_id=thread_id)
    message_content = messages.data[0].content[0].text

    # Remove annotations
    annotations = message_content.annotations
    for annotation in annotations:
        message_content.value = message_content.value.replace(annotation.text, '')

    response_message = message_content.value
    return response_message

async def send_to_llm(transcript):
    response = await submit_message(assistant_id, None, transcript)
    return response
def convert_text_to_speech(text):
    # Implement conversion of text to speech using a TTS service
    # This could be a call to a TTS API like Google TTS or Amazon Polly
    # For example, you could save the audio file and return its URL
    pass

if __name__ == '__main__':
    app.run(debug=True, port=5000)
