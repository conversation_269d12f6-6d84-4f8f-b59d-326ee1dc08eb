import telnyx
from flask import Flask, request, Response

app = Flask(__name__)

@app.route('/webhooks', methods=['POST'])
def respond():
    # Check record_type of object
    data = request.json['data']
    if data.get('record_type') == 'event':
        # Check event type
        event = data.get('event_type')
        print(event, flush=True)
        if event == "call_initiated":
            print("Incoming call", flush=True)
            # You can add code here to answer the call if needed
    return Response(status=200)

if __name__ == '__main__':
    app.run(port=5000, debug=True)
