import os
import requests
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Telnyx API configuration
TELNYX_API_KEY = os.getenv("TELNYX_API_KEY")
TELNYX_CONNECTION_ID = os.getenv("TELNYX_CONNECTION_ID")

# Headers for Telnyx API
headers = {
    "Authorization": f"Bearer {TELNYX_API_KEY}"
}

# Step 1: List all active calls
def list_active_calls():
    url = f"https://api.telnyx.com/v2/connections/{TELNYX_CONNECTION_ID}/active_calls"
    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        calls = response.json().get("data", [])
        print(f"Active calls: {calls}")
        return calls
    else:
        print(f"Failed to list active calls: {response.text}")
        return []

# Step 2: Terminate each active call
def terminate_calls(calls):
    for call in calls:
        call_control_id = call.get("call_control_id")
        if call_control_id:
            url = f"https://api.telnyx.com/v2/calls/{call_control_id}/actions/hangup"
            response = requests.post(url, headers=headers)
            if response.status_code == 200:
                print(f"Call {call_control_id} terminated successfully.")
            else:
                print(f"Failed to terminate call {call_control_id}: {response.text}")

# Main function
if __name__ == "__main__":
    active_calls = list_active_calls()
    if active_calls:
        terminate_calls(active_calls)
    else:
        print("No active calls to terminate.")