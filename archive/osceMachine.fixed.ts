// @ts-nocheck
import { createMachine, assign, send, forwardTo, ActorRefFrom } from 'xstate';
import axios from 'axios';
import { OsceEvents, TransitionReasons } from './osceEvents';
import { OsceContext, OsceEvent, DtmfInputEvent, CaseData, OsceMachineSchema, Utterance } from './types';
import { getCallSid } from './contextUtils';
import { invokeTransitionAgent } from './transitionAgent'; // Import the new agent function

// Declare global backup store type
declare global {
  var contextBackup: {
    lastCallSid?: string;
    contexts: Record<string, any>;
  };
}

// Initialize global context backup if it doesn't exist
if (typeof global.contextBackup === 'undefined') {
  global.contextBackup = { contexts: {} };
}

// Import constants from shared constants file
import { SILENCE_FRAMES_THRESHOLD, MIN_SPEECH_FRAMES } from './constants';

// -------------------- Helpers -----------------------------
const RAG_URL = process.env.RAG_SERVER_URL || 'http://localhost:8001/rag';

const timer = (sec: number, cb: () => void): NodeJS.Timeout => {
  return setTimeout(cb, sec * 1000);
};

// Track valid transitions between states
const validTransitions = {
  'idle': ['intro'],
  'intro': ['transition'], // Intro now goes to transition state
  'history_taking': ['transition_agent', 'transition'], // history_taking can go to RAG or main transition
  'examination': ['transition_agent', 'transition'], // examination can go to RAG or main transition
  'diagnosis': ['transition_agent', 'transition'], // diagnosis can go to RAG or main transition
  'transition_agent': ['history_taking', 'examination', 'diagnosis', 'closing'], // RAG agent returns to functional states
  'transition': ['history_taking', 'examination', 'diagnosis', 'closing', 'idle'] // Generic transition state decides next step
};

// Check if a transition is valid
function isValidTransition(fromState: string, toState: string): boolean {
  return validTransitions[fromState]?.includes(toState) || false;
}

// Declare contextBackup on global scope for context recovery
declare global {
  var contextBackup: {
    lastCallSid?: string;
    lastSelectedCase?: any;
  };
}

// Initialize global backup if not exists
if (typeof global.contextBackup === 'undefined') {
  global.contextBackup = {};
}

// -------------------- Service Implementations -----------------------------

// Service to fetch patient info for a case
const fetchPatient = async (context) => {
  try {
    const safeCallSid = context.callSid || 'MISSING_CALLSID';
    
    if (!context.callSid) {
      console.log(`[XState:${safeCallSid}] Error in fetchPatient service:`);
      console.log(`[XState:${safeCallSid}] - Error: no error data`);
      console.log(`[XState:${safeCallSid}] - Context check: callSid=${safeCallSid}, caseData=${context.caseData ? 'present' : 'missing'}, conversation=${Array.isArray(context.conversation) ? context.conversation.length : 'not-array'}`);
      return { patient: null, error: 'Missing callSid' };
    }
    
    if (!context.caseData) {
      console.log(`[XState:${safeCallSid}] Error in fetchPatient service:`);
      console.log(`[XState:${safeCallSid}] - Error: no error data`);
      console.log(`[XState:${safeCallSid}] - Context check: callSid=${safeCallSid}, caseData=missing, conversation=${Array.isArray(context.conversation) ? context.conversation.length : 'not-array'}`);
      return { patient: null, error: 'Missing caseData' };
    }
    
    // This would normally make an API call - simplified for this implementation
    const patientData = {
      name: "Alex Johnson",
      age: 45,
      background: "Has a history of hypertension."
    };
    
    return { patient: patientData };
  } catch (error) {
    console.error(`[Service:fetchPatient] Error:`, error);
    return { patient: null, error: error.toString() };
  }
};

// Create the state machine
export function createOsceMachine() {
  return createMachine<OsceContext, OsceEvents, OsceMachineSchema>({
    id: 'osce',
    initial: 'idle',
    predictableActionArguments: true,
    context: {
      callSid: undefined,
      caseData: undefined,
      conversation: [],
      currentState: 'idle',
      currentQuestionIdx: 0,
      consecutiveSilenceFrames: 0,
      speechFramesInCurrentTurn: 0,
      hasSpeechDetected: false,
      lastUtterance: '',
      rejectionReason: null,
      responseText: null,
      turnTimings: [],
      turnStartTime: 0,
      currentTurnIndex: 0,
      llmLastTurnDuration: 0,
      ttsLastTurnDuration: 0,
      turnNumber: 0,
      lastDtmfDigit: undefined
    },
    
    states: {
      idle: {
        entry: assign({ currentState: 'idle' }),
        on: {
          DTMF_INPUT: {
            actions: ['logDtmfEvent', 'logGuardSuccess'],
            target: 'intro'
          },
          DTMF_INVALID: {
            actions: ['logGuardFailure']
          },
          START: {
            target: 'intro',
            actions: assign({
              callSid: (context, event) => {
                // Safely get callSid from event, fallback to existing context or default
                const callSid = event?.callSid || context?.callSid || 
                             (typeof global.contextBackup !== 'undefined' && global.contextBackup.lastCallSid) || 
                             'MISSING_CALLSID';
                console.log(`[XState:START] Setting callSid to: ${callSid}`);
                return callSid;
              },
              currentState: 'intro'
            })
          }
        }
      },
      
      intro: {
        entry: [
          // Log entry to state
          (context) => console.log(`[XState Intro] Entered intro state with callSid=${context.callSid}`),
          
          // Set welcome message
          assign({
            responseText: 'Welcome to the virtual patient system. Are you ready to begin a case?',
            currentState: 'intro'
          })
        ],
        on: {
          TTS_FINISHED: {
            target: 'transition',
            actions: [
              // Note state we're transitioning to
              (ctx) => console.log('[XState Intro] TTS Finished for intro, moving to transition state (default)')
            ]
          },
          DTMF_INPUT: {
            target: 'transition',
            actions: [
              // Store DTMF digit in context
              assign({ 
                lastDtmfDigit: (_, event) => event.digit,
                // Add transition reason with the DTMF digit
                transitionReason: (_, event) => TransitionReasons.DTMF_TRANSITION
              }),
              // Note the DTMF input
              (ctx, event) => console.log(`[XState Intro] DTMF Input: ${event.digit}, moving to transition state (dtmf)`)
            ]
          },
          USER_UTTERANCE: {
            // No transition on utterance, wait for TTS_FINISHED
            actions: [
              assign({ 
                lastUtterance: (_, event) => event.text
              }),
              (context, event) => console.log(`[XState Intro] User said: "${event.text}"`)
            ]
          }
        }
      },
      
      transition: {
        entry: [
          // Log entry to transition state
          () => console.log('[XState Transition] Entered transition state.'),
          
          // Update context with state name for guards
          assign({ 
            currentState: 'transition',
            responseText: '' // Clear response text for this intermediate state
          })
        ],
        
        on: {
          // Handle DTMF input in transition state - direct state transition based on digit
          DTMF_INPUT: {
            // Instead of using a dynamic target function which can break in XState v5,
            // we'll use a fixed set of targets and choose based on context
            target: 'history_taking', // Default target
            // Add guards to redirect to other states if needed
            actions: [(context, event) => {
              // Safe access to determine the target state (just logging)
              const safeEvent = event || {};
              const safeData = safeEvent.data || {};
              const targetState = safeData.targetState || 'history_taking';
              console.log(`[XState Transition] Target state resolved to: ${targetState} (fixed target approach)`); 
            }]
          },
          CALL_ENDED: { target: 'ended' }
        }
      },
      
      history_taking: {
        entry: [
          assign({
            currentState: 'history_taking',
            // Add a welcome message that will be spoken when entering this state
            responseText: 'Now starting the history taking part of the examination. You can ask questions about symptoms, medical history, or press 1 for physical examination, 2 for diagnosis, or 3 to end the session.'
          }),
          () => console.log('[XState HistoryTaking] Entered history_taking state.')
        ],
        initial: 'waitingInput',
        
        states: {
          waitingInput: {
            entry: assign({ consecutiveSilenceFrames: 0, speechFramesInCurrentTurn: 0 }),
            on: {
              TRANSCRIPTION: {
                target: 'processing',
                actions: [assign({ 
                  lastUtterance: (_, event) => {
                    // Defensive check to prevent accessing undefined properties
                    if (!event || typeof event.text === 'undefined') {
                      console.error('[XState HistoryTaking] ERROR: event.text is undefined in TRANSCRIPTION handler');
                      return ''; // Return empty string as fallback
                    }
                    return event.text;
                  }
                })]
              },
              SILENCE_DETECTED: {
                target: 'processing',
                cond: (ctx) => ctx.hasSpeechDetected && ctx.consecutiveSilenceFrames >= SILENCE_FRAMES_THRESHOLD
              },
              SPEECH_DETECTED: {
                actions: ['incrementSpeechFrames', assign({ hasSpeechDetected: true })]
              },
              NO_SPEECH_DETECTED: {
                actions: ['incrementSilenceFrames'],
                cond: (ctx) => ctx.hasSpeechDetected
              }
            }
          },
          processing: {
            // First fetch case data to ensure it's available for response generation
            invoke: {
              src: fetchPatient,
              input: (ctx, e: any) => {
                console.log(`[XState HistoryTaking] Processing invocation with context:`, {
                  callSid: ctx.callSid,
                  hasCaseData: ctx.caseData ? true : false,
                  lastUtterance: ctx.lastUtterance
                });
                return { callSid: ctx.callSid, caseData: ctx.caseData };
              },
              onDone: {
                target: 'respondingToPatient',
                actions: [
                  // Store the patient data from the fetch service
                  assign({
                    patient: (_, event) => {
                      const safeEvent = event || {};
                      const safeData = safeEvent.data || {};
                      console.log(`[XState HistoryTaking] Patient data fetched:`, safeData.patient ? 'success' : 'missing');
                      return safeData.patient || null;
                    }
                  }),
                  // Log the successful data fetch
                  (ctx) => console.log(`[XState HistoryTaking] Patient data fetched for ${ctx.callSid}, moving to response generation`)
                ]
              },
              onError: {
                target: 'respondingToPatient',
                actions: [
                  (ctx, ev) => console.error(`[XState HistoryTaking] Error in fetchPatient service for call ${ctx.callSid}:`, ev.data),
                  // Still move forward but without patient data
                  assign({
                    patient: null
                  })
                ]
              }
            }
          },
          // New state to handle generating responses to patient questions
          respondingToPatient: {
            entry: [
              // Log entry to this state
              (ctx) => console.log(`[XState HistoryTaking] Entered respondingToPatient state for ${ctx.callSid}`),
              // Update response text with meaningful content instead of error
              assign({
                responseText: (ctx) => {
                  // Check if there's a last utterance to respond to
                  if (!ctx.lastUtterance) {
                    console.log(`[XState HistoryTaking] No utterance found, using default response`);
                    return "I'm the patient simulator. Please ask me about my symptoms or medical history.";
                  }
                  
                  // Simple simulation of responses based on case data
                  const caseType = ctx.caseData?.scenario || 'Unknown';
                  console.log(`[XState HistoryTaking] Generating response for case type: ${caseType}`);
                  
                  if (caseType === 'Chest Pain') {
                    return "I've been having sharp chest pain for the last 2 days, especially when I take a deep breath or lie down.";
                  } else {
                    return "I'm here because I haven't been feeling well lately. What would you like to know about my symptoms?";
                  }
                },
                // Also update conversation
                conversation: (ctx) => {
                  const existingConversation = Array.isArray(ctx.conversation) ? ctx.conversation : [];
                  const responseText = ctx.responseText || "I'm the patient simulator. Please ask me about my symptoms.";
                  
                  return [
                    ...existingConversation,
                    {
                      speaker: 'assistant',
                      text: responseText
                    }
                  ];
                }
              })
            ],
            // After response is given, transition back to waiting for input
            always: { target: 'waitingInput' }
          }
        }
      },
      
      transition_agent: {
        invoke: {
          src: async (context) => {
            // Simulated transition service
            return { accept: true, toState: 'history_taking' };
          },
          onDone: {
            actions: (ctx, ev) => console.log(`Transition accepted: ${ctx.currentState} -> history_taking`)
          },
          onError: {
            actions: (ctx, ev) => console.error(`Error in transition agent:`, ev.data)
          }
        }
      },
      
      examination: {
        entry: assign({ currentState: 'examination' }),
        on: {
          TTS_FINISHED: { target: 'transition' }
        }
      },
      
      diagnosis: {
        entry: assign({ currentState: 'diagnosis' }),
        on: {
          TTS_FINISHED: { target: 'transition' }
        }
      },
      
      closing: {
        entry: [
          (ctx) => console.log('[XState Closing] Entered closing state.'),
          assign({ 
            currentState: 'closing',
            responseText: 'Thank you for your time today. The session is now complete. Goodbye!'
          })
        ],
        on: {
          TTS_FINISHED: {
            target: 'idle',
            actions: [() => console.log('[XState Closing] TTS finished, returning to idle.')]
          },
          // Add a timeout fallback
          TIMEOUT: {
            target: 'idle',
            actions: [() => console.log('[XState Closing] Timeout reached, returning to idle.')]
          }
        },
        after: {
          5000: {
            target: 'idle',
            actions: () => console.log('[XState Closing] After 5s, moving to idle.')
          }
        }
      },
      
      ended: {
        type: 'final',
        entry: assign({ currentState: 'ended' })
      }
    },
    
    // Global actions
    actions: {
      incrementSpeechFrames: assign({
        speechFramesInCurrentTurn: (ctx) => ctx.speechFramesInCurrentTurn + 1,
        consecutiveSilenceFrames: 0
      }),
      
      incrementSilenceFrames: assign({
        consecutiveSilenceFrames: (ctx) => ctx.consecutiveSilenceFrames + 1
      }),
      
      recordRejectionReason: assign({
        rejectionReason: (_, event) => event.reason || 'unknown'
      }),
      
      recordTurnStart: assign({
        turnStartTime: () => Date.now(),
        speechFramesInCurrentTurn: 0,
        consecutiveSilenceFrames: 0,
        hasSpeechDetected: true
      }),
      
      recordLlmInvokeStart: assign({
        llmInvokeStartTime: () => Date.now()
      }),
      
      recordLlmResponseComplete: assign({
        llmLastTurnDuration: (ctx) => (ctx.llmInvokeStartTime ? Date.now() - ctx.llmInvokeStartTime : 0)
      }),
      
      recordTtsStart: assign({
        ttsStartTime: () => Date.now()
      }),
      
      recordTtsComplete: assign({
        ttsLastTurnDuration: (ctx) => (ctx.ttsStartTime ? Date.now() - ctx.ttsStartTime : 0)
      }),
      
      selectCaseForMachine: (context, _event, meta) => {
        console.log(`[XState Action][${context.callSid}] --- selectCaseForMachine ---`);
        
        // Simulated case selection logic
        const caseData = {
          case_id: 'case2',
          scenario: 'Chest Pain',
          initial_question: 'Got it. Can you describe the chest pain for me?'
        };
        
        // Send the INTERNAL_SET_CASE event with the case data
        console.log(`[XState:${context.callSid}] Sending INTERNAL_SET_CASE event with case: ${caseData.scenario}`);
        meta.self.send({ type: 'INTERNAL_SET_CASE', caseData });
        console.log(`[XState Action][${context.callSid}] --- Finished selectCaseForMachine ---`);
      },
      
      selectCaseAutomatically: (context, _event, { self }) => {
        console.log(`[XState Action][${context.callSid}] --- selectCaseAutomatically ---`);
        
        // Simulated case selection
        const caseData = {
          case_id: 'case2',
          scenario: 'Chest Pain',
          initial_question: 'Got it. Can you describe the chest pain for me?',
          questions: [],
          data: {}
        };
        
        // Send the INTERNAL_SET_CASE event with the case data
        console.log(`[XState:${context.callSid}] Sending INTERNAL_SET_CASE event with case: ${caseData.scenario}`);
        self.send({ type: 'INTERNAL_SET_CASE', caseData });
        console.log(`[XState Action][${context.callSid}] --- Finished selectCaseAutomatically ---`);
      },
      
      invokeTransitionAgent: (context) => {
        // In XState v5 actions, context.self refers to the actor executing the action.
        if (context.self) {
          invokeTransitionAgent(context, context.self);
        } else { 
          // This case should ideally not happen if the action is run by the machine itself.
          console.error(`[XState Action][${context.callSid}] Error: context.self not found for invokeTransitionAgent.`);
        }
      },
      
      logDtmfEvent: (context: OsceContext, event: DtmfInputEvent) => {
        console.log('[XState Transition] Inside SIMPLE DTMF log action. Event:', JSON.stringify(event, null, 2));
        console.log(`[XState Transition] Current state from context: ${context.currentState}`);
        if (event && typeof event.digit !== 'undefined') {
          console.log(`[XState Transition] SIMPLE ACTION: DTMF '${event.digit}' received.`);
          
          // Manually update context without using assign since that seems to be failing
          try {
            (context as any).lastDtmfDigit = event.digit;
            console.log(`[XState Transition] SUCCESS: Updated context.lastDtmfDigit to '${event.digit}'.`); 
          } catch (err) {
            console.error('[XState Transition] ERROR: Failed to update context:', err);
          }
        } else {
          console.error('[XState Transition] ERROR: Event or event.digit is missing/invalid in SIMPLE DTMF log action!');
        }
      },
      
      logGuardSuccess: (context, event) => {
        console.log(`[XState Guard] SUCCESS: Guard passed for DTMF '${event?.digit}' in state '${context.currentState}'`);
      },
      
      logGuardFailure: (context, event) => {
        console.log(`[XState Guard] FAILURE: Guard failed for DTMF '${event?.digit}' in state '${context.currentState}'`);  
      }
    },
    
    // Guards
    guards: {
      isInTransitionState: (context: OsceContext) => {
        const result = context.currentState === 'transition';
        console.log(`[XState Guard] isInTransitionState evaluated to: ${result} (currentState=${context.currentState})`);
        return result;
      }
    }
  });
}

// Function to extract callSid safely
const getCallSid = (event: OsceEvents): string | undefined => {
  if (event && 'callSid' in event) {
    return event.callSid;
  }
  return undefined;
};

// Create and export the state machine
export const osceMachine = createOsceMachine();
