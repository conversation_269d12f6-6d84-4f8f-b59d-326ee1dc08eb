# import json
# import os
# import telnyx
# from flask import Flask, request
# from openai import OpenAI
# from dotenv import load_dotenv
# import time
# import re
# import socket
# import struct
# import wave
# from pydub import AudioSegment
# from io import BytesIO
# import threading


# # Define the IP and port you want to listen on
# UDP_IP = "*************"
# UDP_PORT = 5004

# # Set up the UDP socket to listen for RTP packets
# sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
# sock.bind((UDP_IP, UDP_PORT))

# # Create a WAV file to save the output
# wav_file = wave.open('output.wav', 'wb')
# wav_file.setnchannels(1)  # Mono audio
# wav_file.setsampwidth(2)  # 16-bit PCM (2 bytes per sample)
# wav_file.setframerate(8000)  # G.711 is typically 8kHz




# '''
# Implement raw audio from call, VAD
# '''
# file_lock = threading.Lock()



# def process_strep_packet(packet):
#     """Extracts the RTP payload from a STREP encapsulated packet."""
    
#     # STREP header is 24 bytes, so first we extract that
#     strep_header = packet[:24]
    
#     # Now, parse the STREP header (the first 2 bytes contain version info, etc.)
#     header_info = struct.unpack("!H", strep_header[:2])[0]
    
#     # Extract key information from the STREP header
#     version = (header_info >> 10) & 0xF
#     leg = (header_info >> 5) & 0x1
#     direction = (header_info >> 4) & 0x1
#     header_len = header_info & 0xFF
    
#     # The 16-byte call_leg_id is in bytes 8 to 24 (after reserved fields)
#     call_leg_id = strep_header[8:24]
    
#     # Strip off the STREP header to get the actual RTP packet
#     rtp_packet = packet[header_len:]
    
#     return rtp_packet, version, leg, direction, call_leg_id

# def decode_g711_to_pcmu(rtp_payload):
#     """Decode G.711 PCMU data to PCM using pydub."""
#     # G.711 uses an 8-bit codec, but PCM for .wav requires 16-bit
#     audio_segment = AudioSegment(
#         data=rtp_payload, 
#         sample_width=1,  # G.711 is 1 byte per sample
#         frame_rate=8000,  # G.711 typically operates at 8000Hz
#         channels=1  # Mono
#     )
    
#     # Convert to PCM (16-bit samples)
#     pcm_audio = BytesIO()
#     audio_segment.export(pcm_audio, format="wav")
    
#     return pcm_audio.getvalue()

# def receive_rtp():
#     """Main loop to receive RTP packets and save to WAV."""
#     print(f"Listening on {UDP_IP}:{UDP_PORT}")
    
#     while True:
#         # Receive packet from the socket (assuming the packet size is 1500 bytes max)
#         data, addr = sock.recvfrom(1500)
        
#         # Process the received packet, extracting RTP payload and STREP info
#         rtp_packet, version, leg, direction, call_leg_id = process_strep_packet(data)
        
#         # Decode the G.711 RTP payload to PCM format
#         pcm_data = decode_g711_to_pcmu(rtp_packet)
        
#         # Write PCM data to the WAV file
#         wav_file.writeframes(pcm_data)
        
#         # Print some debug information
#         print(f"Received RTP packet from {addr}")
#         print(f"STREP Version: {version}, Leg: {leg}, Direction: {direction}")
#         print(f"Call Leg ID: {call_leg_id.hex()}")
#         print(f"RTP Packet Length: {len(rtp_packet)} bytes\n")

# if __name__ == "__main__":
#     try:
#         receive_rtp()
#     except KeyboardInterrupt:
#         print("Stopping and closing WAV file.")
#         wav_file.close()





# app = Flask(__name__)

# load_dotenv()

# os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

# client = OpenAI()
# assistant_id = 'asst_dLuE0A9zTpOYkz9m85NWz14x'

# # Dictionary to store call handlers
# call_handlers = {}

# # Set to track processed event IDs
# processed_event_ids = set()

# class CallHandler:
#     def __init__(self, call_control_id):
#         self.call_control_id = call_control_id
#         self.thread = None
#         self.transcript_buffer = ""

#     def handle_answered(self):
#         call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
#         call.call_control_id = self.call_control_id
#         call.transcription_start(language="en", interim_results=True)
#         call.fork_start(target="udp:*************:5004")
#         self.thread = client.beta.threads.create()
#         print(f"Thread created for call {self.call_control_id}: {self.thread.id}")

#     def handle_transcription(self, transcript):
#         print(f"Transcribed Text for call {self.call_control_id}: {transcript}")
#         self.transcript_buffer += " " + transcript

#     def handle_dtmf(self, digit):
#         if digit == "#":
#             print(f'---------# PRESSED for call {self.call_control_id}--------------------')
#             response = self.send_to_llm(self.transcript_buffer.strip()+ " hello there, how can you help?")
#             self.play_tts(response)
#             self.transcript_buffer = ""
#         elif digit == "*":
#             self.hangup()

#     def send_to_llm(self, transcript):
#         print(f"Sending to LLM for call {self.call_control_id}, Thread ID: {self.thread.id}")

#         message = client.beta.threads.messages.create(
#             thread_id=self.thread.id,
#             role="user",
#             content=transcript,
#             run = client.beta.threads.runs.create(
#             thread_id=self.thread.id,
#             assistant_id=assistant_id,
#             tool_choice = 'required',
#         )      
#         run = self.wait_on_run(run)
#         openai_output_raw = self.pretty_str(self.get_response())
#         last_assistant_text = openai_output_raw.split('assistant:')[-1].strip()
#         last_assistant_text = re.sub(r'【[^】]*】', '', last_assistant_text)
#         print(f"LLM response for call {self.call_control_id}: {last_assistant_text}")
#         return last_assistant_text

#     def wait_on_run(self, run):
#         while run.status == "queued" or run.status == "in_progress":
#             run = client.beta.threads.runs.retrieve(
#                 thread_id=self.thread.id,
#                 run_id=run.id,
#             )
#             time.sleep(0.1)
#         return run

#     def pretty_str(self, messages):
#         formatted_str = ""
#         for m in messages:
#             formatted_str += f"{m.role}: {m.content[0].text.value}\n"
#         return formatted_str

#     def get_response(self):
#         return client.beta.threads.messages.list(thread_id=self.thread.id, order="asc")

#     def play_tts(self, response_text):
#         call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
#         call.call_control_id = self.call_control_id
#         call.speak(
#             payload=response_text,
#             language="en-US",
#             voice="Polly.Amy")

#     def hangup(self):
#         call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
#         call.call_control_id = self.call_control_id
#         call.hangup()
#         # Remove this call handler from the dictionary
#         del call_handlers[self.call_control_id]

# @app.route('/webhook', methods=['POST'])
# def inbound():
#     body = json.loads(request.data)
#     event = body.get("data").get("event_type")
#     call_control_id = body.get("data").get("payload").get("call_control_id")

#     try:
#         if event == "call.initiated":
#             handle_call_initiated(body)
#         elif event == "call.answered":
#             handle_call_answered(body)
#         elif event == "call.transcription":
#             handle_call_transcription(body)
#         elif event == 'call.dtmf.received':
#             handle_call_dtmf_received(body)
#         elif event == 'call.hangup':
#             handle_call_hangup(body)
#     except Exception as e:
#         print(f"Error: {e}")
#         return json.dumps({"success": False}), 500, {"ContentType": "application/json"}
#     return json.dumps({"success": True}), 200, {"ContentType": "application/json"}

# def handle_call_initiated(body):
#     call_control_id = body.get("data").get("payload").get("call_control_id")
#     call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
#     call.call_control_id = call_control_id
#     call.answer()
#     call_handlers[call_control_id] = CallHandler(call_control_id)

# def handle_call_answered(body):
#     call_control_id = body.get("data").get("payload").get("call_control_id")
#     if call_control_id in call_handlers:
#         call_handlers[call_control_id].handle_answered()

# def handle_call_transcription(body):
#     call_control_id = body.get("data").get("payload").get("call_control_id")
#     transcript = body.get("data").get("payload").get("transcription_data").get("transcript")
#     if call_control_id in call_handlers:
#         call_handlers[call_control_id].handle_transcription(transcript)

# def handle_call_dtmf_received(body):
#     event_id = body.get("data").get("id")
#     if event_id in processed_event_ids:
#         print(f"Event {event_id} already processed.")
#         return
#     processed_event_ids.add(event_id)

#     call_control_id = body.get("data").get("payload").get("call_control_id")
#     dtmf = body.get("data").get("payload").get("digit")
#     if call_control_id in call_handlers:
#         call_handlers[call_control_id].handle_dtmf(dtmf)

# def handle_call_hangup(body):
#     call_control_id = body.get("data").get("payload").get("call_control_id")
#     if call_control_id in call_handlers:
#         del call_handlers[call_control_id]

# if __name__ == '__main__':
#     app.run(debug=True, port=5000)

#-------------------------------------
import json
import os
import telnyx
from flask import Flask, request
from openai import OpenAI
from dotenv import load_dotenv
import time
import re
import socket
import struct
import wave
from pydub import AudioSegment
from io import BytesIO
import threading

# Flask app setup
app = Flask(__name__)

# Load environment variables
load_dotenv()
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

client = OpenAI()
assistant_id = 'asst_dLuE0A9zTpOYkz9m85NWz14x'

# Dictionary to store call handlers
call_handlers = {}

# Set to track processed event IDs
processed_event_ids = set()

# Create a lock for file writing to avoid conflicts
file_lock = threading.Lock()

# Define the IP and port you want to listen on
UDP_IP = "*************"
UDP_PORT = 5004 #4

def process_strep_packet(packet):
    """Extracts the RTP payload from a STREP encapsulated packet."""
    strep_header = packet[:24]
    header_info = struct.unpack("!H", strep_header[:2])[0]
    version = (header_info >> 10) & 0xF
    leg = (header_info >> 5) & 0x1
    direction = (header_info >> 4) & 0x1
    header_len = header_info & 0xFF
    call_leg_id = strep_header[8:24]
    rtp_packet = packet[header_len:]
    return rtp_packet, version, leg, direction, call_leg_id

def decode_g711_to_pcmu(rtp_payload):
    """Decode G.711 PCMU data to PCM using pydub."""
    audio_segment = AudioSegment(
        data=rtp_payload,
        sample_width=1,
        frame_rate=8000,
        channels=1
    )
    pcm_audio = BytesIO()
    audio_segment.export(pcm_audio, format="wav")
    return pcm_audio.getvalue()

def receive_rtp(call_leg_id):
    """Main loop to receive RTP packets and save to a WAV file for each call."""
    print(f"Listening for RTP on {UDP_IP}:{UDP_PORT} for call leg: {call_leg_id.hex()}")
    
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.bind((UDP_IP, UDP_PORT))
    
    with wave.open(f'output_{call_leg_id.hex()}.wav', 'wb') as wav_file:
        wav_file.setnchannels(1)
        wav_file.setsampwidth(2)
        wav_file.setframerate(8000)
        
        while True:
            data, addr = sock.recvfrom(1500)
            rtp_packet, version, leg, direction, call_leg_id = process_strep_packet(data)
            pcm_data = decode_g711_to_pcmu(rtp_packet)
            
            with file_lock:
                wav_file.writeframes(pcm_data)
            
            print(f"Received RTP from {addr} - Version: {version}, Leg: {leg}, Direction: {direction}")

def start_rtp_listener(call_leg_id):
    """Start a thread to listen for RTP packets."""
    listener_thread = threading.Thread(target=receive_rtp, args=(call_leg_id,), daemon=True)
    listener_thread.start()

class CallHandler:
    def __init__(self, call_control_id):
        self.call_control_id = call_control_id
        self.call_leg_id = None
        self.call_session_id = None
        self.thread = None
        self.transcript_buffer = ""

    def handle_answered(self):
        call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
        call.call_control_id = self.call_control_id
        call.transcription_start(language="en", interim_results=True)
        fork_response = call.fork_start(target=f"udp:{UDP_IP}:{UDP_PORT}")
        self.call_leg_id = body.get("data").get("event_type")#fork_response.get('data', {}).get('payload', {}).get('call_leg_id')
        if self.call_leg_id:
            start_rtp_listener(self.call_leg_id)
        else:
            print(f"Warning: call_leg_id not received for call_control_id: {self.call_control_id}")
        self.thread = client.beta.threads.create()
        print(f"Thread created for call {self.call_control_id}: {self.thread.id}")


    def handle_transcription(self, transcript):
        print(f"Transcribed Text for call {self.call_control_id}: {transcript}")
        self.transcript_buffer += " " + transcript

    def handle_dtmf(self, digit):
        if digit == "#":
            print(f'---------# PRESSED for call {self.call_control_id}--------------------')
            response = self.send_to_llm(self.transcript_buffer.strip() + " hello there, how can you help?")
            self.play_tts(response)
            self.transcript_buffer = ""
        elif digit == "*":
            self.hangup()

    def send_to_llm(self, transcript):
        print(f"Sending to LLM for call {self.call_control_id}, Thread ID: {self.thread.id}")
        message = client.beta.threads.messages.create(
            thread_id=self.thread.id,
            role="user",
            content=transcript,
        )
        run = client.beta.threads.runs.create(
            thread_id=self.thread.id,
            assistant_id=assistant_id,
            tool_choice='required',
        )
        run = self.wait_on_run(run)
        openai_output_raw = self.pretty_str(self.get_response())
        last_assistant_text = openai_output_raw.split('assistant:')[-1].strip()
        last_assistant_text = re.sub(r'【[^】]*】', '', last_assistant_text)
        print(f"LLM response for call {self.call_control_id}: {last_assistant_text}")
        return last_assistant_text

    def wait_on_run(self, run):
        while run.status == "queued" or run.status == "in_progress":
            run = client.beta.threads.runs.retrieve(
                thread_id=self.thread.id,
                run_id=run.id,
            )
            time.sleep(0.1)
        return run

    def pretty_str(self, messages):
        formatted_str = ""
        for m in messages:
            formatted_str += f"{m.role}: {m.content[0].text.value}\n"
        return formatted_str

    def get_response(self):
        return client.beta.threads.messages.list(thread_id=self.thread.id, order="asc")

    def play_tts(self, response_text):
        call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
        call.call_control_id = self.call_control_id
        call.speak(
            payload=response_text,
            language="en-US",
            voice="Polly.Amy"
        )

    def hangup(self):
        call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
        call.call_control_id = self.call_control_id
        call.hangup()
        del call_handlers[self.call_control_id]

@app.route('/webhook', methods=['POST'])
def inbound():
    body = json.loads(request.data)
    event = body.get("data").get("event_type")
    call_control_id = body.get("data").get("payload").get("call_control_id")
    
    try:
        if event == "call.initiated":
            handle_call_initiated(body)
        elif event == "call.answered":
            handle_call_answered(body)
        elif event == "call.transcription":
            handle_call_transcription(body)
        elif event == 'call.dtmf.received':
            handle_call_dtmf_received(body)
        elif event == 'call.hangup':
            handle_call_hangup(body)
    except Exception as e:
        print(f"Error: {e}")
        return json.dumps({"success": False}), 500, {"ContentType": "application/json"}
    return json.dumps({"success": True}), 200, {"ContentType": "application/json"}

def handle_call_initiated(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    call = telnyx.Call(connection_id=os.getenv("TELNYX_CONNECTION_ID"))
    call.call_control_id = call_control_id
    call.answer()
    print('call answered')

def handle_call_answered(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    print('###')
    print(call_control_id)
    print('###')
    # Ensure the CallHandler exists
    if call_control_id not in call_handlers:
        print(f'Creating CallHandler for call_control_id: {call_control_id}')
        call_handlers[call_control_id] = CallHandler(call_control_id)
    call_handlers[call_control_id].handle_answered()
    print('CallHandler.handle_answered() called successfully')

def handle_call_transcription(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    transcript = body.get("data").get("payload").get("transcription_data").get("transcript")
    if call_control_id in call_handlers:
        call_handlers[call_control_id].handle_transcription(transcript)

def handle_call_dtmf_received(body):
    event_id = body.get("data").get("id")
    if event_id in processed_event_ids:
        print(f"Event {event_id} already processed.")
        return
    processed_event_ids.add(event_id)

    call_control_id = body.get("data").get("payload").get("call_control_id")
    dtmf = body.get("data").get("payload").get("digit")
    if call_control_id in call_handlers:
        call_handlers[call_control_id].handle_dtmf(dtmf)

def handle_call_hangup(body):
    call_control_id = body.get("data").get("payload").get("call_control_id")
    if call_control_id in call_handlers:
        del call_handlers[call_control_id]

if __name__ == '__main__':
    app.run(debug=True, port=5000)
