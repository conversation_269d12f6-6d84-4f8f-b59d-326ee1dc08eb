import os
import time
from dotenv import load_dotenv
from langchain_groq import <PERSON>tGroq
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import FAISS
from langchain_community.document_loaders import DirectoryLoader
from langchain.text_splitter import CharacterTextSplitter
from langchain_core.messages import HumanMessage, AIMessage
from langchain_community.chat_message_histories import ChatMessageHistory
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain

load_dotenv()

os.environ["GROQ_API_KEY"] = os.getenv("GROQ_API_KEY")
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

chat = ChatGroq(temperature=0, model_name="llama-3.1-8b-instant")

# Initialize chat history
chat_history = ChatMessageHistory()

def prepare_documents():
    if not os.path.exists("faiss_index"):
        loader = DirectoryLoader('RAG', glob="**/*.txt")
        documents = loader.load()
        text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)
        texts = text_splitter.split_documents(documents)
        embeddings = OpenAIEmbeddings()
        vectorstore = FAISS.from_documents(texts, embeddings)
        vectorstore.save_local("faiss_index")
        print("Vector store created and saved.")
    else:
        print("Vector store already exists.")

prepare_documents()

embeddings = OpenAIEmbeddings()
vectorstore = FAISS.load_local("faiss_index", embeddings, allow_dangerous_deserialization=True)

def expand_query(query):
    prompt = PromptTemplate(
        input_variables=["query"],
        template="""Analyze the following query related to EnergyPlus Utilities' customer service and expand it to improve search results. Focus on the most relevant aspects of the query, prioritizing the following categories only if they are directly related to the query:

1. Energy services (gas, electricity)
2. Rates and plans
3. Customer support
4. Account management
5. Energy efficiency
6. Company information
7. Discounts and loyalty programs

Include synonyms and related terms to ensure comprehensive retrieval. Be specific and thorough in your expansion.

Original query: {query}

Expanded query:"""
    )
    chain = LLMChain(llm=chat, prompt=prompt)
    expanded_query = chain.run(query).strip()
    return expanded_query

def get_rag_response(query):
    expanded_query = expand_query(query)
    #print(f"Expanded query: {expanded_query}")
    docs = vectorstore.similarity_search(expanded_query, k=7)
    context = "\n".join([doc.page_content for doc in docs])
    return context

system_prompt = """You are an AI customer service agent for EnergyPlus Utilities, a gas and electricity provider in the UK. Your role is to provide accurate, helpful, and professional responses based solely on the information in the provided context. Follow these guidelines:

1. Use British English and format responses for text-to-speech delivery.
2. Be polite, empathetic, and patient in all interactions.
3. Provide information only if it's explicitly stated in the context.
4. If asked about information not in the context, say "I apologize, but I don't have that specific information available. I can connect you with a human representative who can help you further."
5. For safety-related issues like gas leaks, prioritize customer safety and provide emergency instructions immediately.
6. Offer to connect the caller with a human representative for complex issues or when you can't fully address their query.
7. Summarize and confirm information provided by the caller.
8. Always provide next steps or options for the caller.
9. When discussing discounts or loyalty programs, be sure to mention all relevant information provided in the context.

Remember, accuracy is crucial. Do not infer or make up any information not directly provided in the context."""

def process_query(query):
    start_time = time.perf_counter()
    
    chat_history.add_user_message(query)
    
    context = get_rag_response(query)
    
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"""Query: {query}

Context: {context}

Please answer the query based on the provided context. Ensure to include all relevant information, especially regarding discounts and loyalty programs if applicable. If the specific information isn't available, offer to connect the caller with a human representative. For any safety-related issues, prioritize providing emergency instructions."""}
    ]

    response = chat.invoke(messages)
    chat_history.add_ai_message(response.content)
    
    end_time = time.perf_counter()
    print(f"Query processing time: {end_time - start_time:.4f} seconds")
    return response.content.strip()

# Example usage
queries = [
    "Hi, I'm interested in signing up for your electricity service. Can you tell me about your rates?",
    "What discounts do you offer?",
    "I think there might be a gas leak at my house. What should I do?",
    "How can I reduce my energy bills?",
    "Tell me about your company's history and values."
]

for query in queries:
    print(f"\nQuery: {query}")
    response = process_query(query)
    print(f"Response: {response}")
    print('-------------------------')