import json
import os
import telnyx
from flask import Flask, request
from dotenv import load_dotenv
import wave
import threading
import audioop
from collections import OrderedDict
import numpy as np
from pyannote.audio import Pipeline
from streamz import Stream
import traceback

# Flask app setup
app = Flask(__name__)

# Load environment variables
load_dotenv()

# Telnyx API key and Connection ID
TELNYX_API_KEY = os.getenv("TELNYX_API_KEY")
TELNYX_CONNECTION_ID = os.getenv("TELNYX_CONNECTION_ID")

telnyx.api_key = TELNYX_API_KEY

# Define the IP and port you want to listen on for RTP
UDP_IP = "0.0.0.0"  # Listen on all available interfaces
UDP_PORT = 5004
UDP_TRANSMIT = "*************"

class CallHandler:
    def __init__(self, call_control_id):
        self.call_control_id = call_control_id
        self.wav_file = None
        self.packet_buffer = OrderedDict()
        self.is_receiving = True
        self.sample_width = 2  # 16-bit audio
        self.channels = 1      # Mono
        self.sample_rate = 8000  # G.711 sample rate
        self.create_vad_pipeline()

    def create_vad_pipeline(self):
        """Create VAD pipeline for real-time voice activity detection."""
        self.vad_pipeline = Pipeline.from_pretrained("pyannote/voice-activity-detection")
        self.stream = Stream()

        # Stream fork: One branch saves the audio, another applies VAD
        self.stream.sink(self.write_wav)
        self.stream.map(self.run_vad).sink(self.display_vad)

    def write_wav(self, audio_chunk):
        """Write audio chunk to WAV file."""
        if self.wav_file is None:
            self.wav_file = wave.open(f"{self.call_control_id}.wav", 'wb')
            self.wav_file.setnchannels(self.channels)
            self.wav_file.setsampwidth(self.sample_width)
            self.wav_file.setframerate(self.sample_rate)

        # Convert float32 [-1, 1] to int16 [-32768, 32767]
        int16_chunk = np.int16(audio_chunk * 32767)
        self.wav_file.writeframes(int16_chunk.tobytes())

    def run_vad(self, audio_chunk):
        """Run VAD on the current audio chunk."""
        return self.vad_pipeline({"waveform": audio_chunk})

    def display_vad(self, vad_result):
        """Display VAD result in the console."""
        if vad_result.get_timeline().coverage() > 0:
            print(f"Voice detected in call {self.call_control_id}")
        else:
            print(f"No voice detected in call {self.call_control_id}")

    def start_rtp_listener(self, call_leg_id):
        """Start the RTP listener thread."""
        print(f"Starting RTP listener for call {self.call_control_id}, call_leg_id: {call_leg_id}")
        # Logic to start receiving RTP packets and process the audio stream
        threading.Thread(target=self.receive_rtp).start()

    def receive_rtp(self):
        """Simulate receiving RTP audio and push chunks to stream."""
        # Example of receiving and processing RTP packets
        # This should be replaced with actual RTP packet processing
        while self.is_receiving:
            audio_chunk = np.random.rand(16000) * 2 - 1  # Replace with actual audio
            self.stream.emit(audio_chunk)

    def hangup(self):
        """Handle call hangup and close resources."""
        self.is_receiving = False
        if self.wav_file:
            self.wav_file.close()
        print(f"Call {self.call_control_id} ended and resources released.")

# Route to handle Telnyx Webhooks
@app.route('/webhook', methods=['POST'])
def webhook():
    try:
        payload = request.get_json()
        event_type = payload.get('data', {}).get('event_type')
        call_control_id = payload.get('data', {}).get('payload', {}).get('call_control_id')

        if event_type == 'call.initiated':
            print(f"Call initiated with call_control_id: {call_control_id}")
            call_handlers[call_control_id] = CallHandler(call_control_id)

        elif event_type == 'call.answered':
            print(f"Call answered with call_control_id: {call_control_id}")
            if call_control_id in call_handlers:
                call_handlers[call_control_id].start_rtp_listener(call_control_id)

        elif event_type == 'call.hangup':
            print(f"Call hangup with call_control_id: {call_control_id}")
            if call_control_id in call_handlers:
                call_handlers[call_control_id].hangup()
                del call_handlers[call_control_id]

        else:
            print(f"Unhandled event type: {event_type}")

    except Exception as e:
        print(f"Error processing webhook: {e}")
        traceback.print_exc()

    return '', 200

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
