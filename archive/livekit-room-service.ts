import axios from 'axios';
import { RoomServiceClient, EgressClient, EncodingOptionsPreset, EgressStatus } from 'livekit-server-sdk';
import type { RoomCompositeOptions } from 'livekit-server-sdk';
import { EncodedFileOutput, EncodedFileType, EncodingOptions, AudioCodec, AzureBlobUpload } from '@livekit/protocol';
import { cleanupCall } from '../../server';

// LiveKit server configuration
const LIVEKIT_SERVER_HOST = process.env.LIVEKIT_SERVER_HOST || 'livekit.andromedaagents.com';
const LIVESERVER_PORT = 3001;
const LIVESERVER_URL = process.env.LIVESERVER_URL || `http://localhost:${LIVESERVER_PORT}`;

// LiveKit room management client
const LIVEKIT_SERVER_URL = process.env.LIVEKIT_SERVER_URL || 'wss://livekit.andromedaagents.com';
const LIVEKIT_API_KEY = process.env.LIVEKIT_API_KEY || 'APIdEkGiWDpf2hf';
const LIVEKIT_API_SECRET = process.env.LIVEKIT_API_SECRET || '3btzP6jufFNcHYRuMd4PoNfTTWMrCBWNaDwnRU7zCZv';

interface RoomCreationRequest {
  userId: string;
  sessionId?: string;
  audioFormat?: {
    sampleRate?: number;
    channels?: number;
    bitDepth?: number;
  };
}

interface RoomCreationResponse {
  success: boolean;
  roomName: string;
  token: string;
  aiToken?: string;  // A/B Test: Add separate AI token
  wsUrl: string;
  error?: string;
  webSocketPort?: number;
  egressId?: string; // Track egress ID for recording
}

interface WebSocketConnectionInfo {
  port: number;
  path: string;
  callSid: string;
}

/**
 * HTTP coordination service for LiveKit room management
 * Communicates with liveserver to handle room creation and WebSocket setup
 */
export class LiveKitRoomService {
  private egressClient: EgressClient;
  private roomServiceClient: RoomServiceClient;

  constructor() {
    // Initialize LiveKit clients
    this.egressClient = new EgressClient(LIVEKIT_SERVER_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET);
    this.roomServiceClient = new RoomServiceClient(LIVEKIT_SERVER_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET);
  }
  
  /**
   * Request room creation from liveserver
   * @param request Room creation parameters
   * @returns Room details and WebSocket connection info
   */
  async createRoom(request: RoomCreationRequest): Promise<RoomCreationResponse> {
    try {
      console.log(`[Room Service] Creating room for user: ${request.userId}`);
      
      const response = await axios.post(`${LIVESERVER_URL}/create-room`, {
        userId: request.userId,
        sessionId: request.sessionId,
        audioFormat: request.audioFormat || {
          sampleRate: 8000,  // Target our existing format
          channels: 1,       // Mono
          bitDepth: 16       // 16-bit
        }
      }, {
        timeout: 10000
      });

      if (response.data.success) {
        console.log(`[Room Service] ✅ Room created: ${response.data.roomName}`);
        console.log(`[Room Service] A/B Test: Using separate AI token: ${!!response.data.aiToken}`);
        return {
          success: true,
          roomName: response.data.roomName,
          token: response.data.clientToken,  // liveserver returns 'clientToken'
          aiToken: response.data.aiToken,    // A/B Test: Use separate AI token
          wsUrl: response.data.serverUrl,    // liveserver returns 'serverUrl'
          webSocketPort: response.data.webSocketPort
        };
      } else {
        console.error(`[Room Service] ❌ Room creation failed:`, response.data.error);
        return {
          success: false,
          roomName: '',
          token: '',
          wsUrl: '',
          error: response.data.error || 'Unknown error'
        };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`[Room Service] ❌ HTTP request failed:`, errorMessage);
      
      return {
        success: false,
        roomName: '',
        token: '',
        wsUrl: '',
        error: `Failed to communicate with liveserver: ${errorMessage}`
      };
    }
  }

  /**
   * Wait for audio tracks to be available in the room
   */
  private async waitForAudioTracks(roomName: string, maxWaitMs: number = 10000): Promise<boolean> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitMs) {
      try {
        const participants = await this.roomServiceClient.listParticipants(roomName);
        let audioTrackCount = 0;
        
        for (const participant of participants) {
          for (const track of participant.tracks) {
            if (track.type === 0) { // Audio track
              audioTrackCount++;
            }
          }
        }
        
        console.log(`[Room Service] Found ${audioTrackCount} audio tracks in room ${roomName}`);
        
        // Count only unmuted audio tracks
        let unmutedAudioTracks = 0;
        for (const participant of participants) {
          for (const track of participant.tracks) {
            if (track.type === 0 && !track.muted) { // Audio track that's not muted
              unmutedAudioTracks++;
            }
          }
        }
        
        console.log(`[Room Service] Audio tracks status: ${audioTrackCount} total, ${unmutedAudioTracks} unmuted`);
        
        // Start recording as soon as we have ANY audio track (user or AI)
        // Room composite will capture all tracks as they become available
        if (audioTrackCount >= 1) {
          console.log(`[Room Service] ✅ Audio track found - starting recording immediately (${audioTrackCount} tracks, ${unmutedAudioTracks} unmuted)`);
          return true;
        }
        
        console.log(`[Room Service] No audio tracks yet, waiting... (found ${audioTrackCount})`);
        
        // If we have participants but no audio tracks yet, wait less time
        if (participants.length > 0) {
          await new Promise(resolve => setTimeout(resolve, 500)); // Shorter wait when participants exist
        } else {
          await new Promise(resolve => setTimeout(resolve, 1000)); // Normal wait when no participants
        }
        
      } catch (error) {
        console.warn(`[Room Service] Error checking for audio tracks:`, error);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.warn(`[Room Service] ⚠️ Timeout waiting for audio tracks in room ${roomName}`);
    return false;
  }

  /**
   * Start room composite recording for the specified room
   * Records the entire room audio to an OGG file named {callSid}.ogg
   * @param roomName The exact room name to record
   * @param callSid Call identifier for file naming
   * @param waitForTracks Whether to wait for audio tracks (default: true, set false for immediate start)
   * @returns Egress ID for tracking the recording
   */
  async startRoomRecording(roomName: string, callSid: string, waitForTracks: boolean = true): Promise<string | null> {
    try {
      console.log(`[Room Service] Starting room composite recording for room: ${roomName}, callSid: ${callSid}`);
      
      // Verify the room exists and has participants before trying to record it
      const rooms = await this.roomServiceClient.listRooms([roomName]);
      if (rooms.length === 0) {
        console.error(`[Room Service] ❌ Cannot start recording - room ${roomName} does not exist`);
        return null;
      }
      
      const room = rooms[0];
      console.log(`[Room Service] ✅ Room ${roomName} exists with ${room.numParticipants} participants`);
      
      if (waitForTracks) {
        // Quick check for audio tracks - start recording immediately if any audio is available
        console.log(`[Room Service] Checking for audio tracks...`);
        const audioTracksReady = await this.waitForAudioTracks(roomName, 3000); // Reduced to 3 seconds max wait
        
        if (!audioTracksReady) {
          console.log(`[Room Service] ⚠️ Starting recording without waiting for all tracks - will capture audio as it becomes available`);
        }
      } else {
        console.log(`[Room Service] Starting recording immediately without waiting for tracks`);
      }
      
      // List participants to see what tracks are available
      try {
        const participants = await this.roomServiceClient.listParticipants(roomName);
        console.log(`[Room Service] Room participants:`, participants.map(p => ({
          identity: p.identity,
          tracks: p.tracks.length,
          trackTypes: p.tracks.map(t => `${t.type}(${t.sid})`)
        })));
        
        // Check track details for debugging
        for (const participant of participants) {
          console.log(`[Room Service] 🎵 Participant: ${participant.identity}, State: ${participant.state}, JoinedAt: ${participant.joinedAt}`);
          for (const track of participant.tracks) {
            if (track.type === 0) { // Audio track
              console.log(`[Room Service] 🎵 Audio track details - Participant: ${participant.identity}, Track: ${track.sid}, Muted: ${track.muted}, Source: ${track.source}, Type: ${track.type}`);
            }
          }
        }
      } catch (participantError) {
        console.warn(`[Room Service] Could not list participants:`, participantError);
      }
      
      console.log(`[Room Service] Proceeding with room composite recording setup for room ${roomName}`);
      
      // Configure Azure Blob Storage upload
      const AZURE_ACCOUNT_NAME = process.env.AZURE_STORAGE_ACCOUNT_NAME;
      const AZURE_ACCOUNT_KEY = process.env.AZURE_STORAGE_ACCOUNT_KEY;
      const AZURE_CONTAINER_NAME = process.env.AZURE_STORAGE_CONTAINER_NAME || 'call-recordings';
      
      if (!AZURE_ACCOUNT_NAME || !AZURE_ACCOUNT_KEY) {
        console.error(`[Room Service] Missing Azure Blob Storage credentials`);
        throw new Error(`Missing Azure credentials: AZURE_STORAGE_ACCOUNT_NAME, AZURE_STORAGE_ACCOUNT_KEY`);
      }
      
      console.log(`[Room Service] Using Azure Blob Storage for direct upload`);
      console.log(`[Room Service] Account Name: ${AZURE_ACCOUNT_NAME}`);
      console.log(`[Room Service] Container: ${AZURE_CONTAINER_NAME}`);
      console.log(`[Room Service] Account Key: ${AZURE_ACCOUNT_KEY.substring(0, 20)}...`);
      
      // Configure Azure Blob upload using LiveKit v2 API
      const azureBlobUpload = new AzureBlobUpload({
        accountName: AZURE_ACCOUNT_NAME,
        accountKey: AZURE_ACCOUNT_KEY,
        containerName: AZURE_CONTAINER_NAME,
      });
      
      const fileOutput = new EncodedFileOutput({
        fileType: EncodedFileType.OGG, // OGG format for audio-only recording
        filepath: `call_recordings/${callSid}.ogg`, // Azure blob name
        output: {
          case: 'azure',
          value: azureBlobUpload
        }
      });
      
      console.log(`[Room Service] ✅ Configured Azure Blob Storage direct upload`);
      console.log(`[Room Service] File will be uploaded to: https://${AZURE_ACCOUNT_NAME}.blob.core.windows.net/${AZURE_CONTAINER_NAME}/call_recordings/${callSid}.ogg`);
      
      const options: RoomCompositeOptions = {
        audioOnly: true, // Only record audio, no video
        // Use default layout and encoding for maximum compatibility
      };
      
      console.log(`[Room Service] Creating room composite egress for room: ${roomName} with Azure Blob direct upload`);
      console.log(`[Room Service] Room composite egress configuration:`, {
        roomName,
        fileType: 'OGG',
        audioOnly: true,
        layout: 'default',
        encodingOptions: 'default',
        storageType: 'Azure Blob Storage',
        azureAccountName: AZURE_ACCOUNT_NAME,
        azureContainer: AZURE_CONTAINER_NAME,
        azureBlobName: `call_recordings/${callSid}.ogg`
      });
      
      // Start the room composite egress using v2 API structure
      const egressInfo = await this.egressClient.startRoomCompositeEgress(roomName, fileOutput, options);
      
      console.log(`[Room Service] ✅ Recording started for room ${roomName}`);
      console.log(`[Room Service] Egress ID: ${egressInfo.egressId}`);
      console.log(`[Room Service] Recording will be uploaded to Azure Blob: https://${AZURE_ACCOUNT_NAME}.blob.core.windows.net/${AZURE_CONTAINER_NAME}/call_recordings/${callSid}.ogg`);
      console.log(`[Room Service] Azure Storage Account: ${AZURE_ACCOUNT_NAME}`);
      console.log(`[Room Service] Status: ${egressInfo.status}`);
      
      return egressInfo.egressId;
      
    } catch (error) {
      console.error(`[Room Service] ❌ Failed to start recording for room ${roomName}:`, error);
      console.error(`[Room Service] Error details:`, error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  /**
   * Stop room recording using egress ID
   * @param egressId The egress ID to stop
   * @returns Success status
   */
  async stopRoomRecording(egressId: string): Promise<boolean> {
    try {
      console.log(`[Room Service] Stopping recording with egress ID: ${egressId}`);
      
      const egressInfo = await this.egressClient.stopEgress(egressId);
      
      console.log(`[Room Service] ✅ Recording stopped for egress: ${egressId}`);
      console.log(`[Room Service] Final status: ${egressInfo.status}`);
      
      return true;
      
    } catch (error) {
      console.error(`[Room Service] ❌ Failed to stop recording ${egressId}:`, error);
      return false;
    }
  }

  /**
   * Get recording status and file information
   * @param egressId The egress ID to check
   * @returns Recording status and file info
   */
  async getRecordingStatus(egressId: string): Promise<{
    status: string;
    filePath?: string;
    fileSize?: number;
    duration?: number;
    error?: string;
  } | null> {
    try {
      const egressInfo = await this.egressClient.listEgress({ egressId });
      
      if (egressInfo.length === 0) {
        return { status: 'not_found', error: 'Egress not found' };
      }
      
      const egress = egressInfo[0];
      
      return {
        status: egress.status.toString(),
        filePath: egress.fileResults?.[0]?.filename,
        fileSize: egress.fileResults?.[0]?.size ? Number(egress.fileResults[0].size) : undefined,
        duration: egress.fileResults?.[0]?.duration ? Number(egress.fileResults[0].duration) : undefined,
      };
      
    } catch (error) {
      console.error(`[Room Service] Error getting recording status for ${egressId}:`, error);
      return { status: 'error', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Set up WebSocket connection info for audio streaming
   * @param roomName Room identifier
   * @returns WebSocket connection details
   */
  async setupWebSocketConnection(roomName: string): Promise<WebSocketConnectionInfo | null> {
    try {
      console.log(`[Room Service] Setting up WebSocket for room: ${roomName}`);
      
      const response = await axios.post(`${LIVESERVER_URL}/setup-websocket`, {
        roomName: roomName
      }, {
        timeout: 5000
      });

      if (response.data.success) {
        return {
          port: response.data.port,
          path: response.data.path,
          callSid: roomName // Use room name as callSid for consistency
        };
      } else {
        console.error(`[Room Service] WebSocket setup failed:`, response.data.error);
        return null;
      }

    } catch (error) {
      console.error(`[Room Service] WebSocket setup error:`, error);
      return null;
    }
  }

  /**
   * Notify liveserver that audio streaming has started
   * @param roomName Room identifier
   * @param callSid Call identifier for tracking
   */
  async notifyStreamingStarted(roomName: string, callSid: string): Promise<boolean> {
    try {
      const response = await axios.post(`${LIVESERVER_URL}/streaming-started`, {
        roomName,
        callSid
      }, {
        timeout: 3000
      });

      return response.data.success === true;
    } catch (error) {
      console.error(`[Room Service] Failed to notify streaming started:`, error);
      return false;
    }
  }

  /**
   * Clean up room and associated resources including stopping any active recordings
   * @param roomName Room identifier
   * @param callSid Call identifier
   * @param egressId Optional egress ID to stop recording
   */
  async cleanupRoom(roomName: string, callSid: string, egressId?: string): Promise<boolean> {
    try {
      console.log(`[Room Service] Starting comprehensive cleanup for room: ${roomName}`);
      
      // 1. Stop any active recordings first
      if (egressId) {
        try {
          console.log(`[Room Service] Stopping recording with egress ID: ${egressId}`);
          await this.stopRoomRecording(egressId);
        } catch (egressError) {
          console.error(`[Room Service] Error stopping recording ${egressId}:`, egressError);
          // Continue with other cleanup even if stopping recording fails
        }
      } else {
        // Try to find and stop any active egress for this room
        try {
          const activeEgresses = await this.egressClient.listEgress({ roomName });
          for (const egress of activeEgresses) {
            if (egress.status === EgressStatus.EGRESS_STARTING || egress.status === EgressStatus.EGRESS_ACTIVE) {
              console.log(`[Room Service] Found active recording ${egress.egressId} for room ${roomName}, stopping it`);
              await this.stopRoomRecording(egress.egressId);
            }
          }
        } catch (egressListError) {
          console.error(`[Room Service] Error listing/stopping egresses for room ${roomName}:`, egressListError);
        }
      }
      
      // 2. Delete the actual LiveKit room
      try {
        // Check if room exists before attempting to delete
        const rooms = await this.roomServiceClient.listRooms([roomName]);
        if (rooms.length > 0) {
          console.log(`[Room Service] Deleting LiveKit room: ${roomName}`);
          await this.roomServiceClient.deleteRoom(roomName);
          console.log(`[Room Service] ✅ LiveKit room deleted: ${roomName}`);
        } else {
          console.log(`[Room Service] Room ${roomName} doesn't exist or already deleted`);
        }
      } catch (roomError) {
        console.error(`[Room Service] Error deleting LiveKit room ${roomName}:`, roomError);
        // Continue with other cleanup even if room deletion fails
      }
      
      // 2. Call general cleanup function (database, state, etc.)
      await cleanupCall(callSid);
      
      console.log(`[Room Service] ✅ Complete room cleanup successful: ${roomName}`);
      return true;

    } catch (error) {
      console.error(`[Room Service] Cleanup error:`, error);
      return false;
    }
  }

  /**
   * Get current room status and participant info
   * @param roomName Room identifier
   */
  async getRoomStatus(roomName: string): Promise<{
    exists: boolean;
    participantCount: number;
    isAIConnected: boolean;
    error?: string;
  }> {
    try {
      const response = await axios.get(`${LIVESERVER_URL}/room-status/${roomName}`, {
        timeout: 3000
      });

      return {
        exists: response.data.exists,
        participantCount: response.data.participantCount || 0,
        isAIConnected: response.data.isAIConnected || false
      };

    } catch (error) {
      return {
        exists: false,
        participantCount: 0,
        isAIConnected: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test connectivity to liveserver and LiveKit infrastructure
   */
  async testConnectivity(): Promise<{
    liveserverReachable: boolean;
    livekitServerReachable: boolean;
    error?: string;
  }> {
    try {
      const response = await testLiveKitConnectivity();
      return {
        liveserverReachable: response.liveserver,
        livekitServerReachable: response.livekitServer,
        error: response.error
      };
    } catch (error) {
      return {
        liveserverReachable: false,
        livekitServerReachable: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

/**
 * Test basic connectivity to LiveKit infrastructure
 */
export async function testLiveKitConnectivity(): Promise<{
  success: boolean;
  liveserver: boolean;
  livekitServer: boolean;
  error?: string;
}> {
  const result = {
    success: false,
    liveserver: false,
    livekitServer: false,
    error: undefined as string | undefined
  };

  try {
    // Test 1: Check liveserver is running
    console.log('[LiveKit Test] Testing liveserver connectivity...');
    try {
      const liveserverResponse = await axios.get(`${LIVESERVER_URL}/health`, {
        timeout: 5000
      });
      result.liveserver = liveserverResponse.status === 200;
      console.log(`[LiveKit Test] Liveserver: ${result.liveserver ? 'OK' : 'FAILED'}`);
    } catch (err) {
      console.log('[LiveKit Test] Liveserver: FAILED (not responding)');
      result.liveserver = false;
    }

    // Test 2: Test LiveKit server connectivity through liveserver
    console.log('[LiveKit Test] Testing LiveKit server connectivity...');
    try {
      const testResponse = await axios.post(`${LIVESERVER_URL}/test-connection`, {
        serverHost: LIVEKIT_SERVER_HOST
      }, {
        timeout: 10000
      });
      result.livekitServer = testResponse.data?.success === true;
      console.log(`[LiveKit Test] LiveKit Server: ${result.livekitServer ? 'OK' : 'FAILED'}`);
    } catch (err) {
      console.log('[LiveKit Test] LiveKit Server: FAILED (connection error)');
      result.livekitServer = false;
    }

    result.success = result.liveserver && result.livekitServer;

    if (result.success) {
      console.log('[LiveKit Test] ✅ All connectivity tests passed');
    } else {
      const failures = [];
      if (!result.liveserver) failures.push('liveserver');
      if (!result.livekitServer) failures.push('LiveKit server');
      result.error = `Failed components: ${failures.join(', ')}`;
      console.log(`[LiveKit Test] ❌ Connectivity test failed: ${result.error}`);
    }

  } catch (error) {
    result.error = error instanceof Error ? error.message : 'Unknown error';
    console.error('[LiveKit Test] ❌ Test execution failed:', result.error);
  }

  return result;
}

/**
 * Quick connectivity check for use during initialization
 */
export async function quickConnectivityCheck(): Promise<boolean> {
  try {
    const result = await testLiveKitConnectivity();
    return result.success;
  } catch {
    return false;
  }
}

// Create singleton instance
export const livekitRoomService = new LiveKitRoomService();
