import os
import telnyx
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables from .env file located in the parent directory
env_path = Path(__file__).resolve().parent.parent / '.env'
load_dotenv(dotenv_path=env_path)

# Set up Telnyx API key and Connection ID
TELNYX_API_KEY = os.getenv("TELNYX_API_KEY")
TELNYX_CONNECTION_ID = os.getenv("TELNYX_CONNECTION_ID")

if not TELNYX_API_KEY:
    raise ValueError("TELNYX_API_KEY is not set in the environment variables")
if not TELNYX_CONNECTION_ID:
    raise ValueError("TELNYX_CONNECTION_ID is not set in the environment variables")

# Set the Telnyx API key
telnyx.api_key = TELNYX_API_KEY


def stop_playback(call_control_id, stop_type="current"):
    """
    Stops the audio playback for a given call.

    Parameters:
        call_control_id (str): The unique identifier of the call.
        stop_type (str): Whether to stop only the current audio ("current") or stop and clear the queue ("all").

    Returns:
        dict: The response from Telnyx API.
    """
    try:
        call = telnyx.Call(connection_id=TELNYX_CONNECTION_ID)
        call.call_control_id = call_control_id

        # Stop the current playback or all playback
        stop_response = call.playback_stop(stop=stop_type)

        print(f"Playback stop response: {stop_response}")
        return stop_response
    except Exception as e:
        print(f"Error in stopping playback for call {call_control_id}: {e}")
        raise
